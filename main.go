package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	_ "go.uber.org/automaxprocs"

	"github.com/MiaoSiLa/missevan-go/config/configsync"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/cmd/consumer"
	"github.com/MiaoSiLa/missevan-main/cmd/web"
	"github.com/MiaoSiLa/missevan-main/config"
)

const (
	appName = "missevan-main"
)

var (
	// Version control for missevan-main
	Version = "0.0.1-dev"

	commandMap map[string]Command
)

// Command interface
type Command interface {
	Name() string
	Run(conf *config.Config) error
}

func register() {
	commandMap = make(map[string]Command)
	cmds := []Command{
		web.NewCommand(),
		consumer.NewCommand(),
	}
	for _, cmd := range cmds {
		commandMap[cmd.Name()] = cmd
	}
}

func main() {
	serviceutil.SetAppName(appName)
	serviceutil.SetVersion(Version)

	flag := flag.NewFlagSet(os.Args[0]+" [mode]", flag.ExitOnError)

	configInCluster := flag.Bool("config-in-cluster", false, "config name in cluster")
	configFile := flag.String("config", "", "config file path")

	showVerbose := flag.Bool("verbose", false, "show verbose debug log")
	showHelp := flag.Bool("help", false, "show help message")

	if len(os.Args) < 2 {
		fmt.Fprintln(os.Stderr, "Please specify run mode")
		flag.Usage()
		os.Exit(1)
	}

	register()
	cmd, ok := commandMap[os.Args[1]]
	if !ok {
		fmt.Fprintf(os.Stderr, "Unknown run mode: %s\n", os.Args[1])
		flag.Usage()
		os.Exit(1)
	}

	err := flag.Parse(os.Args[2:])
	if err != nil {
		log.Fatalf("parse flag error: %v", err)
	}

	if *showHelp {
		flag.Usage()
		return
	}
	// 读取 config, 优先从配置中心获取
	if *configInCluster {
		err = config.LoadInCluster(appName, Version)
		if err != nil {
			log.Fatalf("load config in cluster error: %v", err)
		}
	} else if *configFile != "" {
		err = config.LoadFromYML(*configFile)
		if err != nil {
			log.Fatalf("config error: %v", err)
		}
	} else {
		fmt.Fprintln(os.Stderr, "Please specify a config method")
		flag.Usage()
		os.Exit(1)
	}

	if *showVerbose {
		config.Conf.Log.AccessLevel = "debug"
		config.Conf.Log.ErrorLevel = "debug"
	}

	if config.Conf.Log.Agent.Enabled && config.Conf.Log.Agent.Category == "" {
		config.Conf.Log.Agent.Category = cmd.Name()
	}
	err = logger.Init(appName, &config.Conf.Log)
	if err != nil {
		log.Fatalf("logger error: %v", err)
	}
	goutil.InitGoroutineLogger(logger.LogError)
	configsync.SetClientLogger(logger.WithFields(logger.Fields{}))

	err = cmd.Run(config.Conf)
	if err != nil {
		logger.Fatal(err)
	}
}
