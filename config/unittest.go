//go:build !release
// +build !release

package config

import (
	"github.com/MiaoSiLa/missevan-main/config/params"
)

// InitTestParams init params for testing only
func InitTestParams() {
	Conf.Params.URL = params.SectionURL{
		Main:            "https://www.missevan.com/",
		CDN:             "test://",
		AvatarURL:       "test://avatars/",
		ProfileURL:      "test://profile/",
		CoverURL:        "test://coversmini/",
		DefaultIconURL:  "test://avatars/icon01.png",
		DramaCoverURL:   "test://dramacoversmini/",
		DefaultCoverURL: "test://coversmini/nocover.png",
		RankDetailsURL:  "https://m.uat.missevan.com/ranking",
	}
	Conf.Params.NoticeSoundIDs = []int64{}
	Conf.Params.NoCheckDramaRevenueAuthUserIDs = []int64{}
	Conf.Params.Vip = params.SectionVip{
		MoreURLThemeSkin:   "test://themeskins",
		MoreURLAvatarFrame: "",
		PromotionalPrice:   "9.9",
		Privilege: params.Privilege{
			DiamondNum: 5,
			List: []params.PrivilegeItem{
				{
					Title:   "会员剧免费畅听",
					IconURL: "test://image/a.png",
				},
				{
					Title:   "精品剧集 8 折起",
					IconURL: "test://image/b.png",
				},
			},
		},
		IapProductIDsPrefix: params.IapProductIDsPrefix{
			MissevanIOS:        "com.missevan.CatEarFM.vip",
			MissevanGooglePlay: "cn.missevan.item.vip",
		},
		TradeAgreementURL: "https://link.uat.missevan.com/rule/vip-agreement",
		UserHomePageInfo: params.UserHomePageInfo{
			Guest: params.HomePageInfo{
				Title:    "开通会员，每天领钻石",
				Subtitle: "畅听精品好剧，最低 9.9 元/月",
			},
			UserVipNotYet: params.HomePageInfo{
				Title:    "开通会员，限时 9.9 元",
				Subtitle: "畅听精品好剧，最多可领 155 钻石",
			},
			UserVipInEffect: params.HomePageInfo{
				Title:    "会员权益生效中",
				Subtitle: "%s 到期，每日可领 5 钻石",
			},
			UserVipExpired: params.HomePageInfo{
				Title:    "会员权益已过期，续费享众多福利",
				Subtitle: "畅听精品好剧，每月最多可领 155 钻石",
			},
		},
		VipCenterURL: "https://m.uat.missevan.com/vip",
	}
	Conf.Params.Search = params.SectionSearch{
		HotSearchWordHotIconURL: "test://image/icon.png",
	}
	err := afterLoad()
	if err != nil {
		return
	}
}

func init() {
	InitTestParams()
}
