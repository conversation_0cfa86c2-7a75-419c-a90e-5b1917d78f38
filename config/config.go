package config

import (
	"os"

	yaml "gopkg.in/yaml.v2"

	"github.com/MiaoSiLa/missevan-go/config/configsync"
	goparams "github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger/log"
	"github.com/MiaoSiLa/missevan-main/config/params"
	"github.com/MiaoSiLa/missevan-main/service"
)

// Config is config structure.
type Config struct {
	Web     SectionWeb     `yaml:"web"`
	Log     log.Config     `yaml:"log"`
	HTTP    SectionHTTP    `yaml:"http"`
	Params  params.Params  `yaml:"params"`
	Service service.Config `yaml:"service"`
}

// SectionWeb config for mode web
type SectionWeb struct {
	Address string `yaml:"address"`
	Port    int    `yaml:"port"`
}

// SectionHTTP is sub section of config
type SectionHTTP struct {
	Mode           string `yaml:"mode"`
	RPC<PERSON>ey         string `yaml:"rpc_key"`
	DisableAPISign bool   `yaml:"disable_api_sign"`
	APPAPISignKey  string `yaml:"app_api_sign_key"`

	CSRFAllowTopDomains []string `yaml:"csrf_allow_top_domains"`
}

// Conf config for missevan-main
var Conf *Config

func init() {
	Conf = NewConfig()
}

// BuildDefaultConf is default config setting.
func BuildDefaultConf() Config {
	return *NewConfig()
}

// NewConfig new Config
func NewConfig() *Config {
	conf := &Config{}

	// web
	conf.Web.Port = 3000

	// log
	conf.Log = *log.DefaultConfig

	// http
	conf.HTTP.Mode = "release"
	conf.HTTP.RPCKey = "testkey"
	conf.HTTP.APPAPISignKey = "testkey"

	// Service
	conf.Service = *service.DefaultConfig

	return conf
}

// LoadFromYML load config from yml file
func LoadFromYML(confPath string) error {
	configFile, err := os.ReadFile(confPath)
	if err != nil {
		return err
	}

	err = yaml.Unmarshal(configFile, Conf)
	if err != nil {
		return err
	}
	return afterLoad()
}

// LoadInCluster load config from cluster
func LoadInCluster(appName, version string) error {
	err := configsync.InitSync("", appName, version, nil)
	if err != nil {
		return err
	}
	err = configsync.Unmarshal(Conf)
	if err != nil {
		return err
	}
	return afterLoad()
}

func afterLoad() error {
	// TODO: 移除 missevan-go 中的业务逻辑
	paramsConf := goparams.Params{
		URL: goparams.SectionURL{
			Main:            Conf.Params.URL.Main,
			CDN:             Conf.Params.URL.CDN,
			AvatarURL:       Conf.Params.URL.AvatarURL,
			ProfileURL:      Conf.Params.URL.ProfileURL,
			CoverURL:        Conf.Params.URL.CoverURL,
			DefaultIconURL:  Conf.Params.URL.DefaultIconURL,
			DramaCoverURL:   Conf.Params.URL.DramaCoverURL,
			DefaultCoverURL: Conf.Params.URL.DefaultCoverURL,
		},
	}
	goparams.InitParams(&paramsConf)
	return nil
}
