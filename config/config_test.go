package config

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/logger/log"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestBuildDefaultConf(t *testing.T) {
	assert := assert.New(t)
	conf := BuildDefaultConf()

	assert.Equal("", conf.Web.Address)
	assert.Equal(3000, conf.Web.Port)
	assert.Equal("release", conf.HTTP.Mode)
	assert.Equal(*log.DefaultConfig, conf.Log)
}

func TestLoadFromYML(t *testing.T) {
	assert := assert.New(t)

	err := LoadFromYML("")
	assert.Error(err)

	err = LoadFromYML("./config.go")
	assert.Error(err)

	err = LoadFromYML(`../config.example.yml`)
	assert.NoError(err)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.<PERSON><PERSON>eyChecker(t, tutil.YAML)
	kc.Check(Config{}, "web", "log", "http", "params", "service")
	kc.Check(SectionWeb{}, "address", "port")
	kc.Check(SectionHTTP{}, "mode", "rpc_key", "disable_api_sign", "app_api_sign_key", "csrf_allow_top_domains")
}
