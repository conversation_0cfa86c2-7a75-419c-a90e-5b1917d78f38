package params

// Params for static files
type Params struct {
	URL      SectionURL      `yaml:"url"`
	Security SectionSecurity `yaml:"security"`

	NoticeSoundIDs []int64 `yaml:"notice_sound_ids"`

	NoCheckDramaRevenueAuthUserIDs   []int64       `yaml:"no_check_drama_revenue_auth_user_ids"`
	DramaRevenueCheckSignPopTime     int64         `yaml:"drama_revenue_check_sign_pop_time"`
	DramaRevenueCheckSignPopCanClose int           `yaml:"drama_revenue_check_sign_pop_can_close"`
	Vip                              SectionVip    `yaml:"vip"`
	Search                           SectionSearch `yaml:"search"`
}

// SectionURL items
type SectionURL struct {
	Main            string `yaml:"main"`
	CDN             string `yaml:"cdn"`
	AvatarURL       string `yaml:"avatar_url"`
	ProfileURL      string `yaml:"profile_url"`
	CoverURL        string `yaml:"cover_url"`
	DefaultIconURL  string `yaml:"default_icon_url"`
	DramaCoverURL   string `yaml:"drama_cover_url"`
	DefaultCoverURL string `yaml:"default_cover_url"`
	RankDetailsURL  string `yaml:"rank_details_url"`
}

// SectionSecurity 敏感信息加密相关参数
type SectionSecurity struct {
	SensitiveInformationKey string `yaml:"sensitive_information_key"`
	SensitiveFixedIVKey     string `yaml:"sensitive_fixed_iv_key"`
}

// SectionVip 会员相关配置
type SectionVip struct {
	MoreURLThemeSkin    string              `yaml:"more_url_theme_skin"`
	MoreURLAvatarFrame  string              `yaml:"more_url_avatar_frame"`
	MoreURLAppearance   string              `yaml:"more_url_appearance"`
	PromotionalPrice    string              `yaml:"promotional_price"`
	Privilege           Privilege           `yaml:"privilege"`
	IapProductIDsPrefix IapProductIDsPrefix `yaml:"iap_product_ids_prefix"`
	TradeAgreementURL   string              `yaml:"trade_agreement_url"`
	UserHomePageInfo    UserHomePageInfo    `yaml:"user_home_page_info"`
	VipCenterURL        string              `yaml:"vip_center_url"`
	EnableVipCenter     bool                `yaml:"enable_vip_center"`
}

// Privilege 会员特权列表
type Privilege struct {
	DiamondNum int             `yaml:"diamond_num"`
	List       []PrivilegeItem `yaml:"list"`
}

// PrivilegeItem 会员特权信息
type PrivilegeItem struct {
	Title   string `yaml:"title" json:"title"`
	IconURL string `yaml:"icon_url" json:"icon_url"`
}

// SectionSearch 搜索相关配置
type SectionSearch struct {
	HotSearchWordHotIconURL string `yaml:"hot_search_word_hot_icon_url" json:"hot_search_word_hot_icon_url"`
}

// IapProductIDsPrefix 内购商品标识前缀
type IapProductIDsPrefix struct {
	MissevanIOS        string `yaml:"missevan_ios"`         // Apple Store
	MissevanGooglePlay string `yaml:"missevan_google_play"` // Google Play
}

// UserHomePageInfo 我的页用户会员信息
type UserHomePageInfo struct {
	Guest           HomePageInfo `yaml:"guest"`
	UserVipNotYet   HomePageInfo `yaml:"user_vip_not_yet"`
	UserVipInEffect HomePageInfo `yaml:"user_vip_in_effect"`
	UserVipExpired  HomePageInfo `yaml:"user_vip_expired"`
}

// HomePageInfo 我的页会员信息
type HomePageInfo struct {
	Title    string `yaml:"title"`
	Subtitle string `yaml:"subtitle"`
}
