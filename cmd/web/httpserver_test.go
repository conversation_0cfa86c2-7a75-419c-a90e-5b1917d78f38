package web

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/config"
)

func TestNewRouterEngine(t *testing.T) {
	assert := assert.New(t)
	conf := &config.Config{}
	handler.SetMode(handler.DebugMode)

	// NewRouterEngine 正确运行将会把 mode 设置成 ReleaseMode
	r := NewRouterEngine(conf)
	assert.NotNil(r)
	// 断言 mode 被改成了 ReleaseMode
	assert.Equal(handler.ReleaseMode, handler.Mode())
}
