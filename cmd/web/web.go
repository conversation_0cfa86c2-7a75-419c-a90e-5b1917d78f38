package web

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/json-iterator/go/extra"
	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/apisign"
	"github.com/MiaoSiLa/missevan-go/middlewares/security"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/controllers/app"
	"github.com/MiaoSiLa/missevan-main/controllers/backend"
	"github.com/MiaoSiLa/missevan-main/controllers/discovery"
	"github.com/MiaoSiLa/missevan-main/controllers/drama"
	"github.com/MiaoSiLa/missevan-main/controllers/dramareview"
	"github.com/MiaoSiLa/missevan-main/controllers/person"
	"github.com/MiaoSiLa/missevan-main/controllers/recommend"
	"github.com/MiaoSiLa/missevan-main/controllers/report"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc"
	"github.com/MiaoSiLa/missevan-main/controllers/sound"
	"github.com/MiaoSiLa/missevan-main/controllers/vip"
)

// Command web
type Command struct {
}

// NewCommand new Command
func NewCommand() *Command {
	return &Command{}
}

// Name of the Command
func (*Command) Name() string {
	return "web"
}

// Run the Command
func (c *Command) Run(conf *config.Config) error {
	g, ctx := errgroup.WithContext(context.Background())
	err := initService(&conf.Service)
	if err != nil {
		logger.Fatalf("service error: %v", err)
	}
	extra.RegisterFuzzyDecoders()
	srv := newWebServer(conf)
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", srv.Addr)
		return srv.ListenAndServe()
	})
	osCh := make(chan os.Signal, 1)
	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)
	select {
	case <-ctx.Done():
		err = ctx.Err()
		if err != nil {
			return err
		}
	case <-osCh:
	}
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown server failed: %v", err)
		return err
	}
	return nil
}

func newWebServer(conf *config.Config) *http.Server {
	r := NewRouterEngine(conf)

	hOldApp := oldAppHandler(conf)
	hOldApp.Mount(r)

	x := xHandler(conf)
	x.Mount(r)

	xV2 := xHandlerV2(conf)
	xV2.Mount(r)

	backendV2 := backend.HandlerV2(conf)
	backendV2.Mount(r)

	hRPC := rpc.Handler(conf)
	hRPC.Mount(r)

	hRPCV2 := rpc.HandlerV2(conf)
	hRPCV2.Mount(r)

	srv := NewServer(conf, r)
	return srv
}

// oldAppHandler 用于 missevan-app 项目迁移过来的接口，给线上旧版本的客户端调用
func oldAppHandler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Middlewares: gin.HandlersChain{
			security.CSRFMiddleware(conf.HTTP.CSRFAllowTopDomains...),
			appSign(conf),
			user.Middleware(),
		},
		SubHandlers: []handler.Handler{
			sound.Handler(),
			person.Handler(),
		},
	}
}

func xHandler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "x",
		Middlewares: gin.HandlersChain{
			security.CSRFMiddleware(conf.HTTP.CSRFAllowTopDomains...),
			appSign(conf),
			user.Middleware(),
		},
		SubHandlers: []handler.Handler{
			app.Handler(),
		},
	}
}

func xHandlerV2(conf *config.Config) handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "x",
		Middlewares: gin.HandlersChain{
			security.CSRFMiddleware(conf.HTTP.CSRFAllowTopDomains...),
			// 暂时只针对 Web 项目使用，App 项目不适用，因此暂时不做验签处理
			// appSignV2(conf),
			user.Middleware(),
		},
		SubHandlers: []handler.HandlerV2{
			dramareview.Handler(),
			drama.Handler(),
			person.HandlerV2(),
			sound.HandlerV2(),
			vip.HandlerV2(),
			discovery.HandlerV2(),
			report.HandlerV2(),
			recommend.HandlerV2(),
		},
	}
}

func appSign(conf *config.Config) gin.HandlerFunc {
	signFunc := apisign.Middleware([]byte(config.Conf.HTTP.APPAPISignKey))
	return func(c *gin.Context) {
		equip, err := util.ParseEquipment(c.Request)
		if err != nil {
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, gin.H{
					"code": v.Code,
					"info": v.Message,
				})
				return
			}
			_ = c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if equip.FromApp {
			// 里面有 c.Next()
			signFunc(c)
			return
		}
		c.Next()
	}
}

func appSignV2(conf *config.Config) gin.HandlerFunc {
	signFunc := apisign.MiddlewareV2([]byte(config.Conf.HTTP.APPAPISignKey))
	return func(c *gin.Context) {
		equip, err := util.ParseEquipment(c.Request)
		if err != nil {
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, handler.BasicResponseV2{
					Code:    v.Code,
					Message: v.Message,
				})
				return
			}
			_ = c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if equip.FromApp {
			// 里面有 c.Next()
			signFunc(c)
			return
		}
		c.Next()
	}
}
