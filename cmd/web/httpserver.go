package web

import (
	"fmt"
	"net/http"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/controllers/health"
)

// NewRouterEngine returns a new router engine
func NewRouterEngine(conf *config.Config) *gin.Engine {
	// set server mode
	handler.SetMode(conf.HTTP.Mode)

	r := gin.New()

	middlewares.WithGlobalMiddlewares(r)

	pprof.Register(r)

	h := health.Handler(conf)
	h.Mount(r)

	return r
}

// NewServer new http server
func NewServer(conf *config.Config, h http.Handler) *http.Server {
	logger.Debugf("HTTPD server is running on %s:%d.", conf.Web.Address, conf.Web.Port)
	srv := &http.Server{
		Addr: fmt.Sprintf("%s:%d", conf.Web.Address,
			conf.Web.Port),
		Handler: h}
	return srv
}
