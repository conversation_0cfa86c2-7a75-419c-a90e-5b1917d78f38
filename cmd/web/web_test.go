package web

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc"
)

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conf := new(config.Config)

	hOldApp := oldAppHandler(conf)
	assert.Equal("", hOldApp.Name)
	assert.Equal(3, len(hOldApp.Middlewares))
	require.Equal(2, len(hOldApp.SubHandlers))

	h := xHandler(conf)
	assert.Equal("x", h.Name)
	assert.Equal(3, len(h.Middlewares))
	require.Equal(1, len(h.SubHandlers))

	hV2 := xHandlerV2(conf)
	assert.Equal("x", hV2.Name)
	assert.Equal(2, len(hV2.Middlewares))
	require.Equal(8, len(hV2.SubHandlers))

	hRPC := rpc.Handler(conf)
	assert.Equal("rpc/missevan-main", hRPC.Name)
	assert.Equal(1, len(hRPC.Middlewares))
	require.Equal(6, len(hRPC.SubHandlers))

	hRPCV2 := rpc.HandlerV2(conf)
	assert.Equal("rpc/missevan-main", hRPCV2.Name)
	assert.Equal(1, len(hRPCV2.Middlewares))
	require.Equal(5, len(hRPCV2.SubHandlers))
}
