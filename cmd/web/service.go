package web

import (
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/bilibili/settle"
)

// TODO: 需要去掉不用的实例化
func initService(conf *service.Config) (err error) {
	service.DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		return
	}
	service.MainDB, err = servicedb.InitDatabase(&conf.MainDB)
	if err != nil {
		return
	}
	service.DramaDB, err = servicedb.InitDatabase(&conf.DramaDB)
	if err != nil {
		return
	}
	service.PayDB, err = servicedb.InitDatabase(&conf.PayDB)
	if err != nil {
		return
	}
	service.NewADB, err = servicedb.InitDatabase(&conf.NewADB)
	if err != nil {
		return
	}

	service.Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return
	}
	service.LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		return
	}

	service.MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return
	}
	service.PushService, err = pushservice.NewPushServiceClient(&conf.PushService)
	if err != nil {
		return err
	}
	service.SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		return err
	}

	service.Storage = storage.NewClient(conf.Storage)

	service.Captcha, err = captcha.NewClient(conf.Captcha)
	if err != nil {
		return
	}
	service.Geetest = geetest.NewClient(conf.Geetest)
	service.BilibiliSettle = settle.NewClient(conf.BilibiliSettle)

	service.Databus.AppLogPub = databus.New(&conf.Databus.AppLogPub)

	return service.AfterInit()
}
