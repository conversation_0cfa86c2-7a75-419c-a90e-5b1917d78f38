# Copilot Instructions for missevan-main

## Project Overview
This is a Go web service for the MissEvan (猫耳FM) platform, structured as a modular microservice with web and consumer command modes.

## Architecture Patterns

### Command Pattern Structure
- `main.go` implements a command pattern with two modes: `web` (HTTP server) and `consumer` (message consumer)
- Configuration supports both cluster config and YAML files via `--config-in-cluster` or `--config` flags
- Service components are organized under `service/` with shared initialization patterns

### Controller-Service Architecture
- Controllers are organized by domain: `app/`, `person/`, `drama/`, `discovery/`, etc.
- Each controller package exports a `Handler()` function returning `handler.Handler` or `HandlerV2`
- Actions follow the pattern: `ActionName(c *handler.Context) (handler.ActionResponse, string, error)`
- RPC controllers are grouped under `controllers/rpc/` for internal service communication

### Handler Registration Pattern
```go
// Standard pattern for controller handlers
func Handler() handler.Handler {
    return handler.Handler{
        Name: "controller-name",
        Actions: map[string]*handler.Action{
            "endpoint": handler.NewAction(handler.GET, ActionName, loginRequired),
        },
    }
}
```

### Testing Conventions
- Each package has `TestMain(m *testing.M)` that calls `service.InitTest()`
- Test files use `handler.NewTestContext()` for HTTP tests and `handler.NewRPCTestContext()` for RPC tests
- Key checking pattern: `tutil.NewKeyChecker(t, tutil.Actions).Check(h.Actions, "expected", "endpoints")`

## Configuration & Services

### Multi-Database Setup
- `MainDB`, `DramaDB`, `PayDB`, `NewADB` - different databases for domain separation
- Redis instances: `Redis` (main), `LRURedis` (caching)
- External services: MRPC (internal RPC), PushService, Storage, Captcha/Geetest

### Service Initialization
- `service.Config` contains all service configurations
- Database models follow the pattern `models/domain/mmodelname/`
- Use `service.Redis`, `service.MainDB` etc. for accessing initialized services

## Development Workflows

### Build & Run
```bash
# Build for production
make rider-build

# Run web server
./missevan-main web --config config.yml

# Run consumer
./missevan-main consumer --config config.yml
```

### Testing
- Unit tests: `go test ./...`
- Tests require database setup via `service.InitTest()`
- Use build tag `!release` for test-only code (see `service/unittest.go`)

### Code Quality
- Linting configured in `.golangci.yml` with project-specific rules
- Forbidden patterns: use `util.TimeNow()` instead of `time.Now()`, `servicedb.IsErrNoRows()` instead of `sql.ErrNoRows`
- Import organization: local packages (`github.com/MiaoSiLa`) grouped separately

## Critical Patterns

### RPC vs REST Endpoints
- `/rpc/missevan-main/` prefix for internal service communication
- `/x/` prefix for public REST APIs
- RPC handlers use middleware for authentication: `rpc.Middleware(conf.HTTP.RPCKey)`

### Error Handling
- Use `actionerrors.ErrParams`, `actionerrors.ErrServerInternal()`, `actionerrors.ErrBadRequest()`
- Return pattern: `(handler.ActionResponse, string, error)` where string is user message

### Authentication & Authorization
- Login required actions: set `LoginRequired: true` in `ActionOption`
- Role-based access: use `user.IsRole()` middleware
- User context: access via `c.UserID()`, `c.User()`

### Versioning Strategy
- V1 handlers use `handler.Handler` with `NewAction()`
- V2 handlers use `handler.HandlerV2` with `NewActionV2()` and `ActionOption{}`
- Gradual migration from V1 to V2 pattern

## External Dependencies
- Gin framework for HTTP routing
- GORM for database ORM
- Redis for caching and queues
- Custom `missevan-go` shared library for common functionality
- Build dependencies managed via `gomod.sh` script for private repositories
