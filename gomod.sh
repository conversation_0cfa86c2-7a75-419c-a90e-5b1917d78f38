#!/bin/sh
set -e

hp() {
  http_proxy=$proxy https_proxy=$proxy no_proxy=localhost,*********/8,10.0.0.0/8,**********/12,***********/16,**********/10,*********/4,240.0.0.0/4,hub.maoer.co $@
}

gomod() {
  if [ -n "$2" ]; then
    cd /go/src/$1
  fi

  for dep in `GO111MODULE=off go list -f '{{ join .Deps "\n" }}' ./... | grep github.com/MiaoSiLa | sed -e 's/^\(.*\/MiaoSiLa\/[^\/]*\?\)\/.*/\1/g' | sort | uniq`; do
    if [ ! -d /go/src/$dep ] && [ "$dep" != "$1" ]; then
      echo $dep
      mkdir -p /go/src/$dep

      dep2=`echo $dep | sed -e 's/^github.com\/MiaoSiLa\//git.bilibili.co\/maoer\//'`
      echo "$dep -> $dep2"
      git clone --single-branch --branch ${mgobranch} --depth=1 https://${GITLAB_TOKEN}@$dep2.git /go/src/$dep

      gomod $dep 1
    fi
  done
}

echo mgobranch=${mgobranch}

gomod github.com/MiaoSiLa/missevan-main
