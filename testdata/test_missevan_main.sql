-- GRANT SELECT ON `drama_revenue_reviewer_info` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `drama_revenue_reviewer_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  ,`mobile_encrypt` varchar(255) NOT NULL COMMENT '手机号码（加密）'
  ,`region` smallint unsigned NOT NULL COMMENT '国际电话区号'
  ,KEY `idx_modifiedtime` (`modified_time`)
  ,UNIQUE KEY `uk_userid` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集收益后台用户信息表';

-- GRANT SELECT ON `m_comment_ad` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_comment_ad` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间。单位：秒'
  ,`title` varchar(50) NOT NULL COMMENT '广告标题'
  ,`app_url` varchar(255) NOT NULL COMMENT 'App 广告跳转链接'
  ,`web_url` varchar(255) NOT NULL COMMENT 'Web 广告跳转链接'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '上线时间。单位：秒'
  ,`end_time` bigint NOT NULL DEFAULT '0' COMMENT '下线时间。单位：秒'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论区小黄条广告表';

INSERT INTO `m_comment_ad`
  (`id`, `create_time`, `modified_time`, `title`, `app_url`, `web_url`, `start_time`, `end_time`)
VALUES
  -- TestActionGetAdInfo
  -- TestFindCommentAdByDramaID
  (1001, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试小黄条', 'missevan://test/3', 'https://static.com', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 60)
  ,(1002, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试小黄条未生效', 'missevan://test/3', 'https://static.com', UNIX_TIMESTAMP() + 30, UNIX_TIMESTAMP() + 60)
;

-- GRANT SELECT ON `m_comment_ad_element` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_comment_ad_element` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间。单位：秒'
  ,`element_type` tinyint NOT NULL COMMENT '元素类型。2: 剧集'
  ,`element_id` bigint NOT NULL COMMENT '元素 ID'
  ,`comment_ad_id` bigint NOT NULL COMMENT '评论区小黄条广告 ID'
  ,UNIQUE KEY `uk_elementtype_elementid_commentadid` (`element_type`,`element_id`,`comment_ad_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论区小黄条广告和元素关系表';

INSERT INTO `m_comment_ad_element`
  (`id`, `create_time`, `modified_time`, `element_type`, `element_id`, `comment_ad_id`)
VALUES
  -- TestActionGetAdInfo
  -- TestFindCommentAdByDramaID
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 3000, 1001)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 3001, 1002)
;

-- GRANT SELECT, UPDATE ON `m_emote_exclusive_element` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_emote_exclusive_element` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`element_id` bigint NOT NULL COMMENT '元素 ID'
  ,`element_type` tinyint NOT NULL COMMENT '元素类型。1：剧集'
  ,`package_id` bigint NOT NULL COMMENT '表情包 ID'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '解锁生效单位时间戳，单位：秒'
  ,`end_time` bigint NOT NULL DEFAULT '0' COMMENT '解锁截止时间戳，单位：秒'
  ,`more` json DEFAULT NULL COMMENT '更多信息，文档地址：https://info.missevan.com/pages/viewpage.action?pageId=97888451'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='表情包专属元素表';

INSERT INTO `m_emote_exclusive_element`
  (`id`, `create_time`, `modified_time`, `element_id`, `element_type`, `package_id`, `start_time`,`end_time`,`more`)
VALUES
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 52347, 1, 2333, 0, 0, '{"unlock":false, "unlock_score": 233, "tip": "测试 tip"}')
;

-- GRANT SELECT ON `m_badge` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_badge` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`element_type` tinyint NOT NULL COMMENT '称号所属类型。1：虚拟偶像'
  ,`element_id` bigint NOT NULL COMMENT '元素 ID'
  ,`title` varchar(20) NOT NULL COMMENT '称号名'
  ,`intro` varchar(50) NOT NULL COMMENT '称号简介'
  ,`cover` varchar(255) NOT NULL COMMENT '称号卡片背景图片'
  ,`expire_duration` bigint NOT NULL DEFAULT '0' COMMENT '称号获得后过期时长，为 0 时表示不会过期，单位：秒'
  ,`more` json NOT NULL COMMENT '其他信息'
  ,`icon` varchar(125) DEFAULT NULL COMMENT '称号 icon'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='称号表';

-- TestNewGetBadgeParam、TestActionGetBadge
INSERT INTO `m_badge`
  (`id`, `create_time`, `modified_time`, `element_type`, `element_id`, `title`, `intro`,`cover`,`expire_duration`, `more`, `icon`)
VALUES
  (10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 0, '小耳朵', 'test', '', 0, '', '');

-- GRANT SELECT, INSERT ON `m_user_badge` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_user_badge` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`badge_id` bigint NOT NULL COMMENT '称号 ID'
  ,`expire_time` bigint NOT NULL DEFAULT '0' COMMENT '过期时间戳，单位：秒'
  ,`status` tinyint NOT NULL DEFAULT '0' COMMENT '使用状态，0: 未佩戴；1: 佩戴中'
  ,`no` bigint NOT NULL COMMENT '称号编号，不同称号的编号是分开递增计数的'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户称号表';

CREATE UNIQUE INDEX `uk_userid_badgeid` ON m_user_badge(`user_id`,`badge_id`);

CREATE UNIQUE INDEX `uk_badgeid_no` ON m_user_badge(`badge_id`, `no`);

-- GRANT SELECT ON `missevan_main`.`m_avatar_frame` TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `m_avatar_frame` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`name` varchar(20) NOT NULL COMMENT '头像框名称'
  ,`intro` varchar(255) NOT NULL DEFAULT '' COMMENT '头像框简介，支持 HTML'
  ,`frame` varchar(125) NOT NULL COMMENT '头像框地址'
  ,`start_time` bigint DEFAULT '0' COMMENT '头像框可用开始时间。单位：秒'
  ,`end_time` bigint DEFAULT '0' COMMENT '头像框可用截止时间，为 0 时表示永久有效。单位：秒'
  ,`expire_duration` bigint DEFAULT '0' COMMENT '头像框获得后过期时长，为 0 时表示不会过期。单位：秒'
  ,`icon` varchar(125) DEFAULT NULL COMMENT '头像框 icon'
  ,`type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '0：普通挂件；1：会员挂件'
  ,`more` json DEFAULT NULL COMMENT '更多信息。其中 show_drama_ids 字段中保存该头像框可被展示的剧集播放页的剧集 IDs。e.g. {"show_drama_ids": [1, 2]}'
  ,`sort` bigint NOT NULL DEFAULT '0' COMMENT '排序，值越小越靠前展示'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户（主站）头像框';

INSERT INTO `m_avatar_frame`
  (`id`, `create_time`, `modified_time`, `name`, `intro`, `frame`, `start_time`, `end_time`, `expire_duration`, `icon`, `type`, `more`)
VALUES
  -- FindValidOne
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试头像框', '<p>简介</p>', 'test://test.webp', 0, 0, 0, 'test://icon/test.webp', 0, NULL)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试头像框', '简介', 'test://test.webp', 0, 100, 0, 'test://icon/test.webp', 0, NULL)
  -- TestActionSendAvatarFrame
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试头像框', '简介', 'test://test.webp', 0, 0, 86400, 'test://icon/test.webp', 0, NULL)
  -- TestListWearingByUserIDs、TestActionListAvatarFrame
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试头像框 4', '简介 4', 'test://test_4.webp', 0, 0, 0, 'test://icon/test_4.webp', 0, NULL)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试头像框 5', '简介 5', 'test://test_5.webp', 0, 0, 0, 'test://icon/test_5.webp', 0, '{"show_drama_ids": [23333]}')
  -- TestListVipAvatarFrame
  -- TestActionRecommendBlocks
  -- TestListWearingByUserIDs
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '会员头像框 1', '简介 1', 'test://test_1.webp', 0, 0, 0, 'test://icon/test_a.webp', 1, NULL)
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '会员头像框 2', '简介 2', 'test://test_2.webp', 0, 0, 0, 'test://icon/test_b.webp', 1, NULL)
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '会员头像框 3', '简介 3', 'test://test_3.webp', 0, 0, 0, 'test://icon/test_c.webp', 1, NULL)
;

-- GRANT INSERT, SELECT, UPDATE ON `missevan_main`.`m_user_avatar_frame_map` TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `m_user_avatar_frame_map` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`avatar_frame_id` bigint NOT NULL COMMENT '头像框 ID，m_avatar_frame 表主键'
  ,`expire_time` bigint DEFAULT '0' COMMENT '头像框过期时间，为 0 时表示永久有效。单位：秒'
  ,`status` tinyint DEFAULT '0' COMMENT '使用状态。0: 未佩戴；1: 佩戴中'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户与头像框关系表';

INSERT INTO `m_user_avatar_frame_map`
  (`id`, `create_time`, `modified_time`, `user_id`, `avatar_frame_id`, `expire_time`, `status`)
VALUES
  -- TestListWearingByUserIDs
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 4, 0, 1)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 5, 0, 1)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3222, 6, 0, 1)
  -- TestGetOnWearVipAvatarFrames
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3223, 6, 0, 1)
;

-- GRANT SELECT ON `missevan_main`.`m_homepage_rank` TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `m_homepage_rank` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间，单位：秒'
  ,`bizdate` date NOT NULL COMMENT '业务日期'
  ,`type` tinyint NOT NULL COMMENT '榜单类型（1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜）'
  ,`sub_type` tinyint NOT NULL COMMENT '榜单子类型（1: 日榜；2: 周榜；3: 月榜）'
  ,`data` json DEFAULT NULL COMMENT '榜单数据（对应榜单排序后的元素 ID【音频 ID 或剧集 ID】，e.g. [8012,4002,705,888]）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='榜单表';

INSERT INTO `m_homepage_rank`
  (`id`, `create_time`, `modified_time`, `bizdate`, `type`, `sub_type`, `data`)
VALUES
  -- TestGetDiscoveryRanks
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 1, 1, '[11, 12, 13]')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 1, 2, '[11, 12, 13]')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP() - 24*3600, '%Y-%m-%d'), 2, 2, '[11, 12, 13]')
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 2, 3, '[11, 12, 13]')
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 3, 2, '[11, 12, 13]')
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 3, 2, '[11, 12, 13]')
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 4, 1, '[11, 12, 13]')
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 4, 2, '[11, 12, 13]')
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 5, 1, '[11, 12, 13]')
  ,(10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 5, 2, '[11, 12, 13]')
  ,(11, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 6, 2, '[1217695, 1217696, 1217697]')
  ,(12, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), 6, 3, '[1217695, 1217696, 1217697]')
;

-- GRANT SELECT ON `m_persona_rank` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_persona_rank` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间，单位：秒'
  ,`persona_id` bigint NOT NULL COMMENT '画像 ID'
  ,`rank_type` tinyint NOT NULL COMMENT '榜单类型（同榜单表 m_homepage_rank 的 type）'
  ,`sort` bigint NOT NULL DEFAULT '0' COMMENT '榜单排序'
  ,`name` varchar(50) NOT NULL COMMENT '榜单名称'
  ,`active` tinyint NOT NULL COMMENT '是否默认选中，0: 否；1: 是'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户画像与榜单关联表'
;

INSERT INTO `m_persona_rank`
  (`id`, `create_time`, `modified_time`, `persona_id`, `rank_type`, `sort`, `name`, `active`)
VALUES
  -- TestListByPersonaID
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2002, 8, 1, '热门搜索', 1)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2002, 1, 2, '新品榜', 0)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2002, 2, 3, '人气榜', 0)
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2002, 4, 4, '免费榜', 0)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2002, 5, 5, '言情榜', 0)
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2002, 7, 6, '直播榜', 0)
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2002, 6, 7, '声音恋人榜', 0)
;

-- GRANT SELECT ON `missevan_main`.`m_user_history` TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `m_user_history` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,`delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`element_id` bigint NOT NULL COMMENT '元素 ID'
  ,`element_type` tinyint NOT NULL COMMENT '元素类型（1 音频，2 剧集，3 直播间）'
  ,`access_time` bigint NOT NULL COMMENT '访问时间（毫秒）'
  ,`more` json DEFAULT NULL COMMENT '更多详情'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户历史记录';

INSERT INTO `m_user_history`
  (`id`,`create_time`,`modified_time`,`delete_time`,`user_id`,`element_id`,`element_type`,`access_time`,`more`)
VALUES
  -- TestListLastviewSounds
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1001, 2001, 2, UNIX_TIMESTAMP()*1000, '{"last_play_sound": {"id": 3001, "completion": 0}}')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1001, 2002, 2, UNIX_TIMESTAMP()*1000, '{"play_sound": {"id": 301, "completion": 0}}')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1001, 2003, 2, UNIX_TIMESTAMP()*1000, '{"last_play_sound": {"id": 3002, "completion": 0}}')
  -- TestActionGetUserLastviewEpisode
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 901, 101, 2, UNIX_TIMESTAMP()*1000, '{"last_play_sound": {"id": 1002, "completion": 0}}')
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 901, 102, 2, UNIX_TIMESTAMP()*1000, '{"last_play_sound": {"id": 2003, "completion": 0}}')
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 901, 103, 2, UNIX_TIMESTAMP()*1000, '{"last_play_sound": {"id": 3001, "completion": 0}}')
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 901, 105, 2, UNIX_TIMESTAMP()*1000, '{"last_play_sound": {"id": 5001, "completion": 0}}')
;

-- GRANT SELECT ON `missevan_main`.`user_certification` TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `user_certification` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后修改时间，单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`subtitle` varchar(100) NOT NULL COMMENT '用户认证头衔'
  ,UNIQUE KEY `uk_userid` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户头衔认证信息';

INSERT INTO `user_certification`
  (`id`,`create_time`,`modified_time`,`user_id`,`subtitle`)
VALUES
  -- TestFindByUserID、TestActionGetAuthInfo
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 12, "测试认证头衔")
;

-- GRANT SELECT ON `m_user_config` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_user_config` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`buvid` varchar(64) NOT NULL DEFAULT '' COMMENT '设备号'
  ,`app_config` json NOT NULL COMMENT '用户 APP 选项设置，personalized_recommend 表示个性化推荐开关，0 为关闭，1 为开启'
  ,UNIQUE KEY `uk_userid_buvid` (`user_id`,`buvid`)
  ,KEY `idx_modifiedtime` (`modified_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置项信息表'
;
