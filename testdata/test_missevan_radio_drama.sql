-- GRANT SELECT ON `radio_drama_dramainfo` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_dramainfo` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '剧集 ID'
  ,`name` varchar(60) DEFAULT NULL COMMENT '剧集名称'
  ,`cover` varchar(255) DEFAULT NULL COMMENT '剧集海报'
  ,`cover_color` int unsigned NOT NULL DEFAULT '12434877' COMMENT 'RGB 颜色值'
  ,`abstract` text COMMENT '剧集简介'
  ,`integrity` tinyint(1) NOT NULL COMMENT '完结度'
  ,`age` tinyint NOT NULL COMMENT '年代'
  ,`origin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '创作类型'
  ,`author` varchar(30) DEFAULT NULL COMMENT '原作者'
  ,`birthday` tinyint(1) NOT NULL DEFAULT '0' COMMENT '生日剧'
  ,`cv` varchar(30) DEFAULT NULL COMMENT '生日 CV'
  ,`ip` tinyint(1) NOT NULL DEFAULT '0' COMMENT '同人剧'
  ,`ipname` varchar(30) DEFAULT NULL COMMENT '原作标签'
  ,`type` int NOT NULL COMMENT '分类'
  ,`newest` varchar(30) DEFAULT '' COMMENT '更新至'
  ,`organization_id` int unsigned NOT NULL DEFAULT '0' COMMENT '社团 ID'
  ,`user_id` int NOT NULL COMMENT '所属用户 ID'
  ,`username` varchar(20) NOT NULL COMMENT '所属用户名'
  ,`checked` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态'
  ,`create_time` int NOT NULL COMMENT '发布时间'
  ,`lastupdate_time` int NOT NULL DEFAULT '0' COMMENT '最后编辑时间'
  ,`view_count` bigint NOT NULL DEFAULT '0' COMMENT '查看次数'
  ,`comment_count` int unsigned NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`catalog` int NOT NULL DEFAULT '0' COMMENT '分类 ID'
  ,`alias` varchar(60) DEFAULT NULL COMMENT '别名'
  ,`pay_type` int unsigned NOT NULL DEFAULT '0' COMMENT '付费类型，0: 免费；1: 单音付费；2: 剧集付费'
  ,`push` tinyint unsigned DEFAULT '0' COMMENT '新增单集是否过审推送'
  ,`refined` int unsigned NOT NULL DEFAULT '0' COMMENT '属性，比特位第一位为 1 标识擦边球，比特位第二位为 1 标识日本地区禁听，比特位第三位为 1 标识互动广播剧，比特位第四位为 1 标识无损音质广播剧'
  ,`police` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否报警'
  ,`ip_id` int unsigned NOT NULL DEFAULT '0' COMMENT '剧集所属 IP 的 ID'
  ,`subscription_num` int unsigned DEFAULT '0' COMMENT '订阅（追剧）人数'
  ,`show_revenue` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'UP 主是否能否查看该剧集的的收益（1 为可查看，0 为不可查看）'
  ,`name_letters` varchar(60) NOT NULL DEFAULT '' COMMENT '剧集名称每个字的首字母'
  ,`vip` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否为会员剧，0：否；1：是'
  ,KEY `IDX_SHOW_REVENUE` (`show_revenue`)
  ,KEY `IDX_CHECKED_USER_ID` (`checked`,`user_id`)
  ,KEY `IDX_TYPE` (`type`)
  ,KEY `IDX_CATALOG` (`catalog`)
  ,KEY `idx_checked_catalog_subscription_num` (`checked`,`catalog`,`subscription_num`)
  ,KEY `idx_checked_catalog_lastupdate_time` (`checked`,`catalog`,`lastupdate_time`)
  ,KEY `idx_nameletters` (`name_letters`)
  ,KEY `idx_vip` (`vip`)
) COMMENT='剧集信息表';

INSERT INTO `radio_drama_dramainfo`
  (`id`, `name`, `cover`, `cover_color`, `abstract`, `integrity`, `age`, `origin`, `author`, `birthday`, `cv`, `ip`, `ipname`, `type`, `newest`, `organization_id`, `user_id`, `username`, `checked`, `create_time`, `lastupdate_time`, `view_count`, `comment_count`, `catalog`, `alias`, `pay_type`, `push`, `refined`, `police`, `ip_id`, `subscription_num`, `show_revenue`, `name_letters`, `vip`)
VALUES
  -- TestGetDramaViewCount
  (1, '测试更改修改查看次数（勿删）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '简介 1', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionBatchSubscribe
  -- TestBatchSubscribe
  -- TestActionGetDramaInfo
  -- TestActionRankList
  ,(2, '测试批量追剧', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 1009, 0, 1, 'ACS', 0)
  ,(3, '测试批量追剧（下架的剧）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 3, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(4, '测试批量追剧', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- ID: 5 用来测试不存在的剧集请跳过
  ,(6, '测试批量追剧（用户已经追过的剧）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionGetDramaInfo
  -- TestListDramaInfoByIDs
  ,(7, '剧集名称（审核通过）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '简介', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 4, 89, NULL, 2, 0, 0, 0, 1, 0, 1, 'ACS', 1)
  -- TestListRankDramaInfoByIDs
  ,(8, '剧集名称（审核通过）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 2, 0, 0, 0, 1, 0, 1, 'ACS', 0)
  ,(9, '剧集名称（擦边球 1）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 2, 0, 1, 0, 1, 0, 1, 'ACS', 0)
  ,(10, '剧集名称（报警）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 2, 0, 0, 1, 1, 0, 1, 'ACS', 0)
  -- TestRankParam_getDramaAndSoundRank
  ,(11, '剧集名称（审核通过）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 2, 0, 0, 0, 1, 0, 1, 'ACS', 0)
  ,(12, '剧集名称（审核通过）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 2, 0, 0, 0, 1, 0, 1, 'ACS', 0)
  ,(13, '剧集名称（审核通过）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 2, 0, 0, 0, 1, 0, 1, 'ACS', 0)
  -- TestActionGetUserLastviewed
  ,(101, '测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(102, '测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(103, '测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 3, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(104, '测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(105, '测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestGetDramaIDBySoundID
  ,(300, '测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionGetAdInfo
  ,(3000, '测试 ad info', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionExclusiveEmote
  ,(23333, '测试 exclusive emote', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionEmoteExclusive
  ,(52347, '专属表情包测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, UNIX_TIMESTAMP(), 0, 4, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestGetSubscribeDramaCvIDs、TestListDramaMainOrMinorCvIDs、TestFindDramasViewCount
  ,(52348, '测试获取订阅剧集声优', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 90, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(52349, '测试获取订阅剧集声优', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 100, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(52350, '测试获取订阅剧集声优', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 100, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionGetDramaEpisodes
  ,(52351, '剧集名称（未过审）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 0, UNIX_TIMESTAMP(), 0, 100, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(52352, '剧集名称（审核通过）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 100, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionGetDramaInfoBySounds
  ,(52353, '剧集名称（审核通过）', '202406/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 100, 0, 89, NULL, 1, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(52354, '剧集名称（审核通过）', '202404/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 50, 8, 89, NULL, 0, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  -- TestGetQualityDramaCvNames、TestGetSoundIDDramaCardMap、TestGetDramaCardTags、TestGetDramaIDSpecialTagMap、TestGetMyCatchUpTagDramaIDs
  ,(52355, '剧集名称（审核通过）', '202404/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '剧集简介', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 50, 8, 89, NULL, 0, 0, 0, 0, 1, 0, 1, 'ACS', 0)
  ,(52356, '剧集名称（审核通过）', '202406/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '剧集简介', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 100, 0, 89, NULL, 1, 0, 0, 0, 2, 0, 1, 'ACS', 0)
  ,(52357, '剧集名称（审核通过）', '202404/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '剧集简介', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 50, 8, 89, NULL, 0, 0, 0, 0, 0, 0, 1, 'ACS', 0)
  ,(52358, '剧集名称（审核通过）', '202404/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 50, 8, 89, NULL, 0, 0, 0, 0, 1, 0, 1, 'ACS', 0)
  -- TestFindSameIPRIDDramaIDs
  ,(52359, '剧集名称（审核通过）', '202404/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '剧集简介', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 50, 8, 89, NULL, 0, 0, 0, 0, 10, 0, 1, 'ACS', 0)
  ,(52360, '剧集名称（审核通过）', '202406/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '剧集简介', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 100, 0, 89, NULL, 1, 0, 0, 0, 10, 0, 1, 'ACS', 0)
  ,(52361, '剧集名称（审核通过）', '202404/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '剧集简介', 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 1, UNIX_TIMESTAMP(), 0, 50, 8, 89, NULL, 0, 0, 0, 0, 11, 0, 1, 'ACS', 0)
  ,(52362, '剧集名称（审核未通过）', '202404/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 1, 'test', 2, UNIX_TIMESTAMP(), 0, 50, 8, 89, NULL, 0, 0, 0, 0, 1, 11, 1, 'ACS', 0)
  -- TestGetDramaCheckStatus
  ,(52363, '测试剧集状态为审核中', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 1234, 'test_user_name', 0, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 2, 0, 0, 0, 1, 'ACS', 0)
  ,(52364, '测试剧集状态为再审核', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 1234, 'test_user_name', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 6, 0, 0, 0, 1, 'ACS', 0)
;

-- GRANT SELECT ON `radio_drama_episode` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_episode` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '单集 ID'
  ,`name` varchar(30) NOT NULL COMMENT '单集名称'
  ,`drama_id` int(11) NOT NULL COMMENT '剧集 ID'
  ,`sound_id` int(11) NOT NULL COMMENT '音频 ID'
  ,`date` int(10) NOT NULL DEFAULT '0' COMMENT '发表日期'
  ,`order` int(11) NOT NULL COMMENT '序号'
  ,`type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型, 0: 正片; 1: 访谈; 2: 音乐; 3: 更多资源'
  ,`pay_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '付费类型, 0: 免费; 1: 单音付费; 2: 剧集付费'
  ,`create_time` int(10) NOT NULL COMMENT '创建时间'
  ,`modified_time` int(10) NOT NULL COMMENT '更新时间'
  ,`subtitle` varchar(30) NOT NULL DEFAULT '' COMMENT '单集副标题'
) COMMENT='单集信息表';

INSERT INTO `radio_drama_episode`
  (`id`, `name`, `drama_id`, `sound_id`, `date`, `order`, `type`, `pay_type`, `create_time`, `modified_time`, `subtitle`)
VALUES
  -- TestGetDramaIDBySoundID
  (1, '单集一', 300, 1999, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestActionGetAdInfo
  ,(2, '单集二', 3000, 19996, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(3, '单集三', 3001, 19997, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestActionExclusiveEmote
  -- TestEmoteExclusiveParam_findExclusiveEmote
  -- TestTsParam_checkTsDramaLimit
  ,(4, '单集四', 52347, 1, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestActionListAvatarFrame
  ,(5, 'test', 23333, 666, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestGetSubscribeDramaCvIDs、TestListDramaMainOrMinorCvIDs
  ,(6, 'test', 52348, 667, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(7, 'test', 52349, 668, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(8, 'test', 52350, 669, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestActionGetUserLastviewEpisode
  -- TestListDramaFirstSoundIDs
  ,(16, 'test', 101, 1001, UNIX_TIMESTAMP(), 1, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(17, 'test', 101, 1002, UNIX_TIMESTAMP(), 2, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(18, 'test', 101, 1003, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(19, 'test', 102, 2001, UNIX_TIMESTAMP(), 2, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(20, 'test', 102, 2002, UNIX_TIMESTAMP(), 1, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(21, 'test', 102, 2003, UNIX_TIMESTAMP(), 3, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(22, 'test', 103, 3001, UNIX_TIMESTAMP(), 1, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(23, 'test', 103, 3002, UNIX_TIMESTAMP(), 1, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(24, 'test', 103, 3003, UNIX_TIMESTAMP(), 1, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(25, 'test', 104, 4001, UNIX_TIMESTAMP(), 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestActionGetDramaEpisodes
  ,(26, 'test', 52352, 1217698, UNIX_TIMESTAMP(), 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(27, 'test', 52352, 1217699, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestGetDramaIDsBySoundIDs、TestActionGetDramaInfoBySounds
  ,(28, 'test', 52351, 1217700, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(29, 'test', 52353, 1217701, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(30, 'test', 52354, 1217702, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestGetSoundIDDramaCardMap
  ,(31, 'test', 52355, 1217771, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(32, 'test', 52355, 1217772, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(33, 'test', 52356, 1217773, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(34, 'test', 52357, 1217774, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  -- TestNewInitialPassSoundsParams、TestActionInitialPassSounds
  ,(35, 'test', 52351, 1217776, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(36, 'test', 52352, 1217777, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(37, 'test', 52353, 1217778, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
;

-- GRANT SELECT, INSERT ON `radio_drama_subscription` TO 'missevan_main'@'%'
CREATE TABLE `radio_drama_subscription` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `user_id` bigint(20) NOT NULL COMMENT '用户 ID',
  `drama_id` int(10) unsigned NOT NULL COMMENT '剧集 ID',
  `create_time` int(10) unsigned NOT NULL COMMENT '追剧时间',
  `is_top` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否置顶',
  `update_time` int(10) NOT NULL COMMENT '剧集更新时间',
  `saw_episode` varchar(30) NOT NULL DEFAULT '' COMMENT '上次观看的哪一期',
  `is_saw` tinyint(4) NOT NULL DEFAULT '1' COMMENT '剧集的更新是否已查看，1：已查看，0：未查看',
  `saw_episode_id` int(10) NOT NULL DEFAULT '0' COMMENT '上次观看的哪一期对应的 episode ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `radio_drama_subscription`
  (`id`, `user_id`, `drama_id`, `create_time`, `is_top`, `update_time`, `saw_episode`, `is_saw`, `saw_episode_id`)
VALUES
  -- TestActionBatchSubscribe
  (1, 12, 6, UNIX_TIMESTAMP(), 0, UNIX_TIMESTAMP(), '', 1, 0)
  -- TestListUserSubscribeDramaIDs
  ,(2, 1, 52348, UNIX_TIMESTAMP(), 0, UNIX_TIMESTAMP(), '', 1, 0)
  -- TestIsUserSubscribed
  ,(3, 100, 10, UNIX_TIMESTAMP(), 0, UNIX_TIMESTAMP(), '', 0, 0)
;

-- GRANT SELECT ON `radio_drama_ip` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_ip` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `name` varchar(60) NOT NULL COMMENT '剧集所属 IPR 名',
  `seasons` varchar(2000) NOT NULL COMMENT 'JSON 季度信息',
  `user_id` bigint NOT NULL COMMENT '创建者 ID',
  `create_time` int unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int unsigned NOT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集 IPR 信息表'
;

INSERT INTO `radio_drama_ip`
  (`id`, `name`, `seasons`, `user_id`, `create_time`, `modified_time`)
VALUES
  -- TestFindIPRNameByID
  (1, 'test_ipr1', '', 10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

-- GRANT SELECT ON `radio_drama_dramacopyright` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_dramacopyright` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `drama_id` bigint NOT NULL DEFAULT '0' COMMENT '剧集 ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '剧集名称',
  `copyright_attribution` tinyint NOT NULL DEFAULT '0' COMMENT '版本归属，0：非自有版权；1：自有版权',
  `copyright_owner` tinyint NOT NULL DEFAULT '0' COMMENT '出品方，0：外部出品；1：自出品；2：联合出品',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '分发方式，0：非独播；1：独播',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间'
) COMMENT='版权信息'
;

INSERT INTO `radio_drama_dramacopyright`
  (`id`, `create_time`, `modified_time`, `drama_id`, `name`, `copyright_attribution`, `copyright_owner`, `type`, `delete_time`)
VALUES
  -- TestFindCopyrightByDramaIDs
  (1, 1650424411, 1650424411, 1, '魔道祖师 第一季', 1, 1, 1, 0)
;

-- GRANT SELECT ON `radio_drama_corner_mark` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_corner_mark` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间',
  `drama_id` bigint NOT NULL DEFAULT 0 COMMENT '剧集 ID',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '角标类型，0：其他，详见 more 字段；1：原创；2：精选',
  `more` text NULL COMMENT '特殊角标配置信息'
) COMMENT='剧集角标信息'
;

INSERT INTO `radio_drama_corner_mark`
  (`id`, `create_time`, `modified_time`, `delete_time`, `drama_id`, `type`, `more`)
VALUES
  -- TestFindCornerMarkByDramaIDs
  (1, 1650424411, 1650424411, 0, 1, 1, '')
;

-- GRANT SELECT ON `drama_corner_mark_style` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `drama_corner_mark_style` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '1：已购；2：热播；3：原创；4：独播；5：首发；6：付费；7：精选',
  `text` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本',
  `text_color` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本背景色',
  `text_start_color` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本渐变起始颜色',
  `text_end_color` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本渐变结束颜色',
  `bg_start_color` varchar(50) NOT NULL DEFAULT '' COMMENT '渐变填充色色值',
  `bg_end_color` varchar(50) NOT NULL DEFAULT '' COMMENT '渐变填充色色值',
  `left_icon_url` varchar(255) NOT NULL DEFAULT '' COMMENT '左侧图标'
) COMMENT='剧集角标样式信息'
;

INSERT INTO `drama_corner_mark_style`
  (`id`, `create_time`, `modified_time`, `type`, `text`, `text_color`, `text_start_color`, `text_end_color`, `bg_start_color`, `bg_end_color`, `left_icon_url`)
VALUES
  -- TestListAllMap
  (1, 1650424411, 1650424411, 1, '已购', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (2, 1650424411, 1650424411, 2, '热播', '#FFFFFF', '', '', '#E66465', '#E66465', 'test://cornermark/corner_mark.png'),
  (3, 1650424411, 1650424411, 3, '原创', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (4, 1650424411, 1650424411, 4, '独播', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (5, 1650424411, 1650424411, 5, '首发', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (6, 1650424411, 1650424411, 6, '付费', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (7, 1650424411, 1650424411, 7, '精选', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (8, 1650424411, 1650424411, 8, '会员', '#FFFFFF', '', '', '#E66465', '#E66465', 'test://cornermark/corner_mark.png')
;

-- GRANT SELECT ON `radio_drama_weekly_hot` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_weekly_hot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间',
  `drama_ids` text NOT NULL COMMENT '剧集 IDs，上周热播剧集按热度从高到低，用半角逗号分隔，最多有 40 个剧集 ID',
  `date` varchar(12) NOT NULL DEFAULT '' COMMENT '每周周一日期，举例：2006-01-02'
) COMMENT='每周热播剧'
;

INSERT INTO `radio_drama_weekly_hot`
  (`id`, `create_time`, `modified_time`, `drama_ids`, `date`)
VALUES
  -- TestFindDramaIDsByDate
  (1, 1650424411, 1650424411, '1,2', '2022-10-03')
;

-- GRANT SELECT ON `radio_drama_price` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_price` (
  `drama_id` int(10) unsigned NOT NULL PRIMARY KEY COMMENT '剧集 ID'
  ,`price` int(10) unsigned NOT NULL COMMENT '价格'
  ,`rate` float unsigned NOT NULL COMMENT '分成比例'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  ,`name` varchar(255) NOT NULL COMMENT '剧集名'
  ,`type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '1：单集价格，2：整剧价格'
  ,`rewardable` int(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否可进行打赏（0 为否，1 为是）'
  ,`vip_discount` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '会员打折百分比值，如存入 80 表示折扣力度为原价 80%，为 0 时表示不打折。折扣后若出现小数点，则小数点后金额舍去。注意，单集付费剧需将要购买的单集价格加起来以后再打折'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集价格表'
;

INSERT INTO `radio_drama_price`
  (`drama_id`,`price`,`rate`,`user_id`,`name`,`type`,`rewardable`, `vip_discount`)
VALUES
  -- TestFindPriceByDramaID
  (7, 200, '0.5', 12, '剧集名称', 2, 0, 80)
;

-- GRANT SELECT ON `radio_drama_tag_info` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_tag_info` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间戳，单位：秒'
  ,`name` varchar(20) NOT NULL COMMENT '标签名称'
  ,`type` tinyint NOT NULL COMMENT '标签类型 1：分类标签；2：内容标签'
  ,`status` int NOT NULL DEFAULT '0' COMMENT '属性 0：可见；比特位第 1 位为 1：对用户隐藏；比特位第 2 位为 1：对创作端隐藏'
  ,`sort` int NOT NULL DEFAULT '0' COMMENT '排序值'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集标签表'
;

INSERT INTO `radio_drama_tag_info`
  (`id`, `name`, type, `status`, sort)
VALUES
  -- TestListDramaUploadTags、TestCheckDramaReviewInfo、TestCheckedDramaReview_ReassignDramaTagsInfo、TestListDramaTagsByIDs
  (1, '古风', 1, 0, 0)
  ,(2, '都市', 1, 0, 1)
  ,(3, '青春', 1, 0, 2)
  ,(4, '甜', 2, 0, 0)
  ,(5, '虐', 2, 0, 1)
  ,(6, '轻松', 2, 0, 2)
  -- TestMapUserVisibleDramaTags
  ,(7, '怼（用户不可见）', 2, 1, 2)
;

-- GRANT SELECT ON `radio_drama_episode_cv` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_episode_cv` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID'
  ,`drama_id` bigint NOT NULL COMMENT '剧集 ID'
  ,`episode_id` bigint NOT NULL COMMENT '单集 ID'
  ,`cv_id` bigint NOT NULL COMMENT '声优 ID'
  ,`character` varchar(30) NOT NULL COMMENT '出演角色'
  ,`main` tinyint NOT NULL DEFAULT '3' COMMENT '角色类型 1：主役；2：协役；3：龙套'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='参演声优表'
;

INSERT INTO `radio_drama_episode_cv`
  (`id`, `drama_id`, `episode_id`, `cv_id`, `character`, `main`)
VALUES
  -- TestGetSubscribeDramaCvIDs、TestListDramaMainOrMinorCvIDs、TestActionGetUserDramaCvs、TestDramaCvParam_listSceneSearchDramaCvs、TestListDramaCvIDs
  (1, 52348, 6, 1, '主役', 1)
  ,(2, 52348, 6, 2, '协役', 2)
  ,(3, 52349, 7, 1, '主役', 1)
  ,(4, 52349, 7, 3, '协役', 2)
  ,(5, 52350, 7, 5, '龙套', 3)
  ,(6, 52350, 8, 4, '主役', 1)
  -- TestGetQualityDramaCvNames、TestListDramaCvsMain
  ,(7, 52355, 31, 5, '主役', 1)
  ,(8, 52355, 32, 5, '主役', 1)
  ,(9, 52355, 32, 6, '协役', 2)
  ,(10, 52356, 33, 5, '主役', 1)
  ,(11, 52356, 33, 6, '主役', 1)
  ,(12, 52357, 34, 7, '主役', 1)
;

-- GRANT SELECT ON `drama_saw_history` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `drama_saw_history` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`drama_id` bigint NOT NULL COMMENT '剧集 ID'
  ,`episode_id` bigint NOT NULL COMMENT '上次观看的单集 ID'
  ,`episode_name` varchar(30) NOT NULL DEFAULT '' COMMENT '上次观看的单集名称'
  ,`create_time` bigint NOT NULL COMMENT '创建时间戳，单位：秒'
  ,`update_time` bigint NOT NULL COMMENT '上次观看时间戳，单位：秒'
) ENGINE=InnoDB
;

INSERT INTO `drama_saw_history`
  (`user_id`, `drama_id`, `episode_id`, `episode_name`, `create_time`, `update_time`)
VALUES
  -- TestListDramaMainOrMinorCvIDs
  (1, 52348, 6, '', 1730995200, 1730995200)
  ,(1, 52349, 7, '', 1730995200, 1730995200)
  -- TestListLastviewed
  ,(90, 101, 17, '第二集', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(90, 102, 20, '第三集', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- TestActionGetUserLastviewed
  ,(901, 101, 17, '第二集', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(901, 102, 21, '第三集', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(901, 103, 24, '第三集', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

CREATE TABLE IF NOT EXISTS `checked_drama_review` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`drama_id` bigint NOT NULL COMMENT '剧集 ID'
  ,`name` varchar(60) DEFAULT NULL COMMENT '剧集名称'
  ,`cover` varchar(255) DEFAULT NULL COMMENT '剧集海报'
  ,`cover_color` int(10) unsigned DEFAULT NULL COMMENT 'RGB 颜色值'
  ,`abstract` text COMMENT '剧集简介'
  ,`integrity` tinyint(1) DEFAULT NULL COMMENT '完结度'
  ,`age` tinyint(4) DEFAULT NULL COMMENT '年代'
  ,`origin` tinyint(1) DEFAULT NULL COMMENT '创作类型'
  ,`author` varchar(30) DEFAULT NULL COMMENT '原作者'
  ,`birthday` tinyint(1) DEFAULT NULL COMMENT '生日剧'
  ,`cv` varchar(30) DEFAULT NULL COMMENT '生日 CV'
  ,`ip` tinyint(1) DEFAULT NULL COMMENT '同人剧'
  ,`ipname` varchar(30) DEFAULT NULL COMMENT '原作标签'
  ,`type` int(10) DEFAULT NULL COMMENT '分类'
  ,`newest` varchar(30) DEFAULT NULL COMMENT '更新至'
  ,`organization_id` bigint unsigned DEFAULT NULL COMMENT '社团 ID'
  ,`user_id` bigint DEFAULT NULL COMMENT '所属用户 ID'
  ,`username` varchar(20) DEFAULT NULL COMMENT '所属用户名'
  ,`catalog` int(11) DEFAULT NULL COMMENT '分类 ID'
  ,`alias` varchar(60) DEFAULT NULL COMMENT '别名'
  ,`episodes` text COMMENT '添加或删除的单集'
  ,`tags` text COMMENT '标签'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '记录修改时间戳，单位：秒'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '记录删除时间戳（审核完成时间戳），单位：秒'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集审核表'
;

-- GRANT SELECT ON `app_missevan_radio_drama`.`checked_drama_review` TO 'missevan_main'@'%'
INSERT INTO `checked_drama_review`
  (`id`, `drama_id`, `name`, `cover`, `cover_color`, `abstract`, `integrity`, `age`, `origin`, `author`, `birthday`, `cv`, `ip`, `ipname`, `type`, `newest`, `organization_id`, `user_id`, `username`, `catalog`, `alias`, `episodes`, `tags`)
VALUES
  (1, 5, '测试再审剧集', 'test://20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg', 1481702748, '测试修改剧集简介', 1, 2, 0, '测试修改原作者 1', 0, NULL, 0, NULL, 4, '', 0, 12, 'InVinCiblezzz', NULL, NULL, '', '')
  ,(2, 2, '测试修改剧集名称 1', 'test://20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg', 1481702748, '测试修改剧集简介 1', 1, 2, 0, '测试修改原作者 1', 0, NULL, 0, NULL, 4, '', 0, 12, 'InVinCiblezzz', NULL, NULL, '{"create":[],"update":[{"episode_id":2,"sound_id":234,"name":"测试修改单集信息 1","type":0,"order":0,"date":1594252800},{"episode_id":3,"sound_id":235,"name":"测试修改单集信息 2","type":0,"order":1,"date":1594252800}],"delete":[{"episode_id":4,"sound_id":234,"name":"测试获取剧集信息（单集 3）","order":100000,"type":0,"date":1507607822}]}', '')
  -- TestActionGetDramaEpisodes、TestCheckDramaReviewInfo
  ,(3, 52352, '测试修改剧集名称', 'test://20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg', 1481702748, '测试修改剧集简介 1', 1, 2, 0, '测试修改原作者 1', 0, NULL, 0, NULL, 4, '', 0, 1, 'test', NULL, NULL, '{"create":[{"sound_id":1217770,"name":"测试新增单集","type":0,"order":0,"date":1594252800,"pay_type":0}],"update":[{"episode_id":27,"sound_id":1217699,"name":"测试修改单集","order":100000,"type":1,"date":1507607822}],"delete":[{"episode_id":26,"sound_id":1217698,"name":"测试删除单集","order":200000,"type":2,"date":1507607822}]}', '6,2')
  -- TestGetDramaCheckStatus
  ,(4, 52364, '测试再审剧集', 'test://20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg', 1481702748, '测试修改剧集简介', 1, 2, 0, '测试修改原作者 1', 0, NULL, 0, NULL, 4, '', 0, 12, 'InVinCiblezzz', NULL, NULL, '', '')
;

-- GRANT SELECT ON `app_missevan_radio_drama`.`radio_drama_tag_drama_map` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `radio_drama_tag_drama_map` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间戳，单位：秒'
  ,`drama_id` bigint NOT NULL COMMENT '剧集 ID'
  ,`tag_id` bigint NOT NULL COMMENT '标签 ID'
  ,`sort` int NOT NULL COMMENT '排序值'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集标签关联表';

INSERT INTO `radio_drama_tag_drama_map`
  (`id`, `create_time`, `modified_time`, `drama_id`, `tag_id`, `sort`)
VALUES
  -- TestListDramaTagsByDramaID
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 4, 0)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 5, 1)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 1, 2)
  -- TestMapUserVisibleDramaTags
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 5, 1)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 1, 2)
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 5, 2)
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 1, 1)
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 7, 3)
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 2, 4)
;

-- GRANT SELECT ON `drama_info_addendum` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `drama_info_addendum` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`thumbnail` varchar(255) NOT NULL DEFAULT '' COMMENT '小图（App 显示）'
  ,`shortintro` varchar(255) NOT NULL DEFAULT '' COMMENT '一句话介绍（App 显示）'
  ,`update_period` varchar(30) NOT NULL DEFAULT '' COMMENT '剧集更新周期'
  ,`outline` text COMMENT '剧集概述（JSON）'
  ,`thumbnail_h` varchar(255) NOT NULL DEFAULT '' COMMENT '宽图（B 站播放页推荐模块显示）'
  ,`more` text COMMENT 'json 格式存储的额外信息，其中 theatre_cat_image 为抽中盲盒时显示的猫图；theatre_rank_image 为盲盒剧集排行榜背景图；couple_cover_image 为剧集双人封面'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='radio_drama_dramainfo 的附表';

INSERT INTO `drama_info_addendum`
  (`id`,`thumbnail`,`shortintro`,`update_period`,`outline`,`thumbnail_h`,`more`)
VALUES
  -- TestListShortIntro
  (1, '', '', '每周五更新', null, '', null)
  ,(2, '', '一句话简介', '每周五更新', null, '', null)
  ,(8, '', '一句话简介', '每周五更新', null, '', null)
  -- TestMapDramaInfoAddendum
  ,(52355, '', '测试一句话简介 52355', '', null, '', null)
  ,(52356, '', '测试一句话简介 52356', '', null, '', null)
;
