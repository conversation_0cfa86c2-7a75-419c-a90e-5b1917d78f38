-- GRANT SELECT ON transaction_log TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `transaction_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`from_id` int(11) NOT NULL DEFAULT '0'
  ,`to_id` int(11) NOT NULL DEFAULT '0'
  ,`c_time` int(11) NOT NULL DEFAULT '0'
  ,`gift_id` int(11) NOT NULL COMMENT '0 为知识问答 正整数为正常礼物'
  ,`title` varchar(255) DEFAULT NULL
  ,`ios_coin` int(11) DEFAULT '0'
  ,`android_coin` int(11) DEFAULT '0'
  ,`paypal_coin` int(11) DEFAULT '0'
  ,`tmallios_coin` int(11) NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收入（单元：钻）'
  ,`googlepay_coin` int(11) NOT NULL DEFAULT '0' COMMENT 'Google Pay 收入（单元：钻）'
  ,`all_coin` int(11) NOT NULL DEFAULT '0' COMMENT '钻石总和'
  ,`revenue` double NOT NULL DEFAULT '0' COMMENT '分成后收益'
  ,`income` double DEFAULT NULL
  ,`tax` double DEFAULT NULL
  ,`rate` double DEFAULT NULL
  ,`num` int(11) NOT NULL DEFAULT '1' COMMENT '直播时购买礼物数量；购买语音包时存储季度'
  ,`status` tinyint(4) DEFAULT '0' COMMENT '-3 现金退款\n-2 是取消\n-1 是未完成\n1 是成功'
  ,`type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1：直播间礼物；2：剧集单集购买；3：剧集购买；4：微信男友购买；5：全职抽卡；6：全职季包；7：剧集打赏；8: 求签；9：公会直播收益；'
  ,`suborders_num` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '购买剧集单集时存储子订单数量（本次购买的单集数）；购买语音包时存储作品 ID'
  ,`attr` int(11) NOT NULL DEFAULT '0' COMMENT 'type 为 1 或 9 时，attr 为 1 表示直播续费贵族，为 2 表示直播开通贵族，为 3 表示直播间白给礼物。\ntype 为 2 或 3 时，attr 为 1 表示特殊途径购买。\ntype 为 3 时，attr 为 2 表示剧集兑换。'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '最后修改时间'
  ,`confirm_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '交易确认时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

INSERT INTO `transaction_log`
(`id`, `from_id`, `to_id`, `c_time`, `gift_id`, `title`, `ios_coin`, `android_coin`, `paypal_coin`, `tmallios_coin`, `googlepay_coin`, `all_coin`, `revenue`, `income`, `tax`, `rate`, `num`, `status`, `type`, `suborders_num`, `attr`, `create_time`, `modified_time`, `confirm_time`)
VALUES
  -- TestActionDailyFlowV2、TestFlowMonthlyV2Param_monthlyFlowV2
  (2, 1, 12, UNIX_TIMESTAMP(), 5000, '测试日流水和月流水明细', 100, 0, 0, 10, 0, 10, 5, 10, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-25 13:00:05'))
  ,(3, 1, 12, UNIX_TIMESTAMP(), 5001, '测试日流水和月流水明细', 700, 0, 0, 10, 0, 10, 5, 70, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-05 21:30:00'))
  ,(4, 1, 12, UNIX_TIMESTAMP(), 5001, '测试日流水和月流水明细', 700, 0, 0, 10, 0, 10, 5, 70, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-05 18:30:00'))
  ,(5, 1, 12, UNIX_TIMESTAMP(), 5002, '测试日流水和月流水明细', 100, 0, 0, 10, 0, 10, 5, 10, NULL, NULL, 1, 1, 7, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-08-05 18:30:00'))
  ,(6, 1, 12, UNIX_TIMESTAMP(), 5002, '测试日流水和月流水明细', 500, 0, 0, 10, 0, 10, 5, 50, NULL, NULL, 1, 1, 2, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-07-01 18:30:00'))
;
