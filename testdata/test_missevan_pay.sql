-- GRANT SELECT ON `transaction_log` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `transaction_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`from_id` int NOT NULL
  ,`to_id` int NOT NULL
  ,`c_time` int NOT NULL
  ,`gift_id` int NOT NULL COMMENT '0 为知识问答 正整数为正常礼物'
  ,`title` varchar(255) DEFAULT NULL
  ,`ios_coin` int DEFAULT '0'
  ,`android_coin` int DEFAULT '0'
  ,`paypal_coin` int DEFAULT '0'
  ,`tmallios_coin` int NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收入（单元：钻）'
  ,`googlepay_coin` int NOT NULL DEFAULT '0' COMMENT 'Google Pay 收入（单元：钻）'
  ,`all_coin` int NOT NULL DEFAULT '0' COMMENT '钻石总和（花费的普通钻石与贵族钻石的总和）'
  ,`revenue` double NOT NULL DEFAULT '0' COMMENT '分成后收益'
  ,`income` double DEFAULT NULL
  ,`tax` double DEFAULT NULL
  ,`rate` double DEFAULT NULL
  ,`num` int NOT NULL DEFAULT '1' COMMENT '直播时购买礼物数量；购买语音包时存储季度'
  ,`status` tinyint DEFAULT '0' COMMENT '-5 代充取消交易记录\n-4 已退款（钻石）\n-3 已退款（现金）\n-2 是直播问答取消\n-1 未完成（目前仅用作“直播问答提问中”）\n1 交易成功'
  ,`type` tinyint NOT NULL DEFAULT '1' COMMENT '1：直播间礼物；2：剧集单集购买；3：剧集购买；4：微信男友购买；5：全职抽卡；6：全职季包；7：剧集打赏；8: 求签；9：公会直播收益；'
  ,`suborders_num` int unsigned NOT NULL DEFAULT '1' COMMENT '购买剧集单集时存储子订单数量（本次购买的单集数）；购买语音包时存储作品 ID'
  ,`attr` int unsigned NOT NULL DEFAULT '0' COMMENT '12 位及其之前为具体的商品属性：type 为 1 或 9 时，attr 为 1 表示直播续费贵族，为 2 表示直播开通贵族，为 3 表示直播间白给礼物，为 4 表示幸运签开箱礼物。type 为 2 或 3 时，attr 1 表示特殊途径购买，2 表示剧集兑换；type 为 4 时，attr 0 表示微信男友，1 表示掌心男友；13 位及其之后按位处理'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后修改时间'
  ,`confirm_time` bigint NOT NULL DEFAULT '0' COMMENT '交易确认时间'
)
;

INSERT INTO `transaction_log`
  (`id` ,`from_id` ,`to_id` ,`c_time` ,`gift_id` ,`title` ,`ios_coin` ,`android_coin` ,`paypal_coin` ,`tmallios_coin` ,`googlepay_coin` ,`all_coin` ,`revenue` ,`income` ,`tax` ,`rate` ,`num` ,`status` ,`type` ,`suborders_num` ,`attr` ,`create_time` ,`modified_time`, `confirm_time`)
VALUES
  (1, 12, 0, UNIX_TIMESTAMP(), 2, '测试用户是否已购买过该剧集', 0, 100, 0, 10, 0, 10, 5, 5, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- TestActionDailyFlowV2、TestActionDailyFlowExport
  ,(2, 1, 12, UNIX_TIMESTAMP(), 5000, '测试日流水明细', 100, 0, 0, 10, 0, 10, 5, 10, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-25 13:00:05'))
  ,(3, 1, 12, UNIX_TIMESTAMP(), 5001, '测试日流水明细', 700, 0, 0, 10, 0, 10, 5, 70, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-05 21:30:00'))
  ,(4, 1, 12, UNIX_TIMESTAMP(), 5001, '测试日流水明细', 700, 0, 0, 10, 0, 10, 5, 70, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-05 18:30:00'))
  -- TestFlowDailyV2Param_dailyFlowV2
  ,(5, 2, 12, UNIX_TIMESTAMP(), 6000, '测试日流水明细', 100, 0, 0, 10, 0, 10, 5, 10, NULL, NULL, 1, 1, 2, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-25 13:00:05'))
  ,(6, 2, 12, UNIX_TIMESTAMP(), 6001, '测试日流水明细', 700, 0, 0, 10, 0, 10, 5, 70, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-09-05 21:30:00'))
  ,(7, 2, 12, UNIX_TIMESTAMP(), 6001, '测试日流水明细', 700, 0, 0, 10, 0, 10, 5, 70, NULL, NULL, 1, 1, 7, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-08-05 18:30:00'))
  ,(8, 2, 12, UNIX_TIMESTAMP(), 6002, '测试日流水明细', 200, 0, 0, 10, 0, 10, 5, 20, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-08-05 18:30:00'))
  ,(9, 2, 12, UNIX_TIMESTAMP(), 6002, '测试日流水明细', 400, 0, 0, 10, 0, 10, 5, 40, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-07-04 09:20:00'))
  ,(10, 2, 12, UNIX_TIMESTAMP(), 6003, '测试日流水明细', 500, 0, 0, 10, 0, 10, 5, 50, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-06-01 08:10:00'))
  ,(11, 2, 12, UNIX_TIMESTAMP(), 6004, '测试日流水明细', 800, 0, 0, 10, 0, 10, 5, 80, NULL, NULL, 1, 1, 3, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2024-05-01 08:10:00'))
;

CREATE TABLE IF NOT EXISTS `vip_subscription_sign_agreement` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`pay_type` tinyint NOT NULL COMMENT '付费方式：1 iOS、2 微信、3 支付宝、4 Google Play'
  ,`vip_id` bigint NOT NULL COMMENT '会员套餐价目 ID'
  ,`status` tinyint NOT NULL COMMENT '协议签约状态：1 待签约，2 签约生效中，3 已解约'
  ,`start_time` bigint NOT NULL COMMENT '协议生效时间（秒级时间戳）'
  ,`expire_time` bigint NOT NULL COMMENT '协议失效时间（秒级时间戳），含此时刻'
  ,`more` json DEFAULT NULL COMMENT '更多详情'
  ,KEY `idx_modifiedtime` (`modified_time`)
  ,KEY `idx_userid_vipid` (`user_id`,`vip_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户订阅会员周期扣款签约协议'
;

INSERT INTO `vip_subscription_sign_agreement` 
  (`id`, `create_time`, `modified_time`, `user_id`, `pay_type`, `vip_id`, `status`, `start_time`, `expire_time`, `more`) 
VALUES 
  -- TestActionSendVipRenewalSms、TestGetNextDeductVipUserByTime
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 2, 16, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 6*86400, '')
;

-- GRANT SELECT ON missevan_pay.vip_fee_deducted_record TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `vip_fee_deducted_record` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`vip_id` bigint NOT NULL COMMENT '会员价目 ID'
  ,`sign_agreement_id` bigint NOT NULL COMMENT '签约协议 ID（0 为单次付费）'
  ,`pay_type` tinyint NOT NULL COMMENT '付费方式：1 iOS、2 微信、3 支付宝、4 Google Play'
  ,`price` bigint NOT NULL COMMENT '本次扣款金额（单位：分）'
  ,`status` bigint NOT NULL COMMENT '本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败'
  ,`next_deduct_time` bigint NOT NULL COMMENT '下次扣款时间（秒级时间戳，0 为单次付费）'
  ,`more` JSON COMMENT '更多详情，e.g. { "is_first_topup_discount": true }'
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员扣费记录'
;

INSERT INTO vip_fee_deducted_record
(id, create_time, modified_time, user_id, vip_id, sign_agreement_id, pay_type, price, status, next_deduct_time, more)
VALUES
  -- TestHasSubscribePromotional、TestActionSubscribeInfo
  (1, UNIX_TIMESTAMP() - 1, UNIX_TIMESTAMP(), 3457181, 6, 1, 2, 990, 2, UNIX_TIMESTAMP(), '{"is_first_topup_discount":true}')
  ,(2, UNIX_TIMESTAMP() - 1, UNIX_TIMESTAMP(), 13, 6, 2, 2, 990, 2, UNIX_TIMESTAMP(), '{"is_first_topup_discount":true}')
  -- TestActionSendVipRenewalSms、TestGetNextDeductVipUserByTime
  ,(3, UNIX_TIMESTAMP() - 1, UNIX_TIMESTAMP(), 13, 16, 1, 2, 990, 2, UNIX_TIMESTAMP() + 6*86400, '{"is_first_topup_discount":true}')
;

-- GRANT SELECT ON `m_pay_method` TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `m_pay_method` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间，单位：秒'
  ,`scene` tinyint NOT NULL DEFAULT '0' COMMENT '支付场景（1: 开通 vip；2: 充值钻石）'
  ,`title` varchar(50) NOT NULL DEFAULT '' COMMENT '支付方式名称'
  ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '支付方式图标'
  ,`type` varchar(50) NOT NULL COMMENT '支付方式类型（wechatpay: 微信支付；alipay: 支付宝支付）'
  ,`sort` tinyint NOT NULL DEFAULT '0' COMMENT '展示顺序'
  ,`more` json DEFAULT NULL COMMENT '更多详情'
  ,KEY `idx_modifiedtime` (`modified_time`)
  ,KEY `idx_scene` (`scene`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付方式'
;

INSERT INTO m_pay_method
  (`id`, `create_time`, `modified_time`, `scene`, `title`, `icon`, `type`, `sort`, `more`)
VALUES
  -- TestListVipPayMethod
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, '微信支付', 'test://wechatpay.png', 'wechatpay', 1, '{"active":1}')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, '支付宝支付', 'test://alipay.png', 'alipay', 2, null)
;
