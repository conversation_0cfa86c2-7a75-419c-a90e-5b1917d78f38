-- 仅单测使用，不需要赋权
CREATE TABLE IF NOT EXISTS `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`last_name` varchar(20) NOT NULL
  ,`first_name` varchar(20) NOT NULL
  ,`iconurl` varchar(240) NOT NULL
  ,`age` uint NOT NULL
  -- ,UNIQUE(`last_name`,`first_name`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8;

CREATE UNIQUE INDEX uk_lastname_firstname ON user(`last_name`,`first_name`);

INSERT INTO `user`
  (`id`, `last_name`, `first_name`, `iconurl`, `age`)
VALUES
  -- TestIFExpr/TestLeastExpr/TestGreatestExpr/TestUpdateOnDuplicateKeyExpr
  (1, 'A', 'B', '0.png', 20)
;

-- GRANT SELECT, UPDATE ON `m_sound` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_sound` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`catalog_id` int NOT NULL COMMENT '分类 ID'
  ,`create_time` int NOT NULL COMMENT '创建时间'
  ,`last_update_time` int NOT NULL
  ,`duration` int NOT NULL COMMENT '持续时间'
  ,`user_id` int NOT NULL
  ,`username` varchar(20) NOT NULL
  ,`cover_image` varchar(255) NOT NULL COMMENT '封面图片'
  ,`animationid` int NOT NULL
  ,`characterid` int NOT NULL
  ,`seiyid` int NOT NULL
  ,`soundstr` varchar(100) NOT NULL
  ,`intro` text NOT NULL COMMENT '简介'
  ,`soundurl` varchar(100) NOT NULL
  ,`soundurl_32` varchar(100) NOT NULL
  ,`soundurl_64` varchar(100) NOT NULL
  ,`soundurl_128` varchar(100) NOT NULL
  ,`downtimes` int NOT NULL DEFAULT '0'
  ,`uptimes` int NOT NULL DEFAULT '0' COMMENT '被赞次数'
  ,`checked` int NOT NULL DEFAULT '0' COMMENT '音频状态，-3：转码失败；-2：配音未转码；-1：未转码；0：审核中；1：已审核通过；2：报警音；3：下架音'
  ,`source` int NOT NULL DEFAULT '0' COMMENT '来源'
  ,`download` int NOT NULL DEFAULT '0' COMMENT '是否允许下载'
  ,`view_count` int NOT NULL DEFAULT '0' COMMENT '查看数'
  ,`comment_count` int NOT NULL DEFAULT '0' COMMENT '弹幕数'
  ,`favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏数'
  ,`point` int NOT NULL DEFAULT '0' COMMENT '猫耳数'
  ,`push` int NOT NULL DEFAULT '0' COMMENT '是否推送'
  ,`refined` int DEFAULT '0' COMMENT '是否加精'
  ,`comments_count` int NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`sub_comments_count` int NOT NULL DEFAULT '0' COMMENT '子评论数'
  ,`pay_type` int DEFAULT '0'
  ,`type` int DEFAULT '0' COMMENT '音频类型'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 新增测试音频
INSERT INTO `m_sound`
  (`id`, `catalog_id`, `create_time`, `last_update_time`, `duration`, `user_id`, `username`, `cover_image`, `animationid`, `characterid`, `seiyid`, `soundstr`, `intro`, `soundurl`, `soundurl_32`, `soundurl_64`, `soundurl_128`, `downtimes`, `uptimes`, `checked`, `source`, `download`, `view_count`, `comment_count`, `favorite_count`, `point`, `push`, `refined`, `comments_count`, `sub_comments_count`, `pay_type`, `type`)
VALUES
  -- TestActionGetUserLastviewed
  (1001, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1002, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1003, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(2001, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(2002, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(2003, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 3, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(3001, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(3002, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(3003, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(4001, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(5001, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 3, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  -- TestRecommendStrategy_listSameUserSoundIDs
  -- TestRecommendStrategy_listSameCatalogSoundIDs
  -- TestRecommendStrategy_listSameTagSoundIDs
  -- TestRecommendStrategy_getOrSetRecommendSoundMap
  -- TestListRecommendSoundIDs
  ,(100001, 4, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100002, 4, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100003, 4, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100004, 5, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100005, 6, 1328281178, 1515393856, 2768, 1346282, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100006, 6, 1328281178, 1515393856, 2768, 1346283, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100007, 7, 1328281178, 1515393856, 2768, 1346284, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(1217690, 8, 1328281179, 1515393856, 2769, 1346285, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  -- TestActionTs
  ,(1, 1, 1328281179, 1515393856, 2769, 1346286, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217691, 1, 1328281179, 1515393856, 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  -- TestListSoundInfoByIDs
  ,(1217692, 1, 1328281179, 1515393856, 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频（审核通过）', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217693, 1, 1328281179, 1515393856, 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频（擦边球 1）', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 2, 133, 107, 0, 0)
  ,(1217694, 1, 1328281179, 1515393856, 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频（擦边球 2）', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 8, 133, 107, 0, 0)
  -- TestRankParam_getDramaAndSoundRank
  ,(1217695, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217696, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217697, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  -- TestActionGetDramaEpisodes
  ,(1217698, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217699, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217770, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  -- TestGetSoundIDDramaCardMap
  ,(1217771, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217772, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217773, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217774, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  -- TestNewInitialPassSoundsParams、TestActionInitialPassSounds
  ,(1217775, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
  ,(1217776, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 1, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 0, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 1, 0)
  ,(1217777, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 0, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 2, 0)
  ,(1217778, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 0, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 1, 0)
  ,(1217779, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2769, 12, 'test_user', '201701/24/test.png', 1, 1, 2, '这个音频不过审哦', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 0, 0, 0, 67939, 103, 303, 0, 2, 0, 133, 107, 0, 0)
;

-- GRANT SELECT ON `m_tag_sound_map` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_tag_sound_map`
(
  `tag_id` int NOT NULL COMMENT '标签 ID'
  ,`sound_id` int NOT NULL COMMENT '音频 ID'
)
;

INSERT INTO `m_tag_sound_map`
  (`tag_id`, `sound_id`)
VALUES
  -- TestRecommendStrategy_listSameTagSoundIDs
  -- TestRecommendStrategy_getOrSetRecommendSoundMap
  -- TestListRecommendSoundIDs
  (1, 100001)
  ,(2, 100001)
  ,(1, 100002)
  ,(2, 100002)
  ,(1, 100006)
  ,(3, 100003)
;

-- GRANT SELECT ON `sound_video` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `sound_video` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`sid` int NOT NULL COMMENT '音频 ID'
  ,`video_url` varchar(255) NOT NULL COMMENT '视频 URL 地址'
  ,`create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` int NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`videourl_360` varchar(125) NOT NULL DEFAULT '' COMMENT '360P 视频地址'
  ,`videourl_480` varchar(125) NOT NULL DEFAULT '' COMMENT '480P 视频地址'
  ,`videourl_720` varchar(125) NOT NULL DEFAULT '' COMMENT '720P 视频地址'
  ,`videourl_1080` varchar(125) NOT NULL DEFAULT '' COMMENT '1080P 视频地址'
  ,`attr` int NOT NULL DEFAULT '0' COMMENT '视频属性，比特位第一位为 1 时表示优先播放'
  ,`source` tinyint NOT NULL DEFAULT '0' COMMENT '视频来源，1：后台绑定；2：配音秀'
  ,`checked` tinyint NOT NULL DEFAULT '-1' COMMENT '视频审核状态，-3：转码失败；-1：待转码；0：待审核；1：审核通过'
  ,`more` text NOT NULL COMMENT '视频额外信息，格式为 json 字符串，size 字段存放不同视频大小，单位 Bytes'
)
;

INSERT INTO `sound_video`
  (`id`, `sid`, `video_url`, `create_time`, `modified_time`, `videourl_360`, `videourl_480`, `videourl_720`, `videourl_1080`, `attr`, `source`, `checked`, `more`)
VALUES
  (1, 44809, 'oss://video/test.mp4', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', '', '', '', 0, 1, 1, '')
  -- ListHasVideoSoundIDs
  ,(2, 1217695, 'oss://video/test.mp4', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', '', '', '', 0, 1, 1, '')
  ,(3, 1217696, 'oss://video/test.mp4', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', '', '', '', 0, 1, 1, '')
;

-- GRANT SELECT ON `m_appupdate` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_appupdate`
(
  `id`                int(10) AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'id'
  ,`title`            varchar(32)                         NOT NULL DEFAULT ''   COMMENT 'app名称'
  ,`version`          varchar(32)                         NOT NULL DEFAULT ''   COMMENT '版本号'
  ,`intro`            text                                NOT NULL DEFAULT ''   COMMENT '介绍'
  ,`changelog`        text                                NOT NULL DEFAULT ''   COMMENT '变更日志'
  ,`status`           tinyint(3) unsigned                 NOT NULL DEFAULT '0'  COMMENT '发布状态'
  ,`appurl`           varchar(100)                        NOT NULL DEFAULT ''   COMMENT 'app下载地址'
  ,`update_time`      int(10) unsigned                    NOT NULL DEFAULT '0'  COMMENT '更新时间'
  ,`device`           tinyint(4)                                   DEFAULT '0'  COMMENT '设备类型，0：Android，1：iOS，2：Windows'
  ,`size`             float(5)                            NOT NULL DEFAULT '0'  COMMENT 'app大小(M)'
  ,`force_download`   int(11) unsigned                    NOT NULL DEFAULT '0'  COMMENT '是否强制更新'
  ,`developer`        varchar(255)                        NOT NULL DEFAULT 'Maoer Co.' COMMENT '开发者名称'
  ,`privacy_url`      varchar(255)                        NOT NULL DEFAULT 'https://link.missevan.com/rule/privacy' COMMENT '隐私政策地址'
  ,`permission_url`   varchar(255)                        NOT NULL DEFAULT 'https://link.missevan.com/rule/permission' COMMENT '权限用途地址'
  ,`appurl2`          varchar(100)                        NOT NULL DEFAULT '' COMMENT 'app 下载地址（Android: 64 位包，Windows: zip 包）'
)
;

-- GRANT CREATE, SELECT, UPDATE ON `persona` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `persona` (
  `id` int unsigned AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`equip_id` varchar(36) DEFAULT NULL COMMENT '设备号'
  ,`persona` int unsigned DEFAULT '1' COMMENT '用户画像：1 ~ 8 位存模块画像（1. 大众 2. 普通男 3. 普通女 4. 腐女）；9 ~ 16 位猜你喜欢音推荐策略'
  ,`user_id` bigint DEFAULT NULL COMMENT '用户 ID'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,`buvid` varchar(64) NOT NULL DEFAULT '' COMMENT 'buvid'
  ,`points` varchar(255) NOT NULL DEFAULT '' COMMENT 'JSON 字符串，存储画像分数，如：{"7":100,"9":1}'
)
;

-- GRANT SELECT, UPDATE ON `mowangskuser` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `mowangskuser` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '用户 ID'
  ,`confirm` int unsigned NOT NULL DEFAULT '0' COMMENT '用户权限或身份标识'
  ,`username` varchar(20) NOT NULL
  ,`temp5` varchar(50) DEFAULT NULL COMMENT '邮箱'
  ,`cip` varchar(50) NOT NULL DEFAULT ''
  ,`uip` varchar(50) NOT NULL
  ,`ctime` int unsigned NOT NULL
  ,`utime` int unsigned NOT NULL
  ,`quanxian` varchar(5) NOT NULL
  ,`teamid` int unsigned NOT NULL DEFAULT '1'
  ,`teamname` varchar(20) NOT NULL DEFAULT 'Drrr'
  ,`ban` tinyint unsigned NOT NULL DEFAULT '0'
  ,`ustr` int unsigned NOT NULL DEFAULT '0'
  ,`uint` int unsigned NOT NULL DEFAULT '0'
  ,`uagi` int unsigned NOT NULL DEFAULT '0'
  ,`point` int unsigned NOT NULL DEFAULT '50'
  ,`nowsound` int unsigned NOT NULL DEFAULT '0' COMMENT '记录用户当前M音时长'
  ,`iconid` int unsigned NOT NULL
  ,`iconurl` varchar(60) NOT NULL
  ,`iconcolor` varchar(50) NOT NULL
  ,`subtitle` varchar(10) NOT NULL
  ,`boardiconid` int unsigned NOT NULL DEFAULT '0'
  ,`boardiconurl` varchar(200) NOT NULL
  ,`boardiconcolor` varchar(50) NOT NULL DEFAULT '#B1B1B1m#CECECEm#B1B1B1m#6A6A6Am#B1B1B1'
  ,`coverid` int DEFAULT NULL COMMENT '封面图id'
  ,`coverurl` varchar(60) DEFAULT NULL COMMENT '封面图'
  ,`isnewmsg` tinyint unsigned NOT NULL DEFAULT '0'
  ,`userintro` text
  ,`userintro_audio` int unsigned DEFAULT NULL
  ,`likenum` int unsigned DEFAULT '0' COMMENT '点赞数'
  ,`fansnum` int unsigned DEFAULT '0' COMMENT '粉丝数'
  ,`follownum` int unsigned DEFAULT '0' COMMENT '关注数'
  ,`soundnum` int unsigned DEFAULT '0' COMMENT '个人语音数'
  ,`albumnum` int unsigned DEFAULT '0' COMMENT '个人专辑数'
  ,`imagenum` int unsigned DEFAULT '0' COMMENT '个人图片数'
  ,`feednum` int DEFAULT '0' COMMENT 'feed流未读信息'
  ,`soundnumchecked` int unsigned DEFAULT '0' COMMENT '审核通过的声音'
  ,`imagenumchecked` int unsigned DEFAULT '0' COMMENT '审核通过的图片'
  ,`mlevel` tinyint unsigned DEFAULT '1' COMMENT '用户当前等级'
  ,`avatar` varchar(100) DEFAULT NULL COMMENT '三次元头像'
  ,`icontype` int DEFAULT '1'
  ,`temp6` varchar(15) DEFAULT NULL COMMENT '手机号'
  ,`temp7` smallint unsigned DEFAULT NULL COMMENT '国际电话区号'
  ,`coverurl_new` varchar(100) NOT NULL DEFAULT '' COMMENT '用户上传的封面图协议地址'
  ,`temp1` varchar(32) DEFAULT NULL
  ,`temp2` varchar(32) DEFAULT NULL
  ,`temp3` varchar(32) DEFAULT NULL
  ,`temp4` varchar(32) DEFAULT NULL
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;

-- 新增测试用户
INSERT INTO `mowangskuser`
  (`id`,  `confirm`, `username`, `cip`, `uip`, `ctime`, `utime`, `quanxian`, `teamid`, `teamname`, `ban`, `ustr`, `uint`, `uagi`, `point`, `nowsound`, `iconid`, `iconurl`, `iconcolor`, `subtitle`, `boardiconid`, `boardiconurl`, `boardiconcolor`, `coverid`, `coverurl`, `isnewmsg`, `userintro`, `userintro_audio`, `likenum`, `fansnum`, `follownum`, `soundnum`, `albumnum`, `imagenum`, `feednum`, `soundnumchecked`, `imagenumchecked`, `mlevel`, `avatar`, `icontype`, `coverurl_new`)
VALUES
  (12,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(346286, 0, 'InVinCiblezz', '***************', '127.0.0.1', 1474172405, 0, 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
-- TestFindNeedAndFollowedUserIDs
-- TestBatchFollow
-- TestActionBatchFollow
  ,(18001,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(18002,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(18003,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(18004,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
-- 18005 测试不存在的用户，请跳过此 ID
  ,(18006,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(20240480,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(20240481,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(20240482,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
  ,(20240483,  0, '零月', '***************', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '')
;

-- GRANT SELECT, INSERT ON `m_point_feed` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_point_feed` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`sound_id` bigint unsigned NOT NULL COMMENT '音频 ID'
  ,`user_id` bigint NOT NULL COMMENT '投食者 ID'
  ,`create_time` bigint unsigned NOT NULL COMMENT '投食时间'
  ,`num` smallint unsigned NOT NULL COMMENT '投食鱼干数量'
  ,`catalog_id` bigint NOT NULL COMMENT '音频分类 ID'
);

-- GRANT SELECT, INSERT ON `m_attention_user` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_attention_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `user_active` bigint(20) NOT NULL COMMENT '关注者 ID',
  `user_passtive` bigint(20) NOT NULL COMMENT '被关注者 ID',
  `time` int(11) NOT NULL COMMENT '关注时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关注';

INSERT INTO `m_attention_user`
  (`id`, `user_active`, `user_passtive`, `time`)
VALUES
  -- TestFindNeedAndFollowedUserIDs
  (1, 190, 18006, UNIX_TIMESTAMP())
  -- TestListUnfollowUserCvs
  ,(2, 1, 2, UNIX_TIMESTAMP())
  -- TestDramaCvParam_listSceneLiveRecommendDramaCv
  ,(3, 2, 3, UNIX_TIMESTAMP())
  ,(4, 2, 4, UNIX_TIMESTAMP())
;

-- GRANT SELECT ON `statistic_drama_profit` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `statistic_drama_profit` (
  `id` int(10) unsigned NOT NULL COMMENT '剧集 ID'
  ,`name` varchar(60) NOT NULL COMMENT '剧集名称'
  ,`profit` double unsigned NOT NULL DEFAULT '0' COMMENT '总收益'
  -- ,PRIMARY KEY (`id`)
  -- ,KEY `IDX_PROFIT` (`profit`) USING BTREE
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集总收益统计'
;

INSERT INTO `statistic_drama_profit`
  (`id`,`name`,`profit`)
VALUES
  -- TestGetSortedStatisticDramaProfitList；TestActionDramaList
  (29, '纨绔', '4208.32')
  ,(34, '囧囧有神', '5833.99')
;

-- GRANT SELECT ON `m_event` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS m_event (
  id int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,title varchar(256) NOT NULL COMMENT '活动名称'
  ,mobile_cover varchar(255) DEFAULT NULL COMMENT '手机端封面'
  ,short_intro varchar(255) DEFAULT NULL COMMENT '活动短介绍'
  ,bilibili_url_pc varchar(255) DEFAULT NULL COMMENT 'B站配置的 PC url'
  ,bilibili_url_h5 varchar(255) DEFAULT NULL COMMENT 'B站配置的 H5 url'
  ,tag_id int DEFAULT NULL
  ,`type` tinyint unsigned NOT NULL COMMENT '活动上传类型0是音频，1是图片'
  ,vote_start_time bigint NOT NULL DEFAULT '0' COMMENT '投票开始时间'
  ,draw_start_time bigint NOT NULL DEFAULT '0' COMMENT '抽奖开始时间'
  ,draw_end_time bigint NOT NULL DEFAULT '0' COMMENT '抽奖结束时间'
  ,create_time int unsigned NOT NULL COMMENT '投稿开始时间'
  ,start_time int unsigned NOT NULL COMMENT '活动开始时间'
  ,end_time int unsigned NOT NULL COMMENT '投稿结束时间'
  ,head varchar(255) DEFAULT NULL COMMENT '音频头'
  ,tail varchar(255) DEFAULT NULL COMMENT '音频尾'
  ,extended_fields text COMMENT '额外数据，JSON format'
  ,status tinyint DEFAULT NULL COMMENT '手机端是否可见'
  ,`limit` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '活动每日投票上限'
  ,mini_cover varchar(255) DEFAULT NULL COMMENT '290图片'
  ,limit_work tinyint unsigned DEFAULT '0' COMMENT '每日可投票作品数量限制'
  ,limit_vote tinyint unsigned DEFAULT '0' COMMENT '每日每作品可投票数量限制'
  ,do_comment tinyint unsigned DEFAULT '0' COMMENT '活动是否支持评论 0：否；1：是'
  ,attr tinyint unsigned NOT NULL DEFAULT '3' COMMENT '活动属性'
) ENGINE=InnoDB DEFAULT CHARSET=utf8
;

INSERT INTO m_event
  (id, title, type, create_time, start_time, end_time, extended_fields, status)
VALUES
  -- TestActionGetAvatarFrame
  (233, 'test', 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, NULL, 1)
;

-- GRANT SELECT ON `m_user_vip` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_user_vip` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）',
  `modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）',
  `vip_id` bigint NOT NULL COMMENT 'vip id',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `type` tinyint unsigned NOT NULL COMMENT 'vip 类型，4：点播会员',
  `start_time` bigint NOT NULL COMMENT '开始时间（秒级时间戳）',
  `end_time` bigint NOT NULL COMMENT '过期时间（秒级时间戳）'
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户的会员有效期';

INSERT INTO m_user_vip
  (id, create_time, modified_time, vip_id, user_id, type, start_time, end_time)
VALUES
  -- TestGetUserVipInfo
  -- 由于创建测试数据和单测中使用当前时间可能有时间差，为了测试准确性，会员有效期起止时间设置为固定值
  (1, UNIX_TIMESTAMP() - 200, UNIX_TIMESTAMP() - 200, 1, 10, 4, 1723694194, 1726372594)
  ,(2, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 10, 4, 1726372594, 1728964594)
  ,(3, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 11, 4, 1726372594, 1999999999)
  -- TestActionUserInfo、TestActionSubscribeInfo
  ,(4, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 12, 4, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() + 100)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 13, 4, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() + 100)
  ,(6, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 3457181, 4, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 1)
  -- TestActionGetVipUserIDs
  ,(7, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 5674514, 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 100)
  -- TestActionTakeOffAvatarFrame、TestGetExpiredVipUserIDsOnDay
  ,(8, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 3222, 4, UNIX_TIMESTAMP() - 259200, UNIX_TIMESTAMP() - 172800)
  ,(9, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 3222, 4, UNIX_TIMESTAMP() - 172800, UNIX_TIMESTAMP() - 86400)
  ,(10, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 3222, 4, UNIX_TIMESTAMP() - 172800, UNIX_TIMESTAMP() - 86400)
  -- TestGetOnWearVipAvatarFrames
  ,(11, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 3223, 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

-- GRANT SELECT ON `m_vip` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_vip` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间（秒级时间戳）'
  ,`deduct_fee_schedule` tinyint NOT NULL COMMENT '付费周期：1 单次付费、2 连续包月、3 连续包季'
  ,`sort` int NOT NULL COMMENT '展示顺序'
  ,`price` int NOT NULL COMMENT '价格（单位：分）'
  ,`platform` tinyint NOT NULL COMMENT '平台：1 iOS、2 Android（非 Google 渠道）、3 Google Play'
  ,`more` json DEFAULT NULL COMMENT '更多详情'
  ,KEY `idx_modifiedtime` (`modified_time`)
  ,KEY `idx_platform` (`platform`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='会员价目'
;

INSERT INTO m_vip
  (id, create_time, modified_time, delete_time, deduct_fee_schedule, sort, price, platform, more)
VALUES
  -- TestListVipMenu
  -- TestActionMenu
  -- TestVipMenuParam_getMenuList
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 2, 1, 1900, 1, '{"title":"连续包月","first_title":"连续包月首月","first_subscribe_discount_price":990,"original_price":2500,"first_original_price":1900,"description":"最多可领 155 钻石\\n每月续费 ¥19，可随时取消","first_description":"最多可领 155 钻石\\n每月续费 ¥19，可随时取消","period":2678400,"active":1,"corner_mark_text":"限时"}')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 3, 2, 5300, 1, '{"title":"连续包季","original_price":7500,"description":"最多可领 465 钻石\\n每季续费 ¥53，可随时取消","period":8035200}')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 3, 22800, 1, '{"title":"年费","original_price":30000,"description":"最多可领 1825 钻石","period":31622400}')
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 4, 6800, 1, '{"title":"3 个月","original_price":7500,"description":"最多可领 465 钻石","period":8035200}')
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 5, 2500, 1, '{"title":"1 个月","description":"最多可领 155 钻石","period":2678400}')
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 2, 1, 1500, 2, '{"title":"连续包月","first_title":"连续包月首月","first_subscribe_discount_price":990,"original_price":1800,"first_original_price":1500,"description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","first_description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","period":2678400,"active":1,"corner_mark_text":"限时"}')
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 3, 2, 3900, 2, '{"title":"连续包季","original_price":5400,"description":"最多可领 465 钻石\\n每季续费 ¥39，可随时取消","period":8035200}')
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 3, 16800, 2, '{"title":"年费","original_price":21600,"description":"最多可领 1825 钻石","period":31622400}')
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 4, 4900, 2, '{"title":"3 个月","original_price":5400,"description":"最多可领 465 钻石","period":8035200}')
  ,(10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 5, 1800, 2, '{"title":"1 个月","description":"最多可领 155 钻石","period":2678400}')
  ,(11, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 2, 1, 1500, 3, '{"title":"连续包月","first_title":"连续包月首月","first_subscribe_discount_price":990,"original_price":1800,"first_original_price":1500,"description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","first_description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","period":2678400,"active":1,"corner_mark_text":"限时"}')
  ,(12, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 3, 2, 3900, 3, '{"title":"连续包季","original_price":5400,"description":"最多可领 465 钻石\\n每季续费 ¥39，可随时取消","period":8035200}')
  ,(13, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 3, 16800, 3, '{"title":"年费","original_price":21600,"description":"最多可领 1825 钻石","period":31622400}')
  ,(14, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 4, 4900, 3, '{"title":"3 个月","original_price":5400,"description":"最多可领 465 钻石","period":8035200}')
  ,(15, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 5, 1800, 3, '{"title":"1 个月","description":"最多可领 155 钻石","period":2678400}')
;

-- GRANT SELECT ON `mowangsksoundseiy` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `mowangsksoundseiy` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`name` varchar(40) NOT NULL COMMENT '声优名称'
  ,`icon` varchar(100) NOT NULL COMMENT '头像'
  ,`profile` text NOT NULL COMMENT '简介'
  ,`gender` tinyint NOT NULL COMMENT '性别 1：男；2：女'
  ,`initial` tinyint NOT NULL COMMENT '首字母 0 代表其他, 1 到 26 代表 26 个字母'
  ,`birthyear` smallint NOT NULL COMMENT '出生年份'
  ,`birthmonth` smallint NOT NULL COMMENT '出生月份'
  ,`birthday` smallint NOT NULL COMMENT '出生日'
  ,`birthmonthday` smallint DEFAULT '0' COMMENT '出生日期'
  ,`bloodtype` tinyint(1) NOT NULL COMMENT '血型'
  ,`career` tinyint(1) NOT NULL COMMENT '职业'
  ,`group` varchar(32) NOT NULL COMMENT '社团'
  ,`weibo` varchar(64) NOT NULL COMMENT '微博'
  ,`weiboname` varchar(64) NOT NULL COMMENT '微博名称'
  ,`baike` varchar(64) NOT NULL COMMENT '百科'
  ,`baikename` varchar(64) NOT NULL COMMENT '百科名称'
  ,`mid` bigint DEFAULT '0' COMMENT '用户 ID'
  ,`checked` smallint DEFAULT '0' COMMENT '审核状态'
  ,`soundline1` bigint DEFAULT NULL COMMENT '声线 1'
  ,`soundline2` bigint DEFAULT NULL COMMENT '声线 2'
  ,`soundline3` bigint DEFAULT NULL COMMENT '声线 3'
  ,`seiyalias` varchar(60) DEFAULT NULL
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='声优信息表';

INSERT INTO `mowangsksoundseiy`
 (`id`, `name`, `icon`, `profile`, `gender`, `initial`, `birthyear`, `birthmonth`, `birthday`, `birthmonthday`, `bloodtype`, `career`, `group`, `weibo`, `weiboname`, `baike`, `baikename`, `mid`, `checked`, `soundline1`, `soundline2`, `soundline3`, `seiyalias`)
VALUES
  -- TestListUserCvs、TestListUnfollowUserCvs、TestActionGetUserDramaCvs、TestDramaCvParam_listSceneSearchDramaCvs
  (1, '测试声优', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 0, 1, 0, 0, 0, '')
  ,(2, '测试声优', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 2, 1, 0, 0, 0, '')
  -- TestDramaCvParam_getUserDramaCvs、TestDramaCvParam_listSceneLiveRecommendDramaCv
  ,(3, '测试声优', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 3, 1, 0, 0, 0, '')
  ,(4, '测试声优', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 4, 1, 0, 0, 0, '')
  -- TestListCvsByIDs、TestGetSoundIDDramaCardMap、TestGetQualityDramaCvNames、
  ,(5, '测试声优 5', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 4, 1, 0, 0, 0, '')
  ,(6, '测试声优 6', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 4, 1, 0, 0, 0, '')
  ,(7, '测试声优 7', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 4, 1, 0, 0, 0, '')
;

-- GRANT SELECT ON `m_homepage_icon` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_homepage_icon` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序'
  ,`title` varchar(20) NOT NULL COMMENT '图标名'
  ,`icon` varchar(120) NOT NULL COMMENT '简洁白模式的入口图标'
  ,`dark_icon` varchar(120) NOT NULL COMMENT '黑夜模式的入口图标'
  ,`url` varchar(255) NOT NULL COMMENT '地址'
  ,`create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`tab_id` bigint NOT NULL DEFAULT '0' COMMENT '对应 Tab ID'
  ,`type` int NOT NULL DEFAULT '0' COMMENT '图标类型：1：我的页图标；2：首页 tab 所属图标'
  ,`archive` int NOT NULL DEFAULT '0' COMMENT '是否为历史归档 0 ：否， 1 ：是（归档）'
  ,`anchor_name` varchar(20) NOT NULL DEFAULT '' COMMENT '图标名称标识（只有提示红点的图标设置此字段）'
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='我的页图标';

-- GRANT SELECT ON `you_might_like_module` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `you_might_like_module` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`title` varchar(100) NOT NULL COMMENT '模块名称'
  ,`creator_id` bigint NOT NULL DEFAULT '0' COMMENT '负责人用户 ID'
  ,`element_type` tinyint unsigned NOT NULL COMMENT '模块类型，1：音单模块；2：剧集模块；3：音频模块；5：直播模块'
  ,`element_attr` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '模块的属性，比特位第 3 位为 1 时表示封面图样式为堆叠样式，为 0 时表示封面图样式为扁平样式'
  ,`weekly_task_target` int NOT NULL DEFAULT '0' COMMENT '每周更新音频要求的数量'
  ,`skipped` int unsigned NOT NULL DEFAULT '0' COMMENT '比特位为 1 -- 跳过对应类型检查，0 -- 不跳过；第一位 -- weekly-module，第二位 -- 以后用到的请向这里添加，依此类推'
  ,`update_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '模块更新类型，1：主题模块；2：轮换模块；3：换血模块'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '更新时间'
  ,`more` text COMMENT '额外信息，格式为 json 字符串，详情文档：https://info.missevan.com/pages/viewpage.action?pageId=97891396'
  ,`element_style` tinyint NOT NULL DEFAULT '0' COMMENT '模块样式，0：竖版；1：横版；2：排行榜；3：滑动'
) ENGINE=InnoDB;

INSERT INTO `you_might_like_module`
  (`id`,`title`,`creator_id`,`element_type`,`element_attr`,`weekly_task_target`,`skipped`,`update_type`,`create_time`,`modified_time`,`more`,`element_style`)
VALUES
  -- TestRecommendDramaModules
  -- TestListPersonaModules
  (1001,'会员剧集模块一(半角括号和括号内部文本对用户不可见)', 12, 2, 1, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"url":"https://www.test.com/mdrama/123"}', 1)
  ,(1002,'会员剧集模块二', 12, 2, 1, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"url":"https://www.test.com/mdrama/124"}', 2)
  ,(1003,'会员剧集模块三', 12, 2, 1, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(),'', 3)
;

-- GRANT SELECT ON `m_persona_module_element` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_persona_module_element` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`module_id` bigint NOT NULL COMMENT '模块 ID'
  ,`persona_id` bigint NOT NULL DEFAULT '0' COMMENT '用户画像 ID'
  ,`element_type` tinyint unsigned DEFAULT '0' COMMENT '元素类型，1：音单，2：剧集'
  ,`element_id` bigint NOT NULL DEFAULT '0' COMMENT '元素 ID'
  ,`summary` varchar(255) DEFAULT NULL COMMENT '剧集或音单元素摘要介绍'
  ,`cover` varchar(255) NOT NULL DEFAULT '' COMMENT '剧集或音单自定义封面图'
  ,`sort` int unsigned NOT NULL DEFAULT '0' COMMENT '0 为隐藏元素，正整数为元素排序顺序，由小到大排序'
  ,`create_time` bigint NOT NULL COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL COMMENT '更新时间，单位：秒'
) ENGINE=InnoDB;

INSERT INTO `m_persona_module_element`
  (`id`,`module_id`,`persona_id`,`element_type`,`element_id`,`summary`,`cover`,`sort`,`create_time`,`modified_time`)
VALUES
  -- TestListPersonaModules
  -- TestListRecommendDramas
  -- TestActionRecommendBlocks
  -- TestrecommendDramaModules
  (101, 1001, 2003, 0, 0, '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(102, 1002, 2003, 0, 0, '', '', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(103, 1003, 2003, 0, 0, '', '', 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

-- GRANT SELECT, INSERT, UPDATE ON `m_recommended_elements` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_recommended_elements` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`client` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '平台（0 安卓或 iOS，1 安卓，2 iOS）'
  ,`module_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '模块类型（1 版头图模块、 2  精品必听、活动等四个小图标模块、3 猜你喜欢音模块、4 精品周更模块、5 推荐剧集或音单模块、6 今日推荐音模块）'
  ,`module_id` int unsigned NOT NULL DEFAULT '0' COMMENT '模块 ID'
  ,`element_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '元素类型（0 其它、1 音单、2 剧集、3 单音、4 活动）'
  ,`element_id` bigint NOT NULL DEFAULT '0' COMMENT '元素 ID'
  ,`summary` varchar(255) NOT NULL DEFAULT '' COMMENT '简介'
  ,`cover` varchar(255) NOT NULL DEFAULT '' COMMENT '封面'
  ,`url` varchar(255) NOT NULL DEFAULT '' COMMENT '原始链接'
  ,`sort` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '排序值（0、1、2... 越小越靠前，由程序控制）'
  ,`creator_id` bigint NOT NULL DEFAULT '0' COMMENT '负责人用户 ID'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '自动上线时间戳，单位：秒'
  ,`end_time` bigint NOT NULL DEFAULT '0' COMMENT '自动下线时间戳，单位：秒'
  ,`archive` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否为历史归档：0 为否， 1 为（即为被删去的）'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间戳，单位：秒'
  ,`update_time` int unsigned NOT NULL COMMENT '更新时间戳，单位：秒'
  ,`more` json DEFAULT NULL COMMENT '更多设置'
) ENGINE=InnoDB COMMENT='推荐位表';

INSERT INTO `m_recommended_elements`
  (`id`,`client`,`module_type`,`module_id`,`element_type`,`element_id`,`summary`,`cover`,`url`,`sort`,`creator_id`,`start_time`,`end_time`,`archive`,`create_time`,`update_time`,`more`)
VALUES
  -- TestListRecommendElements
  -- TestListRecommendDramas
  -- TestActionRecommendBlocks
  -- TestrecommendDramaModules
  (100011, 0, 5, 1001, 2, 1, '', '', '', 0, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100012, 0, 5, 1001, 2, 2, '', '', '', 6, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100013, 0, 5, 1001, 2, 3, '', '', '', 2, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100014, 0, 5, 1001, 2, 4, '', '', '', 3, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100015, 0, 5, 1001, 2, 5, '', '', '', 4, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100016, 0, 5, 1001, 2, 6, '', '', '', 5, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100017, 0, 5, 1001, 2, 7, '', '', '', 7, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100018, 0, 5, 1001, 2, 8, '', '', '', 1, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100021, 0, 5, 1002, 2, 11, '', '', '', 6, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100022, 0, 5, 1002, 2, 12, '', '', '', 2, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100023, 0, 5, 1002, 2, 13, '', '', '', 3, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100031, 0, 5, 1003, 2, 101, '', '', '', 4, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100032, 0, 5, 1003, 2, 102, '', '', '', 5, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100033, 0, 5, 1003, 2, 103, '', '', '', 7, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
  ,(100034, 0, 5, 1003, 2, 104, '', '', '', 1, 12, 0, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), null)
;

-- GRANT SELECT ON `m_theme_skin` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_theme_skin` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`title` varchar(50) NOT NULL COMMENT '名称'
  ,`intro` varchar(125) NOT NULL COMMENT '简介'
  ,`vip` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否会员专享。0：否；1：是'
  ,`pay_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '付费类型。0：非付费使用；1：付费使用'
  ,`package` varchar(125) NOT NULL COMMENT '皮肤资源压缩包协议地址'
  ,`more` json NOT NULL COMMENT '更多信息'
  ,`sort` bigint NOT NULL DEFAULT '0' COMMENT '排序，值越小越靠前展示'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用主题皮肤表';

INSERT INTO `m_theme_skin`
  (`id`,`create_time`,`modified_time`,`title`,`intro`,`vip`,`pay_type`,`package`,`more`)
VALUES
  -- TestListVipThemeSkin
  -- TestActionRecommendBlocks
  (1,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), "椰子的假日", "和小猫一起，享受假日！", 1, 0, 'test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.zip', '{"cover":"test://themeskin/202412/25/5e1646155d2497829dbe9541b55bdc8d170940.jpg","list_bg_start_color":"#F8A623","list_bg_end_color":"#F8A629","preview_bg_start_color":"#FFA623","preview_bg_end_color":"#FFA629","preview_covers":["test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.jpg","test://themeskin/202412/25/2e1646155d2497829dbe9541b55bdc8d1lk942.jpg","test://themeskin/202412/25/ye1646155d2497829dbe9541b55bdc88hb0943.jpg"]}')
  ,(2,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), "圣诞季", "merry christmas !", 1, 0, 'test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f942.zip', '{"cover":"test://themeskin/202412/25/5e1646155d2497829dbe9541b55bdc8d170940.jpg","list_bg_start_color":"#F8A623","list_bg_end_color":"#F8A629","preview_bg_start_color":"#FFA623","preview_bg_end_color":"#FFA629","preview_covers":["test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.jpg","test://themeskin/202412/25/2e1646155d2497829dbe9541b55bdc8d1lk942.jpg","test://themeskin/202412/25/ye1646155d2497829dbe9541b55bdc88hb0943.jpg"]}')
  ,(3,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), "盗墓笔记", "张家古楼系列，张起灵陪你探险", 1, 0, 'test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f943.zip', '{"cover":"test://themeskin/202412/25/5e1646155d2497829dbe9541b55bdc8d170940.jpg","list_bg_start_color":"#F8A623","list_bg_end_color":"#F8A629","preview_bg_start_color":"#FFA623","preview_bg_end_color":"#FFA629","preview_covers":["test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.jpg","test://themeskin/202412/25/2e1646155d2497829dbe9541b55bdc8d1lk942.jpg","test://themeskin/202412/25/ye1646155d2497829dbe9541b55bdc88hb0943.jpg"]}')
  ,(4,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), "魔道祖师", "蓝湛倾心推荐，不容错过的魔道世界", 1, 0, 'test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f944.zip', '{"cover":"test://themeskin/202412/25/5e1646155d2497829dbe9541b55bdc8d170940.jpg","list_bg_start_color":"#F8A623","list_bg_end_color":"#F8A629","preview_bg_start_color":"#FFA623","preview_bg_end_color":"#FFA629","preview_covers":["test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.jpg","test://themeskin/202412/25/2e1646155d2497829dbe9541b55bdc8d1lk942.jpg","test://themeskin/202412/25/ye1646155d2497829dbe9541b55bdc88hb0943.jpg"]}')
  ,(5,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), "极简（非会员）", "极简风", 0, 1, 'test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f944.zip', '{"cover":"test://themeskin/202412/25/5e1646155d2497829dbe9541b55bdc8d170940.jpg","list_bg_start_color":"#F8A623","list_bg_end_color":"#F8A629","preview_bg_start_color":"#FFA623","preview_bg_end_color":"#FFA629","preview_covers":["test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.jpg","test://themeskin/202412/25/2e1646155d2497829dbe9541b55bdc8d1lk942.jpg","test://themeskin/202412/25/ye1646155d2497829dbe9541b55bdc88hb0943.jpg"]}')
;

-- GRANT SELECT ON `m_user_theme_skin` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_user_theme_skin` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`theme_skin_id` bigint NOT NULL COMMENT '主题皮肤 ID，m_theme_skin 表主键'
  ,`expire_time` bigint DEFAULT '0' COMMENT '过期时间点，仅对非会员专享皮肤生效，为 0 时表示永久有效。单位：秒'
  ,`status` tinyint DEFAULT '0' COMMENT '装扮状态。0：未装扮；1：装扮中'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户与主题皮肤关系表';

INSERT INTO `m_user_theme_skin`
  (`id`,`create_time`,`modified_time`,`user_id`,`theme_skin_id`,`expire_time`,`status`)
VALUES
  -- TestGetUserThemeSkin
  -- TestActionGetThemeSkin
  -- TestGetUserVipThemeSkinID
  (1,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 11, 5, 0, 0)
  ,(2,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 11, 1, 0, 1)
  ,(3,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 10, 1, 0, 1)
  ,(4,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 9, 5, 0, 1)
  ,(5,  UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 12, 1, 0, 1)
;

-- GRANT SELECT, INSERT ON `m_user_theme_skin_log` TO 'missevan_main'@'%'
CREATE TABLE `m_user_theme_skin_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`theme_skin_id` bigint NOT NULL COMMENT '主题皮肤 ID，m_theme_skin 表主键'
  ,`start_time` bigint DEFAULT '0' COMMENT '开始生效时间点，单位：秒'
  ,`end_time` bigint DEFAULT '0' COMMENT '卸下时间点，单位：秒。为 0 时表示佩戴中'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户主题皮肤佩戴记录表';

INSERT INTO `m_user_theme_skin_log`
  (`id`,`create_time`,`modified_time`,`user_id`,`theme_skin_id`,`start_time`,`end_time`)
VALUES
  -- TestGetUserFirstWearTime
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 10, 1, UNIX_TIMESTAMP(), 0)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 10, 1, 0, UNIX_TIMESTAMP() + 100)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 10, 1, UNIX_TIMESTAMP() + 100, 0)
;

-- GRANT INSERT, SELECT ON `m_user_avatar_frame_log` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_user_avatar_frame_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`avatar_frame_id` bigint NOT NULL COMMENT '头像框 ID，m_avatar_frame 表主键'
  ,`start_time` bigint DEFAULT '0' COMMENT '佩戴时间点，卸下时为 0。单位：秒'
  ,`end_time` bigint DEFAULT '0' COMMENT '卸下时间点，佩戴时为 0。单位：秒'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户头像挂件佩戴日志表';

INSERT INTO `m_user_avatar_frame_log`
  (`id`,`create_time`,`modified_time`,`user_id`,`avatar_frame_id`,`start_time`,`end_time`)
VALUES
  -- TestGetUserFirstWearTime
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 10, 1, UNIX_TIMESTAMP(), 0)
;

-- GRANT SELECT ON `vip_receive_coin_log` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `vip_receive_coin_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`vip_id` bigint NOT NULL COMMENT '领取时的会员 ID'
  ,`coin_num` bigint NOT NULL COMMENT '领取的钻石数'
  ,`receive_time` bigint NOT NULL COMMENT '领取的时间，秒级时间戳'
  ,`more` json NULL COMMENT '更多详情'
  ,PRIMARY KEY (`id`)
  ,KEY `idx_userid_receivetime` (`user_id`,`receive_time`)
  ,KEY `idx_modifiedtime` (`modified_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='会员领钻石日志表';

INSERT INTO `vip_receive_coin_log`
  (`id`, `create_time`, `modified_time`, `user_id`, `vip_id`, `coin_num`, `receive_time`, `more`)
VALUES
  -- TestIsReceivedToday
  (1, UNIX_TIMESTAMP() - 86400, UNIX_TIMESTAMP() - 86400, 1002, 1, 100, UNIX_TIMESTAMP() - 86400, NULL),
  (2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1002, 1, 100, UNIX_TIMESTAMP(), NULL)
;

-- GRANT SELECT ON `m_appearance` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_appearance` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL COMMENT '最后修改时间，单位：秒'
  ,`name` varchar(50) NOT NULL COMMENT '名称'
  ,`intro` varchar(125) NOT NULL COMMENT '简介'
  ,`vip` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否会员免费。0：否；1：是'
  ,`pay_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '付费类型。0：非付费使用；1：付费使用'
  ,`price` bigint NOT NULL COMMENT '价格，单位：分'
  ,`archive` tinyint(2) NOT NULL DEFAULT '0' COMMENT '归档状态 0：未归档；1：已归档'
  ,`appearance`json NOT NULL COMMENT '套装信息'
  ,`more` json NOT NULL COMMENT '更多信息'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外观套装详情表'
;

INSERT INTO `m_appearance`
  (`id`, `create_time`, `modified_time`, `name`, `intro`, `vip`, `pay_type`, `price`, `archive`, `appearance`, `more`)
VALUES
  -- TestListVipAppearanceElements
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试外观套装', '测试', 1, 1, 0, 0, '{}', '{"list_cover_url":"test://image/test.png", "bg_start_color":"#F8A623", "bg_end_color":"#F8A626"}')
;

-- GRANT SELECT ON `m_report_reason` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_report_reason` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`parent_id` bigint NOT NULL COMMENT '上级分类，无上级分类时为 0'
  ,`name` varchar(125) NOT NULL COMMENT '名称'
  ,`sort` bigint DEFAULT '0' COMMENT '举报原因顺序'
  ,`scene` tinyint DEFAULT '0' COMMENT '举报场景，使用比特位标识场景。第 1 位: 稿件举报；第 2 位: 互动举报；第 3 位: 直播举报'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='举报原因分类表';

INSERT INTO `m_report_reason`
  (`id`,`create_time`,`modified_time`,`parent_id`,`name`,`sort`,`scene`)
VALUES
  -- TestListReportReasonsByScene
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, '谣言类不实信息', 2, 1)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, '违法违规', 1, 1)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, '涉政谣言', 1, 1)
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, '赌博诈骗', 2, 1)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, '违法违禁', 1, 1)
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, '其他', 3, 1)
;

-- GRANT INSERT, SELECT, UPDATE ON `m_sound_addendum` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_sound_addendum` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间。单位：秒'
  ,`violation_level` tinyint NOT NULL DEFAULT '0' COMMENT '音频违规等级'
  ,`remark` varchar(1200) NOT NULL DEFAULT '' COMMENT '审核备注'
  ,`first_audit_pass_time` bigint DEFAULT NULL COMMENT '首次审核通过时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音频审核表';

-- GRANT SELECT ON `user_addendum` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `user_addendum` (
    `id` bigint NOT NULL COMMENT '用户 ID'
    ,`sex` tinyint DEFAULT NULL COMMENT '性别（0 未知，1 为男，2 为女）'
    ,`birthday` date DEFAULT NULL
    ,`qq` varchar(32) DEFAULT NULL COMMENT 'QQ 昵称'
    ,`weibo` varchar(32) DEFAULT NULL COMMENT '微博昵称'
    ,`wechat` varchar(32) DEFAULT NULL COMMENT '微信昵称'
    ,`bilibili` varchar(32) DEFAULT NULL COMMENT 'Bilibili 昵称'
    ,`apple` varchar(32) DEFAULT NULL COMMENT 'Apple 昵称'
    ,`message_config` varchar(255) DEFAULT NULL COMMENT '消息设置（JSON 字符串，例: {"receive":1,"fold":0}）'
    ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP'
    ,`ip_detail` text NOT NULL COMMENT '用户 IP 详情'
    ,`birthdate_mmdd` char(4) DEFAULT NULL COMMENT '用于检索的用户生日月日（例如 12 月 26 日生日为 1226）'
    ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
;

INSERT INTO `user_addendum`
    (`id`, `sex`, `birthday`, `qq`, `weibo`, `wechat`, `bilibili`, `apple`, `message_config`, `ip`, `ip_detail`, `birthdate_mmdd`)
VALUES
     -- TestFindUserIDsByBirthday、TestNeedLeapYearProcess、TestActionGetPersona
    (1234, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '127.0.0.1', '{}', '0126')
;

-- GRANT SELECT ON `m_recommended_exposure_level` TO 'missevan_main'@'%'
CREATE TABLE IF NOT EXISTS `m_recommended_exposure_level` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`exposure` bigint NOT NULL COMMENT '曝光量，e.g. 100,000'
  ,`level` varchar(10) NOT NULL COMMENT '曝光等级，e.g. S A B C'
  ,`status` tinyint NOT NULL COMMENT '状态，0：禁用，1：启用'
  ,`scene` tinyint NOT NULL COMMENT '场景，1：首页，2：直播页'
  ,KEY `idx_level_scene_status` (`level`,`scene`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐位曝光等级';

INSERT INTO `m_recommended_exposure_level`
    (`id`, `create_time`, `modified_time`, `exposure`, `level`, `status`, `scene`)
VALUES 
    (1001, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100000, 'S', 1, 1),
    (1002, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 50000, 'A', 1, 1),
    (1003, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 20000, 'B', 1, 1),
    (1004, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 10000, 'C', 1, 2);

-- GRANT SELECT ON `live` TO 'missevan_main'@'%'
CREATE TABLE `live` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`room_id` int unsigned NOT NULL COMMENT '直播间房间 ID'
  ,`catalog_id` int unsigned NOT NULL DEFAULT '0'
  ,`title` varchar(30) NOT NULL DEFAULT '' COMMENT '直播间名称'
  ,`intro` varchar(160) NOT NULL DEFAULT '' COMMENT '直播间简介'
  ,`status` tinyint DEFAULT '0' COMMENT '直播间状态，-1：用户注销（搜索隐藏）；0：没有开启房间；1：房间开启'
  ,`live_start_time` int unsigned NOT NULL DEFAULT '0' COMMENT '开播时间'
  ,`contract_id` int unsigned NOT NULL DEFAULT '1' COMMENT '主播合约 ID'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL COMMENT '修改时间'
  ,`user_id` bigint NOT NULL COMMENT '主播 ID'
  ,`cover` varchar(255) NOT NULL DEFAULT '' COMMENT '直播间封面'
  ,`score` bigint NOT NULL DEFAULT '0' COMMENT '直播间热度'
  ,`username` varchar(20) DEFAULT '' COMMENT '主播昵称，冗余字段'
  ,UNIQUE KEY `uk_user_id` (`user_id`)
  ,UNIQUE KEY `uk_roomid` (`room_id`)
  ,KEY `idx_modifiedtime` (`modified_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间表';

INSERT INTO `live`
    (`id`, `room_id`, `title`, `status`, `user_id`, `create_time`, `modified_time`)
VALUES 
    (1001, 1001, '测试直播间', 1, 1001, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
    (1002, 1002, '测试直播间2', 1, 1002, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
    (1003, 1003, '测试直播间3', 1, 1003, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- GRANT SELECT, INSERT ON `m_third_party_task` TO 'missevan_main'@'%';
CREATE TABLE IF NOT EXISTS `m_third_party_task` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `create_time` bigint NOT NULL COMMENT '创建时间，单位：秒',
  `modified_time` bigint NOT NULL COMMENT '最后修改时间，单位：秒',
  `task_time` bigint NOT NULL COMMENT '任务发起时间',
  `user_id` bigint NOT NULL COMMENT '任务用户 ID',
  `token` varchar(50) NOT NULL COMMENT '任务 token',
  `scene` int NOT NULL COMMENT '第三方场景 1：百度；2：携程；3：大众点评；9：微信公众号',
  `status` int NOT NULL DEFAULT '0' COMMENT '任务状态 0：未完成；1：已完成未领取奖励；2：已完成已领取奖励'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='三方导流用户任务详情'
;

INSERT INTO `m_third_party_task`
  (`id`, `create_time`, `modified_time`, `task_time`, `user_id`, `token`, `scene`, `status`)
VALUES
  -- TestFinishTask
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1996, 'abcdabcdabcdabcdabcdabcdabcdabca', 9, 0)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1997, 'abcdabcdabcdabcdabcdabcdabcdabcb', 9, 2)
  -- TestActionTask
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1998, 'abcdabcdabcdabcdabcdabcdabcdabcc', 9, 0)
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1999, 'abcdabcdabcdabcdabcdabcdabcdabcd', 9, 2)
;
