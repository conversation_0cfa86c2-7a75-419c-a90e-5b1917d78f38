# a config for missevan-main

web:
  address: '' # default: '', aka. any
  port: 3000 # default: 3000

log:
  format: "string" # string or json
  access_log: "stdout" # stdout: output to console, or define log path like "log/access_log"
  access_level: "debug"
  error_log: "stderr" # stderr: output to console, or define log path like "log/error_log"
  error_level: "error"
  agent:
    enabled: false
    dsn: 'udp://logstash.example.com:8911'
    app_id: missevan-main
    # host: '' # log host, defaults to os.Getenv("NODE_NAME") or os.Getenv("HOST") or os.Hostname()
    # instance_id: '' # log instance_id, defaults to os.Getenv("INSTANCE_ID") or os.Hostname()
    # channel_size: 0 # default 1024

http:
  mode: 'release' # default: 'release'
  rpc_key: 'testkey'
  disable_api_sign: false # default: false
  app_api_sign_key: 'testkey'
  csrf_allow_top_domains:
  - 'missevan.com'
  - 'bilibili.com'

params:
  url:
    main: 'https://www.missevan.com/'
    cdn: 'test://'
    avatar_url: 'test://avatars/'
    profile_url: 'test://profile/'
    cover_url: 'test://coversmini/'
    default_icon_url: 'icon://avatars/icon01.png'
    drama_cover_url: 'test://dramacoversmini/'
    default_cover_url: 'test://coversmini/nocover.png'
    rank_details_url: "https://m.uat.missevan.com/ranking"
  security:
    sensitive_information_key: 'openssl_aes_256_cbc_testpassword'
    # 加密 IV 初始化向量值，在需要数据库进行查询的地方使用到这个固定常量
    sensitive_fixed_iv_key: 'testiv'
  # 特殊音频 IDs（给用户做提示用的音频，不需要显示评论弹幕数等）
  notice_sound_ids: [75854, 115193, 223692, 518008, 1217690, 1616367, 519417, 246597, 884495, 1028077]
  # 进入剧集收益后台时，不需要进行短信验证的用户 ID
  no_check_drama_revenue_auth_user_ids: [233]
  # 进入剧集收益后台时，签约提示弹窗不支持关闭时间戳（2025-06-23 00:00:00）单位：秒
  drama_revenue_check_sign_pop_time: 0
  # 进入剧集收益后台时，签约提示弹窗是否支持关闭（0: 不支持 1: 支持）
  drama_revenue_check_sign_pop_can_close: 0
  # 会员相关配置
  vip:
    # 会员中心页 APP 主题皮肤“更多”按钮跳转链接，为空时不下发
    more_url_theme_skin: 'test://themeskins'
    # 会员中心页会员头像挂件“更多”按钮跳转链接，为空时不下发
    more_url_avatar_frame: 'test://avatar_frames'
    # 会员中心页会员 IP 套装“更多”按钮跳转链接，为空时不下发
    more_url_appearance: 'missevan://vip/center/appearance'
    # 开通会员促销价，单位：元。仅用于展示
    promotional_price: "9.9"
    # 会员特权
    privilege:
      # 会员每日可领钻石数量，为 0 时表示当前无此权益
      diamond_num: 5
      list:
        - title: "会员剧免费畅听"
          icon_url: "oss://image/a.png"
        - title: "精品剧集 8 折起"
          icon_url: "oss://image/b.png"
        - title: "点亮会员标识"
          icon_url: "oss://image/c.png"
        - title: "专属主题皮肤"
          icon_url: "oss://image/d.png"
        - title: "专属头像框"
          icon_url: "oss://image/e.png"
        - title: "敬请期待"
          icon_url: "oss://image/f.png"
    # 内购商品标识前缀
    iap_product_ids_prefix:
      # Apple Store
      missevan_ios: "com.missevan.CatEarFM.vip"
      # Google Play
      missevan_google_play: "cn.missevan.item.vip"
    # 会员服务协议
    trade_agreement_url: "https://link.uat.missevan.com/rule/vip-agreement"
    user_home_page_info:
      guest:
        title: '开通会员，每天领钻石'
        subtitle: '畅听精品好剧，最低 9.9 元/月'
      user_vip_not_yet:
        title: '开通会员，限时 9.9 元'
        subtitle: '畅听精品好剧，最多可领 155 钻石'
      user_vip_in_effect:
        title: '会员权益生效中'
        subtitle: '%s 到期，每日可领 5 钻石'
      user_vip_expired:
        title: '会员权益已过期，续费享众多福利'
        subtitle: '畅听精品好剧，每月最多可领 155 钻石'
    # 配置为空时，客户端跳转到原生会员中心页面
    vip_center_url: 'https://m.uat.missevan.com/vip'
    # 会员中心页入口配置，为 false 时不显示入口
    enable_vip_center: false
  search:
    hot_search_word_hot_icon_url: "test://image/icon.png"

service:
  db:
    host: '************'
    port: 3306
    name: 'app_missevan'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/app_missevan?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  main_db:
    host: '************'
    port: 3306
    name: 'missevan_main'
    user: 'user'
    pass: 'password'
  drama_db:
    host: '************'
    port: 3306
    name: 'app_missevan_radio_drama'
    user: 'user'
    pass: 'password'
  pay_db:
    host: '************'
    port: 3306
    name: 'missevan_pay'
    user: 'user'
    pass: 'password'
  new_adb: # 数仓版 ADB
    host: '************'
    port: 3306
    name: 'pay'
    user: 'user'
    pass: 'password'
  redis:
    addr: '************:6379'
    # password: '' # default: ''
    # db: 0 # default: 0
    # pool_size: 10 # default: serviceredis.DefaultPoolSize()
  lru_redis:
    addr: '************:6379'
    db: 0
  storage:
    icon:  # related with bucket (eg. bucket-icon)
      type: 'oss'
      access_key_id: 'accesskey'
      access_key_secret: 'xxxxxx'
      bucket: 'bucket-icon'
      endpoint: 'oss-cn-hangzhou.aliyuncs.com'
      # public_url 配置为 - 时，将禁止获取 public url
      # public_url: 'http://static.example.com/' # storage 地址转公共地址优先级: public_url > public_urls
      public_urls:
      - url: 'http://static.example.com'
        weight: 1
      - url: 'http://foramt.example.com/'
        # weight: 0
      private_key: '' # cdn private key
  pushservice:
    # using mrpc
    url: 'http://127.0.0.1:8098/'
    key: 'xxxxxx'
  mrpc:
    # mrpc url should have `/` suffix
    app:
      url: 'http://127.0.0.1:8017/rpc/'
      key: 'xxxxxx'
    drama:
      url: 'http://127.0.0.1:8080/rpc/'
      key: 'xxxxxx'
    go:
      url: 'http://127.0.0.1:3032/rpc/'
      key: 'xxxxxx'
    sso:
      url: 'http://127.0.0.1:3002/rpc/'
      key: 'xxxxxx'
    live:
      url: 'http://127.0.0.1:3013/rpc/'
      key: 'xxxxxx'
  captcha:
    enabled: true
    access_key_id: 'xxxxx'
    access_key_secret: 'xxxxx'
    app_key: 'xxxxx'
    region_id: 'cn-hangzhou'
    endpoint: 'http://afs.aliyuncs.com'
    slide_url: 'https://www.uat.missevan.com/standalone/403/slide.html'
  # TODO: 后续需要将 geetest 挪到 captcha 下
  geetest:
    url: 'http://api.geetest.com'
    geetest_id: 'testid'
    geetest_key: 'testkey'
    salt: 'salt'
  databus:
    app_log_pub:
      key: 'test1'
      secret: 'test1'
      group: 'AppLog-S'
      topic: 'AppLog-T'
      action: 'pub'
      # pool_size: 10 # pub pool size, default: 10
  # 点播 - 签约信息接口服务配置
  bilibili_settle:
    url: 'http://test-api.bilibili.com'
    app_key: 'testkey'
    app_secret: 'testsecret'
