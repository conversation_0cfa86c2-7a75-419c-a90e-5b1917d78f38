NAME=missevan-main
VERSION=1.8.2-7
TOKEN=${GITHUB_TOKEN}
PROXY=${BUILD_HTTP_PROXY}
MGOBRANCH?=release
APP_NAME?=${NAME}

.PHONY: rider-build

check:
	@echo -e 'NAME=${NAME}\nVERSION=${VERSION}\nTOKEN=${TOKEN}'
	@echo -e 'PROXY=${PROXY}\nMGOBRANCH=${MGOBRANCH}'

version:
	@echo ${VERSION}

rider-build:
	@env token=${TOKEN} proxy=${PROXY} mgobranch=${MGOBRANCH} ./gomod.sh
	env GOOS=linux CGO_ENABLED=0 go install -v -tags release -ldflags "-X main.Version=${VERSION}"
	@printf '#!/bin/sh\n\nexec su-exec nobody /data/app/${APP_NAME}/${NAME} "$$@"\n' > /go/bin/docker-entrypoint.sh
	chmod +x /go/bin/docker-entrypoint.sh
