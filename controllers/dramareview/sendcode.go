package dramareview

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-main/service"
)

type sendCodeParams struct {
	CaptchaToken string `form:"captcha_token" json:"captcha_token"` // 人机校验 token

	mobileNumber *vcode.MobileNumber
	clientIP     string
	userID       int64
}

// ActionSendcode 发送剧集收益后台认证手机号验证码
/**
 * @api {post} /x/drama/review/sendcode 发送剧集收益后台认证手机号验证码
 *
 * @apiVersion 0.1.0
 * @apiName sendcode
 * @apiGroup x/drama/review
 *
 * @apiPermission user
 *
 * @apiParam {String} captcha_token 人机验证 token
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "发送成功",
 *       "data": null
 *     }
 */
func ActionSendcode(c *handler.Context) (handler.ActionResponse, string, error) {
	params, err := newSendcodeParams(c)
	if err != nil {
		return nil, "", err
	}

	// 发送验证码
	err = params.sendcode()
	if err != nil {
		return nil, "", err
	}

	return nil, "发送成功", nil
}

func newSendcodeParams(c *handler.Context) (*sendCodeParams, error) {
	params := new(sendCodeParams)
	err := c.Bind(params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// 获取 IP 和用户 ID
	params.clientIP = c.ClientIP()
	params.userID = c.UserID()

	// 人机校验
	if service.Captcha.IsEnabled() {
		if params.CaptchaToken == "" {
			return nil, actionerrors.ErrParams
		}
		gp, ok := geetest.ParseToken(params.CaptchaToken)
		if !ok {
			return nil, actionerrors.ErrParams
		}
		// TODO: 后续使用 go rpc 调用
		validateResp, err := service.Geetest.Validate(gp, params.clientIP, strconv.FormatInt(params.userID, 10), "", "")
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if !validateResp.IsValid() {
			return nil, actionerrors.ErrCaptchaVerificationFailure
		}
	}
	// 获取用户绑定的认证手机号信息
	params.mobileNumber, err = getUserMobileNumber(params.userID)
	if err != nil {
		return nil, err
	}
	return params, nil
}

// sendcode 发送验证码
func (params *sendCodeParams) sendcode() error {
	scene, err := vcode.SendSmsScene(vcode.ObjectiveTypeDramaRevenue)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	sendParam := vcode.SendVCodeParam{
		RegionMobile: params.mobileNumber.RegionMobile,
		RegionCode:   params.mobileNumber.RegionCode,
		IP:           params.clientIP,
		UserID:       params.userID,
		PostType:     vcode.ObjectiveTypeDramaRevenue,
		Scene:        scene,
	}

	if err = sendParam.Validate(); err != nil {
		if vcode.IsValidateLimitErr(err) {
			return actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, err.Error())
		}
		return actionerrors.ErrServerInternal(err, logger.Fields{"user_id": params.userID, "ip": params.clientIP})
	}

	// 发送短信验证码
	err = sendParam.SendSms()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	return nil
}
