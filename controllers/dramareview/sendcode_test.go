package dramareview

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramarevenuereviewerinfo"
	"github.com/MiaoSiLa/missevan-main/service"
)

var captchaConfig = captcha.Config{
	Enabled:         true,
	AccessKeyID:     "LTAIsNW7Hxzgnxu2",
	AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
	AppKey:          "FFFF0N0N0000000016A7",
	RegionID:        "cn-hangzhou",
	Endpoint:        "https://afs.cn-hangzhou.aliyuncs.com",
	SlideURL:        "https://www.uat.missevan.com/standalone/403/slide.html",
}

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestSendcodeTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(sendCodeParams{}, "captcha_token")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(sendCodeParams{}, "captcha_token")
}

func TestActionSendcode(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试人机验证开关开启时
	var err error
	service.Captcha, err = captcha.NewClient(&captchaConfig)
	require.NoError(err)
	defer func() {
		service.Captcha, _ = captcha.NewClient(captcha.BuildDefaultConfig())
	}()

	cleanup := mrpc.SetMock(pushservice.Scheme+"://api/sms", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	geetestClient := geetest.NewClient(geetest.TestConfig())
	cancel := geetestClient.SetMock(geetest.URIValidate, map[string]string{
		"seccode": "",
	})
	defer cancel()

	// 测试参数错误
	params := new(sendCodeParams)
	api := "/x/drama/review/sendcode"
	c := handler.NewTestContext(http.MethodPost, api, true, params)
	data, message, err := ActionSendcode(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(data)
	assert.Empty(message)

	// 测试人机验证失败
	geetestClient.SetMock(geetest.URIValidate, map[string]string{
		"seccode": "false",
	})
	params.CaptchaToken = "geetest|7e21d91d295a4580e919c9b68ba2ba67|0670b79b9a5a1cd8c4eb3f161a6be005|0670b79b9a5a1cd8c4eb3f161a6be005|jordan"
	c = handler.NewTestContext(http.MethodPost, api, true, params)
	data, message, err = ActionSendcode(c)
	assert.Equal(actionerrors.ErrCaptchaVerificationFailure, err)
	assert.Nil(data)
	assert.Empty(message)

	// 删除测试数据
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Delete("", "user_id = ?", c.UserID()).Error)
	// 创建测试数据
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Create(
		&dramarevenuereviewerinfo.DramaRevenueReviewerInfo{
			UserID: 12,
			Mobile: "18510086543",
			Region: 86,
		}).Error)

	geetestClient.SetMock(geetest.URIValidate, map[string]string{
		"seccode": "true",
	})

	// 删除 redis 测试数据
	service.Redis.Del(serviceredis.LockMobileVCode1.Format("8618510086543"))
	service.Redis.Del("mobile_8618510086543")

	// 测试发送成功
	testIP := "127.0.0.1"
	c = handler.NewTestContext(http.MethodPost, api, true, params)
	c.SetClientIP(testIP)
	keyIPSendCount := serviceredis.KeyCounterVCodeIP1.Format(testIP)
	keyUserSendCount := serviceredis.KeyCounterVCodeUID1.Format(c.User().ID)
	require.NoError(service.Redis.Del(keyIPSendCount, keyUserSendCount).Err())

	data, message, err = ActionSendcode(c)
	require.NoError(err)
	assert.Nil(data)
	assert.EqualValues("发送成功", message)
	// 验证验证码数据
	identityInfo, err := service.Redis.HMGet("mobile_8618510086543", vcode.FieldVCodeObjective).Result()
	require.NoError(err)
	assert.Equal(strconv.Itoa(vcode.ObjectiveTypeDramaRevenue), identityInfo[0])
}

func TestNewSendcodeParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试人机验证开关开启时
	var err error
	service.Captcha, err = captcha.NewClient(&captchaConfig)
	require.NoError(err)
	defer func() {
		service.Captcha, _ = captcha.NewClient(captcha.BuildDefaultConfig())
	}()

	geetestClient := geetest.NewClient(geetest.TestConfig())
	cancel := geetestClient.SetMock(geetest.URIValidate, map[string]string{
		"seccode": "",
	})
	defer cancel()

	// 测试参数错误（captcha_token 为空时）
	body := new(sendCodeParams)
	api := "/x/drama/review/sendcode"
	c := handler.NewTestContext(http.MethodPost, api, true, body)
	params, err := newSendcodeParams(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(params)

	// 测试参数错误（captcha_token 格式错误时）
	body.CaptchaToken = "geetest|7e21d91d295a4580e919c9b68ba2ba67|0670b79b9a5a1cd8c4eb3f161a6be005"
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	params, err = newSendcodeParams(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(params)

	// 测试人机验证失败
	geetestClient.SetMock(geetest.URIValidate, map[string]string{
		"seccode": "false",
	})
	captchaToken := "geetest|7e21d91d295a4580e919c9b68ba2ba67|0670b79b9a5a1cd8c4eb3f161a6be005|0670b79b9a5a1cd8c4eb3f161a6be005|jordan"
	body.CaptchaToken = captchaToken
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	params, err = newSendcodeParams(c)
	assert.Equal(actionerrors.ErrCaptchaVerificationFailure, err)
	assert.Nil(params)

	// 删除测试数据
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Delete("", "user_id = ?", c.UserID()).Error)

	// 测试用户未绑定手机号
	geetestClient.SetMock(geetest.URIValidate, map[string]string{
		"seccode": "true",
	})
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	params, err = newSendcodeParams(c)
	assert.Equal(actionerrors.ErrDramaReviewUserNoMobile, err)
	assert.Nil(params)

	// 创建测试数据
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Create(
		&dramarevenuereviewerinfo.DramaRevenueReviewerInfo{
			UserID: 12,
			Mobile: "18510086543",
			Region: 86,
		}).Error)

	// 测试用户已绑定手机号
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	params, err = newSendcodeParams(c)
	require.NoError(err)
	assert.NotNil(params)
	assert.EqualValues(12, params.userID)
	assert.Equal(captchaToken, params.CaptchaToken)
	assert.Equal("8618510086543", params.mobileNumber.RegionMobile)
}

func TestSendCodeParams_sendcode(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	ip := "127.0.0.101"
	userID := int64(3457181)
	params := &sendCodeParams{
		mobileNumber: &vcode.MobileNumber{
			RegionMobile: "8618510086543",
			RegionCode:   86,
		},
		clientIP: ip,
		userID:   userID,
	}
	// 删除 redis 测试数据（发送手机验证码锁）
	service.Redis.Del(serviceredis.LockMobileVCode1.Format("8618510086543"))

	cleanup := mrpc.SetMock(pushservice.Scheme+"://api/sms", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	// 测试超过 IP 次数限制时
	KeyIPSendCount := serviceredis.KeyCounterVCodeIP1.Format(ip)
	require.NoError(service.Redis.Set(KeyIPSendCount, vcode.LimitVCodeIPCount, 5*time.Second).Err())
	err := params.sendcode()
	errInfo := vcode.ErrSendVCodeMoreTimesDaily.Error()
	assert.Equal(err, actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, errInfo))
	require.NoError(service.Redis.Del(KeyIPSendCount).Err())

	// 删除 redis 测试数据（发送手机验证码锁）
	service.Redis.Del(serviceredis.LockMobileVCode1.Format("8618510086543"))

	// 测试超过用户 ID 次数限制
	KeyUserSendCount := serviceredis.KeyCounterVCodeUID1.Format(userID)
	require.NoError(service.Redis.Set(KeyUserSendCount, vcode.LimitVCodeUserCount, 5*time.Second).Err())
	err = params.sendcode()
	assert.Equal(err, actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, errInfo))
	require.NoError(service.Redis.Del(KeyUserSendCount).Err())

	// 测试操作频繁
	errInfo2 := vcode.ErrSendVCodeMoreTimesPerMinute.Error()
	err = params.sendcode()
	assert.Equal(err, actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, errInfo2))

	// 删除 redis 测试数据（发送手机验证码锁）
	service.Redis.Del(serviceredis.LockMobileVCode1.Format("8618510086543"))
	// 删除 redis 测试数据（手机验证码）
	service.Redis.Del("mobile_8618510086543")

	// 测试发送成功
	err = params.sendcode()
	require.NoError(err)

	identityInfo, err := service.Redis.HMGet("mobile_8618510086543", vcode.FieldVCodeObjective).Result()
	require.NoError(err)
	assert.Equal(strconv.Itoa(vcode.ObjectiveTypeDramaRevenue), identityInfo[0])
}
