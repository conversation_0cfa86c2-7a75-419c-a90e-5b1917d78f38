package dramareview

import (
	"bytes"
	"encoding/csv"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramareviewers"
	"github.com/MiaoSiLa/missevan-main/service"
)

type flowOverviewResp struct {
	TodayCoin        int64 `json:"today_coin"`
	CurrentMonthCoin int64 `json:"current_month_coin"`
}

type flowItem struct {
	Date       string `gorm:"column:date" json:"date"`
	DramaID    int64  `gorm:"column:drama_id" json:"drama_id,omitempty"`
	DramaName  string `gorm:"column:drama_name" json:"drama_name,omitempty"`
	BuyCoin    int64  `gorm:"column:buy_coin" json:"buy_coin"`
	RewardCoin int64  `gorm:"column:reward_coin" json:"reward_coin"`
	Coin       int64  `gorm:"column:coin" json:"coin"`
}

// totalItem 流水汇总字段
type totalItem struct {
	BuyCoin        int64 `json:"buy_coin"`         // 购剧总流水（单位：钻）
	BuyTradeNum    int64 `json:"buy_trade_num"`    // 购剧交易订单数量
	RewardCoin     int64 `json:"reward_coin"`      // 打赏总流水（单位：钻）
	RewardTradeNum int64 `json:"reward_trade_num"` // 打赏交易订单数量
	AllCoin        int64 `json:"all_coin"`         // 总流水（单位：钻）
	AllTradeNum    int64 `json:"all_trade_num"`    // 总交易订单数量
}

type flowListResp struct {
	Data       []flowItem            `json:"data"`
	Pagination util.MarkerPagination `json:"pagination"`
	Total      *totalItem            `json:"total,omitempty"`
}

const (
	monthlyFlowPageSize   = 3
	monthlyFlowV2PageSize = 6
	dailyFlowPageSize     = 50

	// ADB 支持 BIGINT INT SIGNED
	// MySQL 支持 UNSIGNED SIGNED
	// 转整型需要选择 SIGNED 同时满足两种环境
	incomeField    = "CAST(income * 10 AS SIGNED)" // 结算使用 income 进行统计，前端以钻石数量来展现（下线活动兑换、福袋广播剧等 coin 为 0，但结算需要包含这些数据）
	exportMaxCount = 20000                         // 导出明细数据量上限
)

// ActionFlowOverview 流水概览
/**
 * @api {get} /x/drama/review/flow/overview 流水概览
 *
 * @apiVersion 0.1.0
 * @apiName overview
 * @apiGroup x/drama/review/flow
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "today_coin": 123456, // 今日累计钻石流水（单位：钻）
 *         "current_month_coin": 999940 // 当月累计钻石流水（单位：钻）
 *       }
 *     }
 */
func ActionFlowOverview(ctx *handler.Context) (handler.ActionResponse, string, error) {
	userID := ctx.UserID()
	// 目前单个用户配置最多的剧集 ID 数为 300 个以内，若后续持续增加考虑用临时表方式优化
	dramaIDs, err := dramareviewers.GetDramaIDs(ctx.UserContext(), userID)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	resp := flowOverviewResp{}
	if len(dramaIDs) == 0 {
		return resp, "", nil
	}

	now := util.TimeNow()
	beginningOfDay := util.BeginningOfDay(now)
	beginningOfMonth := util.BeginningOfMonth(now)

	err = service.NewADB.Table(transaction.TransactionLog{}.TableName()).
		Select([]string{
			fmt.Sprintf("SUM(IF(confirm_time >= %d, %s, 0)) AS today_coin", beginningOfDay.Unix(), incomeField),
			fmt.Sprintf("SUM(%s) AS current_month_coin", incomeField),
		}).
		Where("gift_id IN (?)", dramaIDs).
		Where("status = ?", transaction.StatusSuccess).
		Where("type IN (?)", []int64{transaction.TypeDrama, transaction.TypeSound, transaction.TypeDramaReward}).
		Where("confirm_time BETWEEN ? AND ?", beginningOfMonth.Unix(), now.Unix()).
		Scan(&resp).Error
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}

	return resp, "", nil
}

func parseDailyFlowMarker(marker string) (confirmDate string, currentTimeUnix, coin, buyCoin, rewardCoin, dramaID int64, hasSameDateLast bool, err error) {
	if marker == "" {
		// 获取第一页数据时，confirm_time < 当前时间点，防止某个剧集突然新增大量交易，会出现第一页的数据往后挤占到第二页的情况发生
		currentTimeUnix = util.TimeNow().Unix()
		return
	}
	items := strings.Split(marker, ",")
	if len(items) != 7 {
		err = errors.New("参数格式错误")
		return
	}

	// 上一页的最小订单确认日期
	confirmDate = items[0]

	// 请求第一页时的时间点
	currentTimeUnix, err = strconv.ParseInt(items[1], 10, 64)
	if err != nil {
		return
	}

	coin, err = strconv.ParseInt(items[2], 10, 64)
	if err != nil {
		return
	}

	buyCoin, err = strconv.ParseInt(items[3], 10, 64)
	if err != nil {
		return
	}

	rewardCoin, err = strconv.ParseInt(items[4], 10, 64)
	if err != nil {
		return
	}

	dramaID, err = strconv.ParseInt(items[5], 10, 64)
	if err != nil {
		return
	}

	var hasSameDateLastFlag int64
	hasSameDateLastFlag, err = strconv.ParseInt(items[6], 10, 64)
	if err != nil {
		return
	}

	if confirmDate == "" || currentTimeUnix < 0 || coin < 0 || buyCoin < 0 || rewardCoin < 0 || dramaID < 0 || hasSameDateLastFlag < 0 || hasSameDateLastFlag > 1 {
		err = errors.New("参数错误")
	}

	if hasSameDateLastFlag == 1 {
		hasSameDateLast = true
	}
	return
}

func parseMonthlyFlowMarker(marker string) (startTime, endTime time.Time, err error) {
	if marker == "" {
		endTime = util.BeginningOfMonth(util.TimeNow())
	} else {
		var lastTimestamp int64
		lastTimestamp, err = strconv.ParseInt(marker, 10, 64)
		if err != nil {
			return
		}
		endTime = time.Unix(lastTimestamp, 0)
	}

	startTime = endTime.AddDate(0, -monthlyFlowPageSize, 0)
	return
}

// 对应月份没有收益时，仍需显示收益为 0 的记录
func fileMonthlyFlowWithZeroCoinRecord(respList []flowItem, endTime time.Time, size int) []flowItem {
	t := util.BeginningOfMonth(endTime.Add(-time.Second))
	list := make([]flowItem, size)
	for i := 0; i < size; i++ {
		list[i] = flowItem{
			Date: t.AddDate(0, -i, 0).Format("2006 年 01 月"),
		}
		for _, item := range respList {
			if list[i].Date == item.Date {
				list[i] = item
				break
			}
		}
	}

	return list
}

func periodicalFlow(extraSelectColumns []string, filterDramaIDs []int64, filterStartTime, filterEndTime, lastConfirmTime *time.Time, groupBy, having, orderBy string, limit int) ([]flowItem, bool, *flowItem, error) {
	selectColumns := []string{
		fmt.Sprintf("SUM(IF(type IN (?), %s, 0)) AS buy_coin", incomeField),
		fmt.Sprintf("SUM(IF(type = ?, %s, 0)) AS reward_coin", incomeField),
		fmt.Sprintf("SUM(%s) AS coin", incomeField),
	}
	if len(extraSelectColumns) > 0 {
		selectColumns = append(selectColumns, extraSelectColumns...)
	}

	var respList []flowItem
	query := service.NewADB.Table(transaction.TransactionLog{}.TableName()).
		Select(selectColumns, []int64{transaction.TypeDrama, transaction.TypeSound}, transaction.TypeDramaReward).
		Where("gift_id IN (?)", filterDramaIDs).
		Where("status = ?", transaction.StatusSuccess).
		Where("type IN (?)", []int64{transaction.TypeDrama, transaction.TypeSound, transaction.TypeDramaReward}).
		Where("income > 0").
		Group(groupBy)
	if filterStartTime != nil {
		query = query.Where("confirm_time >= ?", filterStartTime.Unix())
	}
	if filterEndTime != nil {
		query = query.Where("confirm_time < ?", filterEndTime.Unix())
	}
	if lastConfirmTime != nil {
		// lastConfirmTime 为上一页最后一条记录的日期，confirm_time <= lastConfirmTime，缩小查询范围
		query = query.Where("confirm_time <= ?", lastConfirmTime.Unix())
	}
	if having != "" {
		query = query.Having(having)
	}
	err := query.Order(orderBy).Limit(limit + 1).Scan(&respList).Error
	if err != nil {
		return nil, false, nil, err
	}
	var nextItem *flowItem
	hasMore := len(respList) > limit
	if hasMore {
		nextItem = &respList[len(respList)-1]
		respList = respList[:limit]
	}
	return respList, hasMore, nextItem, nil
}

// ActionMonthlyFlow 月度流水
/**
 * @api {get} /x/drama/review/flow/monthly 月度流水
 *
 * @apiVersion 0.1.0
 * @apiName monthly
 * @apiGroup x/drama/review/flow
 *
 * @apiPermission user
 *
 * @apiParam {String} [marker] 翻页标记（最开始不需要传）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [{
 *           "date": "2024 年 9 月",
 *           "buy_coin": 98760, // 购剧流水（单位：钻）
 *           "reward_coin": 1234, // 打赏流水（单位：钻）
 *           "coin": 99994 // 总流水（单位：钻）
 *         }, {
 *           "date": "2024 年 8 月",
 *           "buy_coin": 88760,
 *           "reward_coin": 1034,
 *           "coin": 89990
 *         }],
 *         "pagination": {
 *           "has_more": true, // 是否还有更多数据
 *           "marker": "1725119999" // 翻页标记
 *         }
 *       }
 *     }
 */
// TODO: 前端用 flow/monthly-v2 接口替换 flow/monthly 后，可删除 flow/monthly
func ActionMonthlyFlow(ctx *handler.Context) (handler.ActionResponse, string, error) {
	marker, _ := ctx.GetParamString("marker")
	startTime, endTime, err := parseMonthlyFlowMarker(marker)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}

	userID := ctx.UserID()
	dramaIDs, err := dramareviewers.GetDramaIDs(ctx.UserContext(), userID)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}

	resp := flowListResp{}
	if len(dramaIDs) != 0 {
		resp.Data, resp.Pagination.HasMore, _, err = periodicalFlow([]string{
			"FROM_UNIXTIME(confirm_time, '%Y 年 %m 月') AS date",
		}, dramaIDs, nil, &endTime, nil, "date", "", "date DESC", monthlyFlowPageSize)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
	}

	resp.Data = fileMonthlyFlowWithZeroCoinRecord(resp.Data, endTime, monthlyFlowPageSize)
	if resp.Pagination.HasMore {
		resp.Pagination.Marker = fmt.Sprintf("%d", startTime.Unix())
	}

	return resp, "", nil
}

// ActionDailyFlow 流水明细（筛选日期或筛选剧集）
/**
 * @api {get} /x/drama/review/flow/daily 流水明细（筛选日期或筛选剧集）
 *
 * @apiVersion 0.1.0
 * @apiName daily
 * @apiGroup x/drama/review/flow
 *
 * @apiPermission user
 *
 * @apiParam {String} [start_date] 起始日期（默认为当天，例：2022-05-21）
 * @apiParam {String} [end_date] 截止日期（默认为当天，例：2022-05-21）
 * @apiParam {Number} [drama_id=0] 剧集 ID
 * @apiParam {String} [marker] 翻页标记（最开始不需要传）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [{
 *           "date": "2024-08-25",
 *           "drama_id": 12345,
 *           "drama_name": "我的王妃是男人",
 *           "buy_coin": 98760, // 购剧流水（单位：钻）
 *           "reward_coin": 1234, // 打赏流水（单位：钻）
 *           "coin": 99994 // 总流水（单位：钻）
 *         }, {
 *           "date": "2024-08-25",
 *           "drama_id": 9888,
 *           "drama_name": "杀破狼",
 *           "buy_coin": 70260,
 *           "reward_coin": 1205,
 *           "coin": 81465
 *         }],
 *         "pagination": {
 *           "has_more": true, // 是否还有更多数据
 *           "marker": "2024-10-25,1729827330,81465,70260,1205,9888,0" // 翻页标记
 *         },
 *         "total": {
 *           "buy_coin": 197520, // 购剧总流水（单位：钻）
 *           "reward_coin": 2468, // 打赏总流水（单位：钻）
 *           "all_coin": 199988 // 总流水（单位：钻）
 *         }
 *       }
 *     }
 */
// TODO: 前端用 flow/daily-v2 接口替换 flow/daily 后，可删除 flow/daily
func ActionDailyFlow(ctx *handler.Context) (handler.ActionResponse, string, error) {
	userID := ctx.UserID()
	dramaID, err := ctx.GetDefaultParamInt64("drama_id", 0)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	reviewDramaIDs, err := dramareviewers.GetDramaIDs(ctx.UserContext(), userID)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	var dramaIDs []int64
	if dramaID == 0 {
		// 默认筛选用户所有剧集
		dramaIDs = reviewDramaIDs
	} else {
		// 判断筛选的剧集用户是否能查看
		ok := util.Includes(len(reviewDramaIDs), func(i int) bool {
			return reviewDramaIDs[i] == dramaID
		})
		if !ok {
			return nil, "", actionerrors.ErrDramaNotFound
		}
		dramaIDs = []int64{dramaID}
	}
	resp := flowListResp{}
	if len(dramaIDs) == 0 {
		return resp, "", nil
	}
	// WORKAROUND: 兼容 date 参数，前端支持筛选时间段时可删除
	var startTime, endTime time.Time
	if date, _ := ctx.GetParamString("date"); date != "" {
		startTime, err = time.ParseInLocation(util.TimeFormatYMD, date, time.Local)
		if err != nil {
			return nil, "", actionerrors.ErrParams
		}
		endTime = util.NextDayTime(startTime)
	} else {
		defaultDate := util.BeginningOfDay(util.TimeNow()).Format(util.TimeFormatYMD)
		startTime, endTime, err = ctx.GetParamDateRange(defaultDate, defaultDate)
		if err != nil {
			return nil, "", actionerrors.ErrParams
		}
		endTime = util.NextDayTime(endTime)
	}

	marker, _ := ctx.GetParamString("marker")
	lastConfirmDate, currentTimeUnix, lastCoin, lastBuyCoin, lastRewardCoin, lastDramaID, hasSameDateLast, err := parseDailyFlowMarker(marker)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}

	var having string
	var lastConfirmTime *time.Time
	if lastConfirmDate != "" {
		// 获取用于 HAVING 条件里的日期
		havingEndTime, err := time.ParseInLocation(util.TimeFormatYMD, lastConfirmDate, time.Local)
		if err != nil {
			return nil, "", actionerrors.ErrParams
		}
		lastConfirmTime = &havingEndTime
		if hasSameDateLast { // 如果下一项记录包含相同的订单确认时间，则过滤时按订单确认时间、总钻石数、购剧钻石数、打赏钻石数、剧集 ID，依次倒序取
			// 拼接 date coin buy_coin reward_coin drama_id 用于 HAVING 条件，保证获取的数据唯一且有序
			havingExtraFilter := "CONCAT(FROM_UNIXTIME(UNIX_TIMESTAMP(date), '%Y%m%d')" +
				", LPAD(coin, 8, 0)" +
				", LPAD(buy_coin, 8, 0)" +
				", LPAD(reward_coin, 8, 0)" +
				", LPAD(drama_id, 8, 0))"

			havingExtraValues := fmt.Sprintf("%s%08d%08d%08d%08d", havingEndTime.Format(util.TimeFormatYMDWithNoSpace), lastCoin, lastBuyCoin, lastRewardCoin, lastDramaID)
			having = fmt.Sprintf("%s < %s", havingExtraFilter, havingExtraValues)
		}
	}

	// 使用快照的当前时间约束结束时间，保证幂等
	currentTime := time.Unix(currentTimeUnix, 0)
	filterEndTime := endTime
	if currentTime.Before(endTime) {
		filterEndTime = currentTime
	}
	var nextItem *flowItem
	resp.Data, resp.Pagination.HasMore, nextItem, err = periodicalFlow([]string{
		"FROM_UNIXTIME(confirm_time, '%Y-%m-%d') AS date",
		"gift_id AS drama_id",
		"title AS drama_name",
	}, dramaIDs, &startTime, &filterEndTime, lastConfirmTime, "date, drama_id", having, "date DESC, coin DESC, buy_coin DESC, reward_coin DESC, drama_id DESC", dailyFlowPageSize)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}

	if resp.Pagination.HasMore {
		lastItem := resp.Data[len(resp.Data)-1]
		var hasSameDateLastFlag int
		if nextItem != nil && nextItem.Date == lastItem.Date {
			hasSameDateLastFlag = 1
		}
		resp.Pagination.Marker = fmt.Sprintf("%s,%d,%d,%d,%d,%d,%d",
			lastItem.Date,
			currentTimeUnix, // 请求第一页时的时间点
			lastItem.Coin,
			lastItem.BuyCoin,
			lastItem.RewardCoin,
			lastItem.DramaID,
			hasSameDateLastFlag,
		)
	}

	// 流水汇总
	resp.Total, err = flowTotal(dramaIDs, &startTime, &endTime, []int{transaction.TypeSound, transaction.TypeDrama, transaction.TypeDramaReward})
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return resp, "", nil
}

// flowTotal 流水汇总处理
func flowTotal(filterDramaIDs []int64, filterStartTime, filterEndTime *time.Time, filterTradeTypes []int) (*totalItem, error) {
	total := new(totalItem)
	selectColumns := []string{
		fmt.Sprintf("SUM(IF(type IN (?), %s, 0)) AS buy_coin", incomeField), // 购剧总流水
		"SUM(IF(type IN (?), 1, 0)) AS buy_trade_num",                       // 购剧交易订单数量
		fmt.Sprintf("SUM(IF(type = ?, %s, 0)) AS reward_coin", incomeField), // 打赏总流水
		"SUM(IF(type = ?, 1, 0)) AS reward_trade_num",                       // 打赏交易订单数量
		fmt.Sprintf("SUM(%s) AS all_coin", incomeField),                     // 总流水
		"COUNT(*) AS all_trade_num",                                         // 总交易订单数量
	}
	buyDramaTypes := []int64{transaction.TypeDrama, transaction.TypeSound}
	err := service.NewADB.Table(transaction.TransactionLog{}.TableName()).
		Select(selectColumns, buyDramaTypes, buyDramaTypes, transaction.TypeDramaReward, transaction.TypeDramaReward).
		Where("gift_id IN (?)", filterDramaIDs).
		Where("status = ?", transaction.StatusSuccess).
		Where("type IN (?)", filterTradeTypes).
		Where("confirm_time >= ? AND confirm_time < ?", filterStartTime.Unix(), filterEndTime.Unix()).Scan(&total).Error
	if err != nil {
		return nil, err
	}
	return total, nil
}

// flowDailyV2Param 新版日流水明细相关参数
type flowDailyV2Param struct {
	userID               int64
	startDate            time.Time
	endDate              time.Time
	dramaIDs             []int64
	tradeTypes           []int
	p                    int64
	pageSize             int64
	firstRequestTimeUnix int64 // 请求第一页时的时间点（时间戳，单位：秒）
}

// flowItemV2 新版日流水明细字段
type flowItemV2 struct {
	Time      string `gorm:"column:time" json:"time"`
	DramaID   int64  `gorm:"column:drama_id" json:"drama_id"`
	DramaName string `gorm:"column:drama_name" json:"drama_name"`
	Coin      int64  `gorm:"column:coin" json:"coin"`
	UserID    int64  `gorm:"column:user_id" json:"user_id"`

	Type int `gorm:"column:type" json:"-"` // 交易类型

	TypeName string `json:"type"` // 交易类型名称
}

// pagination 分页数据
type pagination struct {
	HasMore  bool   `json:"has_more"`
	Marker   string `json:"marker,omitempty"`
	P        int64  `json:"p"`
	PageSize int64  `json:"pagesize"`
}

// flowDailyListV2Resp 新版日流水明细返回值
type flowDailyListV2Resp struct {
	Data       []flowItemV2 `json:"data"`
	Pagination pagination   `json:"pagination"`
	Total      *totalItem   `json:"total,omitempty"`
}

// 交易类型参数值含义
const (
	tradeTypeAll    = iota // 全部
	tradeTypeDrama         // 整剧购买
	tradeTypeSound         // 单集购买
	tradeTypeReward        // 剧集打赏
)

// ActionDailyFlowV2 流水明细
/**
 * @api {get} /x/drama/review/flow/daily-v2 流水明细（可筛选日期、剧集、交易类型）
 *
 * @apiVersion 0.1.0
 * @apiName daily-v2
 * @apiGroup x/drama/review/flow
 *
 * @apiPermission user
 *
 * @apiParam {String} [start_date] 起始日期（默认为当天，例：2022-05-21）
 * @apiParam {String} [end_date] 截止日期（默认为当天，例：2022-05-21）
 * @apiParam {Number} [drama_id=0] 剧集 ID（默认为该用户所有能查看的剧集）
 * @apiParam {number=0,1,2,3} [type=0] 交易类型 0：全部；1：整剧购买；2：单集购买；3：剧集打赏
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=50] 每页个数
 * @apiParam {String} [marker] 分页标记（首页不传）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [{
 *           "time": "2024-08-25 11:18:02",
 *           "drama_id": 12345,
 *           "drama_name": "我的王妃是男人",
 *           "type": "整剧购买", // 类型：整剧购买，单集购买，剧集打赏
 *           "coin": 199, // 流水（单位：钻）
 *           "user_id": 346286 // 交易用户的M号
 *         }, {
 *           "time": "2024-08-25 11:18:03",
 *           "drama_id": 9888,
 *           "drama_name": "杀破狼",
 *           "type": "整剧购买",
 *           "coin": 1205,
 *           "user_id": 814652
 *         }],
 *         "pagination": {
 *           "has_more": false, // 是否有更多数据
 *           "marker": "1729827330", // 翻页标记（请求第一页时的时间戳）
 *           "p": 1,
 *           "pagesize": 50
 *         },
 *         "total": {
 *           "buy_coin": 197520, // 购剧总流水（单位：钻）
 *           "buy_trade_num": 1260, // 购剧交易订单数量
 *           "reward_coin": 2468, // 打赏总流水（单位：钻）
 *           "reward_trade_num": 1260, // 打赏交易订单数量
 *           "all_coin": 199988, // 总流水（单位：钻）
 *           "all_trade_num": 1260 // 总交易订单数量
 *         }
 *       }
 *     }
 */
func ActionDailyFlowV2(ctx *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newFlowDailyV2Param(ctx)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.dailyFlowV2()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	// 流水汇总
	resp.Total, err = flowTotal(param.dramaIDs, &param.startDate, &param.endDate, param.tradeTypes)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return resp, "", nil
}

// newFlowDailyV2Param 初始化参数
func newFlowDailyV2Param(ctx *handler.Context) (*flowDailyV2Param, error) {
	p, pageSize, err := ctx.GetParamPage(&handler.PageOption{DefaultPageSize: dailyFlowPageSize})
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// 请求第一页时的时间点（时间戳，单位：秒）
	marker, _ := ctx.GetParamString("marker")
	var firstRequestTimeUnix int64
	if marker != "" {
		firstRequestTimeUnix, err = strconv.ParseInt(marker, 10, 64)
		if err != nil {
			return nil, actionerrors.ErrParams
		}
	}
	tradeType, err := ctx.GetDefaultParamInt("type", tradeTypeAll)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	var transactionTypes []int
	switch tradeType {
	case tradeTypeAll:
		transactionTypes = []int{transaction.TypeSound, transaction.TypeDrama, transaction.TypeDramaReward}
	case tradeTypeDrama:
		transactionTypes = []int{transaction.TypeDrama}
	case tradeTypeSound:
		transactionTypes = []int{transaction.TypeSound}
	case tradeTypeReward:
		transactionTypes = []int{transaction.TypeDramaReward}
	default:
		return nil, actionerrors.ErrParams
	}
	defaultDate := util.BeginningOfDay(util.TimeNow()).Format(util.TimeFormatYMD)
	startDate, endDate, err := ctx.GetParamDateRange(defaultDate, defaultDate)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	dramaID, err := ctx.GetDefaultParamInt64("drama_id", 0)
	if err != nil || dramaID < 0 {
		return nil, actionerrors.ErrParams
	}
	userID := ctx.UserID()
	reviewDramaIDs, err := dramareviewers.GetDramaIDs(ctx.UserContext(), userID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(reviewDramaIDs) == 0 {
		return nil, actionerrors.ErrDramaNotFound
	}
	var dramaIDs []int64
	if dramaID > 0 {
		if !slices.Contains(reviewDramaIDs, dramaID) {
			return nil, actionerrors.ErrDramaNotFound
		}
		dramaIDs = []int64{dramaID}
	} else {
		// 未筛选剧集 ID，则获取用户可查看的全部剧集
		dramaIDs = reviewDramaIDs
	}
	param := &flowDailyV2Param{
		startDate:            startDate,
		endDate:              util.NextDayTime(endDate),
		dramaIDs:             dramaIDs,
		tradeTypes:           transactionTypes,
		p:                    p,
		pageSize:             pageSize,
		userID:               userID,
		firstRequestTimeUnix: firstRequestTimeUnix,
	}
	if param.p == 1 {
		// 快照用户请求第一页时间，confirm_time < 这个时间点，防止某个剧集突然新增大量交易，会出现第一页的数据往后挤占到第二页的情况发生
		param.firstRequestTimeUnix = util.TimeNow().Unix()
	}
	return param, nil
}

// dailyFlowV2 新版日流水明细获取
func (param *flowDailyV2Param) dailyFlowV2() (*flowDailyListV2Resp, error) {
	selectColumns := []string{
		"FROM_UNIXTIME(confirm_time, '%Y-%m-%d %H:%i:%s') AS time",
		"gift_id AS drama_id",
		"title AS drama_name",
		"type",
		fmt.Sprintf("%s AS coin", incomeField),
		"from_id AS user_id",
	}
	var data []flowItemV2
	query := service.NewADB.Table(transaction.TransactionLog{}.TableName()).
		Select(selectColumns).
		Where("gift_id IN (?)", param.dramaIDs).
		Where("status = ?", transaction.StatusSuccess).
		Where("type IN (?)", param.tradeTypes).
		Where("income > 0").
		Where("confirm_time >= ?", param.startDate.Unix())
	// 快照第一页时间 < 筛选的截止时间，则使用快照第一页时间作为截止时间，防止某个剧集突然新增大量交易，会出现第一页的数据往后挤占到第二页的情况发生
	firstRequestTime := time.Unix(param.firstRequestTimeUnix, 0)
	filterEndTime := param.endDate
	if param.firstRequestTimeUnix > 0 && firstRequestTime.Before(param.endDate) {
		filterEndTime = firstRequestTime
	}
	query = query.Where("confirm_time < ?", filterEndTime.Unix()).
		Order("confirm_time DESC").Limit(param.pageSize + 1)
	if param.p > 1 {
		query = query.Offset(param.pageSize * (param.p - 1))
	}
	err := query.Scan(&data).Error
	if err != nil {
		return nil, err
	}
	end := len(data)
	hasMore := end > int(param.pageSize)
	if hasMore {
		end = int(param.pageSize)
	}

	resp := &flowDailyListV2Resp{
		Data: data[:end],
		Pagination: pagination{
			HasMore:  hasMore,
			Marker:   strconv.FormatInt(param.firstRequestTimeUnix, 10),
			P:        param.p,
			PageSize: param.pageSize,
		},
	}
	resp.tradeTypeName()
	return resp, nil
}

// tradeTypeName 交易类型名称
func (resp *flowDailyListV2Resp) tradeTypeName() {
	for i, item := range resp.Data {
		switch item.Type {
		case transaction.TypeSound:
			resp.Data[i].TypeName = "单集购买"
		case transaction.TypeDrama:
			resp.Data[i].TypeName = "整剧购买"
		case transaction.TypeDramaReward:
			resp.Data[i].TypeName = "剧集打赏"
		default:
			logger.Errorf("错误的剧集订单交易类型：%d，交易剧集 ID：%d", item.Type, item.DramaID)
			// PASS
		}
	}
}

// flowMonthlyV2Param 新版月度流水相关参数
type flowMonthlyV2Param struct {
	userID   int64
	endMonth time.Time
	dramaIDs []int64
	p        int64
	pageSize int64
}

// flowMonthlyListV2Resp 新版月度流水返回值
type flowMonthlyListV2Resp struct {
	Data       []flowItem `json:"data"`
	Pagination pagination `json:"pagination"`
}

// ActionMonthlyFlowV2 月度流水
/**
 * @api {get} /x/drama/review/flow/monthly-v2 月度流水
 *
 * @apiVersion 0.1.0
 * @apiName monthly-v2
 * @apiGroup x/drama/review/flow
 *
 * @apiPermission user
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=6] 每页个数
 * @apiParam {String} [marker] 分页标记（首页不传）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [{
 *           "date": "2024 年 9 月",
 *           "buy_coin": 98760, // 购剧流水（单位：钻）
 *           "reward_coin": 1234, // 打赏流水（单位：钻）
 *           "coin": 99994 // 总流水（单位：钻）
 *         }, {
 *           "date": "2024 年 8 月",
 *           "buy_coin": 88760,
 *           "reward_coin": 1034,
 *           "coin": 89990
 *         }],
 *         "pagination": {
 *           "has_more": false, // 是否有更多数据
 *           "marker": "1730390400", // 分页标记，当前页最后一条记录的月份时间戳
 *           "p": 1,
 *           "pagesize": 50
 *         }
 *       }
 *     }
 */
func ActionMonthlyFlowV2(ctx *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newFlowMonthlyV2Param(ctx)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.monthlyFlowV2()
	resp.Data = fileMonthlyFlowWithZeroCoinRecord(resp.Data, param.endMonth, int(param.pageSize))
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return resp, "", nil
}

// newFlowMonthlyV2Param 初始化新版月度流水接口参数
func newFlowMonthlyV2Param(ctx *handler.Context) (*flowMonthlyV2Param, error) {
	p, pageSize, err := ctx.GetParamPage(&handler.PageOption{DefaultPageSize: monthlyFlowV2PageSize})
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	marker, _ := ctx.GetParamString("marker")
	endMonth := util.BeginningOfMonth(util.TimeNow())
	if marker != "" {
		endMonthUnix, err := strconv.ParseInt(marker, 10, 64)
		if err != nil {
			return nil, actionerrors.ErrParams
		}
		endMonth = time.Unix(endMonthUnix, 0)
	}
	userID := ctx.UserID()
	dramaIDs, err := dramareviewers.GetDramaIDs(ctx.UserContext(), userID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(dramaIDs) == 0 {
		return nil, actionerrors.ErrDramaNotFound
	}
	param := &flowMonthlyV2Param{
		endMonth: endMonth,
		p:        p,
		pageSize: pageSize,
		dramaIDs: dramaIDs,
		userID:   userID,
	}
	return param, nil
}

// monthlyFlowV2 新版月度流水获取
func (param *flowMonthlyV2Param) monthlyFlowV2() (*flowMonthlyListV2Resp, error) {
	selectColumns := []string{
		"FROM_UNIXTIME(confirm_time, '%Y 年 %m 月') AS date",
		fmt.Sprintf("SUM(IF(type IN (?), %s, 0)) AS buy_coin", incomeField),
		fmt.Sprintf("SUM(IF(type = ?, %s, 0)) AS reward_coin", incomeField),
		fmt.Sprintf("SUM(%s) AS coin", incomeField),
	}
	var data []flowItem
	err := service.NewADB.Table(transaction.TransactionLog{}.TableName()).
		Select(selectColumns, []int{transaction.TypeDrama, transaction.TypeSound}, transaction.TypeDramaReward).
		Where("gift_id IN (?)", param.dramaIDs).
		Where("status = ?", transaction.StatusSuccess).
		Where("type IN (?)", []int{transaction.TypeDrama, transaction.TypeSound, transaction.TypeDramaReward}).
		Where("income > 0").
		Where("confirm_time < ?", param.endMonth.Unix()).
		Group("date").Order("date DESC").Limit(param.pageSize + 1).Scan(&data).Error
	if err != nil {
		return nil, err
	}
	end := len(data)
	hasMore := end > int(param.pageSize)
	var marker string
	if hasMore {
		end = int(param.pageSize)
		marker = strconv.FormatInt(param.endMonth.AddDate(0, -int(param.pageSize), 0).Unix(), 10)
	}
	resp := &flowMonthlyListV2Resp{
		Data: data[:end],
		Pagination: pagination{
			HasMore:  hasMore,
			Marker:   marker,
			P:        param.p,
			PageSize: param.pageSize,
		},
	}
	return resp, nil
}

// ActionDailyFlowExport 导出筛选的流水明细
/**
 * @api {get} /x/drama/review/flow/daily-export 导出筛选的日流水明细
 *
 * @apiVersion 0.1.0
 * @apiName daily-export
 * @apiGroup x/drama/review/flow
 *
 * @apiPermission user
 *
 * @apiParam {String} [start_date] 起始日期（默认为当天，例：2022-05-21）
 * @apiParam {String} [end_date] 截止日期（默认为当天，例：2022-05-21）
 * @apiParam {Number} [drama_id=0] 剧集 ID（默认为该用户所有能查看的剧集）
 * @apiParam {number=0,1,2,3} [type=0] 交易类型 0：全部；1：整剧购买；2：单集购买；3：剧集打赏
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response-Header:
 *     HTTP/1.1 200 OK
 *     Content-Type: application/octet-stream
 *     Content-Disposition: attachment; filename="%E5%90%88%E4%BD%9C%E6%96%B9%E6%B5%81%E6%B0%B4%E6%98%8E%E7%BB%8620241109.csv"; filename*=UTF-8''%E5%90%88%E4%BD%9C%E6%96%B9%E6%B5%81%E6%B0%B4%E6%98%8E%E7%BB%8620241109.csv
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 403 Forbidden
 *     {
 *       "code": 100010007,
 *       "message": "导出数据量过大暂不支持，请分批操作或联系商务！",
 *       "data": null
 *     }
 */
func ActionDailyFlowExport(ctx *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newFlowDailyV2Param(ctx)
	if err != nil {
		return nil, "", err
	}
	param.p = 1
	param.pageSize = exportMaxCount
	param.firstRequestTimeUnix = util.TimeNow().Unix()
	flowResp, err := param.dailyFlowV2()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if len(flowResp.Data) == 0 {
		return nil, "", actionerrors.ErrNotFound(handler.CodeUnknownError, "没有可导出的数据")
	}
	if flowResp.Pagination.HasMore {
		return nil, "", actionerrors.ErrForbidden(handler.CodeUnknownError, "导出数据量过大暂不支持，请分批操作或联系商务！")
	}
	filename := fmt.Sprintf("合作方流水明细%s.csv", util.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	err = writeDailyFlowDataToCsv(ctx, flowResp.Data, filename)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return nil, "", handler.ErrRawResponse
}

// writeDailyFlowDataToCsv 导出日流水明细数据到 csv
func writeDailyFlowDataToCsv(ctx *handler.Context, data []flowItemV2, filename string) error {
	records := make([][]string, 0, exportMaxCount+1)
	titleLine := []string{
		"时间", "剧集 ID", "剧集名称", "类型", "流水（钻）", "用户M号",
	}
	records = append(records, titleLine)
	for _, v := range data {
		records = append(records, []string{
			v.Time,
			strconv.FormatInt(v.DramaID, 10),
			v.DramaName,
			v.TypeName,
			strconv.FormatInt(v.Coin, 10),
			strconv.FormatInt(v.UserID, 10),
		})
	}
	var b bytes.Buffer
	b.Write([]byte{0xEF, 0xBB, 0xBF}) // UTF-8 BOM
	w := csv.NewWriter(&b)
	for _, record := range records {
		if err := w.Write(record); err != nil {
			return err
		}
	}
	w.Flush()
	if err := w.Error(); err != nil {
		return err
	}

	ctx.SendDownload(&b, int64(b.Len()), filename)
	return nil
}
