package dramareview

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramarevenuereviewerinfo"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestAuthTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(authParam{}, "identify_code")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(authParam{}, "identify_code")
}

func TestActionAuth(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试请求失败的情况
	params := &authParam{}
	api := "/x/drama/review/auth"
	c := handler.NewTestContext(http.MethodPost, api, true, params)
	_, _, err := ActionAuth(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试认证成功的情况
	testUserID := int64(12)
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().
		Delete("", "user_id = ?", testUserID).Error)
	testMobile := "13333333333"
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Create(
		&dramarevenuereviewerinfo.DramaRevenueReviewerInfo{
			UserID: testUserID,
			Mobile: testMobile,
			Region: 86,
		}).Error)
	regionMobile, err := vcode.RegionMobile(testMobile, vcode.DefaultRegion)
	require.NoError(err)
	identifyKey := serviceredis.KeyMobileVCode1.Format(regionMobile.RegionMobile)
	authKey := keys.KeyDramaRevenueUserAuth1.Format(c.UserID())
	defer service.Redis.Del(identifyKey, authKey)
	pipe := service.Redis.TxPipeline()
	pipe.HMSet(identifyKey,
		vcode.FieldVCodeObjective, vcode.ObjectiveTypeDramaRevenue,
		vcode.FieldVCode, "123456")
	pipe.Expire(identifyKey, time.Minute)
	_, err = pipe.Exec()
	require.NoError(err)
	params = &authParam{
		IdentifyCode: "123456",
	}
	c = handler.NewTestContext(http.MethodPost, api, true, params)
	data, message, err := ActionAuth(c)
	require.NoError(err)
	require.Nil(data)
	assert.Equal("认证成功", message)
	// 验证生成了认证缓存
	value, err := service.Redis.Get(authKey).Result()
	require.NoError(err)
	assert.Equal(value, util.MD5(c.Token()))
}

func TestGetUserMobileNumber(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试用户未绑定剧集收益后台认证手机号
	testUserID := int64(2423345)
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().
		Delete("", "user_id = ?", testUserID).Error)
	_, err := getUserMobileNumber(testUserID)
	assert.Equal(actionerrors.ErrDramaReviewUserNoMobile, err)

	// 测试正常获取手机号信息
	testMobile := "13333333333"
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Create(
		&dramarevenuereviewerinfo.DramaRevenueReviewerInfo{
			UserID: testUserID,
			Mobile: testMobile,
			Region: 86,
		}).Error)
	mobileNumber, err := getUserMobileNumber(testUserID)
	require.NoError(err)
	require.NotNil(mobileNumber)
	assert.Equal("86"+testMobile, mobileNumber.RegionMobile)
}

func TestAuthParam_newAuthParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	api := "/x/drama/review/auth"
	c := handler.NewTestContext(http.MethodPost, api, true, nil)
	_, err := newAuthParam(c)
	assert.EqualError(err, "参数错误")

	// 测试用户未绑定剧集收益后台认证手机号
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().
		Delete("", "user_id = ?", c.UserID()).Error)
	body := &authParam{
		IdentifyCode: "123456",
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	_, err = newAuthParam(c)
	assert.Equal(actionerrors.ErrDramaReviewUserNoMobile, err)

	// 测试验证码错误
	testMobile := "13333333333"
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Create(
		&dramarevenuereviewerinfo.DramaRevenueReviewerInfo{
			UserID: 12,
			Mobile: testMobile,
			Region: 86,
		}).Error)
	regionMobile, err := vcode.RegionMobile(testMobile, vcode.DefaultRegion)
	require.NoError(err)
	identifyKey := serviceredis.KeyMobileVCode1.Format(regionMobile.RegionMobile)
	err = service.Redis.Del(identifyKey).Err()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	_, err = newAuthParam(c)
	assert.EqualError(err, "验证码错误")

	// 测试无错误的情况
	defer service.Redis.Del(identifyKey)
	pipe := service.Redis.TxPipeline()
	pipe.HMSet(identifyKey,
		vcode.FieldVCodeObjective, vcode.ObjectiveTypeDramaRevenue,
		vcode.FieldVCode, "123456")
	pipe.Expire(identifyKey, time.Minute)
	_, err = pipe.Exec()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	params, err := newAuthParam(c)
	require.NoError(err)
	require.NotNil(params)
	assert.Equal(c.UserID(), params.userID)
	assert.Equal(c.Token(), params.userToken)
}

func TestAuthParam_setAuth(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params := &authParam{
		userID:    233,
		userToken: "test_token",
	}
	authKey := keys.KeyDramaRevenueUserAuth1.Format(params.userID)
	require.NoError(service.Redis.Del(authKey).Err())
	err := params.setAuth()
	require.NoError(err)
	// 验证生成了缓存
	value, err := service.Redis.Get(authKey).Result()
	require.NoError(err)
	assert.Equal(value, util.MD5(params.userToken))
}
