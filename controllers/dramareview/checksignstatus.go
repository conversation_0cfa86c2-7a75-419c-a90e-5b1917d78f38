package dramareview

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/bilibili/settle"
)

type checkSignStatusResp struct {
	PartnerName string `json:"partner_name"`
	Status      int    `json:"status"`    // 1：全部签约完成，不弹出提示弹窗；2：未全部签约完成，弹出提示弹窗
	CanClose    int    `json:"can_close"` // 弹窗是否能关闭 0：否；1：是
	URL         string `json:"url"`
}

// 弹窗状态 1: 可支持关闭
const popCanClose = 1

// ActionCheckSignStatus 检查登录合作方是否签约
/**
 * @api {post} /x/drama/review/check-sign-status 检查登录合作方是否签约
 *
 * @apiVersion 0.1.0
 * @apiName check-sign-status
 * @apiGroup x/drama/review
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "partner_name": "合作方名称", // 合作方名称，有待签约时返回
 *         "status": 1, // 1：全部签约完成，不弹出提示弹窗；2：未全部签约完成，弹出提示弹窗
 *         "can_close": 0, // 弹窗是否能关闭 0：否；1：是
 *         "url": "" // 签约地址（未完成签约时返回），为空表示已全部签约完成
 *       }
 *     }
 */
func ActionCheckSignStatus(c *handler.Context) (handler.ActionResponse, string, error) {
	params := new(settle.APIPartnerPopParams)
	params.MID = c.UserID()
	data, err := service.BilibiliSettle.PartnerPop(params)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	canClose := popCanClose
	now := util.TimeNow().Unix()
	// 指定时间开始，该弹窗可根据配置内容来确定是否关闭
	if config.Conf.Params.DramaRevenueCheckSignPopTime <= now {
		canClose = config.Conf.Params.DramaRevenueCheckSignPopCanClose
	}
	if len(data.URLList) == 0 {
		return checkSignStatusResp{
			CanClose: canClose,
			Status:   settle.StateHide,
		}, "", nil
	}
	return checkSignStatusResp{
		PartnerName: data.PartnerName,
		Status:      settle.StatePop,
		CanClose:    canClose,
		URL:         data.URLList[0],
	}, "", nil
}
