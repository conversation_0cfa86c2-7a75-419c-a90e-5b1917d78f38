package dramareview

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/bilibili/settle"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, popCanClose)
}

func TestActionCheckSignStatus(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testMID := int64(1)
	cancel := service.BilibiliSettle.SetMock(settle.APIPartnerPop, settle.APIPartnerPopResult{
		Mid:         testMID,
		PartnerName: "测试",
		State:       settle.StateHide,
		URLList: []string{
			"https://test.com/x",
		},
	})
	defer cancel()

	api := "/x/drama/review/check-sign-status"
	c := handler.NewTestContext(http.MethodPost, api, true, nil)
	c.User().ID = testMID
	res, _, err := ActionCheckSignStatus(c)
	require.NoError(err)
	data, ok := res.(checkSignStatusResp)
	require.True(ok)
	assert.EqualValues("测试", data.PartnerName)
	assert.EqualValues(settle.StatePop, data.Status)
	assert.NotEqualValues("", data.URL)
}
