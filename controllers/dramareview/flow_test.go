package dramareview

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/dramaapi"
)

func TestActionFlowOverview(t *testing.T) {
	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return []dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 1000",
				DramaID: 1000,
			},
			{
				Name:    "剧集 1001",
				DramaID: 1001,
			},
			{
				Name:    "剧集 1002",
				DramaID: 1002,
			},
			{
				Name:    "剧集 1003",
				DramaID: 1003,
			},
		}, nil
	})
	defer cleanup()

	require := require.New(t)
	assert := assert.New(t)
	err := service.NewADB.Exec("INSERT INTO " + transaction.TransactionLog{}.TableName() +
		"(gift_id, title, ios_coin, status, type, confirm_time)" +
		"VALUES " +
		"(1000, '杀破狼', 100, 1, 2, UNIX_TIMESTAMP('2024-09-25 13:00:05'))" +
		",(1001, '魔道祖师', 700, 1, 3, UNIX_TIMESTAMP('2024-09-05 16:30:00'))" +
		",(1002, '撒野', 200, 1, 7, UNIX_TIMESTAMP('2024-09-01 10:20:00'))" +
		",(1003, '伪装学渣', 500, 1, 3, UNIX_TIMESTAMP('2024-08-01 08:10:00'));").Error
	require.NoError(err)
	defer func() {
		require.NoError(service.NewADB.Table(transaction.TransactionLog{}.TableName()).Delete("", "gift_id IN (1000, 1001, 1002, 1003)").Error)
	}()

	util.SetTimeNow(func() time.Time {
		return time.Date(2024, 9, 25, 14, 10, 5, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/overview", true, nil)
	ctx.User().ID = 9999
	resp, msg, err := ActionFlowOverview(ctx)
	require.NoError(err)
	assert.Equal("", msg)
	require.NotNil(resp)
	result, ok := resp.(flowOverviewResp)
	require.True(ok)

	assert.Equal(int64(100), result.TodayCoin)
	assert.Equal(int64(1000), result.CurrentMonthCoin)
}

func TestParseMonthlyFlowMarker(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Date(2024, 9, 25, 19, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	startTime, endTime, err := parseMonthlyFlowMarker("")
	require.NoError(err)
	assert.Equal(time.Date(2024, 6, 1, 0, 0, 0, 0, time.Local).Unix(), startTime.Unix())
	assert.Equal(time.Date(2024, 9, 1, 0, 0, 0, 0, time.Local).Unix(), endTime.Unix())

	startTime, endTime, err = parseMonthlyFlowMarker(fmt.Sprintf("%d", time.Date(2024, 8, 1, 0, 0, 0, 0, time.Local).Unix()))
	require.NoError(err)
	assert.Equal(time.Date(2024, 5, 1, 0, 0, 0, 0, time.Local).Unix(), startTime.Unix())
	assert.Equal(time.Date(2024, 8, 1, 0, 0, 0, 0, time.Local).Unix(), endTime.Unix())
}

func TestFileMonthlyFlowWithZeroCoinRecord(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	result := fileMonthlyFlowWithZeroCoinRecord([]flowItem{
		{
			Date:       "2024 年 12 月",
			BuyCoin:    30,
			RewardCoin: 70,
			Coin:       100,
		},
		{
			Date:       "2024 年 10 月",
			BuyCoin:    50,
			RewardCoin: 150,
			Coin:       200,
		},
	}, time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local), monthlyFlowPageSize)
	require.Len(result, 3)
	assert.Equal("2024 年 12 月", result[0].Date)
	assert.Equal("2024 年 11 月", result[1].Date)
	assert.Equal("2024 年 10 月", result[2].Date)
	assert.Equal(int64(30), result[0].BuyCoin)
	assert.Equal(int64(0), result[1].BuyCoin)
	assert.Equal(int64(50), result[2].BuyCoin)
	assert.Equal(int64(70), result[0].RewardCoin)
	assert.Equal(int64(0), result[1].RewardCoin)
	assert.Equal(int64(150), result[2].RewardCoin)
	assert.Equal(int64(100), result[0].Coin)
	assert.Equal(int64(0), result[1].Coin)
	assert.Equal(int64(200), result[2].Coin)

	result = fileMonthlyFlowWithZeroCoinRecord([]flowItem{}, time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local), monthlyFlowPageSize)
	require.Len(result, 3)
	assert.Equal("2024 年 12 月", result[0].Date)
	assert.Equal("2024 年 11 月", result[1].Date)
	assert.Equal("2024 年 10 月", result[2].Date)
	assert.Equal(int64(0), result[0].BuyCoin)
	assert.Equal(int64(0), result[1].BuyCoin)
	assert.Equal(int64(0), result[2].BuyCoin)
	assert.Equal(int64(0), result[0].RewardCoin)
	assert.Equal(int64(0), result[1].RewardCoin)
	assert.Equal(int64(0), result[2].RewardCoin)
	assert.Equal(int64(0), result[0].Coin)
	assert.Equal(int64(0), result[1].Coin)
	assert.Equal(int64(0), result[2].Coin)
}

func TestPeriodicalFlow(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	err := service.NewADB.Exec("INSERT INTO " + transaction.TransactionLog{}.TableName() +
		"(gift_id, title, income, status, type, confirm_time)" +
		"VALUES " +
		"(2000, '落烛吟', 10, 1, 2, UNIX_TIMESTAMP('2024-09-25 13:00:05'))" +
		",(2001, '梅花香自苦寒中', 70, 1, 3, UNIX_TIMESTAMP('2024-09-05 21:30:00'))" +
		",(2001, '梅花香自苦寒中', 70, 1, 7, UNIX_TIMESTAMP('2024-09-05 18:30:00'))" +
		",(2002, '海的女儿', 20, 1, 7, UNIX_TIMESTAMP('2024-09-05 10:20:00'))" +
		",(2002, '海的女儿', 40, 1, 3, UNIX_TIMESTAMP('2024-09-04 09:20:00'))" +
		",(2003, '秋千', 50, 1, 3, UNIX_TIMESTAMP('2024-09-01 08:10:00'))" +
		",(2004, '默默有期', 80, 1, 3, UNIX_TIMESTAMP('2024-08-01 08:10:00'))").Error
	require.NoError(err)
	defer func() {
		require.NoError(service.NewADB.Table(transaction.TransactionLog{}.TableName()).Delete("", "gift_id IN (2000, 2001, 2002, 2003, 2004)").Error)
	}()

	dramaIDs := []int64{2000, 2001, 2002, 2004}
	startTime := time.Date(2024, 9, 1, 0, 0, 0, 0, time.Local)
	endTime := time.Date(2024, 10, 1, 0, 0, 0, 0, time.Local)

	result, hasMore, nexItem, err := periodicalFlow([]string{
		"FROM_UNIXTIME(confirm_time, '%Y-%m-%d') AS date",
		"gift_id AS drama_id",
		"title AS drama_name",
	}, dramaIDs, &startTime, &endTime, nil, "date, drama_id", "", "date DESC, coin DESC", 2)
	require.NoError(err)
	require.Len(result, 2)
	assert.Equal("2024-09-25", result[0].Date)
	assert.Equal(int64(2000), result[0].DramaID)
	assert.Equal("落烛吟", result[0].DramaName)
	assert.Equal(int64(100), result[0].Coin)
	assert.Equal(int64(0), result[0].RewardCoin)
	assert.Equal(int64(100), result[0].BuyCoin)
	assert.Equal("2024-09-05", result[1].Date)
	assert.Equal(int64(2001), result[1].DramaID)
	assert.Equal("梅花香自苦寒中", result[1].DramaName)
	assert.Equal(int64(1400), result[1].Coin)
	assert.Equal(int64(700), result[1].RewardCoin)
	assert.Equal(int64(700), result[1].BuyCoin)
	assert.True(hasMore)
	require.NotNil(nexItem)
	assert.Equal("2024-09-05", nexItem.Date)
	assert.Equal(int64(2002), nexItem.DramaID)
	assert.Equal("海的女儿", nexItem.DramaName)
	assert.Equal(int64(200), nexItem.RewardCoin)
	assert.Equal(int64(0), nexItem.BuyCoin)
	assert.Equal(int64(200), nexItem.Coin)
}

func TestActionDailyFlow(t *testing.T) {
	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return []dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 3000",
				DramaID: 3000,
			},
			{
				Name:    "剧集 3001",
				DramaID: 3001,
			},
			{
				Name:    "剧集 3002",
				DramaID: 3002,
			},
			{
				Name:    "剧集 3003",
				DramaID: 3003,
			},
			{
				Name:    "剧集 3004",
				DramaID: 3004,
			},
		}, nil
	})
	defer cleanup()

	require := require.New(t)
	assert := assert.New(t)

	// 测试筛选剧集用户不能查看
	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily?drama_id=1", true, nil)
	ctx.User().ID = 9999
	resp, _, err := ActionDailyFlow(ctx)
	assert.Error(err, "该剧集不存在")
	assert.Nil(resp)

	err = service.NewADB.Exec("INSERT INTO " + transaction.TransactionLog{}.TableName() +
		"(gift_id, title, income, status, type, confirm_time)" +
		"VALUES " +
		"(3000, '落烛吟', 10, 1, 2, UNIX_TIMESTAMP('2024-09-25 13:00:05'))" +
		",(3001, '梅花香自苦寒中', 70, 1, 3, UNIX_TIMESTAMP('2024-09-05 21:30:00'))" +
		",(3001, '梅花香自苦寒中', 70, 1, 7, UNIX_TIMESTAMP('2024-09-05 18:30:00'))" +
		",(3002, '海的女儿', 20, 1, 7, UNIX_TIMESTAMP('2024-09-05 10:20:00'))" +
		",(3002, '海的女儿', 40, 1, 3, UNIX_TIMESTAMP('2024-09-04 09:20:00'))" +
		",(3003, '秋千', 50, 1, 3, UNIX_TIMESTAMP('2024-09-01 08:10:00'))" +
		",(3004, '默默有期', 80, 1, 3, UNIX_TIMESTAMP('2024-08-01 08:10:00'))").Error
	require.NoError(err)
	defer func() {
		require.NoError(service.NewADB.Table(transaction.TransactionLog{}.TableName()).Delete("", "gift_id IN (3000, 3001, 3002, 3003, 3004)").Error)
	}()

	// 测试筛选某天数据
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily?start_date=2024-09-05&end_date=2024-09-05", true, nil)
	ctx.User().ID = 9999
	resp, msg, err := ActionDailyFlow(ctx)
	require.NoError(err)
	assert.Equal("", msg)
	result, ok := resp.(flowListResp)
	require.True(ok)
	assert.False(result.Pagination.HasMore)
	assert.Equal("", result.Pagination.Marker)
	require.Len(result.Data, 2)
	assert.Equal("2024-09-05", result.Data[0].Date)
	assert.Equal(int64(1400), result.Data[0].Coin)
	assert.Equal(int64(700), result.Data[0].BuyCoin)
	assert.Equal(int64(700), result.Data[0].RewardCoin)
	assert.Equal(int64(3001), result.Data[0].DramaID)
	assert.Equal("梅花香自苦寒中", result.Data[0].DramaName)

	assert.Equal(int64(200), result.Data[1].Coin)
	assert.Equal(int64(0), result.Data[1].BuyCoin)
	assert.Equal(int64(200), result.Data[1].RewardCoin)
	assert.Equal(int64(3002), result.Data[1].DramaID)
	assert.Equal("海的女儿", result.Data[1].DramaName)

	require.NotNil(result.Total)
	assert.EqualValues(700, result.Total.BuyCoin)
	assert.EqualValues(900, result.Total.RewardCoin)
	assert.EqualValues(1600, result.Total.AllCoin)

	// 测试筛选剧集
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily?drama_id=3001&start_date=2024-09-05&end_date=2024-09-05", true, nil)
	ctx.User().ID = 9999
	resp, msg, err = ActionDailyFlow(ctx)
	require.NoError(err)
	assert.Equal("", msg)
	result, ok = resp.(flowListResp)
	require.True(ok)
	assert.False(result.Pagination.HasMore)
	assert.Equal("", result.Pagination.Marker)
	require.Len(result.Data, 1)
	assert.EqualValues("2024-09-05", result.Data[0].Date)
	assert.EqualValues(1400, result.Data[0].Coin)
	assert.EqualValues(700, result.Data[0].BuyCoin)
	assert.EqualValues(700, result.Data[0].RewardCoin)
	assert.EqualValues(3001, result.Data[0].DramaID)
	assert.Equal("梅花香自苦寒中", result.Data[0].DramaName)

	require.NotNil(result.Total)
	assert.EqualValues(700, result.Total.BuyCoin)
	assert.EqualValues(700, result.Total.RewardCoin)
	assert.EqualValues(1400, result.Total.AllCoin)

	// 测试筛选时段
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily?start_date=2024-08-01&end_date=2024-09-01", true, nil)
	ctx.User().ID = 9999
	resp, msg, err = ActionDailyFlow(ctx)
	require.NoError(err)
	assert.Equal("", msg)
	result, ok = resp.(flowListResp)
	require.True(ok)
	assert.False(result.Pagination.HasMore)
	assert.Equal("", result.Pagination.Marker)
	require.Len(result.Data, 2)
	assert.EqualValues("2024-09-01", result.Data[0].Date)
	assert.EqualValues(500, result.Data[0].Coin)
	assert.EqualValues(500, result.Data[0].BuyCoin)
	assert.EqualValues(0, result.Data[0].RewardCoin)
	assert.EqualValues(3003, result.Data[0].DramaID)
	assert.EqualValues("秋千", result.Data[0].DramaName)

	assert.EqualValues("2024-08-01", result.Data[1].Date)
	assert.EqualValues(800, result.Data[1].Coin)
	assert.EqualValues(800, result.Data[1].BuyCoin)
	assert.EqualValues(0, result.Data[1].RewardCoin)
	assert.EqualValues(3004, result.Data[1].DramaID)
	assert.EqualValues("默默有期", result.Data[1].DramaName)

	require.NotNil(result.Total)
	assert.EqualValues(1300, result.Total.BuyCoin)
	assert.EqualValues(0, result.Total.RewardCoin)
	assert.EqualValues(1300, result.Total.AllCoin)

	// 测试兼容 date 参数
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily?date=2024-09-25", true, nil)
	ctx.User().ID = 9999
	resp, msg, err = ActionDailyFlow(ctx)
	require.NoError(err)
	assert.Equal("", msg)
	result, ok = resp.(flowListResp)
	require.True(ok)
	assert.False(result.Pagination.HasMore)
	assert.Equal("", result.Pagination.Marker)
	require.Len(result.Data, 1)
	assert.EqualValues("2024-09-25", result.Data[0].Date)
	assert.EqualValues(100, result.Data[0].Coin)
	assert.EqualValues(100, result.Data[0].BuyCoin)
	assert.EqualValues(0, result.Data[0].RewardCoin)
	assert.EqualValues(3000, result.Data[0].DramaID)
	assert.EqualValues("落烛吟", result.Data[0].DramaName)

	require.NotNil(result.Total)
	assert.EqualValues(100, result.Total.BuyCoin)
	assert.EqualValues(0, result.Total.RewardCoin)
	assert.EqualValues(100, result.Total.AllCoin)
}

func TestActionMonthlyFlow(t *testing.T) {
	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return &[]dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 4000",
				DramaID: 4000,
			},
			{
				Name:    "剧集 4001",
				DramaID: 4001,
			},
			{
				Name:    "剧集 4002",
				DramaID: 4002,
			},
			{
				Name:    "剧集 4003",
				DramaID: 4003,
			},
			{
				Name:    "剧集 4004",
				DramaID: 4004,
			},
		}, nil
	})
	defer cleanup()

	require := require.New(t)
	assert := assert.New(t)
	err := service.NewADB.Exec("INSERT INTO " + transaction.TransactionLog{}.TableName() +
		"(gift_id, title, income, status, type, confirm_time)" +
		"VALUES " +
		"(4000, '落烛吟', 10, 1, 2, UNIX_TIMESTAMP('2024-09-25 13:00:05'))" +
		",(4001, '梅花香自苦寒中', 70, 1, 3, UNIX_TIMESTAMP('2024-09-05 21:30:00'))" +
		",(4001, '梅花香自苦寒中', 70, 1, 7, UNIX_TIMESTAMP('2024-08-05 18:30:00'))" +
		",(4002, '海的女儿', 20, 1, 3, UNIX_TIMESTAMP('2024-08-05 10:20:00'))" +
		",(4002, '海的女儿', 40, 1, 3, UNIX_TIMESTAMP('2024-07-04 09:20:00'))" +
		",(4003, '秋千', 50, 1, 3, UNIX_TIMESTAMP('2024-06-01 08:10:00'))" +
		",(4004, '默默有期', 80, 1, 3, UNIX_TIMESTAMP('2024-05-01 08:10:00'))").Error
	require.NoError(err)
	defer func() {
		require.NoError(service.NewADB.Table(transaction.TransactionLog{}.TableName()).Delete("", "gift_id IN (4000, 4001, 4002, 4003, 4004)").Error)
	}()

	util.SetTimeNow(func() time.Time {
		return time.Date(2024, 9, 26, 20, 15, 5, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/monthly", true, nil)
	ctx.User().ID = 9999
	resp, msg, err := ActionMonthlyFlow(ctx)
	require.NoError(err)
	assert.Equal("", msg)
	result, ok := resp.(flowListResp)
	require.True(ok)
	assert.True(result.Pagination.HasMore)
	assert.Equal(fmt.Sprintf("%d", time.Date(2024, 6, 1, 0, 0, 0, 0, time.Local).Unix()), result.Pagination.Marker)
	require.Len(result.Data, 3)
	assert.Equal("2024 年 08 月", result.Data[0].Date)
	assert.Equal(int64(200), result.Data[0].BuyCoin)
	assert.Equal(int64(700), result.Data[0].RewardCoin)
	assert.Equal(int64(900), result.Data[0].Coin)
	assert.Equal("2024 年 07 月", result.Data[1].Date)
	assert.Equal(int64(400), result.Data[1].BuyCoin)
	assert.Equal(int64(0), result.Data[1].RewardCoin)
	assert.Equal(int64(400), result.Data[1].Coin)
	assert.Equal("2024 年 06 月", result.Data[2].Date)
	assert.Equal(int64(500), result.Data[2].BuyCoin)
	assert.Equal(int64(0), result.Data[2].RewardCoin)
	assert.Equal(int64(500), result.Data[2].Coin)

	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/monthly?marker="+result.Pagination.Marker, true, nil)
	ctx.User().ID = 9999
	resp, msg, err = ActionMonthlyFlow(ctx)
	require.NoError(err)
	assert.Equal("", msg)
	result, ok = resp.(flowListResp)
	require.True(ok)
	assert.False(result.Pagination.HasMore)
	assert.Equal("", result.Pagination.Marker)
	require.Len(result.Data, 3)
	assert.Equal("2024 年 05 月", result.Data[0].Date)
	assert.Equal(int64(800), result.Data[0].BuyCoin)
	assert.Equal(int64(0), result.Data[0].RewardCoin)
	assert.Equal(int64(800), result.Data[0].Coin)
	assert.Equal("2024 年 04 月", result.Data[1].Date)
	assert.Equal(int64(0), result.Data[1].BuyCoin)
	assert.Equal(int64(0), result.Data[1].RewardCoin)
	assert.Equal(int64(0), result.Data[1].Coin)
	assert.Equal("2024 年 03 月", result.Data[2].Date)
	assert.Equal(int64(0), result.Data[2].BuyCoin)
	assert.Equal(int64(0), result.Data[2].RewardCoin)
	assert.Equal(int64(0), result.Data[2].Coin)
}

func TestFlowTotal(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	startTime := time.Date(2024, 9, 1, 0, 0, 0, 0, time.Local)
	endTime := time.Date(2024, 10, 1, 0, 0, 0, 0, time.Local)

	// 测试空数据
	res, err := flowTotal([]int64{999999}, &startTime, &endTime, []int{transaction.TypeSound, transaction.TypeDrama, transaction.TypeDramaReward})
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(0, res.RewardCoin)
	assert.EqualValues(0, res.RewardCoin)
	assert.EqualValues(0, res.RewardCoin)

	err = service.NewADB.Exec("INSERT INTO " + transaction.TransactionLog{}.TableName() +
		"(gift_id, title, income, status, type, confirm_time)" +
		"VALUES " +
		"(2000, '落烛吟', 10, 1, 2, UNIX_TIMESTAMP('2024-09-25 13:00:05'))" +
		",(2001, '梅花香自苦寒中', 70, 1, 3, UNIX_TIMESTAMP('2024-09-05 21:30:00'))" +
		",(2002, '梅花香自苦寒中', 70, 1, 7, UNIX_TIMESTAMP('2024-09-05 18:30:00'))").Error
	require.NoError(err)
	defer func() {
		require.NoError(service.NewADB.Table(transaction.TransactionLog{}.TableName()).Delete("", "gift_id IN (2000, 2001, 2002)").Error)
	}()

	// 测试筛选剧集 ID
	dramaIDs := []int64{2000, 2001}
	res, err = flowTotal(dramaIDs, &startTime, &endTime, []int{transaction.TypeSound, transaction.TypeDrama, transaction.TypeDramaReward})
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(800, res.BuyCoin)
	assert.EqualValues(2, res.BuyTradeNum)
	assert.EqualValues(0, res.RewardCoin)
	assert.EqualValues(0, res.RewardTradeNum)
	assert.EqualValues(800, res.AllCoin)
	assert.EqualValues(2, res.AllTradeNum)

	// 测试筛选时间
	dramaIDs = []int64{2000, 2001, 2002}
	endTime = time.Date(2024, 9, 6, 0, 0, 0, 0, time.Local)
	res, err = flowTotal(dramaIDs, &startTime, &endTime, []int{transaction.TypeSound, transaction.TypeDrama, transaction.TypeDramaReward})
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(700, res.BuyCoin)
	assert.EqualValues(1, res.BuyTradeNum)
	assert.EqualValues(700, res.RewardCoin)
	assert.EqualValues(1, res.RewardTradeNum)
	assert.EqualValues(1400, res.AllCoin)
	assert.EqualValues(2, res.AllTradeNum)

	// 测试筛选交易类型 - 整剧购买
	endTime = time.Date(2024, 9, 26, 0, 0, 0, 0, time.Local)
	res, err = flowTotal(dramaIDs, &startTime, &endTime, []int{transaction.TypeDrama})
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(700, res.BuyCoin)
	assert.EqualValues(1, res.BuyTradeNum)
	assert.EqualValues(0, res.RewardCoin)
	assert.EqualValues(0, res.RewardTradeNum)
	assert.EqualValues(700, res.AllCoin)
	assert.EqualValues(1, res.AllTradeNum)

	// 测试筛选交易类型 - 单集购买
	res, err = flowTotal(dramaIDs, &startTime, &endTime, []int{transaction.TypeSound})
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(100, res.BuyCoin)
	assert.EqualValues(1, res.BuyTradeNum)
	assert.EqualValues(0, res.RewardCoin)
	assert.EqualValues(0, res.RewardTradeNum)
	assert.EqualValues(100, res.AllCoin)
	assert.EqualValues(1, res.AllTradeNum)

	// 测试筛选交易类型 - 剧集打赏
	res, err = flowTotal(dramaIDs, &startTime, &endTime, []int{transaction.TypeDramaReward})
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(0, res.BuyCoin)
	assert.EqualValues(0, res.BuyTradeNum)
	assert.EqualValues(700, res.RewardCoin)
	assert.EqualValues(1, res.RewardTradeNum)
	assert.EqualValues(700, res.AllCoin)
	assert.EqualValues(1, res.AllTradeNum)
}

func TestActionDailyFlowV2(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return &[]dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 5000",
				DramaID: 5000,
			},
			{
				Name:    "剧集 5001",
				DramaID: 5001,
			},
		}, nil
	})
	defer cleanup()
	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	// 测试第一页
	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-v2?start_date=2024-09-05&end_date=2024-09-25&type=1&p=1&pagesize=2", true, nil)
	ctx.User().ID = int64(1)
	data, _, err := ActionDailyFlowV2(ctx)
	require.NoError(err)
	require.NotNil(data)
	resp, ok := data.(*flowDailyListV2Resp)
	require.True(ok)
	require.Len(resp.Data, 2)
	assert.EqualValues(5000, resp.Data[0].DramaID)
	assert.EqualValues(5001, resp.Data[1].DramaID)
	assert.True(resp.Pagination.HasMore)
	assert.EqualValues(1, resp.Pagination.P)
	assert.EqualValues(2, resp.Pagination.PageSize)
	markerExpected := strconv.FormatInt(now.Unix(), 10)
	assert.EqualValues(markerExpected, resp.Pagination.Marker)
	require.NotNil(resp.Total)
	assert.EqualValues(1500, resp.Total.BuyCoin)
	assert.EqualValues(3, resp.Total.BuyTradeNum)
	assert.EqualValues(0, resp.Total.RewardCoin)
	assert.EqualValues(0, resp.Total.RewardTradeNum)
	assert.EqualValues(1500, resp.Total.AllCoin)
	assert.EqualValues(3, resp.Total.AllTradeNum)
	// 第二页
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-v2?start_date=2024-09-05&end_date=2024-09-25&type=1&p=2&pagesize=2&marker="+markerExpected, true, nil)
	ctx.User().ID = int64(1)
	data, _, err = ActionDailyFlowV2(ctx)
	require.NoError(err)
	require.NotNil(data)
	resp, ok = data.(*flowDailyListV2Resp)
	require.True(ok)
	require.Len(resp.Data, 1)
	assert.EqualValues(5001, resp.Data[0].DramaID)
	assert.False(resp.Pagination.HasMore)
	assert.EqualValues(2, resp.Pagination.P)
	assert.EqualValues(2, resp.Pagination.PageSize)
	assert.EqualValues(markerExpected, resp.Pagination.Marker)
	require.NotNil(resp.Total)
	assert.EqualValues(1500, resp.Total.BuyCoin)
	assert.EqualValues(3, resp.Total.BuyTradeNum)
	assert.EqualValues(0, resp.Total.RewardCoin)
	assert.EqualValues(0, resp.Total.RewardTradeNum)
	assert.EqualValues(1500, resp.Total.AllCoin)
	assert.EqualValues(3, resp.Total.AllTradeNum)
}

func TestNewFlowDailyV2Param(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return &[]dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 5000",
				DramaID: 5000,
			},
			{
				Name:    "剧集 5001",
				DramaID: 5001,
			},
		}, nil
	})
	defer cleanup()

	// 测试分页参数错误
	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-v2?start_date=2024-09-25&end_date=2024-09-25&type=1&p=0", true, nil)
	ctx.User().ID = int64(1)
	param, err := newFlowDailyV2Param(ctx)
	require.Nil(param)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试筛选交易类型错误
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-v2?start_date=2024-09-25&end_date=2024-09-25&type=999&p=1", true, nil)
	param, err = newFlowDailyV2Param(ctx)
	require.Nil(param)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试筛选日期
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-v2?start_date=2024-09-25&end_date=2024-09-25&type=1&p=1", true, nil)
	param, err = newFlowDailyV2Param(ctx)
	require.NoError(err)
	assert.EqualValues(time.Date(2024, 9, 25, 0, 0, 0, 0, time.Local), param.startDate)
	assert.EqualValues(time.Date(2024, 9, 26, 0, 0, 0, 0, time.Local), param.endDate)

	// 测试筛选剧集 ID 错误
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-v2?start_date=2024-09-25&end_date=2024-09-25&drama_id=1&type=1&p=1", true, nil)
	param, err = newFlowDailyV2Param(ctx)
	require.Nil(param)
	assert.Equal(actionerrors.ErrDramaNotFound, err)

	// 测试初始化成功
	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-v2?start_date=2024-09-25&end_date=2024-09-25&type=1&p=1", true, nil)
	param, err = newFlowDailyV2Param(ctx)
	require.NoError(err)
	assert.EqualValues(1, param.p)
	assert.EqualValues(dailyFlowPageSize, param.pageSize)
	assert.EqualValues([]int64{5000, 5001}, param.dramaIDs)
	assert.EqualValues([]int{transaction.TypeDrama}, param.tradeTypes)
	// 断言快照访问第一页时间
	assert.EqualValues(now.Unix(), param.firstRequestTimeUnix)
}

func TestFlowDailyV2Param_dailyFlowV2(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testDramaIDs := []int64{6000, 6001, 6002, 6003, 6004}
	param := flowDailyV2Param{
		startDate:  time.Date(2024, 9, 26, 0, 0, 0, 0, time.Local),
		endDate:    time.Date(2024, 9, 27, 0, 0, 0, 0, time.Local),
		dramaIDs:   testDramaIDs,
		tradeTypes: []int{transaction.TypeDrama},
		p:          1,
		pageSize:   dailyFlowPageSize,
	}
	// 测试返回空数组
	resp, err := param.dailyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	assert.Empty(resp.Data)

	// 测试获取整剧购买流水
	param.startDate = time.Date(2024, 5, 1, 0, 0, 0, 0, time.Local)
	resp, err = param.dailyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Data, 5)
	for _, item := range resp.Data {
		assert.EqualValues("整剧购买", item.TypeName)
	}

	// 测试获取单集购买流水
	param.tradeTypes = []int{transaction.TypeSound}
	resp, err = param.dailyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Data, 1)
	for _, item := range resp.Data {
		assert.EqualValues("单集购买", item.TypeName)
	}

	// 测试获取打赏流水
	param.tradeTypes = []int{transaction.TypeDramaReward}
	resp, err = param.dailyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Data, 1)
	for _, item := range resp.Data {
		assert.EqualValues("剧集打赏", item.TypeName)
	}

	// 测试获取全部流水、排序
	param.tradeTypes = []int{transaction.TypeDrama, transaction.TypeSound, transaction.TypeDramaReward}
	param.p = 1
	param.pageSize = 3
	resp, err = param.dailyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Data, 3)
	// 断言排序
	assert.EqualValues(6000, resp.Data[0].DramaID)
	assert.EqualValues(100, resp.Data[0].Coin)
	assert.EqualValues(transaction.TypeSound, resp.Data[0].Type)
	assert.EqualValues(6001, resp.Data[1].DramaID)
	assert.EqualValues(700, resp.Data[1].Coin)
	assert.EqualValues(transaction.TypeDrama, resp.Data[1].Type)
	assert.EqualValues(6001, resp.Data[2].DramaID)
	assert.EqualValues(700, resp.Data[2].Coin)
	assert.EqualValues(transaction.TypeDramaReward, resp.Data[2].Type)
	assert.True(resp.Pagination.HasMore)
	// 第二页数据
	param.p = 2
	param.pageSize = 3
	resp, err = param.dailyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Data, 3)
	// 断言排序
	assert.EqualValues(6002, resp.Data[0].DramaID)
	assert.EqualValues(200, resp.Data[0].Coin)
	assert.EqualValues(transaction.TypeDrama, resp.Data[0].Type)
	assert.EqualValues(6002, resp.Data[1].DramaID)
	assert.EqualValues(400, resp.Data[1].Coin)
	assert.EqualValues(transaction.TypeDrama, resp.Data[1].Type)
	assert.EqualValues(6003, resp.Data[2].DramaID)
	assert.EqualValues(500, resp.Data[2].Coin)
	assert.EqualValues(transaction.TypeDrama, resp.Data[2].Type)
	assert.True(resp.Pagination.HasMore)
	// 第三页数据
	param.p = 3
	param.pageSize = 3
	resp, err = param.dailyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Data, 1)
	assert.EqualValues(6004, resp.Data[0].DramaID)
	assert.EqualValues(800, resp.Data[0].Coin)
	assert.EqualValues(transaction.TypeDrama, resp.Data[0].Type)
	assert.False(resp.Pagination.HasMore)
}

func TestFlowDailyListV2Resp_tradeTypeName(t *testing.T) {
	assert := assert.New(t)

	testData := &flowDailyListV2Resp{
		Data: []flowItemV2{
			{
				Type: transaction.TypeSound,
			},
			{
				Type: transaction.TypeDrama,
			},
			{
				Type: transaction.TypeDramaReward,
			},
			{
				Type: 0,
			},
		},
	}
	testData.tradeTypeName()
	assert.Equal("单集购买", testData.Data[0].TypeName)
	assert.Equal("整剧购买", testData.Data[1].TypeName)
	assert.Equal("剧集打赏", testData.Data[2].TypeName)
	assert.Equal("", testData.Data[3].TypeName)
}

func TestActionMonthlyFlowV2(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return &[]dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 5000",
				DramaID: 5000,
			},
			{
				Name:    "剧集 5001",
				DramaID: 5001,
			},
			{
				Name:    "剧集 5002",
				DramaID: 5002,
			},
		}, nil
	})
	defer cleanup()
	util.SetTimeNow(func() time.Time {
		return time.Date(2024, 11, 1, 0, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	// 测试第一页
	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/monthly-v2?p=1&pagesize=2", true, nil)
	ctx.User().ID = int64(12)
	data, _, err := ActionMonthlyFlowV2(ctx)
	require.NoError(err)
	require.NotNil(data)
	resp, ok := data.(*flowMonthlyListV2Resp)
	require.True(ok)
	require.Len(resp.Data, 2)
	assert.EqualValues("2024 年 10 月", resp.Data[0].Date)
	assert.EqualValues(0, resp.Data[0].BuyCoin)
	assert.EqualValues(0, resp.Data[0].RewardCoin)
	assert.EqualValues(0, resp.Data[0].Coin)
	assert.EqualValues("2024 年 09 月", resp.Data[1].Date)
	assert.EqualValues(1500, resp.Data[1].BuyCoin)
	assert.EqualValues(0, resp.Data[1].RewardCoin)
	assert.EqualValues(1500, resp.Data[1].Coin)
	assert.True(resp.Pagination.HasMore)
	// 断言 marker 参数是 2024-09-01 时间戳字符串
	markerExpected := "1725120000"
	assert.EqualValues(markerExpected, resp.Pagination.Marker)

	// 第二页
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/monthly-v2?p=2&pagesize=2&marker="+markerExpected, true, nil)
	ctx.User().ID = int64(12)
	data, _, err = ActionMonthlyFlowV2(ctx)
	require.NoError(err)
	require.NotNil(data)
	resp, ok = data.(*flowMonthlyListV2Resp)
	require.True(ok)
	require.Len(resp.Data, 2)
	assert.EqualValues("2024 年 08 月", resp.Data[0].Date)
	assert.EqualValues(0, resp.Data[0].BuyCoin)
	assert.EqualValues(100, resp.Data[0].RewardCoin)
	assert.EqualValues(100, resp.Data[0].Coin)
	assert.EqualValues("2024 年 07 月", resp.Data[1].Date)
	assert.EqualValues(500, resp.Data[1].BuyCoin)
	assert.EqualValues(0, resp.Data[1].RewardCoin)
	assert.EqualValues(500, resp.Data[1].Coin)
	assert.False(resp.Pagination.HasMore)
	assert.EqualValues("", resp.Pagination.Marker)
}

func TestNewFlowMonthlyV2Param(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return &[]dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 5000",
				DramaID: 5000,
			},
			{
				Name:    "剧集 5001",
				DramaID: 5001,
			},
		}, nil
	})
	defer cleanup()
	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	// 测试分页参数错误
	testUserID := int64(12)
	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/monthly-v2?p=0", true, nil)
	param, err := newFlowMonthlyV2Param(ctx)
	require.Nil(param)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试成功返回
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/monthly-v2?p=1", true, nil)
	param, err = newFlowMonthlyV2Param(ctx)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(testUserID, param.userID)
	assert.EqualValues([]int64{5000, 5001}, param.dramaIDs)
	assert.EqualValues(util.BeginningOfMonth(now), param.endMonth)
	assert.EqualValues(1, param.p)
	assert.EqualValues(monthlyFlowV2PageSize, param.pageSize)
}

func TestFlowMonthlyV2Param_monthlyFlowV2(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := flowMonthlyV2Param{
		userID:   int64(12),
		endMonth: time.Date(2024, 10, 1, 0, 0, 0, 0, time.Local),
		p:        1,
		pageSize: 2,
	}

	// 测试返回空数组
	param.dramaIDs = []int64{999998, 999999}
	resp, err := param.monthlyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.NotNil(resp.Data)
	assert.Empty(resp.Data)
	assert.False(resp.Pagination.HasMore)
	assert.EqualValues("", resp.Pagination.Marker)

	// 第一页
	param.dramaIDs = []int64{5000, 5001, 5002}
	resp, err = param.monthlyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.NotEmpty(resp.Data)
	assert.Len(resp.Data, 2)
	assert.EqualValues("2024 年 09 月", resp.Data[0].Date)
	assert.EqualValues(1500, resp.Data[0].BuyCoin)
	assert.EqualValues(0, resp.Data[0].RewardCoin)
	assert.EqualValues(1500, resp.Data[0].Coin)
	assert.EqualValues("2024 年 08 月", resp.Data[1].Date)
	assert.EqualValues(0, resp.Data[1].BuyCoin)
	assert.EqualValues(100, resp.Data[1].RewardCoin)
	assert.EqualValues(100, resp.Data[1].Coin)
	assert.True(resp.Pagination.HasMore)
	// 断言 marker 参数是 2024-08-01 时间戳字符串
	markerExpected := "1722441600"
	assert.EqualValues(markerExpected, resp.Pagination.Marker)

	// 第二页
	param.p = 2
	param.endMonth = time.Unix(1722441600, 0)
	resp, err = param.monthlyFlowV2()
	require.NoError(err)
	require.NotNil(resp)
	require.NotEmpty(resp.Data)
	assert.Len(resp.Data, 1)
	assert.EqualValues("2024 年 07 月", resp.Data[0].Date)
	assert.EqualValues(500, resp.Data[0].BuyCoin)
	assert.EqualValues(0, resp.Data[0].RewardCoin)
	assert.EqualValues(500, resp.Data[0].Coin)
	assert.False(resp.Pagination.HasMore)
	assert.EqualValues("", resp.Pagination.Marker)
}

func TestActionDailyFlowExport(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return &[]dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 5000",
				DramaID: 5000,
			},
			{
				Name:    "剧集 5001",
				DramaID: 5001,
			},
		}, nil
	})
	defer cleanup()
	util.SetTimeNow(func() time.Time { return time.Date(2024, 9, 26, 0, 0, 0, 0, time.Local) })
	defer util.SetTimeNow(nil)

	// 测试没有可导出的数据
	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-export?start_date=2024-09-26&end_date=2024-09-26&type=1", true, nil)
	ctx.User().ID = int64(1)
	_, _, err := ActionDailyFlowExport(ctx)
	assert.Error(err, "没有可导出的数据")

	// 测试可正常导出
	ctx = handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-export?start_date=2024-09-05&end_date=2024-09-25&type=1", true, nil)
	ctx.User().ID = int64(1)
	_, _, err = ActionDailyFlowExport(ctx)
	assert.Equal(handler.ErrRawResponse, err)
	assert.EqualError(err, handler.ErrRawResponse.Error())
	assert.Equal("application/octet-stream", ctx.C.Writer.Header().Get("Content-Type"))
	assert.Equal(`attachment; filename="%E5%90%88%E4%BD%9C%E6%96%B9%E6%B5%81%E6%B0%B4%E6%98%8E%E7%BB%8620240926.csv"; filename*=UTF-8''%E5%90%88%E4%BD%9C%E6%96%B9%E6%B5%81%E6%B0%B4%E6%98%8E%E7%BB%8620240926.csv`, ctx.C.Writer.Header().Get("Content-Disposition"))
	assert.Equal(http.StatusOK, ctx.C.Writer.Status())
	contentLengthStr := ctx.C.Writer.Header().Get("Content-Length")
	contentLength, err := strconv.Atoi(contentLengthStr)
	require.NoError(err)
	assert.True(contentLength > 0)
}

func TestWriteDailyFlowDataToCsv(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/x/drama/review/flow/daily-export?start_date=2024-09-05&end_date=2024-09-25&type=1", true, nil)
	ctx.User().ID = int64(1)
	data := []flowItemV2{
		{
			Time:      "2024-09-25 13:00:05",
			DramaID:   5000,
			DramaName: "测试日流水明细",
			Coin:      100,
			UserID:    1,
			TypeName:  "整剧购买",
		},
		{
			Time:      "2024-09-05 21:30:00",
			DramaID:   5001,
			DramaName: "测试日流水明细",
			Coin:      700,
			UserID:    1,
			TypeName:  "整剧购买",
		},
	}
	err := writeDailyFlowDataToCsv(ctx, data, "test.csv")
	require.NoError(err)
	assert.Equal("application/octet-stream", ctx.C.Writer.Header().Get("Content-Type"))
	assert.Equal(`attachment; filename="test.csv"; filename*=UTF-8''test.csv`, ctx.C.Writer.Header().Get("Content-Disposition"))
	assert.Equal(http.StatusOK, ctx.C.Writer.Status())
	contentLengthStr := ctx.C.Writer.Header().Get("Content-Length")
	contentLength, err := strconv.Atoi(contentLengthStr)
	require.NoError(err)
	assert.True(contentLength > 0)
}
