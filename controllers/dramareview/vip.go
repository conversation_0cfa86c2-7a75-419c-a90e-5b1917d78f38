package dramareview

import (
	"fmt"
	"slices"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramareviewers"
	vipdramarevenueflow "github.com/MiaoSiLa/missevan-main/models/vip/vip_drama_revenue_flow"
)

type vipQuarterlyFlowItem struct {
	Time          string `json:"time"`
	DramaID       int64  `json:"drama_id"`
	DramaName     string `json:"drama_name"`
	VipListenFlow string `json:"vip_listen_flow"`
}

type vipQuarterlyFlowSummary struct {
	TotalFlow     string `json:"total_flow"`
	VipListenFlow string `json:"vip_listen_flow"`
}

type vipQuarterlyFlowResp struct {
	Data       []vipQuarterlyFlowItem  `json:"data"`
	Pagination util.Pagination         `json:"pagination"`
	Summary    vipQuarterlyFlowSummary `json:"summary"`
}

type vipQuarterlyFlowParams struct {
	userID   int64
	dramaIDs []int64

	startDate time.Time
	endDate   time.Time
	page      int64
	pageSize  int64
}

// ActionQuarterlyVipFlow 会员季度流水
/**
 * @api {get} /x/drama/review/flow/quarterly-vip-flow 会员季度流水
 *
 * @apiVersion 0.1.0
 * @apiName quarterly-vip-flow
 * @apiGroup x/drama/review/flow
 *
 * @apiPermission user
 *
 * @apiParam {Number} [drama_id] 剧集 ID
 * @apiParam {String} [start_date] 起始日期
 * @apiParam {String} [end_date] 截止日期
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [{
 *           "time": "2025.10.01 至 2025.12.31",
 *           "drama_id": 9888,
 *           "drama_name": "杀破狼",
 *           "vip_listen_flow": "98760.05" // 会员听剧流水（单位：元）
 *         }],
 *         "pagination": {
 *           "count": 2,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         },
 *         "summary": {
 *           "total_flow": "99105.28",
 *           "vip_listen_flow": "98760.05"
 *         }
 *       }
 *     }
 */
func ActionQuarterlyVipFlow(ctx *handler.Context) (handler.ActionResponse, string, error) {
	var params vipQuarterlyFlowParams
	if err := params.loadVipQuarterlyParams(ctx); err != nil {
		return nil, "", err
	}

	// NOTICE: 若响应时长较长，尝试改用 ADB 看下效果
	query := vipdramarevenueflow.
		VipDramaRevenueFlow{}.DB().
		Where("drama_id IN (?)", params.dramaIDs).
		Where("user_id = ?", params.userID).
		Where("start_time >= ? AND end_time < ?", params.startDate.Unix(), params.endDate.AddDate(0, 0, 1).Unix()).
		Where("attr = ? AND delete_time = 0", vipdramarevenueflow.AttrNormalPartner)

	list, pagination, err := vipQuarterlyData(query, params.page, params.pageSize)
	if err != nil {
		return nil, "", err
	}
	summary, err := vipQuarterlySummary(query)
	if err != nil {
		return nil, "", err
	}

	return vipQuarterlyFlowResp{
		Data:       list,
		Pagination: pagination,
		Summary:    summary,
	}, "", nil
}

func (params *vipQuarterlyFlowParams) loadVipQuarterlyParams(ctx *handler.Context) (err error) {
	params.page, params.pageSize, err = ctx.GetParamPage(&handler.PageOption{DefaultPageSize: 20})
	if err != nil {
		return actionerrors.ErrParams
	}

	// 获取当前日期所属季度
	defaultStartDate, defaultEndDate := getQuarterRange(util.TimeNow())
	params.startDate, params.endDate, err = ctx.GetParamDateRange(defaultStartDate, defaultEndDate)
	if err != nil {
		return actionerrors.ErrParams
	}

	dramaID, err := ctx.GetDefaultParamInt64("drama_id", 0)
	if err != nil || dramaID < 0 {
		return actionerrors.ErrParams
	}
	params.userID = ctx.UserID()

	reviewDramaIDs, err := dramareviewers.GetDramaIDs(ctx.UserContext(), params.userID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(reviewDramaIDs) == 0 {
		return actionerrors.ErrNotFound(handler.CodeDramaNotFound, "没有可查看的剧集")
	}
	if dramaID > 0 {
		if !slices.Contains(reviewDramaIDs, dramaID) {
			return actionerrors.ErrDramaNotFound
		}
		params.dramaIDs = []int64{dramaID}
	} else {
		// 未筛选剧集 ID，则获取用户可查看的全部剧集
		params.dramaIDs = reviewDramaIDs
	}

	return nil
}

func vipQuarterlyData(query *gorm.DB, page, pageSize int64) (list []vipQuarterlyFlowItem, pagination util.Pagination, err error) {
	defer func() {
		if err != nil {
			err = actionerrors.ErrServerInternal(err, nil)
		}
	}()

	list = make([]vipQuarterlyFlowItem, 0, pageSize)
	var count int64
	if err = query.Count(&count).Error; err != nil {
		return
	}

	pagination = util.MakePagination(count, page, pageSize)
	if !pagination.Valid() {
		pagination = util.Pagination{}
		return
	}

	var data []vipdramarevenueflow.VipDramaRevenueFlow
	err = pagination.ApplyTo(query).Select([]string{
		"drama_id",
		"drama_name",
		"vip_listen_flow",
		"start_time",
		"end_time",
	}).Order("start_time DESC").Scan(&data).Error
	if err != nil {
		return
	}

	for i := range data {
		list = append(list, vipQuarterlyFlowItem{
			Time: time.Unix(data[i].StartTime, 0).Format(util.TimeFormatYMD) +
				" 至 " +
				time.Unix(data[i].EndTime-1, 0).Format(util.TimeFormatYMD),
			DramaID:       data[i].DramaID,
			DramaName:     data[i].DramaName,
			VipListenFlow: convertFenToYuan(data[i].VipListenFlow),
		})
	}
	return
}

func vipQuarterlySummary(query *gorm.DB) (vipQuarterlyFlowSummary, error) {
	var summary vipdramarevenueflow.VipDramaRevenueFlow
	err := query.Select([]string{
		"COALESCE(SUM(vip_listen_flow), 0) AS vip_listen_flow",
	}).Scan(&summary).Error
	if err != nil {
		return vipQuarterlyFlowSummary{}, actionerrors.ErrServerInternal(err, nil)
	}

	return vipQuarterlyFlowSummary{
		TotalFlow:     convertFenToYuan(summary.VipListenFlow),
		VipListenFlow: convertFenToYuan(summary.VipListenFlow),
	}, nil
}

func convertFenToYuan(flowInFen int64) string {
	return fmt.Sprintf("%.2f", float64(flowInFen)/100)
}

func getQuarterRange(t time.Time) (startDate, endDate string) {
	year := t.Year()
	month := t.Month()

	var startMonth, endMonth time.Month

	switch {
	case month >= 1 && month <= 3:
		startMonth, endMonth = 1, 3
	case month >= 4 && month <= 6:
		startMonth, endMonth = 4, 6
	case month >= 7 && month <= 9:
		startMonth, endMonth = 7, 9
	case month >= 10 && month <= 12:
		startMonth, endMonth = 10, 12
	}

	// 起始日期：季度的第一天零点
	startDate = time.Date(year, startMonth, 1, 0, 0, 0, 0, t.Location()).Format(util.TimeFormatYMD)

	// 结束日期：季度的最后一天 23:59:59
	// 先获取下季度的第一个月的第一天，再减一天
	endDate = time.Date(year, endMonth+1, 1, 0, 0, 0, 0, t.Location()).Add(-time.Second).Format(util.TimeFormatYMD)
	return
}
