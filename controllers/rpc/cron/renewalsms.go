package cron

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/vip/vipsubscriptionsignagreement"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

const (
	maxSendSmsCountOnce                = 1000        // 一次最多发送短信条数
	vipRenewalAdvanceDay time.Duration = 7           // 会员续费短信通知，要提前几天提醒
	vipRenewalSmsScene                 = "vip_renew" // 会员自动续费前通知场景
)

// ActionSendVipRenewalSms 发送续费短信通知给会员用户
// 运行周期：0 15 * * *
/**
 * @api {post} /rpc/missevan-main/cron/send-vip-renewal-sms 发送续费短信通知给会员用户
 * @apiDescription 会员续费短信通知 每天 15:00 发送短信提醒
 * @apiVersion 0.1.0
 * @apiGroup rpc/missevan-main/cron
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "ok",
 *     "data": null
 *   }
 */
func ActionSendVipRenewalSms(c *handler.Context) (handler.ActionResponse, string, error) {
	// 分批给会员续签短信提醒；分批发送的原因：运营预估会员人数达 17.7w 人/月，平均每天 8k，会员上线初期会有大量用户加入，预估最高一次短信发送在 2w 条。
	var startPage int
	var totalUserNum int64
	var totalNum int64
	var totalSuccessNum int64
	var totalErrorCount int
	renewalDay := util.TimeNow().Add(time.Hour * vipRenewalAdvanceDay * 24)
	startTime := util.BeginningOfDay(renewalDay).Unix()
	endTime := util.NextDayTime(renewalDay).Unix()
	for {
		startPage++
		userIDs, err := vipsubscriptionsignagreement.GetNextDeductVipUserByTime(startTime, endTime, startPage, maxSendSmsCountOnce)
		if err != nil {
			logger.Errorf("第 %d 批次获取用户错误： %v", startPage, err)
			// 累计超过 3 次错误，跳出循环
			totalErrorCount++
			if totalErrorCount > 3 {
				break
			}
			// PASS: 当前批次用户获取错误时跳过当前批次，继续下一批次查询
			continue
		}
		// 没有需要提醒续费的用户时，结束循环
		if len(userIDs) == 0 {
			break
		}
		totalUserNum += int64(len(userIDs))
		// 批量发送短信
		users, err := userapi.GetUsersInfo(c.UserContext(), userIDs)
		if err != nil {
			logger.Errorf("第 %d 批次查询用户信息错误： %v", startPage, err)
			// PASS: 本次查询用户们的信息错误，不影响下一批次短信发送
		}
		smses := prepareSMSBatch(users)
		if len(smses) > 0 {
			totalNum += int64(len(smses))
			err := service.PushService.SendSMSBatch(smses)
			if err != nil {
				logger.Errorf("第 %d 批次发送短信错误： %v", startPage, err)
				// PASS: 本次短信发送失败，不影响下一批次短信发送
			} else {
				totalSuccessNum += int64(len(smses))
			}
		}

		// 查询用户数小于最大发送短信条数为最后一批，则结束循环
		if len(userIDs) < maxSendSmsCountOnce {
			break
		}
	}
	logger.Infof("会员续费短信通知，预计要给 %d 个用户发送短信，发送成功短信总数：%d，发送失败短信总数： %d", totalUserNum, totalSuccessNum, totalNum-totalSuccessNum)
	return nil, "ok", nil
}

// prepareSMSBatch 批量准备短信发送
func prepareSMSBatch(users []userapi.UserInfo) []pushservice.SMS {
	smses := make([]pushservice.SMS, 0, len(users))
	for _, user := range users {
		if user.Mobile != "" {
			smses = append(smses, pushservice.SMS{
				To:         fmt.Sprintf("+%d%s", user.Region, user.Mobile),
				RegionCode: user.Region,
				Scene:      vipRenewalSmsScene,
				Payload:    map[string]interface{}{},
			})
		}
	}
	return smses
}
