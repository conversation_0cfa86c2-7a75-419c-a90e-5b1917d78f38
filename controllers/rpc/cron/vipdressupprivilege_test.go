package cron

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframelog"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
)

func TestActionTakeOffAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试过期会员用户卸载头像框
	c := handler.NewRPCTestContext("/rpc/missevan-main/cron/take-off-avatar-frame", nil)
	require.NoError(museravatarframelog.MUserAvatarFrameLog{}.DB().Delete("", "id > 0").Error)
	_, message, err := ActionTakeOffAvatarFrame(c)
	require.NoError(err)
	assert.Equal("总共卸载头像框用户数：1，成功卸载头像框用户数：1", message)

	// 验证新增 1 条头像框卸载日志
	var mUserAvatarFrameLog []museravatarframelog.MUserAvatarFrameLog
	require.NoError(museravatarframelog.MUserAvatarFrameLog{}.DB().Find(&mUserAvatarFrameLog).Error)
	require.Len(mUserAvatarFrameLog, 1)

	// 验证用户的头像框佩戴关系为已卸载状态
	var userAvatarFrameRelation museravatarframemap.MUserAvatarFrameMap
	require.NoError(museravatarframemap.MUserAvatarFrameMap{}.DB().
		Where("user_id = ? AND avatar_frame_id = ? AND status = ?", mUserAvatarFrameLog[0].UserID, mUserAvatarFrameLog[0].AvatarFrameID, museravatarframemap.StatusTakeoff).
		Find(&userAvatarFrameRelation).Order("modified_time desc").Limit(1).Error)
	require.NotEmpty(userAvatarFrameRelation)
	nowTime := util.TimeNow().Unix()
	assert.True(userAvatarFrameRelation.ModifiedTime <= nowTime)
	assert.True(userAvatarFrameRelation.ModifiedTime > nowTime-1)
}
