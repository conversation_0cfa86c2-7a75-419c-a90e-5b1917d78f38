package cron

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestActionSendVipRenewalSms(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试给会员续费用户发送短信
	cleanup := mrpc.SetMock(pushservice.Scheme+"://api/sms", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()
	c := handler.NewRPCTestContext("/rpc/missevan-main/cron/send-vip-renewal-sms", nil)
	_, msg, err := ActionSendVipRenewalSms(c)
	require.NoError(err)
	assert.Equal("ok", msg)
}

func TestPrepareSMSBatch(t *testing.T) {
	assert := assert.New(t)

	// 测试单个用户的发送准备
	var users = []userapi.UserInfo{
		{
			Mobile: "12345678901",
		},
	}
	smses := prepareSMSBatch(users)
	assert.Equal(1, len(smses))

	// 测试 2 个用户且一个无手机号的发送准备
	users = []userapi.UserInfo{
		{
			Mobile: "12345678901",
		},
		{
			Mobile: "",
		},
	}
	smses = prepareSMSBatch(users)
	assert.Equal(1, len(smses))
}
