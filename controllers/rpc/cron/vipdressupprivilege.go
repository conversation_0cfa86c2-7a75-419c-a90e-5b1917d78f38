package cron

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframelog"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

const (
	maxTakeOffVipAvatarFrameNum = 1000 // 最大一次卸载会员头像框数
)

// ActionTakeOffAvatarFrame 卸载用户头像框
// TODO: 后续头像框相关表迁移到 app_missevan 后可以使用 DMS 任务编排直接处理
// 运行周期：15 0 * * * 每天 00:15 点卸载会员到期的用户头像框，并插入日志
/**
 * @api {post} /rpc/missevan-main/cron/take-off-avatar-frame 卸载用户头像框
 * @apiDescription 每日 00:15 定时查询当天过期会员并卸载头像框和添加卸下日志
 * @apiVersion 0.1.0
 * @apiGroup rpc/missevan-main/cron
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "总共卸载头像框用户数：1，成功卸载头像框用户数：1",
 *     "data": null
 *   }
 */
func ActionTakeOffAvatarFrame(c *handler.Context) (handler.ActionResponse, string, error) {
	// 分批卸载会员过期用户头像框；分批卸载的原因：运营预估会员人数达 17.7w 人/月，平均每天 8k，会员上线初期会有大量用户加入，同一天预估最高 2w 人会员过期。
	// TODO: 会员二期存在购买外观套装有头像框的情况，需要将这部分通过购买获得的头像框未到期的情况，排除卸载范围
	var startPage int
	var totalNum int
	var totalSuccessNum int
	var totalErrorCount int
	vipExpirationTime := util.TimeNow().Add(time.Hour * -24)
	for {
		startPage++
		userIDs, err := muservip.GetExpiredVipUserIDsOnDay(vipExpirationTime, startPage, maxTakeOffVipAvatarFrameNum)
		if err != nil {
			logger.Errorf("第 %d 批次获取会员用户 ID 失败：%v", startPage, err)
			//累计超过 3 次，跳出循环
			totalErrorCount++
			if totalErrorCount > 3 {
				break
			}
			// PASS: 当前批次用户获取错误时跳过当前批次，继续下一批次的查询
			continue
		}

		// 没有需要卸载头像框的用户，结束循环
		if len(userIDs) == 0 {
			break
		}
		userAvatarFrameRelations, err := museravatarframemap.GetOnWearVipAvatarFrames(userIDs)
		if err != nil {
			logger.Errorf("第 %d 批次获取头像框佩戴信息失败：%v", startPage, err)
			// PASS: 当前批次获取用户头像框佩戴信息错误，不影响下一批次用户头像框的卸载
		}
		userAvatarFrameOnWearLogs := make([]museravatarframelog.MUserAvatarFrameLog, 0, len(userAvatarFrameRelations))
		takeOffAvatarFrameMapIDs := make([]int64, 0, len(userAvatarFrameRelations))
		timeNowTimestamp := util.TimeNow().Unix()
		for _, item := range userAvatarFrameRelations {
			userAvatarFrameOnWearLogs = append(userAvatarFrameOnWearLogs, museravatarframelog.MUserAvatarFrameLog{
				CreateTime:    timeNowTimestamp,
				ModifiedTime:  timeNowTimestamp,
				UserID:        item.UserID,
				AvatarFrameID: item.AvatarFrameID,
				StartTime:     0,
				EndTime:       util.NextDayTime(vipExpirationTime).Unix(),
			})
			takeOffAvatarFrameMapIDs = append(takeOffAvatarFrameMapIDs, item.ID)
		}
		if err := museravatarframelog.BatchCreate(userAvatarFrameOnWearLogs); err != nil {
			logger.Errorf("第 %d 批次卸载头像框写入日志失败：%v", startPage, err)
			// PASS: 当前批次卸载头像框日志写入失败，不影响下一批次用户头像框的卸载，continue 避免下面修改成功了之后，没办法通过重跑来修复这部分数据
			continue
		}
		totalNum += len(takeOffAvatarFrameMapIDs)
		err = museravatarframemap.BatchTakeoffByIDs(takeOffAvatarFrameMapIDs)
		if err != nil {
			logger.Errorf("第 %d 批次卸载头像框更新状态失败：%v", startPage, err)
			// PASS: 当前批次头像框佩戴状态更新失败，不影响下一批次用户头像框的卸载
		} else {
			totalSuccessNum += len(takeOffAvatarFrameMapIDs)
		}

		// 如果当前批次的用户少于最大值，说明是最后一批，结束循环
		if len(userIDs) < maxTakeOffVipAvatarFrameNum {
			break
		}
	}
	logger.Infof("外观特权头像挂件过期时自动卸下，总共卸载头像框用户数：%d，成功卸载头像框用户数：%d", totalNum, totalSuccessNum)
	return nil, fmt.Sprintf("总共卸载头像框用户数：%d，成功卸载头像框用户数：%d", totalNum, totalSuccessNum), nil
}
