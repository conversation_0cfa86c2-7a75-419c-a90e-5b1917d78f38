package drama

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

func TestActionGetUserDramaCvs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/drama/get-user-drama-cvs"

	// 测试没有参演声优信息
	param := handler.M{
		"user_id": 999999,
		"scene":   SceneSearch,
	}
	c := handler.NewRPCTestContext(api, param)
	data, _, err := ActionGetUserDramaCvs(c)
	require.NoError(err)
	resp, ok := data.(*dramaCvResp)
	require.True(ok)
	require.NotNil(resp.Cvs)
	assert.Empty(resp.Cvs)

	// 测试搜索业务有参演声优信息
	param = handler.M{
		"user_id": 1,
		"scene":   SceneSearch,
	}
	c = handler.NewRPCTestContext(api, param)
	data, _, err = ActionGetUserDramaCvs(c)
	require.NoError(err)
	assert.NotEmpty(data)
	resp, ok = data.(*dramaCvResp)
	require.True(ok)
	require.Len(resp.Cvs, 1)
	assert.EqualValues(2, resp.Cvs[0].ID)
	assert.EqualValues(2, resp.Cvs[0].UserID)

	// 测试底部导航栏直播 Tab 业务有参演声优信息
	cancel := mrpc.SetMock(userapi.URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{52349, 52350}, nil
	})
	defer cancel()
	param = handler.M{
		"user_id": 1,
		"scene":   SceneLiveRecommend,
	}
	c = handler.NewRPCTestContext(api, param)
	data, _, err = ActionGetUserDramaCvs(c)
	require.NoError(err)
	assert.NotEmpty(data)
	resp, ok = data.(*dramaCvResp)
	require.True(ok)
	require.Len(resp.Cvs, 2)
	assert.EqualValues(3, resp.Cvs[0].ID)
	assert.EqualValues(3, resp.Cvs[0].UserID)
	assert.EqualValues(4, resp.Cvs[1].ID)
	assert.EqualValues(4, resp.Cvs[1].UserID)
}

func TestNewDramaCvParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/drama/get-user-drama-cvs"

	// 测试 user_id 参数错误
	param := handler.M{
		"scene": SceneSearch,
	}
	c := handler.NewRPCTestContext(api, param)
	_, err := newDramaCvParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试 scene 参数错误
	param = handler.M{
		"user_id": 1,
		"scene":   "test",
	}
	c = handler.NewRPCTestContext(api, param)
	_, err = newDramaCvParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试初始化成功
	param = handler.M{
		"user_id": 1,
		"scene":   SceneSearch,
	}
	c = handler.NewRPCTestContext(api, param)
	data, err := newDramaCvParam(c)
	require.NoError(err)
	assert.EqualValues(1, data.UserID)
	assert.EqualValues(SceneSearch, data.Scene)
}

func TestDramaCvParam_getUserDramaCvs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不支持的 scene 参数
	param := dramaCvParam{
		UserID: 1,
		Scene:  "test",
	}
	assert.PanicsWithValue("illegal param scene: test", func() {
		_, _ = param.getUserDramaCvs()
	})

	// 测试搜索业务
	param.Scene = SceneSearch
	data, err := param.getUserDramaCvs()
	require.NoError(err)
	assert.NotEmpty(data.Cvs)

	// 测试底部导航直播 Tab 业务
	cancel := mrpc.SetMock(userapi.URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{52349, 52350}, nil
	})
	defer cancel()
	param.Scene = SceneLiveRecommend
	data, err = param.getUserDramaCvs()
	require.NoError(err)
	assert.NotEmpty(data.Cvs)
}

func TestDramaCvParam_listSceneSearchDramaCvs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := dramaCvParam{
		Scene: SceneSearch,
	}

	// 测试没有参演声优信息
	param.UserID = 99999
	data, err := param.listSceneSearchDramaCvs()
	require.NoError(err)
	require.NotNil(data)
	assert.Empty(data)

	// 测试成功返回
	param.UserID = 1
	data, err = param.listSceneSearchDramaCvs()
	require.NoError(err)
	require.Len(data, 1)
	assert.EqualValues(2, data[0].ID)
	assert.EqualValues(2, data[0].UserID)
}

func TestDramaCvParam_listSceneLiveRecommendDramaCv(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{52349, 52350}, nil
	})
	defer cancel()
	param := dramaCvParam{
		Scene: SceneLiveRecommend,
	}

	// 测试没有参演声优信息
	param.UserID = 2
	data, err := param.listSceneLiveRecommendDramaCv()
	require.NoError(err)
	require.NotNil(data)
	assert.Empty(data)

	// 测试成功返回
	param.UserID = 1
	data, err = param.listSceneLiveRecommendDramaCv()
	require.NoError(err)
	require.Len(data, 2)
	assert.EqualValues(3, data[0].ID)
	assert.EqualValues(3, data[0].UserID)
	assert.EqualValues(4, data[1].ID)
	assert.EqualValues(4, data[1].UserID)
}
