package drama

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaipr"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaprice"
)

type getDramaInfoParam struct {
	DramaID int64 `json:"drama_id"`
	UserID  int64 `json:"user_id,omitempty"`
}

type getDramaInfoResp struct {
	Drama *dramaInfo `json:"drama"`
}

type dramaInfo struct {
	ID          int64                       `json:"id"`
	Name        string                      `json:"name"`
	Checked     int                         `json:"checked"`
	IPRID       int64                       `json:"ipr_id"`
	IPRName     string                      `json:"ipr_name"`
	CoverURL    string                      `json:"cover_url"`
	CoverColor  int                         `json:"cover_color"`
	PayType     int                         `json:"pay_type"`
	Refined     util.BitMask                `json:"refined"`
	Price       int64                       `json:"price"`
	CatalogID   int64                       `json:"catalog_id"`
	VipDiscount *dramaprice.VipDiscountInfo `json:"vip_discount,omitempty"`
}

// ActionGetDramaInfo 获取剧集信息
/**
 * @api {post} /rpc/missevan-main/drama/get-drama-info 获取剧集信息
 *
 * @apiVersion 0.1.0
 * @apiName get-drama-info
 * @apiGroup /rpc/missevan-main/drama
 *
 * @apiParam {Number} drama_id 剧集 ID
 * @apiParam {Number} [user_id=0] 用户 ID，需要获取剧集会员折扣价格时需传入
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "drama": { // 没有该剧或剧集为非过审状态时返回 null
 *           "id": 1,
 *           "name": "椰子",
 *           "checked": 1, // 剧集过审状态，1：过审；4：合约期满下架（调用方应考虑判断用户是否购买了合约期满下架的剧集）
 *           "ipr_id": 100, // 剧集所属 IPR 的 ID
 *           "ipr_name": "天官赐福", // 剧集所属 IPR 的名称
 *           "cover_url": "https://static-test.maoercdn.com/icon01.png", // 剧集封面图
 *           "cover_color": 16777215, // 剧集背景图主颜色，十进制表示
 *           "pay_type": 2, // 付费类型（0 免费；1 单集付费；2 整剧付费）
 *           "refined": 0, // refined 定义（比特位第七位为 1 时表示日本地区禁购剧集）
 *           "price": 200, // 剧集价格（单位：钻）
 *           "catalog_id": 89, // 剧集分区
 *           "vip_discount": { // 剧集折扣信息，当付费剧对用户（包含游客）有会员折扣时返回
 *             "rate": 0.8, // 会员折扣值。折扣后若出现小数点，则用户仅需支付整数部分金额（小数点后金额直接舍去）
 *                          // 单集付费时，先汇总要购买的单集价格然后使用这个值来计算购买后的价格
 *             "price": 287 // 整剧付费剧集的会员折扣价格，仅在整剧付费时下发。单位：钻
 *           },
 *         }
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGetDramaInfo(c *handler.Context) (handler.ActionResponse, error) {
	param := new(getDramaInfoParam)
	err := c.BindJSON(param)
	if err != nil || param.DramaID <= 0 || param.UserID < 0 {
		return nil, actionerrors.ErrParams
	}

	drama, err := dramainfo.FindDramaInfoByID(param.DramaID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if drama == nil {
		return getDramaInfoResp{}, nil
	}

	respDramaInfo := &dramaInfo{
		ID:         drama.ID,
		Name:       *drama.Name,
		Checked:    int(drama.Checked),
		IPRID:      drama.IPID,
		CoverURL:   drama.CoverURL,
		CoverColor: drama.CoverColor,
		PayType:    int(drama.PayType),
		Refined:    drama.Refined,
		CatalogID:  drama.Catalog,
	}

	if drama.IPID > 0 {
		// 有 IPR ID 时获取 IPRName
		respDramaInfo.IPRName, err = dramaipr.FindIPRNameByID(drama.IPID)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if respDramaInfo.IPRName == "" {
			logger.WithField("drama_id", param.DramaID).Error("剧集所属 IPR 的名称为空字符串或 IPR 数据不存在，请联系运营处理")
			// PASS
		}
	}

	if drama.PayType != dramainfo.PayTypeFree {
		// 单集付费和整剧付费类型的剧集时获取剧集价格
		priceInfo, err := dramaprice.FindPriceByDramaID(param.DramaID)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if priceInfo != nil {
			respDramaInfo.Price = priceInfo.Price
			respDramaInfo.VipDiscount, err = priceInfo.GetDramaVipDiscountInfoFromUserID(drama.PayType, param.UserID)
			if err != nil {
				return nil, actionerrors.ErrServerInternal(err, nil)
			}
		}
		if respDramaInfo.Price == 0 {
			logger.WithField("drama_id", param.DramaID).Error("付费剧集未设置价格或设置的价格为 0，请联系运营处理")
			// PASS
		}
	}

	return getDramaInfoResp{Drama: respDramaInfo}, nil
}
