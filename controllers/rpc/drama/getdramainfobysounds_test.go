package drama

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
)

func TestActionGetDramaInfoBySounds(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/drama/get-drama-info-by-sounds"

	// 测试没有剧集信息
	param := handler.M{
		"sound_ids": []int64{999998, 999999},
	}
	c := handler.NewRPCTestContext(api, param)
	data, _, err := ActionGetDramaInfoBySounds(c)
	require.NoError(err)
	resp, ok := data.(*dramaInfoResp)
	require.True(ok)
	assert.Nil(resp.Dramas)

	// 测试有剧集信息
	param = handler.M{
		"sound_ids": []int64{1217700, 1217701, 1217702},
	}
	c = handler.NewRPCTestContext(api, param)
	data, _, err = ActionGetDramaInfoBySounds(c)
	require.NoError(err)
	resp, ok = data.(*dramaInfoResp)
	require.True(ok)
	require.NotNil(resp.Dramas)
	require.Len(resp.Dramas, 2)
	assertDramas(t, resp.Dramas[1217701], resp.Dramas[1217701].ID)
	assertDramas(t, resp.Dramas[1217702], resp.Dramas[1217702].ID)
}

func TestNewDramaInfoParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/drama/get-drama-info-by-sounds"

	// 测试参数错误
	param := handler.M{
		"sound_ids": []int64{},
	}
	c := handler.NewRPCTestContext(api, param)
	_, err := newDramaInfoParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试正常返回
	param = handler.M{
		"sound_ids": []int64{1217698, 1217700, 1217701},
	}
	c = handler.NewRPCTestContext(api, param)
	data, err := newDramaInfoParam(c)
	require.NoError(err)
	assert.EqualValues([]int64{1217698, 1217700, 1217701}, data.SoundIDs)
}

func TestDramaInfoParam_getDramaInfoBySounds(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有对应的剧集
	param := dramaInfoParam{
		SoundIDs: []int64{999998, 999999},
	}
	resp, err := param.getDramaInfoBySounds()
	require.NoError(err)
	assert.Nil(resp.Dramas)

	// 测试正常返回
	param = dramaInfoParam{
		SoundIDs: []int64{1217700, 1217701, 1217702},
	}
	resp, err = param.getDramaInfoBySounds()
	require.NoError(err)
	require.NotNil(resp.Dramas)
	require.Len(resp.Dramas, 2)
	assertDramas(t, resp.Dramas[1217701], resp.Dramas[1217701].ID)
	assertDramas(t, resp.Dramas[1217702], resp.Dramas[1217702].ID)
}

func assertDramas(t *testing.T, dramas dramaItem, expectedDramaID int64) {
	assert := assert.New(t)
	require := require.New(t)

	var expectedDrama dramainfo.RadioDramaDramainfo
	err := dramainfo.RadioDramaDramainfo{}.DB().Select("id, name, cover, pay_type, view_count, comment_count").
		Where("id = ?", expectedDramaID).Take(&expectedDrama).Error
	require.NoError(err)
	assert.EqualValues(expectedDrama.ID, dramas.ID)
	assert.EqualValues(*expectedDrama.Name, dramas.Name)
	assert.EqualValues(expectedDrama.CoverURL, dramas.CoverURL)
	assert.EqualValues(int(expectedDrama.PayType), dramas.PayType)
	assert.EqualValues(expectedDrama.ViewCount, dramas.ViewCount)
	assert.EqualValues(expectedDrama.CommentCount, dramas.CommentCount)
}
