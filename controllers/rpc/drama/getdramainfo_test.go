package drama

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestGetDramaInfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(getDramaInfoParam{}, "drama_id", "user_id")
	kc.Check(getDramaInfoResp{}, "drama")
	kc.Check(dramaInfo{}, "id", "name", "checked", "ipr_id", "ipr_name",
		"cover_url", "cover_color", "pay_type", "refined", "price", "catalog_id", "vip_discount")

	kc.CheckOmitEmpty(getDramaInfoParam{}, "user_id")
	kc.CheckOmitEmpty(dramaInfo{}, "vip_discount")
}

func TestActionGetDramaInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/drama/get-drama-info"

	// 测试参数错误
	var param getDramaInfoParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionGetDramaInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试剧集不存在
	require.NoError(dramainfo.RadioDramaDramainfo{}.DB().Delete("", "id = ?", 5).Error)
	param.DramaID = 5
	c = handler.NewRPCTestContext(api, param)
	resp, err := ActionGetDramaInfo(c)
	require.NoError(err)
	data, ok := resp.(getDramaInfoResp)
	require.True(ok)
	assert.Nil(data.Drama)

	// 测试正常获取剧集
	param.DramaID = 7
	c = handler.NewRPCTestContext(api, param)
	resp, err = ActionGetDramaInfo(c)
	require.NoError(err)
	data, ok = resp.(getDramaInfoResp)
	require.True(ok)
	require.NotEmpty(data)
	require.NotNil(data.Drama)
	assert.EqualValues(7, data.Drama.ID)
	assert.EqualValues("剧集名称（审核通过）", data.Drama.Name)
	assert.EqualValues(dramainfo.CheckedPass, data.Drama.Checked)
	assert.EqualValues(1, data.Drama.IPRID)
	assert.EqualValues("test_ipr1", data.Drama.IPRName)
	assert.NotEmpty(data.Drama.CoverURL)
	assert.EqualValues(12434877, data.Drama.CoverColor)
	assert.EqualValues(dramainfo.PayTypeDrama, data.Drama.PayType)
	assert.EqualValues(0, data.Drama.Refined)
	assert.EqualValues(200, data.Drama.Price)
	assert.EqualValues(89, data.Drama.CatalogID)
	require.NotNil(data.Drama.VipDiscount)
	assert.EqualValues(0.8, data.Drama.VipDiscount.Rate)
	assert.EqualValues(160, data.Drama.VipDiscount.Price)
}
