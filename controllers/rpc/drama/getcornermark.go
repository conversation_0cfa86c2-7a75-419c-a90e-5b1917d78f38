package drama

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacopyright"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermark"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermarkstyle"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
)

type getCornerMarkParam struct {
	DramaIDs []int64 `json:"drama_ids"`
	UserID   int64   `json:"user_id"`

	dramas        []dramainfo.RadioDramaDramainfo
	styles        map[int]*dramacornermarkstyle.DramaCornerMarkStyle
	cornerMarkMap map[int64]dramacornermark.RadioDramaCornerMark
	copyrightMap  map[int64]dramacopyright.RadioDramaDramacopyright
}

// ActionGetCornerMark 获取剧集角标信息
/**
 * @api {post} /rpc/missevan-main/drama/get-corner-mark 获取剧集角标信息
 * @apiVersion 0.1.0
 * @apiName get-corner-mark
 * @apiGroup rpc
 *
 * @apiParam {Number[]} drama_ids 剧集 IDs
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       code: 0,
 *       info: {
 *         "1": { // 剧集 ID
 *           "title": "已购",
 *           "text_color": "#FFFFFF", // 优先判断 text_start_color/text_end_color，没有的话使用这个字段
 *           "text_start_color": "#FFFFFF", // 文字渐变起始颜色。没有时不下发该字段
 *           "text_end_color": "#FFFFFF", // 文字渐变结束颜色。没有时不下发该字段
 *           "bg_start_color": "#E66465",
 *           "bg_end_color": "#E66465",
 *           "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png" // 无左侧图标时不返回该字段
 *         }
 *       }
 *     }
 */
func ActionGetCornerMark(c *handler.Context) (handler.ActionResponse, error) {
	p, err := newGetCornerMarkParam(c)
	if err != nil {
		return nil, err
	}
	return p.getCornerMark(), nil
}

func newGetCornerMarkParam(c *handler.Context) (*getCornerMarkParam, error) {
	var param getCornerMarkParam
	err := c.BindJSON(&param)
	if err != nil || len(param.DramaIDs) == 0 || param.UserID < 0 {
		return nil, actionerrors.ErrParams
	}
	param.dramas, err = dramainfo.FindDramaPayInfo(param.DramaIDs, param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	// 获取全部剧集角标样式
	param.styles = dramacornermarkstyle.ListAllMap()
	// 获取剧集角标管理信息
	cornerMarks, err := dramacornermark.FindCornerMarkByDramaIDs(param.DramaIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	// 获取剧集版权管理信息
	copyrights, err := dramacopyright.FindCopyrightByDramaIDs(param.DramaIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	param.cornerMarkMap = util.ToMap(cornerMarks, "DramaID").(map[int64]dramacornermark.RadioDramaCornerMark)
	param.copyrightMap = util.ToMap(copyrights, "DramaID").(map[int64]dramacopyright.RadioDramaDramacopyright)
	return &param, nil
}

// TODO: 使用 models/drama/dramacornermarkstyle/dramacornermarkstyle.go 中的 GetDramaCornerMark 函数获取剧集角标
func (param *getCornerMarkParam) getCornerMark() map[int64]dramacornermarkstyle.CornerMark {
	result := make(map[int64]dramacornermarkstyle.CornerMark, len(param.dramas))
	for _, drama := range param.dramas {
		styleType := param.getType(drama)
		if styleType == dramacornermarkstyle.TypeNone {
			continue
		}
		if cornerMarkStyle, ok := param.styles[styleType]; ok {
			result[drama.ID] = dramacornermarkstyle.CornerMark{
				Text:           cornerMarkStyle.Text,
				TextColor:      cornerMarkStyle.TextColor,
				TextStartColor: cornerMarkStyle.TextStartColor,
				TextEndColor:   cornerMarkStyle.TextEndColor,
				BgStartColor:   cornerMarkStyle.BgStartColor,
				BgEndColor:     cornerMarkStyle.BgEndColor,
				LeftIconURL:    cornerMarkStyle.LeftIconURL,
			}
		}
	}
	return result
}

// 获取角标样式类型（剧集角标优先级: 已购 > 热播 > 会员 > 原创 > 独播 > 首发 > 付费 > 精选）
func (param *getCornerMarkParam) getType(drama dramainfo.RadioDramaDramainfo) int {
	return dramacornermarkstyle.GetType(drama, param.cornerMarkMap, param.copyrightMap)
}
