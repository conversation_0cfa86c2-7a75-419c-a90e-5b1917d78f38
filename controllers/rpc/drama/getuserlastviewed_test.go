package drama

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGetUserLastviewed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/drama/get-user-lastviewed"

	// 参数错误
	var param getUserLastviewedParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionGetUserLastviewed(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 没有传入用户 ID、传入的剧集 IDs 下无过审音频
	param.DramaIDs = []int64{111, 222, 333}
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionGetUserLastviewed(c)
	require.NoError(err)
	data, ok := res.(getUserLastviewedResp)
	require.True(ok)
	expectRes := getUserLastviewedResp{
		Dramas: make(map[int64]*dramaEpisode),
	}
	assert.Equal(expectRes, data)

	// 没有传入用户 ID、传入的剧集 IDs 下有过审音频
	param.DramaIDs = []int64{101, 102, 103, 104, 105}
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionGetUserLastviewed(c)
	require.NoError(err)
	data, ok = res.(getUserLastviewedResp)
	require.True(ok)
	// 全部返回第一集的音频
	expectResp := getUserLastviewedResp{
		Dramas: map[int64]*dramaEpisode{
			101: {SoundID: 1003},
			102: {SoundID: 2002},
			// 剧集 103 已下架，所以不会返回
			104: {SoundID: 4001},
			// 无过审单集
			105: nil,
		},
	}
	assert.Equal(expectResp, data)

	// 传入用户 ID：该用户无收听进度
	param.UserID = 100009
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionGetUserLastviewed(c)
	require.NoError(err)
	data, ok = res.(getUserLastviewedResp)
	require.True(ok)
	// 全部返回第一集的音频
	assert.Equal(expectResp, data)

	// 传入用户 ID：该用户有收听进度
	param.UserID = 901
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionGetUserLastviewed(c)
	require.NoError(err)
	data, ok = res.(getUserLastviewedResp)
	require.True(ok)
	expectResp = getUserLastviewedResp{
		Dramas: map[int64]*dramaEpisode{
			// 有收听进度，正常返回
			101: {SoundID: 1002},
			// 所收听音频已下架，替换为该剧下第一集
			102: {SoundID: 2002},
			// 剧集 103 有收听记录但剧集已下架，所以不会返回
			// 无收听进度
			104: {SoundID: 4001},
			105: nil,
		},
	}
	assert.Equal(expectResp, data)
}
