package drama

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
)

type dramaInfoParam struct {
	SoundIDs []int64 `json:"sound_ids"`
}

type dramaInfoResp struct {
	Dramas map[int64]dramaItem `json:"dramas"`
}

type dramaItem struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	CoverURL     string `json:"cover_url"`
	PayType      int    `json:"pay_type"`
	ViewCount    int64  `json:"view_count"`
	CommentCount int64  `json:"comment_count"`
}

// ActionGetDramaInfoBySounds 根据音频 IDs 获取所属剧集信息
/**
 * @api {post} /rpc/missevan-main/drama/get-drama-info-by-sounds 根据音频 IDs 获取所属剧集信息
 *
 * @apiVersion 0.1.0
 * @apiName get-drama-info-by-sounds
 * @apiGroup /rpc/missevan-main/drama
 *
 * @apiParam {Number[]} sound_ids 音频 IDs
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "dramas": { // 如果所有音频都不属于剧集，则返回 null
 *           "123": { // 音频 ID，对应的剧集未过审或者不存在时，则不返回
 *             "id": 9888, // 剧集 ID
 *             "name": "name", // 剧集名称
 *             "cover_url": "https://static-test.maoercdn.com/icon01.png", // 剧集封面图
 *             "pay_type": 0, // 剧集付费类型 0 免费；1 单集付费；2 整剧付费
 *             "view_count": 123, // 播放次数
 *             "comment_count": 123 // 评论数
 *           },
 *           "124": {
 *             "id": 9889,
 *             "name": "name",
 *             "cover_url": "https://static-test.maoercdn.com/icon01.png",
 *             "pay_type": 0,
 *             "view_count": 123,
 *             "comment_count": 123
 *           }
 *         }
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGetDramaInfoBySounds(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newDramaInfoParam(c)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.getDramaInfoBySounds()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

// newDramaInfoParam 初始化参数
func newDramaInfoParam(c *handler.Context) (*dramaInfoParam, error) {
	param := new(dramaInfoParam)
	err := c.BindJSON(&param)
	if err != nil || len(param.SoundIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

// getDramaInfoBySounds 获取剧集信息
func (p *dramaInfoParam) getDramaInfoBySounds() (*dramaInfoResp, error) {
	soundIDDramaIDMap, err := dramainfo.GetDramaIDsBySoundIDs(p.SoundIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(soundIDDramaIDMap) == 0 {
		// 没有找到对应的剧集
		return &dramaInfoResp{Dramas: nil}, nil
	}
	dramaIDs := make([]int64, len(soundIDDramaIDMap))
	for _, dramaID := range soundIDDramaIDMap {
		dramaIDs = append(dramaIDs, dramaID)
	}
	dramas, err := dramainfo.ListDramaInfoByIDs(dramaIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(dramas) == 0 {
		// 没有找到对应的剧集
		return &dramaInfoResp{Dramas: nil}, nil
	}
	dramaMap := goutil.ToMap(dramas, "ID").(map[int64]*dramainfo.RadioDramaDramainfo)
	resp := new(dramaInfoResp)
	resp.Dramas = make(map[int64]dramaItem, len(dramaMap))
	for soundID, dramaID := range soundIDDramaIDMap {
		if drama, ok := dramaMap[dramaID]; ok {
			resp.Dramas[soundID] = dramaItem{
				ID:           drama.ID,
				Name:         *drama.Name,
				CoverURL:     drama.CoverURL,
				PayType:      int(drama.PayType),
				ViewCount:    drama.ViewCount,
				CommentCount: drama.CommentCount,
			}
		}
	}
	return resp, nil
}
