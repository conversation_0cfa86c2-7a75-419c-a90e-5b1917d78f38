package drama

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramataginfo"
	"github.com/MiaoSiLa/missevan-main/models/sound/msound"
)

func TestActionGetDramaEpisodes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/drama/get-drama-episodes"

	// 测试参数错误
	params := handler.M{"drama_id": 0}
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionGetDramaEpisodes(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户未登录，访问未过审剧集
	params = handler.M{"drama_id": 52351}
	c = handler.NewRPCTestContext(api, params)
	_, err = ActionGetDramaEpisodes(c)
	assert.Equal(actionerrors.ErrDramaNotFound, err)

	// 测试用户未登录，访问已过审剧集
	params = handler.M{"drama_id": 52352}
	c = handler.NewRPCTestContext(api, params)
	resp, err := ActionGetDramaEpisodes(c)
	require.NoError(err)
	r := resp.(*getDramaEpisodesResp)
	assert.Equal(int64(52352), r.Drama.ID)
	assert.Len(r.Episodes.Episode, 0)
	assert.Len(r.Episodes.Music, 1)
	assert.Len(r.Episodes.Ft, 1)
	assert.Nil(r.Like)

	// 测试用户已登录且是 UP 主自己
	params = handler.M{"drama_id": 52352, "user_id": 1}
	c = handler.NewRPCTestContext(api, params)
	resp, err = ActionGetDramaEpisodes(c)
	require.NoError(err)
	r = resp.(*getDramaEpisodesResp)
	assert.Equal(int64(52352), r.Drama.ID)
	assert.Len(r.Episodes.Episode, 1)
	assert.Len(r.Episodes.Music, 0)
	assert.Len(r.Episodes.Ft, 1)
	assert.False(*r.Like)
}

func TestCheckDramaReviewInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var p getDramaEpisodesParams
	p.DramaID = 52352
	require.NoError(dramainfo.RadioDramaDramainfo{}.DB().First(&p.dramaInfo, 2).Error)
	p.UserID = p.dramaInfo.UserID
	require.NoError(dramaepisode.RadioDramaEpisode{}.DB().
		Select("`id`, `name`, `drama_id`, `sound_id`, `order`, `type`, `pay_type`, `subtitle`").
		Where("drama_id = ?", p.DramaID).
		Order("`order` ASC").
		Scan(&p.dramaEpisodes).Error)

	err := p.checkDramaReviewInfo()
	assert.NoError(err)
	assert.EqualValues("测试修改剧集名称", *p.dramaInfo.Name)
	assert.EqualValues("测试修改单集", p.dramaEpisodes[0].Name)
	assert.EqualValues("测试新增单集", p.dramaEpisodes[1].Name)
	require.Len(p.dramaInfo.Tags, 2)
	assert.EqualValues(6, p.dramaInfo.Tags[0].ID)
	assert.EqualValues("轻松", p.dramaInfo.Tags[0].Name)
	assert.EqualValues(radiodramataginfo.TypeContent, p.dramaInfo.Tags[0].Type)
	assert.EqualValues(2, p.dramaInfo.Tags[1].ID)
	assert.EqualValues("都市", p.dramaInfo.Tags[1].Name)
	assert.EqualValues(radiodramataginfo.TypeClassification, p.dramaInfo.Tags[1].Type)
}

func TestCheckedStatus(t *testing.T) {
	assert := assert.New(t)

	checked1 := []int{msound.CheckedPass, msound.CheckedPolice}
	checked2 := []int{
		msound.CheckedUnpass,
		msound.CheckedPass,
		msound.CheckedPolice,
		msound.CheckedSoundTranscode,
	}

	checked := checkedStatus(false)
	assert.Equal(checked1, checked)

	checked = checkedStatus(true)
	assert.Equal(checked2, checked)
}
