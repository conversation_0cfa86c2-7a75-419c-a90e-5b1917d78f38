package drama

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/models/drama/checkeddramareview"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramasubscription"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramataginfo"
	"github.com/MiaoSiLa/missevan-main/models/sound/msound"
)

type getDramaEpisodesParams struct {
	DramaID int64 `json:"drama_id"` // 剧集 ID
	UserID  int64 `json:"user_id"`  // 用户 ID

	dramaInfo          dramainfo.RadioDramaDramainfo
	dramaEpisodes      []dramaepisode.RadioDramaEpisode
	checkedDramaReview checkeddramareview.CheckedDramaReview // 待审核的剧集信息
	sounds             []msound.MSound
	like               *bool
}

type getDramaEpisodesResp struct {
	Drama     dramainfo.RadioDramaDramainfo `json:"drama"`
	Episodes  episodesResp                  `json:"episodes"`
	Like      *bool                         `json:"like,omitempty"`
	ViewCount int64                         `json:"view_count"`
}

type episodesResp struct {
	Ft      []dramaepisode.RadioDramaEpisode `json:"ft"`
	Music   []dramaepisode.RadioDramaEpisode `json:"music"`
	Episode []dramaepisode.RadioDramaEpisode `json:"episode"`
}

// ActionGetDramaEpisodes 获取剧集和单集信息
/**
 * @api {post} /rpc/missevan-main/drama/get-drama-episodes 获取剧集和单集信息
 *
 * @apiVersion 0.1.0
 * @apiName get-drama-episodes
 * @apiGroup /rpc/missevan-main/drama
 *
 * @apiParam {Number} drama_id 剧集 ID
 * @apiParam {Number} [user_id] 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "drama": {
 *           "id": 30, // 剧集 ID
 *           "name": "望星辰之草原情殇", // 剧集名称
 *           "origin": 0, // 创作类型
 *           "alias": "望星辰之草原情殇别名", // 别名
 *           "age": 1, // 年代
 *           "author": "张三", // 原作者
 *           "integrity": 1, // 完结度 1：长篇未完结；2：长篇完结；3：全一期
 *           "cover": "https://static.missevan.com/dramacoversmini/dramacover/201906/06/test.jpg", // 剧集海报
 *           "type": 3, // 分类
 *           "type_name": "全年龄", // 分类名称
 *           "checked": 0, // '剧集审核状态，0：未审核；1：审核通过；2：审核未通过（临时下架）；4：合约到期下架',
 *           "catalog": 89, // 分类 ID
 *           "abstract": "单元测试剧集简介", // 剧集简介
 *           "serialize": true, // 是否已完结
 *           "pay_type": 0,  / 付费类型, 0: 免费; 1: 单音付费; 2: 剧集付费'
 *           "num": 0, // 剧集价格
 *           "purchased": false, // 用户是否已购买过该剧集
 *           "tags": [  // 选中的剧集标签，按数组顺序展示
 *             {
 *               "type": 2, // 标签类型 1：分类标签；2：内容标签
 *               "id": 2,
 *               "name": "甜"
 *             },
 *             {
 *               "type": 1,
 *               "id": 1,
 *               "name": "古风"
 *             }
 *           ],
 *           "organization_id": 0 // 社团 ID
 *         },
 *         "episodes": [{
 *           "ft": [],
 *           "music": [],
 *           "episode": [{
 *             "id": 3, // 单集 ID
 *             "name": "测试单集", // 单集名称
 *             "drama_id": 30, // 剧集 ID
 *             "sound_id": 11, // 音频 ID
 *             "order": 1, // 序号
 *             "type": 1, // 类型, 0: 正片; 1: 访谈; 2: 音乐; 3: 更多资源
 *             "pay_type": 1, // 付费类型, 0: 免费; 1: 单音付费; 2: 剧集付费
 *             "subtitle": "单集副标题", // 单集副标题
 *             "soundstr": "测试音频" // 音频名称
 *           },
 *           {
 *             "id": 4,
 *             "name": "测试单集 1",
 *             "drama_id": 30,
 *             "sound_id": 12,
 *             "order": 2,
 *             "type": 1,
 *             "pay_type": 1,
 *             "subtitle": "单集副标题",
 *             "soundstr": "测试音频"
 *           }]
 *         }],
 *         "like": false, // 用户是否订阅
 *         "view_count": 1 // 剧集观看总量
 *       }
 *     }
 */
func ActionGetDramaEpisodes(c *handler.Context) (handler.ActionResponse, error) {
	var p getDramaEpisodesParams

	// 读取并检查请求参数
	err := p.checkParams(c)
	if err != nil {
		return nil, err
	}

	// 查询剧集信息
	err = p.findDrama()
	if err != nil {
		return nil, err
	}

	// 查询单集信息
	err = p.findDramaEpisodes()
	if err != nil {
		return nil, err
	}

	// 查询音频信息
	err = p.findDramaSounds()
	if err != nil {
		return nil, err
	}

	return p.resp(), nil
}

func (p *getDramaEpisodesParams) checkParams(c *handler.Context) error {
	err := c.BindJSON(&p)
	if err != nil {
		return actionerrors.ErrParams
	}
	if p.DramaID <= 0 || p.UserID < 0 {
		return actionerrors.ErrParams
	}

	return nil
}

func (p *getDramaEpisodesParams) findDrama() error {
	err := dramainfo.RadioDramaDramainfo{}.DB().Take(&p.dramaInfo, "id = ?", p.DramaID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return actionerrors.ErrDramaNotFound
		}
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 对于游客和非 UP 主只能查看过审剧集
	if (p.UserID == 0 || p.UserID != p.dramaInfo.UserID) && p.dramaInfo.Checked != dramainfo.CheckedPass {
		return actionerrors.ErrDramaNotFound
	}

	// 获取剧集标签信息
	tags, err := radiodramataginfo.ListDramaTagsByDramaID(p.DramaID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	p.dramaInfo.Tags = tags

	if p.UserID > 0 {
		// 用户是否已购买过该剧集
		// REVIEW: UP 主自己用不用返回购买状态
		purchased, err := p.dramaInfo.IsUserPurchased(p.UserID)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
		p.dramaInfo.Purchased = &purchased

		// 用户是否已订阅剧集
		like, err := radiodramasubscription.IsUserSubscribed(p.UserID, p.DramaID)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
		p.like = &like
	}

	return nil
}

func (p *getDramaEpisodesParams) findDramaEpisodes() error {
	// 只返回 type 为正剧、访谈、音乐的单集，type 为更多的单集不返回
	types := []int{dramaepisode.TypeDrama, dramaepisode.TypeInterview, dramaepisode.TypeMusic}
	// order 是关键字，需要加反引号，否则 SQL 执行会报错
	err := dramaepisode.RadioDramaEpisode{}.DB().
		Select("`id`, `name`, `drama_id`, `sound_id`, `order`, `type`, `pay_type`, `subtitle`").
		Where("drama_id = ? AND type IN (?)", p.DramaID, types).
		Order("`order` ASC").
		Scan(&p.dramaEpisodes).Error
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 对于已过审剧集的 UP 主自己显示修改后处于再审中的剧集信息
	err = p.checkDramaReviewInfo()
	if err != nil {
		return err
	}

	return nil
}

func (p *getDramaEpisodesParams) findDramaSounds() error {
	if len(p.dramaEpisodes) > 0 {
		checked := checkedStatus(p.UserID == p.dramaInfo.UserID)

		soundIDs := make([]int64, 0, len(p.dramaEpisodes))
		for _, dep := range p.dramaEpisodes {
			soundIDs = append(soundIDs, dep.SoundID)
		}

		err := msound.MSound{}.DB().Select("id, soundstr, view_count").
			Where("id IN (?) AND checked IN (?)", soundIDs, checked).
			Scan(&p.sounds).Error
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
	}

	return nil
}

func (p *getDramaEpisodesParams) resp() *getDramaEpisodesResp {
	resp := getDramaEpisodesResp{
		// 只返回 type 为正剧、访谈、音乐的单集，type 为更多的单集不返回
		Episodes: episodesResp{
			Episode: make([]dramaepisode.RadioDramaEpisode, 0, len(p.dramaEpisodes)),
			Ft:      make([]dramaepisode.RadioDramaEpisode, 0, len(p.dramaEpisodes)),
			Music:   make([]dramaepisode.RadioDramaEpisode, 0, len(p.dramaEpisodes)),
		},
		Like:  p.like,
		Drama: p.dramaInfo,
	}
	if len(p.dramaEpisodes) > 0 {
		var soundStrMap = make(map[int64]string, len(p.sounds))
		for _, sound := range p.sounds {
			soundStrMap[sound.ID] = sound.Soundstr
			resp.ViewCount += sound.ViewCount
		}

		for _, dep := range p.dramaEpisodes {
			if _, ok := soundStrMap[dep.SoundID]; ok {
				switch dep.Type {
				case dramaepisode.TypeDrama:
					resp.Episodes.Episode = append(resp.Episodes.Episode, dep)
				case dramaepisode.TypeInterview:
					resp.Episodes.Ft = append(resp.Episodes.Ft, dep)
				case dramaepisode.TypeMusic:
					resp.Episodes.Music = append(resp.Episodes.Music, dep)
				}
			}
		}
	}

	return &resp
}

func (p *getDramaEpisodesParams) checkDramaReviewInfo() error {
	if p.UserID <= 0 || p.UserID != p.dramaInfo.UserID || p.dramaInfo.Checked != dramainfo.CheckedPass {
		return nil
	}

	err := checkeddramareview.CheckedDramaReview{}.DB().
		Select("name, origin, alias, age, author, integrity, cover, type, catalog, abstract, organization_id, episodes, tags").
		Where("drama_id = ? AND delete_time = 0", p.DramaID).
		Take(&p.checkedDramaReview).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil
		}
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 根据再审剧集信息重新赋值剧集信息
	err = p.checkedDramaReview.ReassignDramaInfo(&p.dramaInfo)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 根据再审单集信息重新赋值单集信息
	err = p.checkedDramaReview.ReassignDramaEpisodesInfo(&p.dramaEpisodes)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	return nil
}

func checkedStatus(self bool) []int {
	// 过审、报警
	if !self {
		return []int{msound.CheckedPass, msound.CheckedPolice}
	}

	// UP 主自己能看到加入剧集中的待审、过审、报警、未转码的单音
	return []int{
		msound.CheckedUnpass,
		msound.CheckedPass,
		msound.CheckedPolice,
		msound.CheckedSoundTranscode,
	}
}
