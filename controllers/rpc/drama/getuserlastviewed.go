package drama

import (
	"slices"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramasawhistory"
	"github.com/MiaoSiLa/missevan-main/models/sound/msound"
)

// getUserLastviewedParam 获取指定剧集下用户最新收听的单集参数
type getUserLastviewedParam struct {
	DramaIDs []int64 `json:"drama_ids"`
	UserID   int64   `json:"user_id"`
}

// getUserLastviewedResp 获取指定剧集下用户最新收听的单集返回值
type getUserLastviewedResp struct {
	Dramas map[int64]*dramaEpisode `json:"dramas"`
}

// dramaEpisode 剧集下单集信息
type dramaEpisode struct {
	SoundID int64 `json:"sound_id"`
}

// ActionGetUserLastviewed 获取指定剧集下用户最新收听的单集信息
/**
 * @api {post} /rpc/missevan-main/drama/get-user-lastviewed 获取指定剧集下用户最新收听的单集信息
 * @apiDescription 若传入用户 ID 为 0 或用户未收听过该剧或最新收听单集状态异常，返回该剧下第一个过审单集；剧集未过审时不返回该剧信息；剧集过审但剧集下没有过审单集或第一个单集未过审时返回空的剧集元素
 * @apiVersion 0.1.0
 * @apiName get-user-lastviewed
 * @apiGroup /rpc/missevan-main
 *
 * @apiParam {Number[]} drama_ids 剧集 IDs
 * @apiParam {Number} [user_id=0] user_id 用户 ID（当不传或传入 0 时，返回剧集下第一个过审单集信息）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "dramas": {
 *           "699321": { // 剧集 ID
 *             "sound_id": 2938471 // 音频 ID
 *           },
 *           "699322": {
 *             "sound_id": 2938472
 *           },
 *           "699323": null // 该剧已过审但剧集下没有过审单集或第一个单集未过审
 *         }
 *       }
 *     }
 * @apiSuccessExample Success-Response: 所有传入剧集未过审或不存在
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "dramas": []
 *       }
 *     }
 */
func ActionGetUserLastviewed(c *handler.Context) (handler.ActionResponse, error) {
	param := new(getUserLastviewedParam)
	err := c.BindJSON(param)
	if err != nil || len(param.DramaIDs) == 0 || param.UserID < 0 {
		return nil, actionerrors.ErrParams
	}
	// 获取已过审剧集 IDs
	passDramaIDs, err := dramainfo.ListCheckPassDramaIDs(param.DramaIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	passDramaIDsLen := len(passDramaIDs)
	dramas := make(map[int64]*dramaEpisode, passDramaIDsLen)
	// 初始化返回数据
	resp := getUserLastviewedResp{
		Dramas: dramas,
	}
	if passDramaIDsLen == 0 {
		return resp, nil
	}
	for _, dramaID := range passDramaIDs {
		resp.Dramas[dramaID] = nil
	}
	passViewedDramaIDs, viewedSoundIDs := make([]int64, 0, passDramaIDsLen), make([]int64, 0, passDramaIDsLen)
	if param.UserID != 0 {
		// 获取剧集收听进度
		episodeList, err := dramasawhistory.ListLastviewed(param.UserID, passDramaIDs)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if len(episodeList) > 0 {
			for _, episode := range episodeList {
				// 获取收听记录中音频 IDs
				viewedSoundIDs = append(viewedSoundIDs, episode.SoundID)
			}
			// 获取收听记录中过审音频 IDs
			passViewedSoundIDs, err := msound.ListCheckPassSoundIDs(viewedSoundIDs)
			if err != nil {
				return nil, actionerrors.ErrServerInternal(err, nil)
			}
			if len(passViewedSoundIDs) > 0 {
				for _, episode := range episodeList {
					if slices.Contains(passViewedSoundIDs, episode.SoundID) {
						// 返回收听过剧集的最新收听音频
						resp.Dramas[episode.DramaID] = &dramaEpisode{
							SoundID: episode.SoundID,
						}
						// 记录有收听进度的剧集 IDs
						passViewedDramaIDs = append(passViewedDramaIDs, episode.DramaID)
					}
				}
			}
		}
	}
	notViewedDramaIDs := sets.Diff(passDramaIDs, passViewedDramaIDs)
	if len(notViewedDramaIDs) > 0 {
		// 获取未收听剧集的第一集音频
		firstSoundList, err := dramaepisode.ListDramaFirstSoundIDs(notViewedDramaIDs)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		firstSoundListLen := len(firstSoundList)
		if firstSoundListLen == 0 {
			// （未收听剧集中）没有过审音频
			return resp, nil
		}
		firstSoundIDs := make([]int64, 0, firstSoundListLen)
		for _, item := range firstSoundList {
			firstSoundIDs = append(firstSoundIDs, item.SoundID)
		}
		passFirstSoundIDs, err := msound.ListCheckPassSoundIDs(firstSoundIDs)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if len(passFirstSoundIDs) > 0 {
			for _, item := range firstSoundList {
				if slices.Contains(passFirstSoundIDs, item.SoundID) {
					resp.Dramas[item.DramaID] = &dramaEpisode{
						SoundID: item.SoundID,
					}
				}
			}
		}
	}
	return resp, nil
}
