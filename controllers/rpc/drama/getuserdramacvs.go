package drama

import (
	"fmt"
	"slices"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisodecv"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramasubscription"
	"github.com/MiaoSiLa/missevan-main/models/mowangsksoundseiy"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

// 业务场景
const (
	SceneSearch        = "search"         // 搜索页业务
	SceneLiveRecommend = "live_recommend" // 底部导航直播 tab 页推荐业务
)

var sceneList = []string{SceneSearch, SceneLiveRecommend}

type dramaCvParam struct {
	UserID int64  `json:"user_id"`
	Scene  string `json:"scene"`

	uc mrpc.UserContext
}

type dramaCvResp struct {
	Cvs []cvInfo `json:"cvs"`
}

type cvInfo struct {
	ID     int64 `json:"id"`      // 声优 ID
	UserID int64 `json:"user_id"` // 声优对应的用户 ID
}

// ActionGetUserDramaCvs 获取用户已追或已购的剧集参演声优信息
/**
 * @api {post} /rpc/missevan-main/drama/get-user-drama-cvs 获取用户已追或已购的剧集参演声优信息
 *
 * @apiDescription 只返回有M号的参演声优
 * 搜索页业务（search）：用户已追剧集下的所有有M号的参演声优
 * 底部导航直播 tab 页推荐业务（live_recommend）：用户已追或已购剧集下，所有有M号且未关注的主役、协役
 *
 * @apiVersion 0.1.0
 * @apiName get-user-drama-cvs
 * @apiGroup /rpc/missevan-main/drama
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {string='search','live_recommend'} scene 业务场景（不同业务，取数范围不同） search: 搜索页业务；live_recommend: 底部导航直播 tab 页推荐业务
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "cvs": [ // 没有参演声优时返回空数组
 *           {
 *             "id": 1079, // 声优 ID
 *             "user_id": 1 // 声优对应的用户 ID
 *           },
 *           {
 *             "id": 1078,
 *             "user_id": 2
 *           }
 *         ]
 *       }
 *     }
 */
func ActionGetUserDramaCvs(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newDramaCvParam(c)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.getUserDramaCvs()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

func newDramaCvParam(c *handler.Context) (*dramaCvParam, error) {
	param := new(dramaCvParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || !slices.Contains(sceneList, param.Scene) {
		return nil, actionerrors.ErrParams
	}
	param.uc = c.UserContext()
	return param, nil
}

func (param *dramaCvParam) getUserDramaCvs() (*dramaCvResp, error) {
	resp := new(dramaCvResp)
	switch param.Scene {
	case SceneSearch:
		cvs, err := param.listSceneSearchDramaCvs()
		if err != nil {
			return nil, err
		}
		resp.Cvs = cvs
	case SceneLiveRecommend:
		cvs, err := param.listSceneLiveRecommendDramaCv()
		if err != nil {
			return nil, err
		}
		resp.Cvs = cvs
	default:
		panic(fmt.Sprintf("illegal param scene: %s", param.Scene))
	}
	return resp, nil
}

// listSceneSearchDramaCvs 获取用户已追剧集下的所有有M号的参演声优（搜索页业务）
func (param *dramaCvParam) listSceneSearchDramaCvs() ([]cvInfo, error) {
	// 订阅剧集
	subscribeDramaIDs, err := radiodramasubscription.ListUserSubscribeDramaIDs(param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(subscribeDramaIDs) == 0 {
		return []cvInfo{}, nil
	}
	cvIDs, err := dramaepisodecv.ListDramaCvIDs(subscribeDramaIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(cvIDs) == 0 {
		return []cvInfo{}, nil
	}
	// 获取有M号的参演声优
	cvs, err := mowangsksoundseiy.ListUserCvs(cvIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	data := make([]cvInfo, 0, len(cvs))
	for _, cv := range cvs {
		data = append(data, cvInfo{
			ID:     cv.ID,
			UserID: cv.MID,
		})
	}
	return data, nil
}

// listSceneLiveRecommendDramaCv 获取用户已追或已购剧集下，所有有M号且未关注的主役、协役（底部导航直播 tab 页推荐业务）
func (param *dramaCvParam) listSceneLiveRecommendDramaCv() ([]cvInfo, error) {
	// 订阅剧集
	subscribeDramaIDs, err := radiodramasubscription.ListUserSubscribeDramaIDs(param.UserID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id": param.UserID,
		}).Error(err)
		// PASS
	}
	// 已购剧集
	boughtDramaIDs, err := userapi.GetPaidDramaIDs(param.uc, param.UserID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id": param.UserID,
		}).Errorf("访问 rpc 接口出错，error: %v", err)
		// PASS
	}
	// 去重剧集 IDs
	dramaIDs := make([]int64, 0, len(subscribeDramaIDs)+len(boughtDramaIDs))
	dramaIDs = append(dramaIDs, subscribeDramaIDs...)
	dramaIDs = append(dramaIDs, boughtDramaIDs...)
	dramaIDs = sets.Uniq(dramaIDs)
	if len(dramaIDs) == 0 {
		return []cvInfo{}, nil
	}
	cvIDs, err := dramaepisodecv.ListDramaMainOrMinorCvIDs(dramaIDs, param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(cvIDs) == 0 {
		return []cvInfo{}, nil
	}
	cvs, err := mowangsksoundseiy.ListUnfollowUserCvs(param.UserID, cvIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(cvs) == 0 {
		return []cvInfo{}, nil
	}
	cvIDcvMap := goutil.ToMap(cvs, "ID").(map[int64]mowangsksoundseiy.MowangskSoundSeiy)
	// 需要按照 cvIDs 的顺序返回
	data := make([]cvInfo, 0, len(cvs))
	for _, cvID := range cvIDs {
		if cv, ok := cvIDcvMap[cvID]; ok {
			data = append(data, cvInfo{
				ID:     cv.ID,
				UserID: cv.MID,
			})
		}
	}
	return data, nil
}
