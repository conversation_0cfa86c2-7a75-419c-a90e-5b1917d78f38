package drama

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacopyright"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermark"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermarkstyle"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
)

func TestActionGetCornerMark(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := handler.M{
		"drama_ids": []int64{1, 7},
		"user_id":   346286,
	}
	c := handler.NewRPCTestContext("/rpc/drama/get-corner-mark", params)
	result, err := ActionGetCornerMark(c)
	require.NoError(err)
	require.NotNil(result)
	r := result.(map[int64]dramacornermarkstyle.CornerMark)
	assert.Len(r, 2)
	assert.Equal("付费", r[1].Text)
	assert.Equal("会员", r[7].Text)

	params = handler.M{
		"drama_ids": []int64{99999},
		"user_id":   346286,
	}
	c = handler.NewRPCTestContext("/rpc/drama/get-corner-mark", params)
	result, err = ActionGetCornerMark(c)
	require.NoError(err)
	assert.Empty(result)
}

func TestNewGetCornerMarkParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := handler.M{
		"drama_ids": []int64{1},
		"user_id":   346286,
	}
	c := handler.NewRPCTestContext("/rpc/drama/get-corner-mark", params)
	result, err := newGetCornerMarkParam(c)
	require.NoError(err)
	assert.NotNil(result)
}

func TestGetCornerMarkParam_getCornerMark(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &getCornerMarkParam{
		dramas: []dramainfo.RadioDramaDramainfo{
			{
				ID:      1,
				PayType: dramainfo.PayTypeFree,
				Vip:     dramainfo.VipNot,
			},
			{
				ID:      7,
				PayType: dramainfo.PayTypeDrama,
				Vip:     dramainfo.VipDrama,
			},
		},
		styles: map[int]*dramacornermarkstyle.DramaCornerMarkStyle{
			dramacornermarkstyle.TypeHot: {
				Text:           "热播",
				TextColor:      "#FFFFFF",
				TextStartColor: "",
				TextEndColor:   "",
				BgStartColor:   "#E66465",
				BgEndColor:     "#E66465",
				LeftIconURL:    "http://static-test.missevan.com/cornermark/corner_mark.png",
			},
			dramacornermarkstyle.TypeVip: {
				Text:           "会员",
				TextColor:      "",
				TextStartColor: "#FFFFFF",
				TextEndColor:   "#FFFFFF",
				BgStartColor:   "#E66465",
				BgEndColor:     "#E66465",
				LeftIconURL:    "http://static-test.missevan.com/cornermark/corner_mark.png",
			},
		},
		cornerMarkMap: map[int64]dramacornermark.RadioDramaCornerMark{
			1: {
				DramaID: 1,
				Type:    1,
			},
		},
		copyrightMap: map[int64]dramacopyright.RadioDramaDramacopyright{
			1: {
				DramaID: 1,
				Type:    1,
			},
		},
	}
	util.SetTimeNow(func() time.Time {
		return time.Date(2022, 10, 9, 6, 6, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)
	result := param.getCornerMark()
	require.NotNil(result)
	require.Len(result, 2)
	assert.Equal("热播", result[1].Text)
	assert.Equal("http://static-test.missevan.com/cornermark/corner_mark.png", result[1].LeftIconURL)
	assert.Equal("", result[1].TextStartColor)
	assert.Equal("", result[1].TextEndColor)
	assert.Equal("会员", result[7].Text)
	assert.Equal("http://static-test.missevan.com/cornermark/corner_mark.png", result[7].LeftIconURL)
	assert.Equal("#FFFFFF", result[7].TextStartColor)
	assert.Equal("#FFFFFF", result[7].TextEndColor)
}

func TestGetCornerMarkParam_getType(t *testing.T) {
	assert := assert.New(t)

	param := &getCornerMarkParam{
		DramaIDs: []int64{1, 2},
		dramas: []dramainfo.RadioDramaDramainfo{
			{
				ID:      1,
				PayType: 0,
			},
		},
		cornerMarkMap: map[int64]dramacornermark.RadioDramaCornerMark{
			1: {
				DramaID: 1,
				Type:    1,
			},
		},
		copyrightMap: map[int64]dramacopyright.RadioDramaDramacopyright{
			1: {
				DramaID: 1,
				Type:    1,
			},
		},
	}
	util.SetTimeNow(func() time.Time {
		return time.Date(2022, 10, 9, 6, 6, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)
	result := param.getType(param.dramas[0])
	assert.Equal(dramacornermarkstyle.TypeHot, result)
	util.SetTimeNow(nil)

	param = &getCornerMarkParam{
		DramaIDs: []int64{1},
		dramas: []dramainfo.RadioDramaDramainfo{
			{
				ID:      1,
				PayType: 0,
			},
		},
		cornerMarkMap: map[int64]dramacornermark.RadioDramaCornerMark{
			999: {
				DramaID: 999,
				Type:    1,
			},
		},
		copyrightMap: map[int64]dramacopyright.RadioDramaDramacopyright{
			999: {
				DramaID: 999,
				Type:    1,
			},
		},
	}
	result = param.getType(param.dramas[0])
	assert.Equal(dramacornermarkstyle.TypeNone, result)

	integrity := dramainfo.IntegrityNameSerializing
	param = &getCornerMarkParam{
		DramaIDs: []int64{1},
		dramas: []dramainfo.RadioDramaDramainfo{
			{
				ID:        1,
				PayType:   dramainfo.PayTypeFree,
				Integrity: &integrity,
			},
		},
		cornerMarkMap: map[int64]dramacornermark.RadioDramaCornerMark{
			1: {
				DramaID: 1,
				Type:    dramacornermark.TypeSelected,
			},
		},
		copyrightMap: map[int64]dramacopyright.RadioDramaDramacopyright{
			1: {
				DramaID: 1,
				Type:    dramacopyright.TypeFirstLaunch,
			},
		},
	}
	result = param.getType(param.dramas[0])
	assert.Equal(dramacornermarkstyle.TypeFirstLaunch, result)

	param = &getCornerMarkParam{
		DramaIDs: []int64{1, 2},
		dramas: []dramainfo.RadioDramaDramainfo{
			{
				ID:      1,
				PayType: 0,
				Refined: 1,
			},
		},
		cornerMarkMap: map[int64]dramacornermark.RadioDramaCornerMark{
			1: {
				DramaID: 1,
				Type:    1,
			},
		},
		copyrightMap: map[int64]dramacopyright.RadioDramaDramacopyright{
			1: {
				DramaID: 1,
				Type:    1,
			},
		},
	}
	result = param.getType(param.dramas[0])
	assert.Equal(dramacornermarkstyle.TypeNone, result)
}
