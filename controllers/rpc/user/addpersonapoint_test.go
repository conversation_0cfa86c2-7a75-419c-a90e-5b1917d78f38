package user

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestAddPersonaPointTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(addPersonaPointParam{}, "persona_id", "point", "equip_id", "buvid", "user_id")
	kc.CheckOmitEmpty(addPersonaPointParam{}, "user_id")
	kc.Check(addPersonaPointResp{}, "equip_persona", "user_persona")
	kc.CheckOmitEmpty(addPersonaPointResp{}, "equip_persona", "user_persona")
}

func TestNewAddPersonaPointParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/add-persona-point"

	// 测试参数为空
	var param addPersonaPointParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newAddPersonaPointParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数缺少设备号和用户 ID
	param.PersonaID = persona.TypeOtome
	param.Point = 233
	c = handler.NewRPCTestContext(api, param)
	_, err = newAddPersonaPointParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试画像 ID 参数错误
	param.PersonaID = persona.TypeGirl
	param.EquipID = "test_equip_id"
	param.UserID = 123
	c = handler.NewRPCTestContext(api, param)
	_, err = newAddPersonaPointParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试画像分参数错误
	param.PersonaID = persona.TypeFujoshi
	param.EquipID = "test_equip_id"
	param.UserID = 123
	param.Point = 0
	c = handler.NewRPCTestContext(api, param)
	_, err = newAddPersonaPointParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取正常接口参数
	param.Point = -233
	c = handler.NewRPCTestContext(api, param)
	res, err := newAddPersonaPointParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(param.EquipID, res.EquipID)
	assert.EqualValues(param.UserID, res.UserID)
}

func TestActionAddPersonaPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/rpc/missevan-main/user/add-persona-point"
	var param addPersonaPointParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionAddPersonaPoint(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试没有设备画像时，增加腐女分画像且画像分为负数的情况
	require.NoError(persona.Persona{}.DB().Delete("", "equip_id = ?", testEquipID).Error)
	param.PersonaID = persona.TypeFujoshi
	param.Point = -233
	param.EquipID = testEquipID
	param.BUVID = "test_buvid"
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionAddPersonaPoint(c)
	require.NoError(err)
	data, ok := res.(addPersonaPointResp)
	require.True(ok)
	assert.EqualValues(persona.TypeGirl, data.EquipPersona)
	var equipInfo persona.Persona
	require.NoError(equipInfo.DB().Where("equip_id = ?", param.EquipID).Take(&equipInfo).Error)
	assert.EqualValues(data.EquipPersona, equipInfo.Persona)
	require.NotNil(equipInfo.PointMap)
	assert.Zero(equipInfo.PointMap[persona.TypeFujoshi])

	// 测试没有设备画像时，增加设备乙女画像分数的情况
	require.NoError(persona.Persona{}.DB().Delete("", "equip_id = ?", testEquipID).Error)
	param.PersonaID = persona.TypeOtome
	param.Point = persona.OtomeMinPoint - 1
	param.EquipID = testEquipID
	param.BUVID = "test_buvid"
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionAddPersonaPoint(c)
	require.NoError(err)
	data, ok = res.(addPersonaPointResp)
	require.True(ok)
	assert.EqualValues(persona.TypeGirl, data.EquipPersona)
	var equipPersona persona.Persona
	require.NoError(equipPersona.DB().Where("equip_id = ?", param.EquipID).Take(&equipPersona).Error)
	assert.EqualValues(data.EquipPersona, equipPersona.Persona)
	require.NotNil(equipPersona.PointMap)
	assert.EqualValues(4, equipPersona.PointMap[persona.TypeOtome])
	assert.Zero(equipPersona.PointMap[persona.TypeFujoshi])

	// 测试有设备画像时，增加设备乙女画像分数的情况
	param.PersonaID = persona.TypeOtome
	param.Point = 1
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionAddPersonaPoint(c)
	require.NoError(err)
	data, ok = res.(addPersonaPointResp)
	require.True(ok)
	assert.EqualValues(persona.TypeOtome, data.EquipPersona)
	var equipPersona1 persona.Persona
	require.NoError(equipPersona1.DB().Where("equip_id = ?", param.EquipID).Take(&equipPersona1).Error)
	assert.EqualValues(data.EquipPersona, equipPersona1.Persona)
	require.NotNil(equipPersona1.PointMap)
	assert.EqualValues(5, equipPersona1.PointMap[persona.TypeOtome])
	assert.Zero(equipPersona.PointMap[persona.TypeFujoshi])

	// 测试没有用户画像时，增加乙女分画像且画像分为负数的情况
	field := strconv.FormatInt(testUserID, 10)
	keyOtome := keys.KeyUserPersonaPoints1.Format(persona.TypeOtome)
	require.NoError(service.Redis.HDel(keyOtome, field).Err())
	keyFujoshi := keys.KeyUserPersonaPoints1.Format(persona.TypeFujoshi)
	require.NoError(service.Redis.HDel(keyFujoshi, field).Err())
	require.NoError(persona.Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	param.EquipID = ""
	param.UserID = testUserID
	param.PersonaID = persona.TypeOtome
	param.Point = -233
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionAddPersonaPoint(c)
	require.NoError(err)
	data, ok = res.(addPersonaPointResp)
	require.True(ok)
	assert.EqualValues(persona.TypeGirl, data.UserPersona)
	var userInfo persona.Persona
	require.NoError(userInfo.DB().Where("user_id = ?", param.UserID).Take(&userInfo).Error)
	assert.EqualValues(data.UserPersona, userInfo.Persona)
	require.NotNil(userInfo.PointMap)
	assert.Zero(userInfo.PointMap[persona.TypeOtome])

	// 测试没有用户画像时，增加设备和用户腐女画像分数的情况
	require.NoError(persona.Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	param.EquipID = testEquipID
	param.UserID = testUserID
	param.PersonaID = persona.TypeFujoshi
	param.Point = 3
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionAddPersonaPoint(c)
	require.NoError(err)
	data, ok = res.(addPersonaPointResp)
	require.True(ok)
	assert.EqualValues(persona.TypeOtome, data.UserPersona)
	assert.EqualValues(persona.TypeOtome, data.EquipPersona)
	var userPersona persona.Persona
	require.NoError(userPersona.DB().Where("user_id = ?", param.UserID).Take(&userPersona).Error)
	assert.NotNil(userPersona.PointMap)
	assert.EqualValues(3, userPersona.PointMap[persona.TypeFujoshi])
	assert.EqualValues(5, userPersona.PointMap[persona.TypeOtome])

	// 测试有用户画像时，增加用户腐女画像分数的情况
	param.EquipID = ""
	param.PersonaID = persona.TypeOtome
	param.Point = 2
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionAddPersonaPoint(c)
	require.NoError(err)
	data, ok = res.(addPersonaPointResp)
	require.True(ok)
	assert.EqualValues(persona.TypeOtome, data.UserPersona)
	var userPersona1 persona.Persona
	require.NoError(userPersona1.DB().Where("user_id = ?", param.UserID).Take(&userPersona1).Error)
	assert.NotNil(userPersona1.PointMap)
	assert.EqualValues(7, userPersona1.PointMap[persona.TypeOtome])
	assert.EqualValues(3, userPersona1.PointMap[persona.TypeFujoshi])
}
