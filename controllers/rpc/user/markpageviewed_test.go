package user

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestActionMarkPageViewed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/mark-page-viewed"

	// 测试参数错误
	param := markParam{
		UserID: 0,
		Scene:  "",
	}
	c := handler.NewRPCTestContext(api, param)
	_, _, err := ActionMarkPageViewed(c)
	assert.Equal(actionerrors.ErrParams, err)

	testTimeUnix := int64(1730390400)
	util.SetTimeNow(func() time.Time {
		return time.Unix(testTimeUnix, 0)
	})
	defer util.SetTimeNow(nil)

	// 测试成功返回
	param.UserID = 1
	param.Scene = sceneTabBarLive
	key := keys.KeyUserTabBarLiveLastRequestTime1.Format(param.UserID)
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	c = handler.NewRPCTestContext(api, param)
	_, _, err = ActionMarkPageViewed(c)
	require.NoError(err)
	// 断言已标记
	data, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(testTimeUnix, data)
}

func TestNewMarkParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := markParam{
		UserID: 0,
		Scene:  "",
	}
	api := "/rpc/missevan-main/user/mark-page-viewed"

	// 测试参数错误
	c := handler.NewRPCTestContext(api, param)
	data, err := newMarkParam(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(data)

	// 测试成功返回
	param.UserID = 1
	param.Scene = sceneTabBarLive
	c = handler.NewRPCTestContext(api, param)
	data, err = newMarkParam(c)
	require.NoError(err)
	require.NotNil(data)
	assert.EqualValues(1, data.UserID)
	assert.EqualValues(sceneTabBarLive, data.Scene)
}

func TestMark(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := markParam{
		UserID: 1,
		Scene:  sceneTabBarMyListen,
	}
	key := keys.KeyUserTabBarLiveLastRequestTime1.Format(param.UserID)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	testTimeUnix := int64(1730390400)
	util.SetTimeNow(func() time.Time {
		return time.Unix(testTimeUnix, 0)
	})
	defer util.SetTimeNow(nil)

	// 测试成功返回
	param.Scene = sceneTabBarLive
	err = param.mark()
	require.NoError(err)
	// 断言已标记
	data, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(testTimeUnix, data)
}
