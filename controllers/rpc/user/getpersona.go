package user

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/persona"
)

type getPersonaParam struct {
	EquipID string `json:"equip_id,omitempty"` // 设备号
	BUVID   string `json:"buvid,omitempty"`    // buvid
	UserID  int64  `json:"user_id,omitempty"`  // 用户 ID
}

type getPersonaResp struct {
	Persona    int64 `json:"persona"`                // 画像信息
	IsNewUser  *bool `json:"is_new_user,omitempty"`  // 是否为新建用户画像
	IsNewEquip *bool `json:"is_new_equip,omitempty"` // 是否为新建设备画像
	Sex        int   `json:"sex"`                    // 用户性别，0 未知，1 表示男，2 表示女
}

func newGetPersonaParam(c *handler.Context) (*getPersonaParam, error) {
	param := new(getPersonaParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.EquipID == "" && param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	return param, nil
}

// ActionGetPersona 获取用户画像和性别
/**
 * @api {post} /rpc/missevan-main/user/get-persona 获取画像信息
 * @apiDescription 若接口参数 user_id 和 equip_id 同时存在优先根据 user_id 获取用户画像
 * @apiVersion 0.1.0
 * @apiName get-persona
 * @apiGroup rpc/missevan-main/user
 *
 * @apiParam {String} [equip_id] 设备号
 * @apiParam {String} [buvid] buvid
 * @apiParam {Number} [user_id] 用户 ID
 *
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": { // 不存在画像时返回普通女性画像
 *         "persona": 719, // persona 包含模块画像和猜你喜欢音推荐策略
 *         "is_new_user": false, // 是否为新建用户画像，传入有效 user_id 时返回该字段
 *         "is_new_equip": true // 是否为新建设备画像，传入有效 equip_id 时返回该字段
 *         "sex": 1 // 用户性别，0 未知，1 为男，2 为女
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 */
func ActionGetPersona(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGetPersonaParam(c)
	if err != nil {
		return nil, err
	}

	resp, err := persona.GetPersonaInfo(param.EquipID, param.BUVID, param.UserID)
	if err != nil {
		return nil, err
	}
	return getPersonaResp{
		Persona:    resp.Persona,
		IsNewUser:  resp.IsNewUser,
		IsNewEquip: resp.IsNewEquip,
		Sex:        resp.Sex,
	}, nil
}
