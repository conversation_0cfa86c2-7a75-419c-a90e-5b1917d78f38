package user

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

type addPersonaPointParam struct {
	PersonaID int64  `json:"persona_id"`        // 画像 ID
	Point     int64  `json:"point"`             // 画像分
	EquipID   string `json:"equip_id"`          // 设备号
	BUVID     string `json:"buvid"`             // buvid
	UserID    int64  `json:"user_id,omitempty"` // 用户 ID

	equipPersona *persona.Persona
}

type addPersonaPointResp struct {
	EquipPersona int64 `json:"equip_persona,omitempty"` // 设备画像
	UserPersona  int64 `json:"user_persona,omitempty"`  // 用户画像
}

func newAddPersonaPointParam(c *handler.Context) (*addPersonaPointParam, error) {
	param := new(addPersonaPointParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if !goutil.HasElem(persona.PersonaPointTypes, param.PersonaID) ||
		param.Point == 0 || (param.EquipID == "" && param.UserID <= 0) {
		return nil, actionerrors.ErrParams
	}

	return param, nil
}

// ActionAddPersonaPoint 增加画像分数
/**
 * @api {post} /rpc/missevan-main/user/add-persona-point 增加画像分数
 * @apiVersion 0.1.0
 * @apiName add-persona-point
 * @apiGroup rpc/missevan-main/user
 *
 * @apiParam {Number} persona_id 画像 ID
 * @apiParam {Number} point 画像分
 * @apiParam {String} equip_id 设备号
 * @apiParam {String} buvid buvid
 * @apiParam {Number} [user_id] 用户 ID
 *
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "equip_persona": 2, // 设备画像，若接口参数 equip_id 为空，该字段不返回
 *         "user_persona": 3 // 用户画像，若接口参数 user_id 为空，该字段不返回
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 */
func ActionAddPersonaPoint(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newAddPersonaPointParam(c)
	if err != nil {
		return nil, err
	}

	var resp addPersonaPointResp
	// 增加设备画像分
	if param.EquipID != "" {
		param.equipPersona, err = param.addEquipPersonaPoint()
		if err != nil {
			return nil, err
		}
		resp.EquipPersona = param.equipPersona.Persona
	}
	if param.UserID > 0 {
		// 增加用户画像
		userPersona, err := param.addUserPersonaPoint()
		if err != nil {
			return nil, err
		}
		resp.UserPersona = userPersona.Persona
	}
	return resp, nil
}

func (param *addPersonaPointParam) addEquipPersonaPoint() (*persona.Persona, error) {
	equipPersona, err := persona.GetEquipPersona(param.EquipID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	if equipPersona == nil {
		equipPersona = &persona.Persona{
			EquipID: &param.EquipID,
			BUVID:   param.BUVID,
			Persona: persona.TypeGirl, // 默认画像
		}
		// WORKAROUND: 目前画像分还没有迁移到 MySQL 中，还需要在 redis 中查一次，待数据全部迁移后删除该逻辑
		if err = equipPersona.GetPersonaPointsFromRedis(keys.KeyEquipPersonaPoints1, param.EquipID); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		equipPersona.PointMap[param.PersonaID] += param.Point
		if point := equipPersona.PointMap[param.PersonaID]; point < 0 {
			equipPersona.PointMap[param.PersonaID] = 0
		}
		if err = equipPersona.CreatePersona(); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	} else {
		if err = equipPersona.AddPersonaPoint(param.PersonaID, param.Point); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}
	return equipPersona, nil
}

func (param *addPersonaPointParam) addUserPersonaPoint() (*persona.Persona, error) {
	userPersona, err := persona.GetUserPersona(param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	if userPersona == nil {
		userPersona = &persona.Persona{
			UserID:  goutil.NewInt64(param.UserID),
			Persona: persona.TypeGirl, // 默认画像
		}
		// WORKAROUND: 目前画像分还没有迁移到 MySQL 中，还需要在 redis 中查一次，待数据全部迁移后删除该逻辑
		if err = userPersona.GetPersonaPointsFromRedis(keys.KeyUserPersonaPoints1, strconv.FormatInt(param.UserID, 10)); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if param.equipPersona != nil {
			for personaID, point := range param.equipPersona.PointMap {
				// 同步设备画像分至用户画像分
				userPersona.PointMap[personaID] += point
			}
		} else {
			userPersona.PointMap[param.PersonaID] += param.Point
		}
		if point := userPersona.PointMap[param.PersonaID]; point < 0 {
			userPersona.PointMap[param.PersonaID] = 0
		}
		if err = userPersona.CreatePersona(); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	} else {
		if err = userPersona.AddPersonaPoint(param.PersonaID, param.Point); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}
	return userPersona, nil
}
