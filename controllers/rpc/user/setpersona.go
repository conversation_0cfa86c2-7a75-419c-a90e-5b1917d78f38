package user

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

type setPersonaParam struct {
	Persona     int64  `json:"persona"`                // 画像信息
	EquipID     string `json:"equip_id"`               // 设备号
	BUVID       string `json:"buvid"`                  // buvid
	UserID      int64  `json:"user_id,omitempty"`      // 用户 ID
	SetStrategy bool   `json:"set_strategy,omitempty"` // 是否同时设置猜你喜欢音推荐策略，默认不更新

	equipPersona *persona.Persona
}

type setPersonaResp struct {
	Persona int64 `json:"persona"` // 画像 ID
}

func newSetPersonaParam(c *handler.Context) (*setPersonaParam, error) {
	param := new(setPersonaParam)
	err := c.BindJ<PERSON>(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.Persona <= 0 || param.EquipID == "" {
		return nil, actionerrors.ErrParams
	}

	return param, nil
}

// ActionSetPersona 设置画像 ID
/**
 * @api {post} /rpc/missevan-main/user/set-persona 设置画像 ID
 * @apiVersion 0.1.0
 * @apiName set-persona
 * @apiGroup rpc/missevan-main/user
 *
 * @apiParam {Number} persona 画像信息
 * @apiParam {String} equip_id 设备号
 * @apiParam {String} buvid buvid
 * @apiParam {Number} [user_id] 用户 ID
 * @apiParam {Boolean} [set_strategy=false] 是否同时设置猜你喜欢音推荐策略，默认不更新
 *
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "persona": 2
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 */
func ActionSetPersona(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSetPersonaParam(c)
	if err != nil {
		return nil, err
	}

	// 设置设备画像
	persona, err := param.setEquipPersona()
	if err != nil {
		return nil, err
	}
	if param.UserID > 0 {
		// 设置用户画像
		persona, err = param.setUserPersona()
		if err != nil {
			return nil, err
		}
	}
	return setPersonaResp{Persona: persona}, nil
}

func (param *setPersonaParam) setEquipPersona() (int64, error) {
	// 获取设备画像
	equipPersona, err := persona.GetEquipPersona(param.EquipID)
	if err != nil {
		return 0, actionerrors.ErrServerInternal(err, nil)
	}
	if equipPersona == nil {
		// 新建设备画像
		equipPersona = &persona.Persona{
			Persona: param.Persona,
			EquipID: &param.EquipID,
			BUVID:   param.BUVID,
		}
		// WORKAROUND: 目前画像分还没有迁移到 MySQL 中，还需要在 redis 中查一次，待数据全部迁移后删除该逻辑
		if err = equipPersona.GetPersonaPointsFromRedis(keys.KeyEquipPersonaPoints1, param.EquipID); err != nil {
			return 0, actionerrors.ErrServerInternal(err, nil)
		}
		if err = equipPersona.CreatePersona(); err != nil {
			return 0, actionerrors.ErrServerInternal(err, nil)
		}
	} else if param.UserID <= 0 {
		// 若接口参数中用户 ID 不存在，则更新设备画像，否则更新用户画像
		err = equipPersona.UpdatePersona(param.Persona, param.SetStrategy)
		if err != nil {
			return 0, actionerrors.ErrServerInternal(err, nil)
		}
	}
	param.equipPersona = equipPersona
	return equipPersona.Persona, nil
}

func (param *setPersonaParam) setUserPersona() (int64, error) {
	// 获取用户画像
	userPersona, err := persona.GetUserPersona(param.UserID)
	if err != nil {
		return 0, actionerrors.ErrServerInternal(err, nil)
	}
	if userPersona == nil {
		// 新建用户画像
		userPersona = &persona.Persona{
			Persona: param.Persona,
			UserID:  goutil.NewInt64(param.UserID),
		}
		// WORKAROUND: 目前画像分还没有迁移到 MySQL 中，还需要在 redis 中查一次，待数据全部迁移后删除该逻辑
		if err = userPersona.GetPersonaPointsFromRedis(keys.KeyUserPersonaPoints1, strconv.FormatInt(param.UserID, 10)); err != nil {
			return 0, actionerrors.ErrServerInternal(err, nil)
		}
		for personaID, point := range param.equipPersona.PointMap {
			// 同步设备画像分至用户画像分
			userPersona.PointMap[personaID] += point
		}
		if err = userPersona.CreatePersona(); err != nil {
			return 0, actionerrors.ErrServerInternal(err, nil)
		}
	} else {
		// 更新用户画像
		err = userPersona.UpdatePersona(param.Persona, param.SetStrategy)
		if err != nil {
			return 0, actionerrors.ErrServerInternal(err, nil)
		}
	}
	return userPersona.Persona, nil
}
