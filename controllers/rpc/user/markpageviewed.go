package user

import (
	"slices"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// 页面场景
const (
	sceneTabBarLive     = "tab_bar_live"      // 底部导航栏直播页面
	sceneTabBarMyListen = "tab_bar_my_listen" // 底部导航栏我听页面
)

var sceneList = []string{sceneTabBarLive, sceneTabBarMyListen}

type markParam struct {
	UserID int64  `json:"user_id"`
	Scene  string `json:"scene"`
}

// ActionMarkPageViewed 标记某个场景（页面）被查看时的信息
/**
 * @api {post} /rpc/missevan-main/user/mark-page-viewed 标记某个场景（页面）被查看时的信息
 *
 * @apiVersion 0.1.0
 * @apiName mark-page-viewed
 * @apiGroup /rpc/missevan-main/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} scene 页面场景 tab_bar_live：底部导航栏直播页面；tab_bar_my_listen：底部导航栏我听页面（暂未使用）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "记录成功",
 *       "data": null
 *     }
 */
func ActionMarkPageViewed(c *handler.Context) (handler.ActionResponse, string, error) {
	mp, err := newMarkParam(c)
	if err != nil {
		return nil, "", err
	}
	err = mp.mark()
	if err != nil {
		return nil, "", err
	}
	return nil, "记录成功", nil
}

// newMarkParam 参数校验
func newMarkParam(c *handler.Context) (*markParam, error) {
	param := new(markParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || !slices.Contains(sceneList, param.Scene) {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

// mark 标记某个场景
func (param *markParam) mark() error {
	switch param.Scene {
	case sceneTabBarLive:
		param.markTabBarLive()
	default:
		// TODO: 新增场景待完善
		return nil
	}
	return nil
}

// markTabBarLive 标记用户访问底部导航栏【直播】Tab 时间
func (param *markParam) markTabBarLive() {
	key := keys.KeyUserTabBarLiveLastRequestTime1.Format(param.UserID)
	service.Redis.Set(key, util.TimeNow().Unix(), 7*24*time.Hour)
}
