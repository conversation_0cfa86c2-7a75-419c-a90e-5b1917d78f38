package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestNewGetUserConfigParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/get-user-config"

	tests := []struct {
		name     string
		payload  interface{}
		expected bool
		userId   int64
		buvid    string
	}{
		{
			name:     "InvalidParamsEmptyParams",
			payload:  getUserConfigParam{},
			expected: false,
		},
		{
			name:     "ValidParamsUserID",
			payload:  getUserConfigParam{UserID: 12345},
			expected: true,
			userId:   12345,
		},
		{
			name:     "ValidParamsBUVID",
			payload:  getUserConfigParam{BUVID: "test-buvid"},
			expected: true,
			buvid:    "test-buvid",
		},
		{
			name:     "ValidParamsUserIDAndBUVID",
			payload:  getUserConfigParam{UserID: 12345, BUVID: "test-buvid"},
			expected: true,
			userId:   12345,
			buvid:    "test-buvid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := handler.NewRPCTestContext(api, tt.payload)

			param, err := newGetUserConfigParam(c)
			if !tt.expected {
				assert.Error(err)
				assert.Equal(actionerrors.ErrParams, err)
				assert.Nil(param)
			} else {
				require.NoError(err)
				require.NotNil(param)
				assert.Equal(tt.userId, param.UserID)
				assert.Equal(tt.buvid, param.BUVID)
			}
		})
	}
}

func TestActionGetUserConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/get-user-config"

	t.Run("ValidParamsUserID", func(t *testing.T) {
		c := handler.NewRPCTestContext(api, getUserConfigParam{UserID: 12345})

		resp, _, err := ActionGetUserConfig(c)
		require.NoError(err)
		assert.NotNil(resp)
	})
}
