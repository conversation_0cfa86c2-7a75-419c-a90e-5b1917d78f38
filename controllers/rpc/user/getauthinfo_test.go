package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/person/usercertification"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestGetAuthInfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(getAuthInfoParams{}, "user_id")
	kc.Check(getAuthInfoResponse{}, "auth_info")
	kc.Check(authInfoData{}, "type", "title", "subtitle")
	kc.CheckOmitEmpty(authInfoData{}, "subtitle")
}

func TestActionGetAuthInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		api = "/rpc/missevan-main/user/get-auth-info"
	)

	// 测试参数错误
	param := getAuthInfoParams{
		UserID: -99999,
	}
	c := handler.NewRPCTestContext(api, param)
	_, _, err := ActionGetAuthInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试正常用户
	param = getAuthInfoParams{
		UserID: 12,
	}
	require.NoError(service.DB.Table(user.MowangskUser{}.TableName()).Update("confirm", 33554512).
		Where("id = ?", param.UserID).Error)
	c = handler.NewRPCTestContext(api, param)
	resp, _, err := ActionGetAuthInfo(c)
	require.Nil(err)
	assert.NotNil(resp)
	data, ok := resp.(getAuthInfoResponse)
	require.True(ok)
	require.NotNil(data.AuthInfo)
	assert.Equal("个人认证", data.AuthInfo.Title)
	assert.Equal("测试认证头衔", data.AuthInfo.Subtitle)
}

func TestGetAuthInfoResponse_fullAuthInfo(t *testing.T) {
	assert := assert.New(t)

	resp := getAuthInfoResponse{
		AuthInfo: &authInfoData{
			Type: user.ConfirmGoldenVIP,
		},
	}
	resp.fullAuthInfo(nil)
	assert.Equal("UP 主认证", resp.AuthInfo.Title)
	resp.fullAuthInfo(&usercertification.UserCertification{Subtitle: "test"})
	assert.Equal("个人认证", resp.AuthInfo.Title)
	assert.Equal("test", resp.AuthInfo.Subtitle)

	resp = getAuthInfoResponse{
		AuthInfo: &authInfoData{
			Type: user.ConfirmBlueVIP,
		},
	}
	resp.fullAuthInfo(nil)
	assert.Equal("机构官方认证", resp.AuthInfo.Title)
	resp.fullAuthInfo(&usercertification.UserCertification{Subtitle: "test"})
	assert.Equal("机构认证", resp.AuthInfo.Title)
	assert.Equal("test", resp.AuthInfo.Subtitle)
}
