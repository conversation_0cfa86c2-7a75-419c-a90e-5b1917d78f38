package user

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

type getVipUserIDsParams struct {
	UserIDs []int64 `json:"user_ids"`
}

type getVipUserIDsResp struct {
	VipUserIDs []int64 `json:"vip_user_ids"`
}

// ActionGetVipUserIDs 获取点播会员用户 IDs
/**
 * @api {post} /rpc/missevan-main/user/get-vip-user-ids 获取会员用户 IDs
 *
 * @apiVersion 0.1.0
 * @apiName get-vip-user-ids
 * @apiGroup /rpc/missevan-main/user
 *
 * @apiParam {Number} user_ids 用户 IDs
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "vip_user_ids": [1, 233] // 会员用户 ID 组成的数组，没有时返回空数组
 *       }
 *     }
 */
func ActionGetVipUserIDs(c *handler.Context) (handler.ActionResponse, string, error) {
	var param getVipUserIDsParams
	err := c.BindJSON(&param)
	if err != nil || len(param.UserIDs) == 0 {
		return nil, "", actionerrors.ErrParams
	}

	vipUserIDs, err := muservip.ListVipUserIDs(param.UserIDs)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	resp := getVipUserIDsResp{
		VipUserIDs: vipUserIDs,
	}
	return resp, "", nil
}
