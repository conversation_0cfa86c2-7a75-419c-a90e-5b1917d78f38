package user

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-main/models/person/usercertification"
)

type getAuthInfoParams struct {
	UserID int64 `json:"user_id"`
}

type getAuthInfoResponse struct {
	AuthInfo *authInfoData `json:"auth_info"`
}

type authInfoData struct {
	Type     uint   `json:"type"`
	Title    string `json:"title"`
	Subtitle string `json:"subtitle,omitempty"`
}

// ActionGetAuthInfo 获取用户头衔认证信息
/**
 * @api {post} /rpc/missevan-main/user/get-auth-info 获取用户头衔认证信息
 *
 * @apiVersion 0.1.0
 * @apiName get-auth-info
 * @apiGroup /rpc/missevan-main/user
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "auth_info": { // 用户认证信息，无认证时返回 nil
 *           "type": 2, // 认证类型，2：金 V；3：蓝 V
 *           "title": "UP 主认证", // 认证类型描述
 *           "subtitle": "知名声优椰子酱" // 认证头衔，无头衔时不下发
 *         }
 *       }
 *     }
 */
func ActionGetAuthInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	param := new(getAuthInfoParams)
	err := c.BindJSON(&param)
	if err != nil || param.UserID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	u, err := user.FindByUserID(param.UserID)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if u == nil {
		return nil, "", actionerrors.ErrUserNotFound
	}
	authType := user.GetAuthenticated(u.Confirm)
	if authType != user.ConfirmGoldenVIP && authType != user.ConfirmBlueVIP {
		return nil, "", nil
	}
	resp := getAuthInfoResponse{AuthInfo: &authInfoData{Type: authType}}
	uc, err := usercertification.FindByUserID(param.UserID)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	resp.fullAuthInfo(uc)
	return resp, "", nil
}

func (resp *getAuthInfoResponse) fullAuthInfo(uc *usercertification.UserCertification) {
	if uc == nil {
		if resp.AuthInfo.Type == user.ConfirmGoldenVIP {
			resp.AuthInfo.Title = "UP 主认证"
			return
		}
		resp.AuthInfo.Title = "机构官方认证"
		return
	}

	resp.AuthInfo.Subtitle = uc.Subtitle
	if resp.AuthInfo.Type == user.ConfirmGoldenVIP {
		resp.AuthInfo.Title = "个人认证"
		return
	}
	resp.AuthInfo.Title = "机构认证"
}
