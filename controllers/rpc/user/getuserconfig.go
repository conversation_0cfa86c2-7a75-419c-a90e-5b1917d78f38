package user

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-main/models/user"
)

type getUserConfigParam struct {
	UserID int64  `json:"user_id"` // 用户ID
	BUVID  string `json:"buvid"`   // 设备号
}

type getUserConfigResp struct {
	Config *user.MUserConfig `json:"config"` // 用户配置信息
}

func newGetUserConfigParam(c *handler.Context) (*getUserConfigParam, error) {
	param := new(getUserConfigParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// 用户 ID 和设备号至少需要提供一个
	if param.UserID <= 0 && param.BUVID == "" {
		return nil, actionerrors.ErrParams
	}

	return param, nil
}

// ActionGetUserConfig 获取用户配置信息
/**
 * @api {post} /rpc/missevan-main/user/get-user-config 获取用户配置信息
 * @apiDescription 若接口参数 user_id 不为 0，优先根据 user_id 获取用户配置
 * @apiVersion 0.1.0
 * @apiName get-user-config
 * @apiGroup rpc/missevan-main/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} buvid 设备号
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "config": {
 *           "user_id": 12345,               // 用户 ID
 *           "buvid": "",                    // 设备号，已登录用户为空字符串
 *           "app_config": {                 // APP 配置信息
 *             "personalized_recommend": 1,  // 是否开启个性化推荐，0 关闭，1 开启
 *             "show_subscribe_drama": 1,    // 是否在个人主页公开我的追剧，0 关闭，1 开启
 *             "show_user_collect": 1,       // 是否在个人主页公开我的收藏，0 关闭，1 开启
 *             "message_notification": {     // 用户消息推送设置
 *               "at_me": 1,                 // @我，0 关闭，1 开启
 *               "like": 1,                  // 点赞，0 关闭，1 开启
 *               "comment": 1,               // 评论，0 关闭，1 开启
 *               "private_message": 1,       // 私信，0 关闭，1 开启
 *               "live": 1,                  // 主播开播提醒，0 关闭，1 开启
 *               "interest_recommend": 1     // 推送我可能感兴趣的内容，0 关闭，1 开启
 *             }
 *           }
 *         }
 *       }
 *     }
 */
func ActionGetUserConfig(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newGetUserConfigParam(c)
	if err != nil {
		return nil, "", err
	}

	// 获取登录用户的注册时间，未登录用户传 0
	var ctime int64 = 0
	if param.UserID > 0 {
		ctime, err = user.MowangskUser{}.GetCTimeByID(param.UserID)
		if err != nil {
			logger.WithField("user_id", param.UserID).Errorf("GetCTimeByID failed, error: %v", err)
			// PASS
		}
	}

	// 使用模型获取用户配置
	userConfig, err := user.GetUserConfig(param.UserID, param.BUVID, ctime)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, logger.Fields{"user_id": param.UserID, "buvid": param.BUVID})
	}

	return getUserConfigResp{Config: userConfig}, "", nil
}
