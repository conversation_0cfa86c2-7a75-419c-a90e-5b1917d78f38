package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

func TestActionGetVipUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/rpc/missevan-main/user/get-vip-user-ids"
	var param getVipUserIDsParams
	c := handler.NewRPCTestContext(api, param)
	_, _, err := ActionGetVipUserIDs(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试传入的 user_ids 中有会员
	testUserID := int64(5674514)
	param.UserIDs = []int64{testUserID}
	c = handler.NewRPCTestContext(api, param)
	res, message, err := ActionGetVipUserIDs(c)
	require.NoError(err)
	assert.Equal("", message)
	data, ok := res.(getVipUserIDsResp)
	require.True(ok)
	require.NotEmpty(data.VipUserIDs)
	require.EqualValues(testUserID, data.VipUserIDs[0])

	// 测试传入的 user_ids 中没有会员
	require.NoError(muservip.MUserVip{}.DB().Delete("", "user_id = ?", testUserID).Error)
	c = handler.NewRPCTestContext(api, param)
	res, message, err = ActionGetVipUserIDs(c)
	require.NoError(err)
	assert.Equal("", message)
	data, ok = res.(getVipUserIDsResp)
	require.True(ok)
	assert.Empty(data.VipUserIDs)
}
