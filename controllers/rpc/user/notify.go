package user

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// notify event 类型
const (
	notifyTypeLogout         = "logout"
	notifyTypeUpdatePassword = "update_password"
)

type newNotifyParam struct {
	UserID  int64  `json:"user_id" binding:"required"`
	Event   string `json:"event" binding:"required"`
	EquipID string `json:"equip_id"`
}

type notifyResp struct {
	OK int `json:"ok"`
}

// ActionNotify 接收 rpc 消息后根据事件类型处理消息
/**
 * @api {post} /rpc/missevan-main/user/notify 接收 rpc 消息后根据事件类型处理消息
 * @apiVersion 0.1.0
 * @apiName notify
 * @apiGroup rpc/missevan-main/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} event 消息类型
 * @apiParam {String} [equip_id] 设备号
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "ok": 1 // 处理成功返回 1, 未处理或处理失败返回 0
 *       }
 *     }
 */
func ActionNotify(c *handler.Context) (handler.ActionResponse, string, error) {
	var param newNotifyParam
	err := c.BindJSON(&param)
	if err != nil || param.UserID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	switch param.Event {
	case notifyTypeLogout:
		if param.EquipID == "" {
			return nil, "", actionerrors.ErrParams
		}
		err = removeUserPlayEquipCache(param.UserID, param.EquipID)
		if err != nil {
			logger.WithField("user_id", param.UserID).Error(err)
			return &notifyResp{OK: 0}, "", nil
		}
	case notifyTypeUpdatePassword:
		err = removeUserPlayEquipCache(param.UserID, "")
		if err != nil {
			logger.WithField("user_id", param.UserID).Error(err)
			return &notifyResp{OK: 0}, "", nil
		}
	default:
		return &notifyResp{OK: 0}, "", nil
	}
	return &notifyResp{OK: 1}, "", nil
}

func removeUserPlayEquipCache(userID int64, equipID string) (err error) {
	key := keys.KeyUserPlayEquip1.Format(userID)
	if equipID != "" {
		err = service.Redis.ZRem(key, equipID).Err()
	} else {
		err = service.Redis.Del(key).Err()
	}
	if err != nil {
		return err
	}
	return nil
}
