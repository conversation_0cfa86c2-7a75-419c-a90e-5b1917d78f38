package user

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestNotifyTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(newNotifyParam{}, "user_id", "event", "equip_id")
	kc.Check(notifyResp{}, "ok")
}

func TestActionNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/notify"

	// 测试参数为空
	var param newNotifyParam
	c := handler.NewRPCTestContext(api, param)
	_, _, err := ActionNotify(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户 ID 参数错误
	param.UserID = -1
	c = handler.NewRPCTestContext(api, param)
	_, _, err = ActionNotify(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试事件参数不匹配
	param.UserID = testUserID
	param.Event = "test"
	c = handler.NewRPCTestContext(api, param)
	res, _, err := ActionNotify(c)
	require.NoError(err)
	data, ok := res.(*notifyResp)
	require.True(ok)
	assert.Zero(data.OK)

	// 测试删除账号播放设备缓存集合
	redisData := []*redis.Z{
		{Score: 1, Member: "test_equip_id1"},
		{Score: 2, Member: "test_equip_id2"},
		{Score: 3, Member: "test_equip_id3"},
	}
	key := keys.KeyUserPlayEquip1.Format(testUserID)
	require.NoError(service.Redis.Del(keys.KeyUserPlayEquip1.Format(param.UserID)).Err())
	require.NoError(service.Redis.ZAdd(key, redisData...).Err())

	param.Event = notifyTypeUpdatePassword
	c = handler.NewRPCTestContext(api, param)
	res, _, err = ActionNotify(c)
	require.NoError(err)
	data, ok = res.(*notifyResp)
	require.True(ok)
	assert.Equal(1, data.OK)
	exists, err := service.Redis.Exists(key).Result()
	require.NoError(err)
	assert.Zero(exists)
}

func TestRemoveUserPlayEquipCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除指定设备缓存
	redisData := []*redis.Z{
		{Score: 1, Member: "test_equip_id1"},
		{Score: 2, Member: "test_equip_id2"},
		{Score: 3, Member: "test_equip_id3"},
	}
	key := keys.KeyUserPlayEquip1.Format(testUserID)
	require.NoError(service.Redis.Del(keys.KeyUserPlayEquip1.Format(testUserID)).Err())
	require.NoError(service.Redis.ZAdd(key, redisData...).Err())
	require.NoError(removeUserPlayEquipCache(testUserID, "test_equip_id2"))
	exists, err := service.Redis.Exists(key).Result()
	require.NoError(err)
	require.NotZero(exists)
	_, err = service.Redis.ZScore(key, "test_equip_id2").Result()
	assert.True(serviceredis.IsRedisNil(err))

	// 测试删除账号播放设备缓存集合
	require.NoError(removeUserPlayEquipCache(testUserID, ""))
	exists, err = service.Redis.Exists(key).Result()
	require.NoError(err)
	assert.Zero(exists)
}
