package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	testUserID  = 1234
	testEquipID = "test_equip_id"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(setPersonaParam{}, "persona", "equip_id", "buvid", "user_id", "set_strategy")
	kc.Check(setPersonaResp{}, "persona")
	kc.CheckOmitEmpty(setPersonaParam{}, "user_id", "set_strategy")
}

func TestNewSetPersonaParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/set-persona"

	// 测试参数为空
	var param setPersonaParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newSetPersonaParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试缺少设备号
	param.Persona = 123
	c = handler.NewRPCTestContext(api, param)
	_, err = newSetPersonaParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取正常接口参数
	param.EquipID = "test_equip_id"
	param.UserID = 123
	c = handler.NewRPCTestContext(api, param)
	res, err := newSetPersonaParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(param.EquipID, res.EquipID)
	assert.EqualValues(param.UserID, res.UserID)
}

func TestActionSetPersona(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/rpc/missevan-main/user/set-persona"
	var param setPersonaParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionSetPersona(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试没有设备画像
	require.NoError(persona.Persona{}.DB().Delete("", "equip_id = ?", testEquipID).Error)
	param.Persona = persona.TypeBoy
	param.EquipID = testEquipID
	param.BUVID = "test_buvid"
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionSetPersona(c)
	require.NoError(err)
	data, ok := res.(setPersonaResp)
	require.True(ok)
	assert.EqualValues(persona.TypeBoy, data.Persona)
	var equipPersona persona.Persona
	require.NoError(equipPersona.DB().Where("equip_id = ?", param.EquipID).Take(&equipPersona).Error)
	assert.EqualValues(data.Persona, equipPersona.Persona)

	// 测试设置设备画像
	param.Persona = persona.TypeGirl
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionSetPersona(c)
	require.NoError(err)
	data, ok = res.(setPersonaResp)
	require.True(ok)
	assert.EqualValues(persona.TypeGirl, data.Persona)
	var equipPersona1 persona.Persona
	require.NoError(equipPersona1.DB().Where("equip_id = ?", param.EquipID).Take(&equipPersona1).Error)
	assert.EqualValues(data.Persona, equipPersona1.Persona)

	// 测试用户画像不存在
	require.NoError(persona.Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	param.UserID = testUserID
	param.Persona = persona.TypeBoy
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionSetPersona(c)
	require.NoError(err)
	data, ok = res.(setPersonaResp)
	require.True(ok)
	assert.EqualValues(persona.TypeBoy, data.Persona)
	var userPersona persona.Persona
	require.NoError(userPersona.DB().Where("user_id = ?", param.UserID).Take(&userPersona).Error)
	assert.EqualValues(data.Persona, userPersona.Persona)

	// 测试设置用户画像
	require.NoError(userPersona.DB().Where("user_id = ?", userPersona.UserID).
		Update("points", "{\"7\":8,\"9\":11}").Error)
	param.Persona = persona.TypeGirl
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionSetPersona(c)
	require.NoError(err)
	data, ok = res.(setPersonaResp)
	require.True(ok)
	assert.EqualValues(persona.TypeOtome, data.Persona)
	var userPersona1 persona.Persona
	require.NoError(userPersona1.DB().Where("user_id = ?", param.UserID).Take(&userPersona1).Error)
	assert.EqualValues(data.Persona, userPersona1.Persona)
}
