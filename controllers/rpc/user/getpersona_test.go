package user

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestGetPersonaTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(getPersonaParam{}, "equip_id", "buvid", "user_id")
	kc.CheckOmitEmpty(getPersonaParam{}, "equip_id", "buvid", "user_id")
	kc.Check(getPersonaResp{}, "persona", "is_new_user", "is_new_equip", "sex")
	kc.CheckOmitEmpty(getPersonaResp{}, "is_new_user", "is_new_equip")
}

func TestNewGetPersonaParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/get-persona"

	// 测试参数为空
	var param addPersonaPointParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newGetPersonaParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取正常接口参数
	param.EquipID = testEquipID
	param.UserID = testUserID
	c = handler.NewRPCTestContext(api, param)
	res, err := newGetPersonaParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(param.EquipID, res.EquipID)
	assert.EqualValues(param.UserID, res.UserID)
}

func TestActionGetPersona(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/user/get-persona"

	// 测试用户设备画像不存在
	equipID := "test_equip_id"
	require.NoError(persona.Persona{}.DB().Delete("", "equip_id = ?", equipID).Error)
	param := getPersonaParam{
		EquipID: testEquipID,
		BUVID:   "test_buvid",
	}
	c := handler.NewRPCTestContext(api, param)
	res, err := ActionGetPersona(c)
	require.NoError(err)
	data, ok := res.(getPersonaResp)
	require.True(ok)
	assert.EqualValues(persona.TypeGirl, data.Persona)
	assert.True(*data.IsNewEquip)
	assert.EqualValues(0, data.Sex)
	var equipPersona persona.Persona
	require.NoError(equipPersona.DB().Where("equip_id = ?", param.EquipID).Take(&equipPersona).Error)
	assert.EqualValues(data.Persona, equipPersona.Persona)

	// 测试获取用设备画像
	updates := map[string]interface{}{
		"persona": persona.TypeGirl | persona.MaskStrategyLikeSounds,
		"points":  "{\"7\":2,\"9\":5}",
	}
	require.NoError(equipPersona.DB().Updates(updates).Error)
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionGetPersona(c)
	require.NoError(err)
	data, ok = res.(getPersonaResp)
	require.True(ok)
	assert.EqualValues(0, data.Sex)
	assert.EqualValues(persona.TypeOtome|persona.MaskStrategyLikeSounds, data.Persona)
	assert.False(*data.IsNewEquip)
	// 设备画像被更新为由画像分区分的画像
	var equipPersona1 persona.Persona
	require.NoError(equipPersona.DB().Where("equip_id = ?", equipID).Take(&equipPersona1).Error)
	assert.EqualValues(data.Persona, equipPersona1.Persona)

	// 测试用户画像不存在
	field := strconv.FormatInt(testUserID, 10)
	keyOtome := keys.KeyUserPersonaPoints1.Format(persona.TypeOtome)
	require.NoError(service.Redis.HDel(keyOtome, field).Err())
	keyFujoshi := keys.KeyUserPersonaPoints1.Format(persona.TypeFujoshi)
	require.NoError(service.Redis.HDel(keyFujoshi, field).Err())
	require.NoError(service.Redis.HSet(keyOtome, field, 9).Err())
	require.NoError(persona.Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	param.UserID = testUserID
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionGetPersona(c)
	require.NoError(err)
	data, ok = res.(getPersonaResp)
	require.True(ok)
	assert.EqualValues(persona.TypeOtome|persona.MaskStrategyLikeSounds, data.Persona)
	assert.True(*data.IsNewUser)
	assert.EqualValues(1, data.Sex)
	var userPersona persona.Persona
	require.NoError(userPersona.DB().Where("user_id = ?", testUserID).Take(&userPersona).Error)
	assert.EqualValues(data.Persona, userPersona.Persona)
	assert.EqualValues(14, userPersona.PointMap[persona.TypeOtome])

	// 测试获取用户画像
	db := persona.Persona{}.DB().Where("user_id = ?", testUserID).Update("points", "{\"7\":2,\"9\":4}")
	require.NoError(db.Error)
	require.EqualValues(1, db.RowsAffected)
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionGetPersona(c)
	require.NoError(err)
	data, ok = res.(getPersonaResp)
	require.True(ok)
	assert.EqualValues(persona.TypeGirl|persona.MaskStrategyLikeSounds, data.Persona)
	assert.False(*data.IsNewUser)
	assert.EqualValues(1, data.Sex)
	var userPersona1 persona.Persona
	require.NoError(userPersona1.DB().Where("user_id = ?", testUserID).Take(&userPersona1).Error)
	assert.EqualValues(data.Persona, userPersona1.Persona)

	// 测试提供不存在的用户 ID
	param.UserID = int64(999)
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionGetPersona(c)
	require.NoError(err)
	data, ok = res.(getPersonaResp)
	require.True(ok)
	assert.EqualValues(0, data.Sex)
}
