package comment

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/comment/mcommentad"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
)

// adInfoParam 获取评论区小黄条广告信息接口的参数
type adInfoParam struct {
	OS          goutil.Platform `json:"os"`           // 访问来源。1: Android; 2: iOS; 3: Web
	ElementType int             `json:"element_type"` // 元素类型。3：音频
	ElementID   int64           `json:"element_id"`   // 元素 ID

	adInfo *mcommentad.AdInfo
}

// commentAd 评论区小黄条广告信息
type commentAd struct {
	ID          int64  `json:"id"`           // 评论区小黄条广告 ID
	Title       string `json:"title"`        // 广告标题
	URL         string `json:"url"`          // 广告跳转链接
	ElementType int    `json:"element_type"` // 元素类型。2: 剧集
	ElementID   int64  `json:"element_id"`   // 元素 ID
}

// adInfoResp 获取评论区小黄条广告信息接口的返回值
type adInfoResp struct {
	CommentAd *commentAd `json:"comment_ad"` // 广告信息
}

// ActionGetAdInfo 获取评论区小黄条广告信息
/**
 * @api {post} /rpc/missevan-main/comment/get-ad-info 获取评论区小黄条广告信息
 *
 * @apiVersion 0.1.0
 * @apiName get-ad-info
 * @apiGroup /rpc/missevan-main/comment
 *
 * @apiParam {Number} os 访问来源。1: Android; 2: iOS; 3: Web
 * @apiParam {Number} element_type 元素类型。3: 音频
 * @apiParam {Number} element_id 元素 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "comment_ad": {
 *           "id": 1, // 小黄条广告 ID
 *           "title": "惊！超好听的广播剧~",
 *           "url": "http://www.test.com/drama/1", // 跳转链接
 *           "element_type": 2, // 广告关联元素类型。2: 剧集
 *           "element_id": 233 // 广告关联元素 ID
 *         }
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGetAdInfo(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newAdParam(c)
	if err != nil {
		return nil, err
	}
	if param.adInfo == nil {
		return adInfoResp{CommentAd: nil}, nil
	}
	url := ""
	switch param.OS {
	case goutil.Web:
		url = param.adInfo.WebURL
	default:
		// 目前仅客户端和 Web 会请求下发，默认下发 App 端地址
		url = param.adInfo.AppURL
	}
	if url == "" {
		// 若未配置相关跳转地址，则不下发
		return adInfoResp{CommentAd: nil}, nil
	}
	commentAdInfo := &commentAd{
		ID:          param.adInfo.ID,
		Title:       param.adInfo.Title,
		URL:         url,
		ElementType: param.adInfo.ElementType,
		ElementID:   param.adInfo.ElementID,
	}
	return adInfoResp{CommentAd: commentAdInfo}, nil
}

func newAdParam(c *handler.Context) (*adInfoParam, error) {
	var param adInfoParam
	err := c.BindJSON(&param)
	allowOS := []goutil.Platform{goutil.Android, goutil.IOS, goutil.Web, goutil.HarmonyOS}
	if err != nil || param.ElementID <= 0 || !goutil.HasElem(allowOS, param.OS) {
		return nil, actionerrors.ErrParams
	}
	if param.ElementType == mcommentad.ElementTypeSound {
		// 目前单音未直接关联小黄条广告，需要获取其所在剧集的小黄条广告
		dramaID, err := dramainfo.GetDramaIDBySoundID(param.ElementID)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if dramaID == 0 {
			// 单音未关联剧集
			return &param, nil
		}
		param.adInfo, err = mcommentad.FindCommentAd(mcommentad.ElementTypeDrama, dramaID)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		return &param, nil
	}
	// TODO: 之后可能需要支持其他元素类型的小黄条广告
	return &param, nil
}
