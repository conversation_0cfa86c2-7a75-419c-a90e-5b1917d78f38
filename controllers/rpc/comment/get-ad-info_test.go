package comment

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/comment/mcommentad"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestActionGetAdInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uri := "/rpc/missevan-main/comment/get-ad-info"
	// 测试参数错误
	c := handler.NewRPCTestContext(uri, adInfoParam{
		OS:          5,
		ElementType: mcommentad.ElementTypeSound,
		ElementID:   1996,
	})
	result, err := ActionGetAdInfo(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	// 测试正常返回
	c = handler.NewRPCTestContext(uri, adInfoParam{
		OS:          goutil.Android,
		ElementType: mcommentad.ElementTypeSound,
		ElementID:   19996,
	})
	result, err = ActionGetAdInfo(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok := result.(adInfoResp)
	require.True(ok)
	exceptAdResp := adInfoResp{
		CommentAd: &commentAd{
			ID:          1001,
			Title:       "测试小黄条",
			URL:         "missevan://test/3",
			ElementType: mcommentad.ElementTypeDrama,
			ElementID:   3000,
		},
	}
	assert.Equal(exceptAdResp, resp)

	// 测试没有 OS 对应的 url 时不下发
	c = handler.NewRPCTestContext(uri, adInfoParam{
		OS:          goutil.Web,
		ElementType: mcommentad.ElementTypeSound,
		ElementID:   19996,
	})
	require.NoError(mcommentad.MCommentAd{}.DB().Where("id = ?", 1001).
		Update("web_url", "").Error)
	result, err = ActionGetAdInfo(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok = result.(adInfoResp)
	require.True(ok)
	exceptAdResp = adInfoResp{
		CommentAd: nil,
	}
	assert.Equal(exceptAdResp, resp)

	// 测试没有符合条件的小黄条广告
	c = handler.NewRPCTestContext(uri, adInfoParam{
		OS:          goutil.Android,
		ElementType: mcommentad.ElementTypeSound,
		ElementID:   19997,
	})
	result, err = ActionGetAdInfo(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok = result.(adInfoResp)
	require.True(ok)
	assert.Equal(exceptAdResp, resp)

	// 测试单音未关联剧集
	c = handler.NewRPCTestContext(uri, adInfoParam{
		OS:          goutil.Android,
		ElementType: mcommentad.ElementTypeSound,
		ElementID:   1998,
	})
	result, err = ActionGetAdInfo(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok = result.(adInfoResp)
	require.True(ok)
	assert.Equal(exceptAdResp, resp)
}
