package comment

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/memoteexclusiveelement"
	"github.com/MiaoSiLa/missevan-main/models/soundcomment"
)

const testExclusiveEmoteDramaID = 52347

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(emoteExclusiveParam{}, "element_id", "element_type")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(emoteExclusiveRespItem{}, "package_id", "status", "tip", "start_time", "end_time")
	kc.Check(emoteExclusiveResp{}, "emotes")

	kc.CheckOmitEmpty(emoteExclusiveRespItem{}, "tip", "start_time", "end_time")
}

func TestActionExclusiveEmote(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试错误参数
	api := "/rpc/missevan-main/comment/exclusive-emote"
	c := handler.NewRPCTestContext(api, nil)
	_, err := ActionExclusiveEmote(c)
	assert.EqualError(err, "参数错误")

	// 获取未解锁的专属表情包
	param := emoteExclusiveParam{
		ElementID:   1,
		ElementType: soundcomment.TypeSound,
	}
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionExclusiveEmote(c)
	require.NoError(err)
	data, ok := res.(emoteExclusiveResp)
	require.True(ok)
	assert.EqualValues(2333, data.Emotes[0].PackageID)
	assert.Equal(memoteexclusiveelement.StatusLocked, data.Emotes[0].Status)
	assert.Equal("测试 tip", data.Emotes[0].Tip)

	// 获取解锁的专属表情包
	more := memoteexclusiveelement.More{
		Unlock:      false,
		UnlockScore: 2,
		Tip:         "测试 tip",
	}
	encodeMore, err := json.Marshal(more)
	require.NoError(err)
	endTime := util.TimeNow().AddDate(0, 0, 15).Unix()
	updates := map[string]interface{}{
		"more":     string(encodeMore),
		"end_time": endTime,
	}
	require.NoError(memoteexclusiveelement.MEmoteExclusiveElement{}.DB().Where("package_id = ?", data.Emotes[0].PackageID).Updates(updates).Error)
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionExclusiveEmote(c)
	require.NoError(err)
	data, ok = res.(emoteExclusiveResp)
	require.True(ok)
	assert.Equal(memoteexclusiveelement.StatusUnlock, data.Emotes[0].Status)
	assert.Equal(endTime, *data.Emotes[0].EndTime)
	assert.Nil(data.Emotes[0].StartTime)
	emote := new(memoteexclusiveelement.MEmoteExclusiveElement)
	require.NoError(emote.DB().Where("package_id = ?", data.Emotes[0].PackageID).Take(emote).Error)
	assert.True(emote.MoreInfo.Unlock)

	// 测试获取其他评论类型的专属表情
	param.ElementType = soundcomment.TypeAlbum
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionExclusiveEmote(c)
	require.NoError(err)
	data, ok = res.(emoteExclusiveResp)
	require.True(ok)
	assert.NotNil(data.Emotes)
	assert.Empty(data.Emotes)
}

func TestEmoteExclusiveParam_findExclusiveEmote(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := emoteExclusiveParam{
		ElementID:   1,
		ElementType: soundcomment.TypeSound,
	}
	err := param.findExclusiveEmote()
	require.NoError(err)
	require.NotEmpty(param.emotes)
	assert.EqualValues(testExclusiveEmoteDramaID, param.emotes[0].ElementID)
}

func TestEmoteExclusiveParam_checkEmoteExclusiveUnlocked(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	nowUnix := util.TimeNow().Unix()
	param := &emoteExclusiveParam{
		emotes: []*memoteexclusiveelement.MEmoteExclusiveElement{
			{
				ID:           1,
				ModifiedTime: nowUnix,
				MoreInfo: memoteexclusiveelement.More{
					Unlock:         false,
					UnlockScore:    233,
					ExpireDuration: 60,
				},
			},
		},
		dramaID:          testExclusiveEmoteDramaID,
		emoteElementType: memoteexclusiveelement.ElementTypeDrama,
	}
	// 测试未解锁专属表情
	err := param.checkEmoteExclusiveUnlocked()
	require.NoError(err)
	assert.False(param.emotes[0].MoreInfo.Unlock)

	// 测试解锁专属表情
	more := memoteexclusiveelement.More{
		Unlock:      false,
		UnlockScore: 2,
		Tip:         "测试 tip",
	}
	encodeMore, err := json.Marshal(more)
	require.NoError(err)
	updates := map[string]interface{}{
		"modified_time": nowUnix,
		"more":          string(encodeMore),
	}
	require.NoError(memoteexclusiveelement.MEmoteExclusiveElement{}.DB().Where("id = ?", param.emotes[0].ID).Updates(updates).Error)
	param.emotes[0].MoreInfo.UnlockScore = 2
	err = param.checkEmoteExclusiveUnlocked()
	require.NoError(err)
	assert.True(param.emotes[0].MoreInfo.Unlock)
	emote := new(memoteexclusiveelement.MEmoteExclusiveElement)
	require.NoError(emote.DB().Where("id = ?", param.emotes[0].ID).Take(emote).Error)
	assert.NotZero(emote.StartTime)
	assert.Equal(util.BeginningOfDay(time.Unix(emote.StartTime, 0)).
		Add(time.Duration(param.emotes[0].MoreInfo.ExpireDuration)*time.Second).Unix(), emote.EndTime)
	assert.True(emote.MoreInfo.Unlock)
}

func TestEmoteExclusiveParam_buildResp(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := emoteExclusiveParam{
		emotes: []*memoteexclusiveelement.MEmoteExclusiveElement{
			{
				PackageID: 2333,
				StartTime: util.TimeNow().Unix(),
				MoreInfo: memoteexclusiveelement.More{
					Unlock: false,
					Tip:    "测试 tip",
				},
			},
		},
	}
	require.NoError(param.buildResp())
	assert.Equal(memoteexclusiveelement.StatusLocked, param.resp.Emotes[0].Status)
	assert.Equal(param.emotes[0].MoreInfo.Tip, param.resp.Emotes[0].Tip)
	assert.NotNil(param.resp.Emotes[0].StartTime)
	assert.Nil(param.resp.Emotes[0].EndTime)
}
