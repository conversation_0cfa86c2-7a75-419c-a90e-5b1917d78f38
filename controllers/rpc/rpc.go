package rpc

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc/comment"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc/cron"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc/drama"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc/person"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc/rank"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc/sound"
	"github.com/MiaoSiLa/missevan-main/controllers/rpc/user"
)

// Handler returns the registered handler
func Handler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "rpc/missevan-main",
		Middlewares: gin.HandlersChain{
			rpc.Middleware(conf.HTTP.RPCKey),
		},
		SubHandlers: []handler.Handler{
			soundHandler(),
			commentHandler(),
			userHandler(),
			personHandler(),
			dramaHandler(),
			rankHandler(),
		},
		Actions: map[string]*handler.Action{
			"/echo": handler.NewAction(handler.POST, actionEcho, false),
		},
	}
}

func actionEcho(c *handler.Context) (handler.ActionResponse, error) {
	var s interface{}
	err := c.BindJSON(&s)
	return s, err
}

func soundHandler() handler.Handler {
	sound := handler.Handler{
		Name: "sound",
		Actions: map[string]*handler.Action{
			"get-recommend": handler.NewAction(handler.POST, sound.ActionGetRecommend, false),
		},
	}
	return sound
}

func commentHandler() handler.Handler {
	comment := handler.Handler{
		Name: "comment",
		Actions: map[string]*handler.Action{
			"get-ad-info":     handler.NewAction(handler.POST, comment.ActionGetAdInfo, false),
			"exclusive-emote": handler.NewAction(handler.POST, comment.ActionExclusiveEmote, false),
		},
	}
	return comment
}

func userHandler() handler.Handler {
	return handler.Handler{
		Name: "user",
		Actions: map[string]*handler.Action{
			"set-persona":       handler.NewAction(handler.POST, user.ActionSetPersona, false),
			"add-persona-point": handler.NewAction(handler.POST, user.ActionAddPersonaPoint, false),
			"get-persona":       handler.NewAction(handler.POST, user.ActionGetPersona, false),
		},
	}
}

func personHandler() handler.Handler {
	return handler.Handler{
		Name: "person",
		Actions: map[string]*handler.Action{
			"send-badge":        handler.NewAction(handler.POST, person.ActionSendBadge, false),
			"list-badge":        handler.NewAction(handler.POST, person.ActionListBadge, false),
			"get-badge":         handler.NewAction(handler.POST, person.ActionGetBadge, false),
			"send-avatar-frame": handler.NewAction(handler.POST, person.ActionSendAvatarFrame, false),
			"list-avatar-frame": handler.NewAction(handler.POST, person.ActionListAvatarFrame, false),
		},
	}
}

func dramaHandler() handler.Handler {
	return handler.Handler{
		Name: "drama",
		Actions: map[string]*handler.Action{
			"get-corner-mark":     handler.NewAction(handler.POST, drama.ActionGetCornerMark, false),
			"get-drama-info":      handler.NewAction(handler.POST, drama.ActionGetDramaInfo, false),
			"get-user-lastviewed": handler.NewAction(handler.POST, drama.ActionGetUserLastviewed, false),
		},
	}
}

func rankHandler() handler.Handler {
	return handler.Handler{
		Name: "rank",
		Actions: map[string]*handler.Action{
			"list": handler.NewAction(handler.POST, rank.ActionRankList, false),
		},
	}
}

// HandlerV2 returns the registered handlerV2
func HandlerV2(conf *config.Config) handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "rpc/missevan-main",
		Middlewares: gin.HandlersChain{
			rpc.Middleware(conf.HTTP.RPCKey),
		},
		SubHandlers: []handler.HandlerV2{
			userHandlerV2(),
			dramaHandlerV2(),
			cronHandlerV2(),
			personHandlerV2(),
			soundHandlerV2(),
		},
	}
}

func userHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "user",
		Actions: map[string]*handler.ActionV2{
			"mark-page-viewed": handler.NewActionV2(handler.POST, user.ActionMarkPageViewed, handler.ActionOption{LoginRequired: false}),
			"notify":           handler.NewActionV2(handler.POST, user.ActionNotify, handler.ActionOption{LoginRequired: false}),
			"get-auth-info":    handler.NewActionV2(handler.POST, user.ActionGetAuthInfo, handler.ActionOption{LoginRequired: false}),
			"get-vip-user-ids": handler.NewActionV2(handler.POST, user.ActionGetVipUserIDs, handler.ActionOption{LoginRequired: false}),
			"get-user-config":  handler.NewActionV2(handler.POST, user.ActionGetUserConfig, handler.ActionOption{LoginRequired: false}),
		},
	}
}

func dramaHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "drama",
		Actions: map[string]*handler.ActionV2{
			"get-user-drama-cvs":       handler.NewActionV2(handler.POST, drama.ActionGetUserDramaCvs, handler.ActionOption{LoginRequired: false}),
			"get-drama-episodes":       handler.NewActionV2(handler.POST, drama.ActionGetDramaEpisodes, handler.ActionOption{LoginRequired: false}),
			"get-drama-info-by-sounds": handler.NewActionV2(handler.POST, drama.ActionGetDramaInfoBySounds, handler.ActionOption{LoginRequired: false}),
		},
	}
}

func cronHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "cron",
		Actions: map[string]*handler.ActionV2{
			"take-off-avatar-frame": handler.NewActionV2(handler.POST, cron.ActionTakeOffAvatarFrame, handler.ActionOption{LoginRequired: false}),
			"send-vip-renewal-sms":  handler.NewActionV2(handler.POST, cron.ActionSendVipRenewalSms, handler.ActionOption{LoginRequired: false}),
		},
	}
}

func personHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "person",
		Actions: map[string]*handler.ActionV2{
			"get-theme-skin": handler.NewActionV2(handler.POST, person.ActionGetThemeSkin, handler.ActionOption{LoginRequired: false}),
			"get-vip-wears":  handler.NewActionV2(handler.POST, person.ActionGetVipWears, handler.ActionOption{LoginRequired: false}),
		},
	}
}

func soundHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "sound",
		Actions: map[string]*handler.ActionV2{
			"initial-pass-sounds": handler.NewActionV2(handler.POST, sound.ActionInitialPassSounds, handler.ActionOption{LoginRequired: false}),
		},
	}
}
