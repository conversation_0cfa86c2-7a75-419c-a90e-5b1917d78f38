package sound

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/sound"
)

type recommendParams struct {
	SoundID int64 `json:"sound_id"` // 音频 ID
	Num     int64 `json:"num"`      // 推荐音频数量
}

type recommendResp struct {
	Sounds []sound.RecommendSound `json:"sounds"`
}

// ActionGetRecommend 获取推荐音频信息
/**
 * @api {post} /rpc/missevan-main/sound/get-recommend 获取推荐音频信息
 * @apiVersion 0.1.0
 * @apiName get-recommend
 * @apiGroup rpc/missevan-main/sound
 *
 * @apiParam {Number} sound_id 音频 ID
 * @apiParam {Number} num 需要推荐的音频数量
 *
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "sounds": [{
 *           "id": 1099295,
 *           "soundstr": "【天官赐福】若花怜蝶【剧情版】",
 *           "view_count": 323983,
 *           "all_comments": 8121, // 总互动数（总评论数 + 弹幕数）
 *           "front_cover": "https://static-test.maoercdn.com/coversmini/201810/22/test.jpg",
 *           "video": false, // 是否绑定了视频
 *           "strategy_id": 3 // 推荐策略 ID
 *         }]
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGetRecommend(c *handler.Context) (handler.ActionResponse, error) {
	var param recommendParams
	err := c.BindJSON(&param)
	if err != nil || param.SoundID <= 0 || param.Num <= 0 {
		return nil, actionerrors.ErrParams
	}
	recommends, err := sound.FindRecommendSounds(param.SoundID, param.Num)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return recommendResp{Sounds: recommends}, nil
}
