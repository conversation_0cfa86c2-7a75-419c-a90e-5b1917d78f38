package sound

import (
	"fmt"
	"html"
	"math"
	"regexp"
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	muser "github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/drama/checkeddramareview"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/sound"
	msound "github.com/MiaoSiLa/missevan-main/models/sound"
	"github.com/MiaoSiLa/missevan-main/models/sound/msoundaddendum"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/dramaapi"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

const soundMaxThreshold = 10

type soundsItem struct {
	SoundID        int64 `json:"sound_id"`
	OperatorUserID int64 `json:"operator_user_id"`
}

type initialPassSoundsParams struct {
	Sounds []soundsItem `json:"sounds"`

	soundInfos            []msound.MSound
	soundIDUserIDMap      map[int64]soundsItem                     // 音频 ID 对应操作人 ID map
	soundIDDramaIDMap     map[int64]int64                          // 音频 ID 对应的剧集 ID map
	dramaIDCheckStatusMap map[int64]checkeddramareview.CheckStatus // 剧集 ID 对应的审核信息 map
}

type initialPassSoundsResp struct {
	SuccessReleaseSoundIDs []int64 `json:"success_release_sound_ids"`
}

// GetDramasBySoundIDs 根据音频 IDs 获取剧集信息
func (param *initialPassSoundsParams) GetDramasBySoundIDs(soundIDs []int64) (err error) {
	param.soundIDDramaIDMap, err = dramainfo.GetDramaIDsBySoundIDs(soundIDs)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(param.soundIDDramaIDMap) == 0 {
		return nil
	}
	dramaIDs := make([]int64, 0, len(param.soundIDDramaIDMap))
	for _, dramaID := range param.soundIDDramaIDMap {
		dramaIDs = append(dramaIDs, dramaID)
	}

	param.dramaIDCheckStatusMap, err = checkeddramareview.GetDramaCheckStatus(sets.Uniq(dramaIDs))
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	return nil
}

func newInitialPassSoundsParams(c *handler.Context) (*initialPassSoundsParams, error) {
	param := new(initialPassSoundsParams)
	err := c.Bind(&param)
	if err != nil || len(param.Sounds) == 0 {
		return nil, actionerrors.ErrParams
	}
	// 获取接口音频 ID 列表
	soundIDs := make([]int64, 0, len(param.Sounds))
	for _, soundInfo := range param.Sounds {
		soundIDs = append(soundIDs, soundInfo.SoundID)
	}

	// 查询音频信息
	var sounds []msound.MSound
	err = msound.MSound{}.DB().Where("id IN (?)", soundIDs).Find(&sounds).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	needPaySoundIDs := make([]int64, 0, len(sounds))
	param.soundInfos = make([]msound.MSound, 0, len(sounds))
	for _, soundInfo := range sounds {
		if soundInfo.Checked != msound.CheckedUnpass {
			logger.WithField("sound_id", soundInfo.ID).Errorf("定时过审音频时，音频审核状态异常：%d", soundInfo.Checked)
			continue
		}
		if soundInfo.PayType == sound.PayBySound || soundInfo.PayType == sound.PayByDrama {
			needPaySoundIDs = append(needPaySoundIDs, soundInfo.ID)
		}
		param.soundInfos = append(param.soundInfos, soundInfo)
	}
	if len(param.soundInfos) == 0 {
		return param, nil
	}

	if len(needPaySoundIDs) > 0 {
		if err = param.GetDramasBySoundIDs(needPaySoundIDs); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}

	sounds = make([]msound.MSound, 0, len(soundIDs))
	for _, soundInfo := range param.soundInfos {
		if soundInfo.PayType == sound.PayBySound || soundInfo.PayType == sound.PayByDrama {
			dramaID, ok := param.soundIDDramaIDMap[soundInfo.ID]
			if !ok {
				// 剧集添加单集还没过审（为再审状态）
				logger.WithFields(logger.Fields{
					"sound_id": soundInfo.ID,
				}).Error("定时过审音频时，音频对应的付费剧集还未过再审")
				continue
			}
			checkStatus, ok := param.dramaIDCheckStatusMap[dramaID]
			if !ok {
				logger.WithFields(logger.Fields{
					"drama_id": dramaID,
				}).Error("定时过审音频时，剧集不存在")
				continue
			}
			if checkStatus.Checked == dramainfo.CheckedPass && checkStatus.ReviewDramaID != 0 {
				logger.WithFields(logger.Fields{
					"sound_id": soundInfo.ID,
					"drama_id": dramaID,
				}).Error("定时过审音频时，音频对应的付费剧集还未过再审")
				continue
			}
		}
		sounds = append(sounds, soundInfo)
	}
	param.soundInfos = sounds
	param.soundIDUserIDMap = util.ToMap(param.Sounds, "SoundID").(map[int64]soundsItem)
	return param, nil
}

var censorshipPattern = regexp.MustCompile(`(不要|不能|禁止|不|别|勿)过审`)

// ActionInitialPassSounds 批量过审音频
/**
 * @api {post} /rpc/missevan-main/sound/initial-pass-sounds 批量过审音频
 * @apiVersion 0.1.0
 * @apiName initial-pass-sounds
 * @apiGroup rpc/missevan-main/sound
 *
 * @apiParam {Object[]} sounds 音频信息数组
 * @apiParam {Number} sounds.user_id 操作人 ID
 * @apiParam {Number} sounds.operator_user_id 音频 ID
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "sounds": [
 *         {
 *           "operator_user_id": 987654321,
 *           "sound_id": "2333",
 *         },
 *         {
 *           "operator_user_id": 987654322,
 *           "sound_id": "2334"
 *         }
 *       ]
 *     }
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "message": "通过审核成功",
 *       "data": {
 *         "success_release_sound_ids": [12223, 345435]
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null

 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} message 服务器内部错误
 * @apiError (500) {Object} data null
 */
func ActionInitialPassSounds(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newInitialPassSoundsParams(c)
	if err != nil {
		return nil, "", err
	}
	if len(param.soundInfos) == 0 {
		return nil, "音频都已过审", nil
	}

	soundNum := len(param.soundInfos)
	if soundNum >= soundMaxThreshold {
		// 当数量过多可能导致接口性能变差，加上日志方便判断是否需要优化接口性能
		logger.Warnf("定时上线同时上线音频数量超过 %d，当前数量为 %d", soundMaxThreshold, soundNum)
	}
	box := goclient.NewRPCAdminLogBox(c)
	personHomepageKeys := make([]string, 0, len(param.soundInfos))
	systemMessages := make([]pushservice.SystemMsg, 0, len(param.soundInfos))
	resp := initialPassSoundsResp{
		SuccessReleaseSoundIDs: make([]int64, 0, len(param.soundInfos)),
	}
	for _, soundInfo := range param.soundInfos {
		if matches := censorshipPattern.FindStringSubmatch(soundInfo.Soundstr); len(matches) > 0 {
			// TODO: 发送 bot 消息通知
			logger.WithField("sound_id", soundInfo.ID).Errorf("过审音频失败，请与相关方核实上线时间，上线时需先手动去掉“%s”关键字", matches[0])
			continue
		}
		// 过审音频
		err = passSound(soundInfo)
		if err != nil {
			logger.WithField("sound_id", soundInfo.ID).Error(err)
			continue
		}
		resp.SuccessReleaseSoundIDs = append(resp.SuccessReleaseSoundIDs, soundInfo.ID)

		// 音频过审后对剧集的操作
		_, err = dramaapi.SoundCheck(c.UserContext(), soundInfo.ID, msound.CheckedPass, dramaapi.OperationPassSound)
		if err != nil {
			logger.WithField("sound_id", soundInfo.ID).Errorf("请求 rpc/drama/sound-check 接口失败，原因：%v", err)
			// PASS
		}
		// 构建系统消息
		soundURL := config.Conf.Params.URL.Main + "sound/" + strconv.FormatInt(soundInfo.ID, 10)
		content := fmt.Sprintf("您的稿件《%s》（音频 ID：%d）已经通过审核啦，<a target=\"_blank\" title=\"(～ o ～)~zZ\" href=\"%s\">点击查看</a>。",
			html.EscapeString(soundInfo.Soundstr), soundInfo.ID, soundURL)
		systemMessages = append(systemMessages, pushservice.SystemMsg{
			UserID:  soundInfo.UserID,
			Title:   "您的稿件已通过审核",
			Content: content,
		})
		// 构建后台操作记录
		intro := fmt.Sprintf("审核通过音频，音频 ID: %d，名称：%s，用户 ID: %d", soundInfo.ID, soundInfo.Soundstr, soundInfo.UserID)
		box.Add(adminlogger.CatalogPass, intro, goclient.AdminLogOptions{
			ChannelID: util.NewInt64(soundInfo.ID),
			UserID:    util.NewInt64(param.soundIDUserIDMap[soundInfo.ID].OperatorUserID),
		})
		// 构建 App 个人主页缓存 key
		personHomepageKeys = append(personHomepageKeys, keys.KeyPersonHomepage1.Format(soundInfo.UserID))
	}
	// 发送系统通知
	if len(systemMessages) > 0 {
		if err = service.PushService.SendSystemMsg(systemMessages); err != nil {
			logger.Error(err)
			// PASS
		}
	}
	// 音频过审后，删除 App 个人主页缓存
	if len(personHomepageKeys) > 0 {
		if err = service.Redis.Del(sets.Uniq(personHomepageKeys)...).Err(); err != nil {
			logger.WithField("keys", personHomepageKeys).Error(err)
			// PASS
		}
	}
	// 后台操作记录
	if err = box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return resp, "", nil
}

func passSound(soundInfo msound.MSound) error {
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		nowUnix := util.TimeNow().Unix()
		// 音频状态修改为过审
		err := tx.Table(msound.MSound{}.TableName()).
			Where("id = ?", soundInfo.ID).Updates(map[string]interface{}{
			"checked":          msound.CheckedPass,
			"last_update_time": nowUnix,
		}).Error
		if err != nil {
			return err
		}

		// 音频审核通过后，记录审核通过时间
		err = msoundaddendum.UpdateFirstAuditPassTime(tx, soundInfo.ID, nowUnix)
		if err != nil {
			return err
		}

		// 更新用户信息
		u, err := muser.FindByUserID(soundInfo.UserID)
		if err != nil {
			return err
		}
		if u == nil {
			return fmt.Errorf("用户不存在，用户 ID: %d", soundInfo.UserID)
		}
		updates := map[string]interface{}{
			// 个人过审核音频数 +1
			"soundnumchecked": gorm.Expr("soundnumchecked + 1"),
			// 音频总时长加上此音时长
			"nowsound": gorm.Expr("nowsound + ?", soundInfo.Duration),
		}
		// 自动提升等级
		oneMinuteMs := util.SecondOneMinute * 1000
		nowSoundMinutes := math.Ceil(float64(u.NowSound+soundInfo.Duration) / float64(oneMinuteMs))
		leftTime := u.MLevel*muser.MinutesOfLevel - int(nowSoundMinutes)
		if leftTime <= 0 && u.MLevel < muser.MaxLevel {
			nowMLevel := math.Ceil(nowSoundMinutes / muser.MinutesOfLevel)
			updates["mlevel"] = nowMLevel
		}
		return tx.Table(muser.MowangskUser{}.TableName()).Where("id = ?", u.ID).Updates(updates).Error
	})
}
