package sound

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	msound "github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service/dramaapi"
)

func TestInitialPassSoundsTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(initialPassSoundsParams{}, "sounds")
	kc.Check(initialPassSoundsResp{}, "success_release_sound_ids")
	kc.Check(soundsItem{}, "sound_id", "operator_user_id")
}

func TestNewInitialPassSoundsParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取接口参数
	c := handler.NewRPCTestContext("/rpc/missevan-main/sound/initial-pass-sounds", initialPassSoundsParams{
		Sounds: []soundsItem{
			{
				SoundID:        1217775,
				OperatorUserID: 1,
			},
			{
				SoundID:        1217776,
				OperatorUserID: 2,
			},
			{
				SoundID:        1217777,
				OperatorUserID: 3,
			},
			{
				SoundID:        1217778,
				OperatorUserID: 3,
			},
		},
	})
	result, err := newInitialPassSoundsParams(c)
	require.NoError(err)
	require.Len(result.soundInfos, 1)
	assert.EqualValues(1217778, result.soundInfos[0].ID)
}

func TestActionInitialPassSounds(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试音频通过审核
	cleanSoundCheck := mrpc.SetMock(dramaapi.URIDramaSoundCheck, func(input interface{}) (interface{}, error) {
		i, ok := input.(map[string]any)
		require.True(ok)
		soundID, ok := i["sound_id"].(int64)
		require.True(ok)
		assert.EqualValues(1217778, soundID)
		soundChecked, ok := i["sound_checked"].(int)
		require.True(ok)
		assert.EqualValues(msound.CheckedPass, soundChecked)
		operation, ok := i["operation"].(int)
		require.True(ok)
		assert.EqualValues(dramaapi.OperationPassSound, operation)

		return dramaapi.SoundCheckResp{
			Update: dramaapi.SoundCheckUpdateInfo{
				IPR:    true,
				Newest: true,
				IsSaw:  true,
			},
		}, nil
	})
	defer cleanSoundCheck()
	cleanSysPush := mrpc.SetMock("pushservice://api/systemmsg", func(input any) (any, error) {
		i, ok := input.(map[string]any)
		require.True(ok)
		msg, ok := i["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(msg)
		assert.Equal("您的稿件已通过审核", msg[0].Title)
		assert.EqualValues(12, msg[0].UserID)
		assert.Equal("您的稿件《测试推荐音频》（音频 ID：1217778）已经通过审核啦，<a target=\"_blank\" title=\"(～ o ～)~zZ\" href=\"https://www.missevan.com/sound/1217778\">点击查看</a>。", msg[0].Content)
		return "success", nil
	})
	defer cleanSysPush()
	called := false
	cancelAddadminlog := mrpc.SetMock("go://util/addadminlog", func(input any) (output any, err error) {
		called = true
		return "success", nil
	})
	defer cancelAddadminlog()
	c := handler.NewRPCTestContext("/rpc/missevan-main/sound/initial-pass-sounds", initialPassSoundsParams{
		Sounds: []soundsItem{
			{
				SoundID:        1217775,
				OperatorUserID: 1,
			},
			{
				SoundID:        1217776,
				OperatorUserID: 2,
			},
			{
				SoundID:        1217777,
				OperatorUserID: 3,
			},
			{
				SoundID:        1217778,
				OperatorUserID: 3,
			},
			{
				SoundID:        1217779,
				OperatorUserID: 3,
			},
		},
	})
	res, _, err := ActionInitialPassSounds(c)
	require.NoError(err)
	data, ok := res.(initialPassSoundsResp)
	require.True(ok)
	require.Len(data.SuccessReleaseSoundIDs, 1)
	assert.EqualValues(1217778, data.SuccessReleaseSoundIDs[0])
	assert.True(called)
	var m msound.MSound
	require.NoError(m.DB().Where("id = 1217778").Find(&m).Error)
	assert.Equal(msound.CheckedPass, m.Checked)
}
