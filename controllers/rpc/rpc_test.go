package rpc

import (
	"encoding/json"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/config"
)

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	conf := config.BuildDefaultConf()
	handler := Handler(&conf)

	assert.Equal(handler.Name, "rpc/missevan-main")
	assert.Equal(1, len(handler.Middlewares))
	assert.Equal(6, len(handler.SubHandlers))

	kc.Check(handler, "/echo")
}

func TestActionEcho(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := strings.NewReader(`{"t":1}`)
	c := handler.NewTestContext(http.MethodPost, "/echo", false, param)
	s, err := actionEcho(c)
	require.NoError(err)
	b, e := json.Marshal(s)
	require.NoError(e)
	assert.Equal(`{"t":1}`, string(b))
}

func TestSoundHandler(t *testing.T) {
	assert := assert.New(t)

	h := soundHandler()
	assert.Equal("sound", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "get-recommend")
}

func TestCommentHandler(t *testing.T) {
	assert := assert.New(t)

	h := commentHandler()
	assert.Equal("comment", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "get-ad-info", "exclusive-emote")
}

func TestUserHandler(t *testing.T) {
	assert := assert.New(t)

	h := userHandler()
	assert.Equal("user", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "set-persona", "add-persona-point", "get-persona")
}

func TestPersonHandler(t *testing.T) {
	assert := assert.New(t)

	h := personHandler()
	assert.Equal("person", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "send-badge", "list-badge", "get-badge", "send-avatar-frame", "list-avatar-frame")
}

func TestDramaHandler(t *testing.T) {
	assert := assert.New(t)

	h := dramaHandler()
	assert.Equal("drama", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "get-corner-mark", "get-drama-info", "get-user-lastviewed")
}

func TestRankHandler(t *testing.T) {
	assert := assert.New(t)

	h := rankHandler()
	assert.Equal("rank", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "list")
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	conf := config.BuildDefaultConf()
	handler := HandlerV2(&conf)

	assert.Equal(handler.Name, "rpc/missevan-main")
	assert.Equal(1, len(handler.Middlewares))
	assert.Equal(5, len(handler.SubHandlers))
	assert.Equal(5, len(handler.SubHandlers))
}

func TestUserHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := userHandlerV2()
	assert.Equal("user", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "mark-page-viewed", "notify", "get-auth-info", "get-vip-user-ids", "get-user-config")
	kc.Check(h.Actions, "mark-page-viewed", "notify", "get-auth-info", "get-user-config", "get-vip-user-ids")
}

func TestDramaHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := dramaHandlerV2()
	assert.Equal("drama", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "get-user-drama-cvs", "get-drama-episodes", "get-drama-info-by-sounds")
}

func TestPersonHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := personHandlerV2()
	assert.Equal("person", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "get-theme-skin", "get-vip-wears")
}

func TestSoundHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := soundHandlerV2()
	assert.Equal("sound", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "initial-pass-sounds")
}
