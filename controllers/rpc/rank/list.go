package rank

import (
	"slices"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/mhomepagerank"
)

type rankListParam struct {
	Type    int    `json:"type"`              // 榜单类型。1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜
	SubType int    `json:"sub_type"`          // 榜单周期类型。1: 日榜；2: 周榜；3: 月榜
	Bizdate string `json:"bizdate,omitempty"` // 查询榜单的业务日期，YYYY-mm-dd 格式，如：2024-08-07
}

type rankListResp struct {
	RankElementType int               `json:"rank_element_type"`       // 榜单元素类型，1：音频；2：剧集
	RankElementIDs  []int64           `json:"rank_element_ids"`        // 查询的榜单数据，按榜单里的先后顺序排序后的元素 ID（剧集 ID 或音频 ID）
	RankElements    []rankElementItem `json:"rank_elements,omitempty"` // 榜单数据详情，榜单不存在或榜单数据为空时不返回。对榜单元素类型是剧集的返回
	Bizdate         string            `json:"bizdate"`                 // 查询榜单的业务日期，YYYY-mm-dd 格式，如：2024-08-07
}

type rankElementItem struct {
	DramaID int64 `json:"drama_id"` // 剧集 ID
	IPRID   int64 `json:"ipr_id"`   // 剧集所属的 IP ID
}

func newRankListParam(c *handler.Context) (*rankListParam, error) {
	param := new(rankListParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.Bizdate != "" {
		if _, err = time.Parse(util.TimeFormatYMD, param.Bizdate); err != nil {
			return nil, actionerrors.ErrParams
		}
	}

	if !slices.Contains(mhomepagerank.TypeRankList, param.Type) ||
		!slices.Contains(mhomepagerank.SubTypeRankList, param.SubType) {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

// ActionRankList 获取榜单列表
/**
 * @api {post} /rpc/missevan-main/rank/list 获取榜单列表
 *
 * @apiVersion 0.1.0
 * @apiName list
 * @apiGroup /rpc/missevan-main/rank
 *
 * @apiParam {number{1-6}} type 榜单类型。1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜
 * @apiParam {number{1-3}} sub_type 榜单周期类型。1: 日榜；2: 周榜；3: 月榜
 * @apiParam {String} [bizdate] 查询榜单的业务日期，参数为空时查询最新榜单数据，YYYY-mm-dd 格式，如：2024-08-07
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "rank_element_type": 2, // 榜单元素类型，2：剧集；3：音频
 *         "rank_element_ids": [666,233,777], // 查询的榜单数据，按榜单里的先后顺序排序后的元素 ID（剧集 ID 或音频 ID）。榜单不存在或榜单数据为空时，返回空数组
 *         "rank_elements": [ // 榜单数据详情，榜单不存在或榜单数据为空时不返回。对榜单元素类型是剧集的返回
 *           {
 *             "drama_id": 233, // 剧集 ID
 *             "ipr_id": 1234 // 剧集所属的 IP ID, 不存在时返回 0
 *           }
 *         ],
 *         "bizdate": "2024-08-07" // 查询榜单的业务日期，YYYY-mm-dd 格式，如：2024-08-07
 *       }
 *     }
 *
 */
func ActionRankList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newRankListParam(c)
	if err != nil {
		return nil, err
	}
	rankInfo, err := mhomepagerank.FindRank(param.Type, param.SubType, param.Bizdate)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	resp := rankListResp{
		RankElementType: mhomepagerank.GetRankElementType(param.Type),
	}
	if rankInfo == nil || len(rankInfo.DataInfo) == 0 {
		resp.RankElementIDs = []int64{}
		return resp, nil
	}
	resp.RankElementIDs = rankInfo.DataInfo
	err = param.fillRankElements(&resp)
	if err != nil {
		return nil, err
	}
	resp.Bizdate = rankInfo.Bizdate
	return resp, nil
}

func (param *rankListParam) fillRankElements(resp *rankListResp) error {
	if resp.RankElementType != mhomepagerank.ElementTypeDrama {
		// 榜单元素类型不是剧集的返回
		return nil
	}

	resp.RankElements = make([]rankElementItem, 0, len(resp.RankElementIDs))
	dramas, err := dramainfo.ListDramaInfoByIDs(resp.RankElementIDs)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	dramaMap := util.ToMap(dramas, "ID").(map[int64]*dramainfo.RadioDramaDramainfo)
	for _, elementID := range resp.RankElementIDs {
		item := rankElementItem{
			DramaID: elementID,
		}
		if drama, ok := dramaMap[elementID]; ok {
			item.IPRID = drama.IPID
		} else {
			logger.Errorf("人气周榜中剧集 ID: %d 不存在或不可查看", elementID)
			// PASS
		}
		resp.RankElements = append(resp.RankElements, item)
	}
	return nil
}
