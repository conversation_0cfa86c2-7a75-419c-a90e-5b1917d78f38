package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/mhomepagerank"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(rankListParam{}, "type", "sub_type", "bizdate")
	kc.Check(rankListResp{}, "rank_element_type", "rank_element_ids", "bizdate", "rank_elements")
	kc.Check(rankElementItem{}, "drama_id", "ipr_id")

	kc.CheckOmitEmpty(rankListResp{}, "rank_elements")
}

func TestNewRankListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/rank/list"

	// 测试参数为空
	var param rankListParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newRankListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试榜单业务周期参数错误
	param.Bizdate = "2024/08/08"
	c = handler.NewRPCTestContext(api, param)
	_, err = newRankListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试榜单类型参数错误
	param.Bizdate = "2024-08-08"
	param.Type = 233
	c = handler.NewRPCTestContext(api, param)
	_, err = newRankListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试榜单周期类型参数错误
	param.Type = mhomepagerank.TypeRankDramaNew
	param.SubType = 233
	c = handler.NewRPCTestContext(api, param)
	_, err = newRankListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取正常接口参数
	param.SubType = mhomepagerank.SubTypeRankWeek
	c = handler.NewRPCTestContext(api, param)
	res, err := newRankListParam(c)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(param.Type, res.Type)
	assert.EqualValues(param.SubType, res.SubType)
	assert.EqualValues(param.Bizdate, res.Bizdate)
}

func TestActionRankList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/rank/list"

	// 测试参数错误
	var param rankListParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionRankList(c)
	assert.EqualError(err, "参数错误")

	// 测试无榜单
	require.NoError(mhomepagerank.MHomepageRank{}.DB().Delete("", "type = ? AND sub_type = ?",
		mhomepagerank.TypeRankDramaPopularity, mhomepagerank.SubTypeRankWeek).Error)
	param.Type = mhomepagerank.TypeRankDramaPopularity
	param.SubType = mhomepagerank.SubTypeRankWeek
	param.Bizdate = "2024-08-08"
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionRankList(c)
	require.NoError(err)
	data, ok := res.(rankListResp)
	require.True(ok)
	assert.NotNil(data.RankElementIDs)
	require.Len(data.RankElementIDs, 0)
	assert.Equal(mhomepagerank.ElementTypeDrama, data.RankElementType)
	assert.Nil(data.RankElements)

	// 测试获取榜单数据
	rankInfo := mhomepagerank.MHomepageRank{
		Type:    mhomepagerank.TypeRankDramaPopularity,
		SubType: mhomepagerank.SubTypeRankWeek,
		Data:    []byte("[2,4]"),
		Bizdate: "2024-08-08",
	}
	require.NoError(rankInfo.DB().Create(&rankInfo).Error)
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionRankList(c)
	require.NoError(err)
	data, ok = res.(rankListResp)
	require.True(ok)
	require.Len(data.RankElementIDs, 2)
	assert.Equal([]int64{2, 4}, data.RankElementIDs)
	assert.Equal(mhomepagerank.ElementTypeDrama, data.RankElementType)
	require.Len(data.RankElements, 2)
	assert.EqualValues(2, data.RankElements[0].DramaID)
	assert.EqualValues(1009, data.RankElements[0].IPRID)
	assert.EqualValues(4, data.RankElements[1].DramaID)
	assert.EqualValues(0, data.RankElements[1].IPRID)

	// 测试榜单数据为空时
	require.NoError(rankInfo.DB().Update("data", "").Where("type = ? AND sub_type = ?",
		mhomepagerank.TypeRankDramaPopularity, mhomepagerank.SubTypeRankWeek).Error)
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionRankList(c)
	require.NoError(err)
	data, ok = res.(rankListResp)
	require.True(ok)
	assert.NotNil(data.RankElementIDs)
	require.Len(data.RankElements, 0)
	assert.Equal(mhomepagerank.ElementTypeDrama, data.RankElementType)
	assert.Nil(data.RankElements)
}

func TestRankListParam_fillRankElements(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := rankListResp{
		RankElementType: mhomepagerank.ElementTypeSound,
		RankElementIDs:  []int64{2, 4},
	}

	param := rankListParam{}
	// 测试榜单元素类型不是剧集的获取榜单详情
	require.NoError(param.fillRankElements(&resp))
	assert.Nil(resp.RankElements)

	// 测试榜单元素类型是剧集的获取榜单详情
	resp.RankElementType = mhomepagerank.ElementTypeDrama
	require.NoError(param.fillRankElements(&resp))
	require.Len(resp.RankElements, 2)
	assert.EqualValues(2, resp.RankElements[0].DramaID)
	assert.EqualValues(1009, resp.RankElements[0].IPRID)
	assert.EqualValues(4, resp.RankElements[1].DramaID)
	assert.EqualValues(0, resp.RankElements[1].IPRID)
}
