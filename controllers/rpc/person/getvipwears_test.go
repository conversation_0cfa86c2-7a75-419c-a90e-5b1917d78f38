package person

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGetVipWears(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	uri := "/rpc/missevan-main/person/get-vip-wears"
	c := handler.NewRPCTestContext(uri, nil)
	_, _, err := ActionGetVipWears(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 用户当前非会员
	params := getVipWearsParams{
		UserID: 1991,
	}
	c = handler.NewRPCTestContext(uri, params)
	res, _, err := ActionGetVipWears(c)
	require.NoError(err)
	assert.Nil(res)

	// 用户当前为会员且有会员装扮
	params.UserID = 12
	c = handler.NewRPCTestContext(uri, params)
	res, _, err = ActionGetVipWears(c)
	require.NoError(err)
	require.NotNil(res)
	data, ok := res.(getVipWearsResp)
	require.True(ok)
	// 正在装扮会员主题皮肤
	assert.NotNil(data.ThemeSkin)
	assert.EqualValues(1, data.ThemeSkin.ID)
	// 未佩戴会员头像挂件
	assert.Nil(data.AvatarFrame)

	// 用户当前为会员且无会员装扮
	params.UserID = 13
	c = handler.NewRPCTestContext(uri, params)
	res, _, err = ActionGetVipWears(c)
	require.NoError(err)
	require.Nil(res)
}
