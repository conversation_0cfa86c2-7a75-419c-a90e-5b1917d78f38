package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-main/models/person/muserbadge"
)

type listBadgeParam struct {
	ElementType int   `json:"element_type"`         // 称号类型。1：虚拟偶像；2：猫耳十周年称号
	ElementID   int64 `json:"element_id,omitempty"` // 元素 ID。如 element_type 传入 1 时，element_id 为虚拟偶像 ID。没有时不传或传 0
	UserID      int64 `json:"user_id"`              // 用户 ID
}

type listBadgeResp struct {
	Badges []badgeItem `json:"badges"`
}

type badgeItem struct {
	ID         int64  `json:"id"`          // 称号 ID
	Title      string `json:"title"`       // 称号名称
	Intro      string `json:"intro"`       // 称号简介
	NO         string `json:"no"`          // 称号编号
	CoverURL   string `json:"cover_url"`   // 称号图片
	IconURL    string `json:"icon_url"`    // 称号 icon 地址
	CreateTime int64  `json:"create_time"` // 获取称号时间点，单位：秒
}

func newListBadgeParam(c *handler.Context) (*listBadgeParam, error) {
	param := new(listBadgeParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.ElementType <= 0 || param.ElementID < 0 || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 查询用户
	exists, err := user.Exists(param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrUserNotFound
	}

	return param, nil
}

// ActionListBadge 用户称号列表
/**
 *  @api {post} /rpc/missevan-main/person/list-badge 用户称号列表
 *
 * @apiVersion 0.1.0
 * @apiName list-badge
 * @apiGroup /rpc/missevan-main/person
 *
 * @apiParam {number=1,2} element_type 称号类型。1：虚拟偶像称号；2：猫耳十周年称号
 * @apiParam {Number} [element_id=0] 元素 ID。如 element_type 传入 1 时，element_id 为虚拟偶像 ID。没有时不传或传 0
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "badges": [ // 用户没有称号时 badges 返回 []
 *           {
 *             "id": 2333, // 称号 ID
 *             "title": "称号名称",
 *             "intro": "称号简介",
 *             "no": "000001", // 称号编号
 *             "cover_url": "https://static-test.maoercdn.com/test.png", // 称号图片
 *             "icon_url": "https://static-test.maoercdn.com/icon.png", // 称号 icon 地址
 *             "create_time": 1694737121 // 获取称号时间点，单位：秒
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     {
 *       "code": 100010007,
 *       "info": "参数错误"
 *     }
 */
func ActionListBadge(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newListBadgeParam(c)
	if err != nil {
		return nil, err
	}

	listBadge, err := muserbadge.ListUserBadge(param.ElementType, param.ElementID, param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	if len(listBadge) == 0 {
		return listBadgeResp{
			Badges: []badgeItem{},
		}, nil
	}
	var resp listBadgeResp
	resp.Badges = make([]badgeItem, 0, len(listBadge))
	for _, item := range listBadge {
		// TODO: 后续称号支持佩戴功能后返回佩戴和过期状态
		resp.Badges = append(resp.Badges, badgeItem{
			ID:         item.ID,
			Title:      item.Title,
			Intro:      item.Intro,
			NO:         item.NOStr,
			CoverURL:   item.CoverURL,
			IconURL:    item.IconURL,
			CreateTime: item.CreateTime,
		})
	}
	return resp, nil
}
