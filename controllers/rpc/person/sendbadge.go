package person

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mbadge"
	"github.com/MiaoSiLa/missevan-main/models/person/muserbadge"
)

type sendBadgeParam struct {
	BadgeID int64 `json:"badge_id"` // 称号 ID
	UserID  int64 `json:"user_id"`  // 用户 ID

	badgeInfo *mbadge.MBadge
	userBadge *muserbadge.MUserBadge
}

type sendBadgeResp struct {
	ID       int64  `json:"id"`        // 称号 ID
	Title    string `json:"title"`     // 称号名称
	NO       string `json:"no"`        // 称号编号
	CoverURL string `json:"cover_url"` // 称号图片
	IconURL  string `json:"icon_url"`  // 称号 icon 地址
}

func newSendBadgeParam(c *handler.Context) (*sendBadgeParam, error) {
	param := new(sendBadgeParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.BadgeID <= 0 || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 查询用户
	exists, err := user.Exists(param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrUserNotFound
	}

	// 查询称号
	param.badgeInfo, err = mbadge.FindBadgeByID(param.BadgeID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.badgeInfo == nil {
		return nil, actionerrors.ErrBadgeNotFound
	}

	return param, nil
}

// ActionSendBadge 发放用户称号接口
/**
 *  @api {post} /rpc/missevan-main/person/send-badge 发放用户称号接口
 *
 * @apiVersion 0.1.0
 * @apiName send-badge
 * @apiGroup /rpc/missevan-main/person
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} badge_id 称号 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "id": 1, // 称号 ID
 *         "title": "称号名称",
 *         "no": "10001", // 称号编号
 *         "cover_url": "https://static-test.maoercdn.com/test.png", // 称号图片
 *         "icon_url": "https://static-test.maoercdn.com/icon.png" // 称号 icon 地址
 *       }
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     {
 *       "code": 100010007,
 *       "info": "参数错误"
 *     }
 */
func ActionSendBadge(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSendBadgeParam(c)
	if err != nil {
		return nil, err
	}
	param.userBadge = &muserbadge.MUserBadge{
		UserID:  param.UserID,
		BadgeID: param.BadgeID,
		Status:  muserbadge.StatusTakeoff, // 暂时没有佩戴功能，默认未佩戴称号
	}
	if param.badgeInfo.ExpireDuration != 0 {
		// NOTICE: 过期时间点按自然日零点计算
		param.userBadge.ExpireTime = util.BeginningOfDay(util.TimeNow().
			Add(time.Duration(param.badgeInfo.ExpireDuration) * time.Second)).Unix()
	}
	const retryCount = 3
	for retry := 0; retry < retryCount; retry++ {
		err = param.sendBadge(retry > 0)
		if err == nil {
			break
		}
		if servicedb.IsUniqueError(err) {
			logFields := logger.Fields{
				"user_id":  param.userBadge.UserID,
				"badge_id": param.userBadge.BadgeID,
				"no":       param.userBadge.NO,
			}
			if retry < retryCount-1 {
				logger.WithFields(logFields).Warnf("称号编号触发唯一索引 error: %v", err)
				// 延时 100ms 后重试
				<-time.After(100 * time.Millisecond)
				continue
			} else {
				logger.WithFields(logFields).Errorf("称号编号触发唯一索引 error: %v", err)
			}
		}
		return nil, actionerrors.ErrServerInternal(err, logger.Fields{
			"user_id":  param.userBadge.UserID,
			"badge_id": param.userBadge.BadgeID,
		})
	}

	return sendBadgeResp{
		ID:       param.badgeInfo.ID,
		Title:    param.badgeInfo.Title,
		NO:       param.userBadge.NOStr,
		CoverURL: param.badgeInfo.CoverURL,
		IconURL:  param.badgeInfo.IconURL,
	}, nil
}

func (param *sendBadgeParam) sendBadge(isRetry bool) (err error) {
	if isRetry {
		err = servicedb.Tx(param.userBadge.DB(), func(tx *gorm.DB) error {
			param.userBadge.NO, err = muserbadge.GetNextNO(tx, param.BadgeID, param.badgeInfo.MoreInfo.SpecialNOList)
			if err != nil {
				return err
			}
			return nil
		})
	} else {
		param.userBadge.NO, err = muserbadge.GetNextNO(nil, param.BadgeID, param.badgeInfo.MoreInfo.SpecialNOList)
	}
	if err != nil {
		return err
	}

	return param.userBadge.CreateUserBadge()
}
