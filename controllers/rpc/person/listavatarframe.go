package person

import (
	"slices"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/models/soundcomment"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

type listAvatarFrameParams struct {
	UserIDs     []int64 `json:"user_ids"`
	Scene       int     `json:"scene,omitempty"`
	ElementType *int    `json:"element_type,omitempty"`
	ElementID   *int64  `json:"element_id,omitempty"`
}

// 返回的用户头像框信息
type avatarFrameInfo struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	AvatarFrameURL string `json:"avatar_frame_url"`
	IconURL        string `json:"icon_url"`
}

// 获取用户佩戴的头像框场景
const (
	sceneDefault int = iota // 默认场景
	sceneComment            // 评论区场景
)

// ActionListAvatarFrame 批量获取用户佩戴中的头像框
/**
 * @api {post} /rpc/missevan-main/person/list-avatar-frame 批量获取用户佩戴中的头像框
 *
 * @apiVersion 0.1.0
 * @apiName list-avatar-frame
 * @apiGroup /rpc/missevan-main/person/
 *
 * @apiParam {Number[]} user_ids 用户 ID
 * @apiParam {number=0,1} [scene=0] 获取场景。0：默认；1：评论页
 * @apiParam {Number} [element_type] 元素类型
 * @apiParam {Number} [element_id] 元素 ID。如获取场景为音频播放评论页时，传入音频 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {  // 用户头像框信息，都无头像框时返回空 map
 *       "233": { // key 为用户 ID，若未佩戴头像或用户不存在，不返回该用户 ID 对应的 key
 *         "id": 1, // 头像框 ID
 *         "name": "头像框名称",
 *         "avatar_frame_url": "https://test.com/test.png", // 头像框地址
 *         "icon_url": "https://test.com/icon/test.png" // icon 地址
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 */
func ActionListAvatarFrame(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newListAvatarFrameParams(c)
	if err != nil {
		return nil, err
	}

	avatarFrameList, err := museravatarframemap.ListWearingByUserIDs(params.UserIDs, false)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	avatarFrameList, err = params.filterLimitAvatarFrame(avatarFrameList)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	avatarFramesMap := make(map[int64]avatarFrameInfo, len(avatarFrameList))
	for _, avatarFrame := range avatarFrameList {
		avatarFramesMap[avatarFrame.UserID] = avatarFrameInfo{
			ID:             avatarFrame.AvatarFrameID,
			Name:           avatarFrame.Name,
			AvatarFrameURL: avatarFrame.AvatarFrameURL,
			IconURL:        avatarFrame.IconURL,
		}
	}

	return avatarFramesMap, nil
}

func newListAvatarFrameParams(c *handler.Context) (*listAvatarFrameParams, error) {
	param := new(listAvatarFrameParams)
	err := c.BindJSON(param)
	if err != nil || len(param.UserIDs) == 0 ||
		(param.Scene != sceneDefault && param.Scene != sceneComment) ||
		(param.ElementType != nil && *(param.ElementType) < 0) ||
		(param.ElementID != nil && *(param.ElementID) < 0) ||
		(param.Scene == sceneComment && (param.ElementType == nil || param.ElementID == nil)) {
		return nil, actionerrors.ErrParams
	}

	return param, nil
}

// 过滤被限制的头像框
func (p *listAvatarFrameParams) filterLimitAvatarFrame(avatarFrameList []*museravatarframemap.UserAvatarFrame) ([]*museravatarframemap.UserAvatarFrame, error) {
	avatarFrameListLen := len(avatarFrameList)
	if avatarFrameListLen == 0 {
		return avatarFrameList, nil
	}
	maybeVipUserIDs := make([]int64, 0, avatarFrameListLen)
	for _, avatarFrame := range avatarFrameList {
		if avatarFrame.Type == mavatarframe.TypeVip {
			maybeVipUserIDs = append(maybeVipUserIDs, avatarFrame.UserID)
		}
	}
	var vipUserIDs []int64
	var err error
	if len(maybeVipUserIDs) > 0 {
		vipUserIDs, err = muservip.ListVipUserIDs(maybeVipUserIDs)
		if err != nil {
			return nil, err
		}
	}
	dramaID := int64(-1)
	newAvatarFrameList := make([]*museravatarframemap.UserAvatarFrame, 0, avatarFrameListLen)
	for _, avatarFrame := range avatarFrameList {
		if avatarFrame.Type == mavatarframe.TypeVip && !slices.Contains(vipUserIDs, avatarFrame.UserID) {
			// 头像框是 vip 头像框且用户不是 vip 用户时，过滤掉用户头像框
			continue
		}

		// 目前过滤的规则：部分头像框在仅在个人主页、我的页、指定剧集评论区下展示
		// 文档地址：https://info.missevan.com/pages/viewpage.action?pageId=118070863
		if p.Scene == sceneComment {
			if len(avatarFrame.MoreInfo.ShowDramaIDs) != 0 {
				// 头像框配置了 ShowDramaIDs 时，在评论区场景，仅在指定剧集评论区下可见
				if *p.ElementType != soundcomment.TypeSound {
					continue
				}
				if dramaID == -1 {
					dramaID, err = dramainfo.GetDramaIDBySoundID(*p.ElementID)
					if err != nil {
						return nil, err
					}
				}
				if !slices.Contains(avatarFrame.MoreInfo.ShowDramaIDs, dramaID) {
					// 剧集音频评论区，该剧集 ID 不在 ShowDramaIDs 中时，过滤掉
					continue
				}
			}
		}
		newAvatarFrameList = append(newAvatarFrameList, avatarFrame)
	}
	return newAvatarFrameList, nil
}
