package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframelog"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskinlog"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

type themeSkin struct {
	ID            int64 `json:"id"`
	FirstWearTime int64 `json:"first_wear_time"`
}

type avatarFrame struct {
	ID            int64 `json:"id"`
	FirstWearTime int64 `json:"first_wear_time"`
}

type getVipWearsResp struct {
	ThemeSkin   *themeSkin   `json:"theme_skin,omitempty"`
	AvatarFrame *avatarFrame `json:"avatar_frame,omitempty"`
}

type getVipWearsParams struct {
	UserID int64 `json:"user_id"`
}

// ActionGetVipWears 获取用户当前正使用的会员装扮信息
/**
 * @api {post} /rpc/missevan-main/person/get-vip-wears 获取用户当前正使用的会员装扮信息
 *
 * @apiVersion 0.1.0
 * @apiName get-theme-skin
 * @apiGroup /rpc/missevan-main/person/
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": { // 会员装扮信息，用户当前非会员或未使用会员装扮时返回 null
 *       "theme_skin": { // 会员主题皮肤，未装扮时不下发
 *         "id": 1, // 当前使用的主题皮肤 ID
 *         "first_wear_time": 1739243566 // 第一次装扮该主题的时间戳，单位：秒
 *       },
 *       "avatar_frame": { // 会员头像挂件，未佩戴时不下发
 *         "id": 1, // 当前使用的头像挂件 ID
 *         "first_wear_time": 1739243566 // 第一次佩戴该头像挂件的时间戳，单位：秒
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null
 */
func ActionGetVipWears(c *handler.Context) (handler.ActionResponse, string, error) {
	param := new(getVipWearsParams)
	err := c.BindJSON(param)
	if err != nil || param.UserID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	isVipUser, err := muservip.IsVip(param.UserID)
	if err != nil {
		return nil, "", err
	}
	if !isVipUser {
		return nil, "", nil
	}
	var resp getVipWearsResp
	themeSkinID, err := mthemeskin.GetUserVipThemeSkinID(param.UserID)
	if err != nil {
		return nil, "", err
	}
	if themeSkinID != 0 {
		firstWearTime, err := muserthemeskinlog.GetUserFirstWearTime(param.UserID, themeSkinID)
		if err != nil {
			return nil, "", err
		}
		resp.ThemeSkin = &themeSkin{
			ID:            themeSkinID,
			FirstWearTime: firstWearTime,
		}
	}
	avatarFrameID, err := museravatarframemap.GetUserVipAvatarFrameID(param.UserID)
	if err != nil {
		return nil, "", err
	}
	if avatarFrameID != 0 {
		firstWearTime, err := museravatarframelog.GetUserFirstWearTime(param.UserID, avatarFrameID)
		if err != nil {
			return nil, "", err
		}
		resp.AvatarFrame = &avatarFrame{
			ID:            avatarFrameID,
			FirstWearTime: firstWearTime,
		}
	}
	if resp.ThemeSkin == nil && resp.AvatarFrame == nil {
		return nil, "", nil
	}
	return resp, "", nil
}
