package person

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/person/mbadge"
	"github.com/MiaoSiLa/missevan-main/models/person/muserbadge"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	testUserID  = 12
	testUserID2 = 13
	testBadgeID = 233
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestSendBadgeTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(sendBadgeParam{}, "badge_id", "user_id")
	kc.Check(sendBadgeResp{}, "id", "title", "no", "cover_url", "icon_url")
}

func TestNewSendBadgeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/person/send-badge"

	// 测试参数为空
	var param sendBadgeParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newSendBadgeParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数缺少用户 ID
	param.BadgeID = 999999
	c = handler.NewRPCTestContext(api, param)
	_, err = newSendBadgeParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户不存在
	param.UserID = 999999
	c = handler.NewRPCTestContext(api, param)
	_, err = newSendBadgeParam(c)
	assert.Equal(actionerrors.ErrUserNotFound, err)

	// 测试称号不存在
	param.UserID = testUserID
	param.BadgeID = 999999
	c = handler.NewRPCTestContext(api, param)
	_, err = newSendBadgeParam(c)
	assert.Equal(actionerrors.ErrBadgeNotFound, err)

	// 测试获取正常接口参数
	require.NoError(mbadge.MBadge{}.DB().Delete("", "id = ?", testBadgeID).Error)
	badgeInfo := &mbadge.MBadge{
		ID:    testBadgeID,
		Title: "测试称号名",
		Intro: "测试称号简介",
		Cover: "test://test/202309/27/test.png",
		Icon:  "test://test/202309/27/icon/test.png",
	}
	require.NoError(badgeInfo.DB().Create(badgeInfo).Error)
	param.BadgeID = testBadgeID
	c = handler.NewRPCTestContext(api, param)
	res, err := newSendBadgeParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(testUserID, res.UserID)
	assert.EqualValues(testBadgeID, res.BadgeID)
	assert.NotNil(res.badgeInfo)
	assert.Equal(badgeInfo.Title, res.badgeInfo.Title)
	assert.Equal(badgeInfo.Intro, res.badgeInfo.Intro)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", res.badgeInfo.CoverURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", res.badgeInfo.IconURL)
}

func TestActionSendBadge(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/person/send-badge"

	// 测试参数错误
	var param sendBadgeParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionSendBadge(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试发放用户称号
	require.NoError(muserbadge.MUserBadge{}.DB().
		Delete("", "user_id = ?", testUserID).Error)
	require.NoError(mbadge.MBadge{}.DB().Delete("", "id = ?", testBadgeID).Error)
	badgeInfo := &mbadge.MBadge{
		ID:    233,
		Title: "测试称号名",
		Intro: "测试称号简介",
		Cover: "test://test/202309/27/test.png",
		Icon:  "test://test/202309/27/icon/test.png",
	}
	require.NoError(badgeInfo.DB().Create(badgeInfo).Error)
	param.BadgeID = testBadgeID
	param.UserID = testUserID
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionSendBadge(c)
	require.NoError(err)
	data, ok := res.(sendBadgeResp)
	require.True(ok)
	assert.EqualValues(param.BadgeID, data.ID)
	assert.Equal(badgeInfo.Title, data.Title)
	assert.NotEmpty(data.NO)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", data.CoverURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", data.IconURL)
	var userbadgeInfo muserbadge.MUserBadge
	require.NoError(userbadgeInfo.DB().Where("user_id = ?", param.UserID).Take(&userbadgeInfo).Error)
	assert.EqualValues(param.UserID, userbadgeInfo.UserID)
	assert.EqualValues(param.BadgeID, userbadgeInfo.BadgeID)
	assert.Zero(userbadgeInfo.ExpireTime)
	assert.EqualValues(muserbadge.StatusTakeoff, userbadgeInfo.Status)
	assert.Equal(data.NO, userbadgeInfo.NOStr)
}

func TestSendBadgeParam_sendBadge(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(mbadge.MBadge{}.DB().Delete("", "id = ?", testBadgeID).Error)
	badgeInfo := &mbadge.MBadge{
		ID:    testBadgeID,
		Title: "测试称号名",
		Intro: "测试称号简介",
		Cover: "test://test/202309/27/test.png",
		Icon:  "test://test/202309/27/icon/test.png",
	}
	require.NoError(badgeInfo.DB().Create(badgeInfo).Error)
	param := &sendBadgeParam{
		BadgeID:   testBadgeID,
		badgeInfo: &mbadge.MBadge{},
		userBadge: &muserbadge.MUserBadge{
			UserID:  testUserID2,
			BadgeID: testBadgeID,
		},
	}
	require.NoError(param.sendBadge(true))
	assert.NotZero(param.userBadge.ID)
	assert.NotZero(param.userBadge.NO)
}
