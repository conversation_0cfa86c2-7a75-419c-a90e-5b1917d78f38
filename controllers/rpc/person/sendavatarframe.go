package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
)

type sendParams struct {
	UserID         int64 `json:"user_id"`
	AvatarFrameID  int64 `json:"avatar_frame_id"`
	Status         *int  `json:"status,omitempty"`
	AutoWear       *int  `json:"auto_wear,omitempty"`
	ExpireType     int   `json:"expire_type,omitempty"`
	ExpireDuration int64 `json:"expire_duration,omitempty"`

	avatarFrame *mavatarframe.MAvatarFrame
}

type sendResp struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	AvatarFrameURL string `json:"avatar_frame_url"`
	IconURL        string `json:"icon_url"`
	ExpireTime     int64  `json:"expire_time"`
	ExpireDuration int64  `json:"expire_duration"`
	IsNew          bool   `json:"is_new"`
}

// ActionSendAvatarFrame 给用户发放头像框
/**
 * @api {post} /rpc/missevan-main/person/send-avatar-frame 给用户发放头像框
 *
 * @apiVersion 0.1.0
 * @apiName send-avatar-frame
 * @apiGroup /rpc/missevan-main/person/
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} avatar_frame_id 头像框 ID
 * @apiParam {number=0,1} [status] 发放后的佩戴状态。0：不佩戴；1：佩戴
 * @apiParam {number=0,1} [auto_wear] 是否自动佩戴（该字段优先级高于 status）；不传或为 0：不自动佩戴，1：若当前没有佩戴的头像框则自动佩戴
 * @apiParam {Number} [expire_duration=0] 发放后的有效时长，单位：秒。传 0 或未传入时，使用头像框默认有效期
 * @apiParam {number=0,1} [expire_type=0] 头像框过期方式。0：按自然日过期（按过期当日 23:59:59 过期）；1：按过期时间点过期
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": { // 发放成功或之前已获得该头像框时下发相关头像框信息
 *       "id": 1, // 头像框 ID
 *       "name": "头像框名称",
 *       "avatar_frame_url": "https://test.com/test.png", // 头像框地址
 *       "icon_url": "https://test.com/icon/test.png", // icon 地址
 *       "expire_time": 1695657600, // 过期时间点，为 0 表示永久有效。单位：秒
 *       "expire_duration": 86400, // 头像框获取后过期时长，为 0 表示永久有效。单位：秒。
 *       "is_new": true // 是否为新下发，重复下发时返回 false
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 */
func ActionSendAvatarFrame(c *handler.Context) (handler.ActionResponse, error) {
	var params sendParams
	err := params.load(c)
	if err != nil {
		return nil, err
	}
	isNew, userAvatarFrame, err := museravatarframemap.SendAvatarFrame(params.avatarFrame, params.UserID, params.Status, params.AutoWear, params.ExpireType, params.ExpireDuration)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	var expireDuration int64
	if userAvatarFrame.ExpireTime != museravatarframemap.ExpireTimeForeverEffective {
		expireDuration = userAvatarFrame.ExpireTime - goutil.TimeNow().Unix()
	}
	return sendResp{
		ID:             params.AvatarFrameID,
		Name:           params.avatarFrame.Name,
		AvatarFrameURL: params.avatarFrame.AvatarFrameURL,
		IconURL:        params.avatarFrame.IconURL,
		ExpireTime:     userAvatarFrame.ExpireTime,
		ExpireDuration: expireDuration,
		IsNew:          isNew,
	}, nil
}

func (p *sendParams) load(c *handler.Context) error {
	err := c.BindJSON(p)
	if err != nil || p.UserID <= 0 || p.AvatarFrameID <= 0 || (p.Status == nil && p.AutoWear == nil) ||
		(p.Status != nil && *p.Status != museravatarframemap.StatusTakeoff && *p.Status != museravatarframemap.StatusWearing) ||
		(p.ExpireType != museravatarframemap.ExpireTypeDay && p.ExpireType != museravatarframemap.ExpireTypeTime) ||
		(p.ExpireDuration < 0) {
		return actionerrors.ErrParams
	}

	userExists, err := user.Exists(p.UserID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if !userExists {
		logger.WithFields(logger.Fields{
			"user_id":         p.UserID,
			"avatar_frame_id": p.AvatarFrameID,
		}).Error("出现给不存在的用户发放头像框的情况")
		return actionerrors.ErrUserNotFound
	}
	avatarFrame, err := mavatarframe.FindValidOne(p.AvatarFrameID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if avatarFrame == nil {
		logger.WithFields(logger.Fields{
			"user_id":         p.UserID,
			"avatar_frame_id": p.AvatarFrameID,
		}).Error("出现给用户发放不存在或未生效头像框的情况")
		return actionerrors.ErrAvatarFrameNotFound
	}
	p.avatarFrame = avatarFrame
	return nil
}
