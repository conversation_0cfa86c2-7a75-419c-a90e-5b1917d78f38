package person

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/person/mbadge"
)

func TestGetBadgeTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(getBadgeParam{}, "badge_id")
	kc.Check(getBadgeResp{}, "badge")
	kc.Check(badgeInfo{}, "id", "title", "intro", "cover_url", "icon_url")
}

func TestNewGetBadgeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/person/get-badge"

	// 测试参数为空
	var param getBadgeParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newGetBadgeParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数错误
	param.BadgeID = -1
	c = handler.NewRPCTestContext(api, param)
	_, err = newGetBadgeParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取正常接口参数
	param.BadgeID = 10
	c = handler.NewRPCTestContext(api, param)
	res, err := newGetBadgeParam(c)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(param.BadgeID, res.BadgeID)
}

func TestActionGetBadge(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/person/get-badge"

	// 测试参数错误
	var param getBadgeParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionGetBadge(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试称号不存在
	param.BadgeID = 99999999
	require.NoError(mbadge.MBadge{}.DB().Delete("", "id = ? ", param.BadgeID).Error)
	c = handler.NewRPCTestContext(api, param)
	_, err = ActionGetBadge(c)
	assert.Equal(actionerrors.ErrBadgeNotFound, err)

	// 测试正常获取到称号
	param.BadgeID = 10
	c = handler.NewRPCTestContext(api, param)
	resp, err := ActionGetBadge(c)
	require.NoError(err)
	data, ok := resp.(getBadgeResp)
	require.True(ok)
	assert.EqualValues(10, data.Badge.ID)
}
