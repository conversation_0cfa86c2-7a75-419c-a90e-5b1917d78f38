package person

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/models/soundcomment"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, sceneDefault)
	assert.Equal(1, sceneComment)
}

func TestListAvatarFrameTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(listAvatarFrameParams{}, "user_ids", "scene", "element_type", "element_id")
	kc.Check(avatarFrameInfo{}, "id", "name", "avatar_frame_url", "icon_url")
}

func TestActionListAvatarFrame(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	uri := "/rpc/person/list-avatar-frame"
	c := handler.NewRPCTestContext(uri, nil)
	result, err := ActionListAvatarFrame(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	// 测试有用户 ID 但对应用户都没有佩戴的头像框时
	params := listAvatarFrameParams{UserIDs: []int64{1001, 1002}}
	c = handler.NewRPCTestContext(uri, params)
	result, err = ActionListAvatarFrame(c)
	require.NoError(err)
	assert.Empty(result)

	// 测试有用户 ID 且有用户佩戴的头像框时
	params = listAvatarFrameParams{UserIDs: []int64{1, 2, 3}}
	c = handler.NewRPCTestContext(uri, params)
	result, err = ActionListAvatarFrame(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok := result.(map[int64]avatarFrameInfo)
	require.True(ok)
	require.Len(resp, 2)
	avatarFrame1, ok := resp[1]
	require.True(ok)
	assert.EqualValues(4, avatarFrame1.ID)
	assert.Equal("测试头像框 4", avatarFrame1.Name)
	assert.Equal(service.Storage.Parse("test://test_4.webp"), avatarFrame1.AvatarFrameURL)
	assert.Equal(service.Storage.Parse("test://icon/test_4.webp"), avatarFrame1.IconURL)
	avatarFrame2, ok := resp[2]
	require.True(ok)
	assert.EqualValues(5, avatarFrame2.ID)
	assert.Equal("测试头像框 5", avatarFrame2.Name)
	assert.Equal(service.Storage.Parse("test://test_5.webp"), avatarFrame2.AvatarFrameURL)
	assert.Equal(service.Storage.Parse("test://icon/test_5.webp"), avatarFrame2.IconURL)
	// 验证用户没有佩戴头像框
	_, ok = resp[3]
	assert.False(ok)

	// 测试用户佩戴了头像框，但是在剧集评论区场景，该头像框未被限制展示的情况
	params = listAvatarFrameParams{
		UserIDs:     []int64{1, 2},
		Scene:       sceneComment,
		ElementType: util.NewInt(soundcomment.TypeSound),
		ElementID:   util.NewInt64(666),
	}
	c = handler.NewRPCTestContext(uri, params)
	result, err = ActionListAvatarFrame(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok = result.(map[int64]avatarFrameInfo)
	require.True(ok)
	require.Len(resp, 2)
	avatarFrame, ok := resp[1]
	require.True(ok)
	assert.EqualValues(4, avatarFrame.ID)
	avatarFrame, ok = resp[2]
	require.True(ok)
	assert.EqualValues(5, avatarFrame.ID)

	// 测试用户佩戴了头像框，但是在剧集评论区场景，该头像框被限制展示的情况
	require.NoError(mavatarframe.MAvatarFrame{}.DB().Where("id = ?", 5).Update("more", "{\"show_drama_ids\": [999]}").Error)
	c = handler.NewRPCTestContext(uri, params)
	result, err = ActionListAvatarFrame(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok = result.(map[int64]avatarFrameInfo)
	require.True(ok)
	require.Len(resp, 1)
	avatarFrame, ok = resp[1]
	require.True(ok)
	assert.EqualValues(4, avatarFrame.ID)
}

func TestNewListAvatarFrameParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	uri := "/rpc/person/list-avatar-frame"
	c := handler.NewRPCTestContext(uri, nil)
	param, err := newListAvatarFrameParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试参数为空
	params := listAvatarFrameParams{}
	c = handler.NewRPCTestContext(uri, params)
	param, err = newListAvatarFrameParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试参数 UserIDs 为空
	params = listAvatarFrameParams{UserIDs: []int64{}}
	c = handler.NewRPCTestContext(uri, params)
	param, err = newListAvatarFrameParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试 scene 参数错误
	params = listAvatarFrameParams{
		UserIDs: []int64{1001, 1002},
		Scene:   999999,
	}
	c = handler.NewRPCTestContext(uri, params)
	param, err = newListAvatarFrameParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试 element_type 参数错误
	params = listAvatarFrameParams{
		UserIDs:     []int64{1001, 1002},
		Scene:       sceneDefault,
		ElementType: util.NewInt(-1),
	}
	c = handler.NewRPCTestContext(uri, params)
	param, err = newListAvatarFrameParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试 element_id 参数错误
	params = listAvatarFrameParams{
		UserIDs:     []int64{1001, 1002},
		Scene:       sceneDefault,
		ElementType: util.NewInt(1),
		ElementID:   util.NewInt64(-1),
	}
	c = handler.NewRPCTestContext(uri, params)
	param, err = newListAvatarFrameParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试 scene 为评论场景时，参数 element_id 或 element_type 未传时报错
	params = listAvatarFrameParams{
		UserIDs:     []int64{1001, 1002},
		Scene:       sceneComment,
		ElementType: nil,
		ElementID:   nil,
	}
	c = handler.NewRPCTestContext(uri, params)
	param, err = newListAvatarFrameParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试参数正常
	params = listAvatarFrameParams{UserIDs: []int64{1001, 1002}}
	c = handler.NewRPCTestContext(uri, params)
	param, err = newListAvatarFrameParams(c)
	require.NoError(err)
	require.NotEmpty(param)
	assert.Equal(param.UserIDs, []int64{1001, 1002})
}

func TestListAvatarFrameParams_filterLimitAvatarFrame(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试可以过滤掉限定剧集下展示的头像框
	avatarFrameList := []*museravatarframemap.UserAvatarFrame{
		&museravatarframemap.UserAvatarFrame{
			MoreInfo: mavatarframe.MoreInfo{ShowDramaIDs: []int64{54654}},
		},
	}
	p := listAvatarFrameParams{
		Scene:       sceneComment,
		ElementType: util.NewInt(soundcomment.TypeSound),
		ElementID:   util.NewInt64(666),
	}
	list, err := p.filterLimitAvatarFrame(avatarFrameList)
	require.NoError(err)
	assert.Empty(list)

	// 测试不过滤掉限定剧集下展示的头像框
	avatarFrameList[0].MoreInfo.ShowDramaIDs = []int64{23333}
	list, err = p.filterLimitAvatarFrame(avatarFrameList)
	require.NoError(err)
	assert.Equal(avatarFrameList, list)

	// 测试目前仅对剧集评论场景做过滤
	p.Scene = sceneDefault
	list, err = p.filterLimitAvatarFrame(avatarFrameList)
	require.NoError(err)
	assert.Equal(avatarFrameList, list)

	// 测试对非剧集评论场景做过滤
	p.Scene = sceneComment
	p.ElementType = util.NewInt(soundcomment.TypeEvent)
	list, err = p.filterLimitAvatarFrame(avatarFrameList)
	require.NoError(err)
	assert.Empty(list)

	// 测试 list 长度为 0 的时候没有过滤操作
	p.ElementType = util.NewInt(soundcomment.TypeSound)
	avatarFrameList = make([]*museravatarframemap.UserAvatarFrame, 0)
	list, err = p.filterLimitAvatarFrame(avatarFrameList)
	require.NoError(err)
	assert.Equal(avatarFrameList, list)

	// 过滤 vip 头像框
	p.UserIDs = []int64{testUserID, 99999999}
	avatarFrameList = append(avatarFrameList, []*museravatarframemap.UserAvatarFrame{
		{
			Type:   mavatarframe.TypeVip,
			UserID: testUserID,
		},
		{
			Type:   mavatarframe.TypeVip,
			UserID: 99999999,
		},
	}...)
	list, err = p.filterLimitAvatarFrame(avatarFrameList)
	require.NoError(err)
	require.Len(list, 1)
	assert.EqualValues(mavatarframe.TypeVip, list[0].Type)
	assert.EqualValues(testUserID, list[0].UserID)
}
