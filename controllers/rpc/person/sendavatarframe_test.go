package person

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
)

func TestSendAvatarFrame_TagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(sendParams{}, "user_id", "avatar_frame_id", "status", "auto_wear", "expire_type", "expire_duration")
	kc.Check(sendResp{}, "id", "name", "avatar_frame_url", "icon_url", "expire_time", "expire_duration", "is_new")
}

func TestActionSendAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误的情况
	testAvatarFrameID := int64(3)
	params := sendParams{
		UserID:        0,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	api := "/rpc/person/send-avatar-frame"
	c := handler.NewRPCTestContext(api, params)
	resp, err := ActionSendAvatarFrame(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(resp)

	// 测试用户不存在的情况
	params = sendParams{
		UserID:        999999999,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	c = handler.NewRPCTestContext(api, params)
	resp, err = ActionSendAvatarFrame(c)
	require.Equal(actionerrors.ErrUserNotFound, err)
	assert.Nil(resp)

	// 测试头像框不存在的情况
	params = sendParams{
		UserID:        testUserID,
		AvatarFrameID: 999999999,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	c = handler.NewRPCTestContext(api, params)
	resp, err = ActionSendAvatarFrame(c)
	require.Equal(actionerrors.ErrAvatarFrameNotFound, err)
	assert.Nil(resp)

	// 测试正常发放头像框的情况
	util.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	})
	defer util.SetTimeNow(nil)
	require.NoError(museravatarframemap.MUserAvatarFrameMap{}.DB().
		Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).Delete("").Error)
	params = sendParams{
		UserID:        testUserID,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	c = handler.NewRPCTestContext(api, params)
	resp, err = ActionSendAvatarFrame(c)
	require.NoError(err)
	require.NotNil(resp)
	data, ok := resp.(sendResp)
	require.True(ok)
	assert.Equal(data.ID, testAvatarFrameID)
	nextDayTime := util.NextDayTime(util.TimeNow()).Unix()
	assert.EqualValues(nextDayTime, data.ExpireTime)
	assert.EqualValues(nextDayTime-util.TimeNow().Unix(), data.ExpireDuration)
	assert.True(data.IsNew)
	// 验证数据
	m := new(museravatarframemap.MUserAvatarFrameMap)
	require.NoError(m.DB().Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).
		Take(m).Error)
	assert.Equal(museravatarframemap.StatusWearing, m.Status)
	assert.EqualValues(nextDayTime, m.ExpireTime)

	// 测试重复发放头像框的情况
	c = handler.NewRPCTestContext(api, params)
	resp, err = ActionSendAvatarFrame(c)
	require.NoError(err)
	require.NotNil(resp)
	data, ok = resp.(sendResp)
	require.True(ok)
	assert.Equal(data.ID, testAvatarFrameID)
	assert.EqualValues(nextDayTime+86400, data.ExpireTime)
	assert.EqualValues(data.ExpireTime-util.TimeNow().Unix(), data.ExpireDuration)
	assert.False(data.IsNew)
	// 验证数据
	m = new(museravatarframemap.MUserAvatarFrameMap)
	require.NoError(m.DB().Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).
		Take(m).Error)
	assert.Equal(museravatarframemap.StatusWearing, m.Status)
	assert.EqualValues(nextDayTime+86400, m.ExpireTime)
}

func TestSendParams_load(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试 user_id 参数错误
	testAvatarFrameID := int64(3)
	params := sendParams{
		UserID:        0,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	api := "/rpc/person/send-avatar-frame"
	c := handler.NewRPCTestContext(api, params)
	err := params.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试 avatar_frame_id 参数错误
	params = sendParams{
		UserID:        testUserID,
		AvatarFrameID: 0,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	c = handler.NewRPCTestContext(api, params)
	err = params.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试 status 参数错误
	params = sendParams{
		UserID:        testUserID,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(233),
	}
	c = handler.NewRPCTestContext(api, params)
	err = params.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试传了 auto 但是 status 参数错误的状况
	params = sendParams{
		UserID:        testUserID,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(233),
		AutoWear:      util.NewInt(1),
	}
	c = handler.NewRPCTestContext(api, params)
	err = params.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户不存在的情况
	params = sendParams{
		UserID:        999999999,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	c = handler.NewRPCTestContext(api, params)
	err = params.load(c)
	assert.Equal(actionerrors.ErrUserNotFound, err)

	// 测试头像框不存在的情况
	params = sendParams{
		UserID:        testUserID,
		AvatarFrameID: 999999999,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	c = handler.NewRPCTestContext(api, params)
	err = params.load(c)
	assert.Equal(actionerrors.ErrAvatarFrameNotFound, err)

	// 测试正常绑定参数的情况
	params = sendParams{
		UserID:        testUserID,
		AvatarFrameID: testAvatarFrameID,
		Status:        util.NewInt(museravatarframemap.StatusWearing),
	}
	c = handler.NewRPCTestContext(api, params)
	err = params.load(c)
	require.NoError(err)
	assert.EqualValues(testUserID, params.UserID)
	assert.Equal(testAvatarFrameID, params.AvatarFrameID)
	assert.Equal(museravatarframemap.StatusWearing, *params.Status)
	require.NotNil(params.avatarFrame)
	assert.Equal(testAvatarFrameID, params.avatarFrame.ID)
}
