package person

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
)

func TestActionGetThemeSkin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	uri := "/rpc/missevan-main/person/get-theme-skin"
	c := handler.NewRPCTestContext(uri, nil)
	_, _, err := ActionGetThemeSkin(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 正常获取到主题皮肤信息
	params := getThemeSkinParams{
		UserID: 12,
	}
	c = handler.NewRPCTestContext(uri, params)
	res, _, err := ActionGetThemeSkin(c)
	require.NoError(err)
	data, ok := res.(getThemeSkinResp)
	require.True(ok)
	expectData := getThemeSkinResp{
		ThemeSkin: &mthemeskin.UserThemeSkinInfo{
			ID:         1,
			Vip:        mthemeskin.VipLimit,
			Package:    "test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.zip",
			PackageURL: "https://static-test.maoercdn.com/themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.zip",
		},
	}
	assert.Equal(expectData, data)
}
