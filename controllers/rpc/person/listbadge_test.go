package person

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mbadge"
	"github.com/MiaoSiLa/missevan-main/models/person/muserbadge"
	"github.com/MiaoSiLa/missevan-main/service"
)

const testElementID = 233

func TestListBadgeTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(listBadgeParam{}, "element_type", "element_id", "user_id")
	kc.Check(listBadgeResp{}, "badges")
	kc.Check(badgeItem{}, "id", "title", "intro", "no", "cover_url", "icon_url", "create_time")
}

func TestNewListBadgeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/person/list-badge"

	// 测试参数为空
	var param listBadgeParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newListBadgeParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数缺少用户 ID
	param.ElementType = mbadge.TypeVirtualIdol
	param.ElementID = testElementID
	c = handler.NewRPCTestContext(api, param)
	_, err = newListBadgeParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户不存在
	param.UserID = 999999
	c = handler.NewRPCTestContext(api, param)
	_, err = newListBadgeParam(c)
	assert.Equal(actionerrors.ErrUserNotFound, err)

	// 测试获取正常接口参数
	param.UserID = testUserID
	c = handler.NewRPCTestContext(api, param)
	res, err := newListBadgeParam(c)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(testUserID, res.UserID)
	assert.EqualValues(mbadge.TypeVirtualIdol, res.ElementType)
	assert.EqualValues(testElementID, res.ElementID)

	// 测试 element_id 为 0 的情况
	param.ElementID = 0
	c = handler.NewRPCTestContext(api, param)
	res, err = newListBadgeParam(c)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(testUserID, res.UserID)
	assert.EqualValues(mbadge.TypeVirtualIdol, res.ElementType)
	assert.EqualValues(0, res.ElementID)
}

func TestActionListBadge(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/missevan-main/person/list-badge"

	// 测试参数错误
	var param listBadgeParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionListBadge(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取用户称号列表
	require.NoError(mbadge.MBadge{}.DB().Delete("", "element_type = ? AND element_id = ?",
		mbadge.TypeVirtualIdol, testElementID).Error)
	var badgeInfos []mbadge.MBadge
	for i := 1; i <= 2; i++ {
		badgeInfos = append(badgeInfos, mbadge.MBadge{
			ID:          int64(i),
			ElementType: mbadge.TypeVirtualIdol,
			ElementID:   testElementID,
			Title:       "测试称号名",
			Intro:       "测试称号简介",
			Cover:       "test://test/202309/27/test.png",
			Icon:        "test://test/202309/27/icon/test.png",
		})
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, mbadge.MBadge{}.TableName(), badgeInfos))

	require.NoError(muserbadge.MUserBadge{}.DB().Delete("", "user_id = ?", testUserID).Error)
	var userBadges []muserbadge.MUserBadge
	for i := 1; i <= 2; i++ {
		userBadges = append(userBadges, muserbadge.MUserBadge{
			UserID:     testUserID,
			BadgeID:    int64(i),
			NO:         int64(i),
			CreateTime: util.TimeNow().Unix(),
		})
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, muserbadge.MUserBadge{}.TableName(), userBadges))

	param.ElementType = mbadge.TypeVirtualIdol
	param.ElementID = testElementID
	param.UserID = testUserID
	c = handler.NewRPCTestContext(api, param)
	resp, err := ActionListBadge(c)
	require.NoError(err)
	data, ok := resp.(listBadgeResp)
	require.True(ok)
	require.Len(data.Badges, 2)
	assert.EqualValues(1, data.Badges[0].ID)
	assert.Equal("测试称号名", data.Badges[0].Title)
	assert.Equal("测试称号简介", data.Badges[0].Intro)
	assert.Equal("000001", data.Badges[0].NO)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", data.Badges[0].CoverURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", data.Badges[0].IconURL)
	assert.NotZero(data.Badges[0].CreateTime)
	assert.EqualValues(2, data.Badges[1].ID)
	assert.NotEmpty(data.Badges[1].Title)
	assert.NotEmpty(data.Badges[1].Intro)
	assert.Equal("000002", data.Badges[1].NO)
	assert.NotEmpty(data.Badges[1].CoverURL)
	assert.NotZero(data.Badges[1].CreateTime)
}
