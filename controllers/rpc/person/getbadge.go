package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/person/mbadge"
)

type getBadgeParam struct {
	BadgeID int64 `json:"badge_id"` // 称号 ID
}

type getBadgeResp struct {
	Badge badgeInfo `json:"badge"`
}

type badgeInfo struct {
	ID       int64  `json:"id"`        // 称号 ID
	Title    string `json:"title"`     // 称号名称
	Intro    string `json:"intro"`     // 称号简介
	CoverURL string `json:"cover_url"` // 称号图片
	IconURL  string `json:"icon_url"`  // 称号 icon
}

func newGetBadgeParam(c *handler.Context) (*getBadgeParam, error) {
	param := new(getBadgeParam)
	err := c.Bind<PERSON>(&param)
	if err != nil || param.BadgeID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

// ActionGetBadge 获取称号信息
/**
 *  @api {post} /rpc/missevan-main/person/get-badge 获取称号信息
 *
 * @apiVersion 0.1.0
 * @apiName get-badge
 * @apiGroup /rpc/missevan-main/person
 *
 * @apiParam {Number} badge_id 称号 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "badge": {
 *           "id": 2333, // 称号 ID
 *           "title": "称号名称",
 *           "intro": "称号简介",
 *           "cover_url": "https://static-test.maoercdn.com/test.png", // 称号图片
 *           "icon_url": "https://static-test.maoercdn.com/icon.png" // 称号 icon 地址
 *         }
 *       }
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     {
 *       "code": 200160004,
 *       "info": "称号不存在"
 *     }
 */
func ActionGetBadge(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGetBadgeParam(c)
	if err != nil {
		return nil, err
	}

	badge, err := mbadge.FindBadgeByID(param.BadgeID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if badge == nil {
		return nil, actionerrors.ErrBadgeNotFound
	}
	return getBadgeResp{
		Badge: badgeInfo{
			ID:       badge.ID,
			Title:    badge.Title,
			Intro:    badge.Intro,
			CoverURL: badge.CoverURL,
			IconURL:  badge.IconURL,
		},
	}, nil
}
