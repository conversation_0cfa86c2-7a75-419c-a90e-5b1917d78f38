package app

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/appupdate"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestActionDownload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	c := handler.NewTestContext(http.MethodGet, "/x/app/download", false, nil)
	_, err := ActionDownload(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/download?device=%d", -1), false, nil)
	_, err = ActionDownload(c)
	assert.Equal(actionerrors.ErrDownloadLimit, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/download?device=%d", appupdate.DeviceIOS), false, nil)
	_, err = ActionDownload(c)
	assert.Equal(actionerrors.ErrDownloadLimit, err)

	// 无下载版本
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/download?device=%d", appupdate.DeviceWindows), false, nil)
	_, err = ActionDownload(c)
	assert.EqualError(err, "服务器内部错误: "+errAppNotFound.Error())

	// 准备测试数据
	now := util.TimeNow()
	appOld := appupdate.AppUpdate{
		ID:         1,
		Title:      "MissEvan",
		Version:    "1",
		Intro:      "1.0.0.21",
		Status:     appupdate.StatusPublished,
		Device:     appupdate.DeviceWindows,
		UpdateTime: now.Add(-time.Minute).Unix(),
		AppURL2:    "",
	}
	appLatest := appupdate.AppUpdate{
		ID:         2,
		Title:      "MissEvan",
		Version:    "2",
		Intro:      "1.0.0.22",
		Status:     appupdate.StatusPublished,
		Device:     appupdate.DeviceWindows,
		UpdateTime: now.Unix(),
		AppURL2:    "test://123.exe",
	}
	defer func() {
		err = service.DB.Table(appupdate.TableName()).Delete(&appOld).Delete(&appLatest).Error
		assert.NoError(err)
	}()
	err = service.DB.Table(appupdate.TableName()).Create(&appOld).Create(&appLatest).Error
	require.NoError(err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/download?device=%d", appupdate.DeviceWindows), false, nil)
	_, err = ActionDownload(c)
	require.Equal(handler.ErrRawResponse, err)
}
