package app

import (
	"errors"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

var errAppNotFound = errors.New("没有可下载的 App 版本")

// Handler return handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "app",
		Actions: map[string]*handler.Action{
			"/download": handler.NewAction(handler.GET, ActionDownload, false),
			"/info":     handler.NewAction(handler.GET, ActionInfo, false),
		},
	}
}
