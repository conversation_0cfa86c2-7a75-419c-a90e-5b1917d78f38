package app

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler()
	checker := tutil.NewKeyChecker(t, tutil.Actions)

	assert.Equal("app", h.Name)
	checker.Check(h.Actions, "/download", "/info")
}
