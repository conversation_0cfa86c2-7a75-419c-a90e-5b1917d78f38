package app

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/appupdate"
	"github.com/MiaoSiLa/missevan-main/service"
)

type appInfoResp struct {
	Name          string        `json:"name"`
	Version       string        `json:"version"`
	Developer     string        `json:"developer"`
	Size          util.Float2DP `json:"size"`
	UpdateDate    string        `json:"update_date"`
	PrivacyURL    string        `json:"privacy_url"`
	PermissionURL string        `json:"permission_url"`
	HotUpdateURL  string        `json:"hot_update_url,omitempty"`
}

// ActionInfo 客户端版本信息接口
/**
 * @api {get} /x/app/info 客户端版本信息接口
 * @apiDescription 获取可用的 App 版本，开发者以及相关信息的接口
 * @apiVersion 0.1.0
 * @apiName app-info
 * @apiGroup app
 *
 * @apiParam {Number} device 客户端类型 0: 安卓, 1: iOS, 2: Windows
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "name": "猫耳FM", // App 名称
 *         "version": "5.5.1", // App 版本
 *         "developer": "北京喵斯拉网络科技有限公司", // 开发者名称
 *         "size": 70.80, // App 安装包大小，单位：兆字节，MB
 *         "update_date": "2021-06-28", // App 更新时间
 *         "privacy_url": "https://link.missevan.com/rule/privacy", // 隐私政策链接
 *         "permission_url": "https://link.missevan.com/rule/app-permission-android", // 权限用途链接
 *         "hot_update_url": "https://static.maoercdn.com/test.json" // Windows 热更新配置
 *       }
 *     }
 *
 */
func ActionInfo(c *handler.Context) (handler.ActionResponse, error) {
	device, err := c.GetParamInt("device")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if device < 0 || device > appupdate.DeviceHarmonyOS {
		return nil, actionerrors.ErrParams
	}

	appInfo, err := appupdate.Latest(device)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if appInfo == nil {
		return nil, actionerrors.ErrServerInternal(errAppNotFound, nil)
	}

	resp := &appInfoResp{
		Name:          appInfo.Title,
		Developer:     appInfo.Developer,
		Size:          util.Float2DP(appInfo.Size),
		UpdateDate:    time.Unix(appInfo.UpdateTime, 0).Format(util.TimeFormatYMD),
		PrivacyURL:    appInfo.PrivacyURL,
		PermissionURL: appInfo.PermissionURL,
	}

	// 设置 version
	resp.Version = appInfo.Intro
	// TODO: 需要统一版本号所在的字段
	if device == appupdate.DeviceIOS {
		resp.Version = appInfo.Version
	}

	// 设置 Windows 热更新链接
	if device == appupdate.DeviceWindows && appInfo.AppURL != "" {
		resp.HotUpdateURL = service.Storage.Parse(appInfo.AppURL)
	}

	return resp, nil
}
