package sound

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// Handler return handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "sound",
		Actions: map[string]*handler.Action{
			// iOS < 6.1.3 Android < 6.1.3 使用 GET 方式请求 /sound/ts 投食接口
			"ts": handler.NewAction(handler.GET, ActionTsOlderThan613, true),
		},
	}
}

// HandlerV2 return handlerV2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "sound",
		Actions: map[string]*handler.ActionV2{
			"ts": handler.NewActionV2(handler.POST, ActionTs, handler.ActionOption{LoginRequired: true}),
		},
	}
}
