package sound

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	msound "github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/user"
	keys2 "github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

var tsAPI = "/x/sound/ts"
var tsAPIOlderThan613Fmt = "/sound/ts?sound_id=%d"

func TestActionTs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := map[string]int64{
		"sound_id": 1217691,
		"num":      1,
	}
	err := service.DB.Table(msound.MSound{}.TableName()).Where("id = ?", param["sound_id"]).UpdateColumn("point", 0).Error
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, tsAPI, true, param)
	c.User().ID = int64(346286)
	data, message, err := ActionTs(c)
	require.NoError(err)
	assert.Equal("投食成功，此音共收到 1 个小鱼干", message)
	resp, ok := data.(*tsResp)
	require.True(ok)
	assert.Equal(&tsResp{
		Point: 1,
	}, resp)
}

func TestActionTsOlderThan613(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testSoundID := int64(1217691)
	err := service.DB.Table(msound.MSound{}.TableName()).Where("id = ?", testSoundID).UpdateColumn("point", 0).Error
	require.NoError(err)

	// 测试 Android < 5.7.1
	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf(tsAPIOlderThan613Fmt, testSoundID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/5.7.0 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	data, err := ActionTsOlderThan613(c)
	require.NoError(err)
	assert.Equal("投食成功，此音共收到 1 个小鱼干", data)

	// 测试 iOS < 4.8.3
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf(tsAPIOlderThan613Fmt, testSoundID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/4.8.2 (iOS;12.0.1;iPhone7,2)")
	data, err = ActionTsOlderThan613(c)
	require.NoError(err)
	assert.Equal("投食成功，此音共收到 2 个小鱼干", data)

	// 测试 Android < 6.1.3
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf(tsAPIOlderThan613Fmt, testSoundID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.2 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	data, err = ActionTsOlderThan613(c)
	require.NoError(err)
	resp, ok := data.(tsOlderThan613Resp)
	require.True(ok)
	assert.Equal(tsOlderThan613Resp{
		Point: 3,
		Msg:   "投食成功，此音共收到 3 个小鱼干",
	}, resp)

	// 测试 iOS < 6.1.3
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf(tsAPIOlderThan613Fmt, testSoundID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.2 (iOS;12.0.1;iPhone7,2)")
	data, err = ActionTsOlderThan613(c)
	require.NoError(err)
	resp, ok = data.(tsOlderThan613Resp)
	require.True(ok)
	assert.Equal(tsOlderThan613Resp{
		Point: 4,
		Msg:   "投食成功，此音共收到 4 个小鱼干",
	}, resp)

	// 测试 Android ≥ 6.1.3
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf(tsAPIOlderThan613Fmt, testSoundID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.3 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	_, err = ActionTsOlderThan613(c)
	assert.Error(err, "非法请求")

	// 测试 iOS ≥ 6.1.3
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf(tsAPIOlderThan613Fmt, testSoundID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.3 (iOS;12.0.1;iPhone7,2)")
	_, err = ActionTsOlderThan613(c)
	assert.Error(err, "非法请求")
}

func TestNewTsParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	param := map[string]int64{
		"sound_id": -1,
		"num":      1,
	}
	c := handler.NewTestContext(http.MethodPost, tsAPI, true, param)
	p, err := newTsParam(c)
	assert.EqualError(err, "参数错误")
	assert.Nil(p)

	// 测试不能进行投食操作的音频
	param["sound_id"] = 99999
	c = handler.NewTestContext(http.MethodPost, tsAPI, true, param)
	p, err = newTsParam(c)
	assert.EqualError(err, "暂不能对当前内容进行此操作")
	assert.Nil(p)

	// 测试用户小鱼干数量 < 投食数量
	testUserID := int64(346286)
	param["sound_id"] = 1217691
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID).UpdateColumn("point", 0).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, tsAPI, true, param)
	c.User().ID = testUserID
	p, err = newTsParam(c)
	assert.EqualError(err, "没有小鱼干可以投食了 T_T")
	assert.Nil(p)

	// 测试触发每日投食音频上限
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID).UpdateColumn("point", 59).Error
	require.NoError(err)
	nowDate := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	key := keys.LockUserTsSound3.Format(testUserID, nowDate, param["sound_id"])
	err = service.Redis.Set(key, TsSoundPointLimitOneDay, 5*time.Minute).Err()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, tsAPI, true, param)
	c.User().ID = testUserID
	p, err = newTsParam(c)
	assert.EqualError(err, fmt.Sprintf("每天只能对同一音频投食 %d 小鱼干哦", TsSoundPointLimitOneDay))
	assert.Nil(p)

	// 测试触发每日投食剧集上限
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	key = keys.LockUserTsDrama3.Format(testUserID, nowDate, int64(52347))
	err = service.Redis.Set(key, TsDramaPointLimitOneDay, 5*time.Minute).Err()
	require.NoError(err)
	param["sound_id"] = 1
	c = handler.NewTestContext(http.MethodPost, tsAPI, true, param)
	c.User().ID = testUserID
	p, err = newTsParam(c)
	assert.EqualError(err, fmt.Sprintf("每天只能对同一剧集投食 %d 小鱼干哦", TsDramaPointLimitOneDay))
	assert.Nil(p)
}

func TestTsParam_getTsSound(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试成功获取投食音频信息
	testSoundID := int64(1217691)
	p := tsParam{
		SoundID: testSoundID,
	}
	err := p.getTsSound()
	require.NoError(err)
	assert.NotNil(p.sound)
	assert.EqualValues(testSoundID, p.sound.ID)

	// 测试投食音频不存在
	p1 := tsParam{
		SoundID: int64(999999),
	}
	err = p1.getTsSound()
	require.NoError(err)
	assert.Nil(p1.sound)
}

func TestTsParam_checkUserPointEnough(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户不存在
	testUserID := int64(99999)
	testSoundID := int64(1)
	p := tsParam{
		SoundID: testSoundID,
		userID:  testUserID,
		Num:     1,
	}
	res, err := p.checkUserPointEnough()
	require.NoError(err)
	assert.False(res)

	// 测试用户小鱼干数量 < 投食数量
	p.userID = int64(346286)
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", p.userID).UpdateColumn("point", 0).Error
	require.NoError(err)
	res, err = p.checkUserPointEnough()
	require.NoError(err)
	assert.False(res)

	// 测试用户小鱼干数量 ≥ 投食数量
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", p.userID).UpdateColumn("point", 1).Error
	require.NoError(err)
	res, err = p.checkUserPointEnough()
	require.NoError(err)
	assert.True(res)
}

func TestTsParam_checkTsLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1)
	testSoundID := int64(1)
	testDramaID := int64(52347)
	nowDate := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	key1 := keys.LockUserTsSound3.Format(testUserID, nowDate, testSoundID)
	key2 := keys.LockUserTsDrama3.Format(testUserID, nowDate, testDramaID)
	err := service.Redis.Del(key1, key2).Err()
	require.NoError(err)

	// 测试没有触发每日投食限制
	p := tsParam{
		SoundID: testSoundID,
		userID:  testUserID,
		Num:     TsPointNumTen,
	}
	err = p.checkTsLimit()
	require.NoError(err)
	// 断言没有回滚
	count, err := service.Redis.Get(key1).Int64()
	require.NoError(err)
	assert.EqualValues(p.Num, count)
	count, err = service.Redis.Get(key2).Int64()
	require.NoError(err)
	assert.EqualValues(p.Num, count)

	// 测试触发每日音频投食上限
	testSoundLimit := 199
	err = service.Redis.Set(key1, testSoundLimit, 5*time.Minute).Err()
	require.NoError(err)
	testDramaLimit := 1
	err = service.Redis.Set(key2, testDramaLimit, 5*time.Minute).Err()
	require.NoError(err)
	p.limitKey = limitKey{}
	err = p.checkTsLimit()
	assert.EqualError(err, fmt.Sprintf("每天只能对同一音频投食 %d 小鱼干哦", TsSoundPointLimitOneDay))
	// 断言回滚
	count, err = service.Redis.Get(key1).Int64()
	require.NoError(err)
	assert.EqualValues(testSoundLimit, count)
	count, err = service.Redis.Get(key2).Int64()
	require.NoError(err)
	assert.EqualValues(testDramaLimit, count)

	// 测试触发每日剧集投食上限
	testSoundLimit = 1
	err = service.Redis.Set(key1, testSoundLimit, 5*time.Minute).Err()
	require.NoError(err)
	testDramaLimit = 499
	err = service.Redis.Set(key2, testDramaLimit, 5*time.Minute).Err()
	require.NoError(err)
	p.limitKey = limitKey{}
	err = p.checkTsLimit()
	assert.EqualError(err, fmt.Sprintf("每天只能对同一剧集投食 %d 小鱼干哦", TsDramaPointLimitOneDay))
	// 断言回滚
	count, err = service.Redis.Get(key1).Int64()
	require.NoError(err)
	assert.EqualValues(testSoundLimit, count)
	count, err = service.Redis.Get(key2).Int64()
	require.NoError(err)
	assert.EqualValues(testDramaLimit, count)
}

func TestTsParam_checkTsSoundLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1)
	testSoundID := int64(1)
	nowDate := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	key := keys.LockUserTsSound3.Format(testUserID, nowDate, testSoundID)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	// 测试没有达到每日音频投食上限
	p := tsParam{
		SoundID: testSoundID,
		userID:  testUserID,
		Num:     1,
	}
	res, err := p.checkTsSoundLimit()
	require.NoError(err)
	assert.True(res)

	// 测试达到每日音频投食上限
	err = service.Redis.Set(key, TsSoundPointLimitOneDay, 5*time.Minute).Err()
	require.NoError(err)
	res, err = p.checkTsSoundLimit()
	require.NoError(err)
	assert.False(res)
}

func TestTsParam_checkTsDramaLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1)
	testSoundID := int64(1)
	testDramaID := int64(52347)
	nowDate := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	key := keys.LockUserTsDrama3.Format(testUserID, nowDate, testDramaID)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	// 测试没有达到每日剧集投食上限
	p := tsParam{
		SoundID: testSoundID,
		userID:  testUserID,
		Num:     1,
	}
	res, err := p.checkTsDramaLimit()
	require.NoError(err)
	assert.True(res)

	// 测试达到每日剧集投食上限
	err = service.Redis.Set(key, TsDramaPointLimitOneDay, 5*time.Minute).Err()
	require.NoError(err)
	res, err = p.checkTsDramaLimit()
	require.NoError(err)
	assert.False(res)
}

func TestTsParam_ts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testSoundID := int64(1217691)
	testUserID := int64(346286)
	err := service.DB.Table(msound.MSound{}.TableName()).Where("id = ?", testSoundID).UpdateColumn("point", 0).Error
	require.NoError(err)
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID).UpdateColumn("point", 59).Error
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, tsAPI, true, nil)
	// 测试投食 1 个小鱼干
	p := tsParam{
		SoundID: testSoundID,
		userID:  testUserID,
		Num:     TsPointNumOne,
		c:       c,
	}
	err = p.getTsSound()
	require.NoError(err)
	assert.EqualValues(0, p.sound.Point)
	var expected, userPoint, upPoint int64
	// 获取投食前用户的鱼干数
	err = service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", p.userID).Row().Scan(&userPoint)
	require.NoError(err)
	// 获取投食前 UP 主的鱼干数
	err = service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", p.sound.UserID).Row().Scan(&upPoint)
	require.NoError(err)
	// 清空 databus 数据
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	data, err := p.ts()
	require.NoError(err)
	require.NotNil(data)
	// 断言音频的鱼干数
	err = service.DB.Table(msound.MSound{}.TableName()).Select("point").
		Where("id = ?", p.SoundID).Row().Scan(&expected)
	require.NoError(err)
	assert.EqualValues(expected, data.Point)
	// 断言用户的鱼干数
	originUserPoint := userPoint
	err = service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", p.userID).Row().Scan(&userPoint)
	require.NoError(err)
	assert.EqualValues(originUserPoint-int64(p.Num), userPoint)
	// 断言 UP 主的鱼干数
	originUpPoint := upPoint
	err = service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", p.sound.UserID).Row().Scan(&upPoint)
	require.NoError(err)
	assert.EqualValues(originUpPoint, upPoint)
	// 断言 databus
	msgs := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	key := keys2.DatabusKeyUserPointDetailLog1.Format(p.userID)
	require.EqualValues(key, m.Key)

	// 测试投食 10 个小鱼干
	p.Num = TsPointNumTen
	err = p.getTsSound()
	require.NoError(err)
	assert.EqualValues(data.Point, p.sound.Point)
	// 清空 databus 数据
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	data, err = p.ts()
	require.NoError(err)
	require.NotNil(data)
	// 断言音频的鱼干数
	err = service.DB.Table(msound.MSound{}.TableName()).Select("point").
		Where("id = ?", p.SoundID).Row().Scan(&expected)
	require.NoError(err)
	assert.EqualValues(expected, data.Point)
	// 断言用户的鱼干数
	originUserPoint = userPoint
	err = service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", p.userID).Row().Scan(&userPoint)
	require.NoError(err)
	assert.EqualValues(originUserPoint-int64(p.Num), userPoint)
	// 断言 UP 主的鱼干数
	originUpPoint = upPoint
	err = service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", p.sound.UserID).Row().Scan(&upPoint)
	require.NoError(err)
	assert.EqualValues(originUpPoint+TsSoundReward, upPoint)
	// 断言 databus
	msgs = service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 2)
	m = <-msgs
	key1 := keys2.DatabusKeyUserPointDetailLog1.Format(p.userID)
	require.EqualValues(key1, m.Key)
	m = <-msgs
	key2 := keys2.DatabusKeyUserPointDetailLog1.Format(p.sound.UserID)
	require.EqualValues(key2, m.Key)

	// 测试 UP 主自己投食
	p.userID = int64(12)
	// 重新赋值，排除上面测试的影响
	p.hasReward = false
	err = p.getTsSound()
	require.NoError(err)
	assert.EqualValues(data.Point, p.sound.Point)
	// 清空 databus 数据
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	data, err = p.ts()
	require.NoError(err)
	require.NotNil(data)
	// 断言音频的鱼干数
	err = service.DB.Table(msound.MSound{}.TableName()).Select("point").
		Where("id = ?", p.SoundID).Row().Scan(&expected)
	require.NoError(err)
	assert.EqualValues(expected, data.Point)
	// 断言 UP 主的鱼干数
	originUpPoint = upPoint
	err = service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", p.userID).Row().Scan(&upPoint)
	require.NoError(err)
	assert.EqualValues(originUpPoint-int64(p.Num), upPoint)
	// 断言 databus
	msgs = service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m = <-msgs
	key = keys2.DatabusKeyUserPointDetailLog1.Format(p.userID)
	require.EqualValues(key, m.Key)

	// 测试投食失败后，扣除记录在限制 key 中的鱼干数
	p.Num = TsPointNumOne
	p.limitKey.soundKey = keys.LockUserTsSound3.Format(p.userID, util.TimeNow().Format(util.TimeFormatYMDWithNoSpace), p.SoundID)
	originalCount, err := service.Redis.Get(p.limitKey.soundKey).Int64()
	require.NoError(err)
	assert.True(originalCount > 0)
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", p.userID).UpdateColumn("point", 0).Error
	require.NoError(err)
	_, err = p.ts()
	assert.EqualError(err, "没有小鱼干可以投食了 T_T")
	// 断言扣除记录在限制 key 中的鱼干数
	count, err := service.Redis.Get(p.limitKey.soundKey).Int64()
	require.NoError(err)
	assert.Equal(originalCount-int64(p.Num), count)
}
