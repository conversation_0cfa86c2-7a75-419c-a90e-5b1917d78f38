package sound

import (
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	msound "github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/mpointfeed"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// 允许投食的鱼干数量
const (
	TsPointNumOne = 1
	TsPointNumTen = 10
)

// 投食限制
const (
	TsSoundPointLimitOneDay = 200 // 用户每天给同一个音频投食的鱼干数量上限
	TsDramaPointLimitOneDay = 500 // 用户每天给同一个剧集投食的鱼干数量上限
)

// 投食奖励和获取奖励机制
const (
	TsSoundReward  = 5  // UP 主获得奖励的鱼干数量
	TsSoundTrigger = 10 // UP 主获得奖励触发机制的数量（新收到的小鱼干数量满 10）
)

type tsParam struct {
	SoundID int64 `json:"sound_id" form:"sound_id"`
	Num     int   `json:"num" form:"num"`

	userID    int64
	hasReward bool // 投食后，音频 UP 主是否获取奖励

	c     *handler.Context
	sound *msound.MSound

	limitKey limitKey
}

type limitKey struct {
	soundKey string
	dramaKey string
}

type tsResp struct {
	Point int64 `json:"point"`
}

// ActionTs 给音频投食
/**
 * @api {post} /x/sound/ts 给音频投食
 *
 * @apiVersion 0.1.0
 * @apiName ts
 * @apiGroup /x/sound
 *
 * @apiPermission user
 *
 * @apiParam {Number} sound_id 单音 ID
 * @apiParam {number=1,10} [num=1] 鱼干数量
 *
 * @apiSuccessExample {json} 投食成功
 *     {
 *       "code": 0,
 *       "message": "投食成功，此音共收到 50 个小鱼干",
 *       "data": {
 *         "point": 50 // 投食成功后音频收到的小鱼干数量
 *       }
 *     }
 * @apiErrorExample {json} 鱼干余额不足:
 *     {
 *       "code": 200360104,
 *       "message": "没有小鱼干可以投食了 T_T",
 *       "data": null
 *     }
 */
func ActionTs(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newTsParam(c)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.ts()
	if err != nil {
		return nil, "", err
	}
	return resp, fmt.Sprintf("投食成功，此音共收到 %d 个小鱼干", resp.Point), nil
}

func newTsParam(c *handler.Context) (*tsParam, error) {
	param := new(tsParam)
	var err error
	if c.Equip().IsAppOlderThan("6.1.3", "6.1.3") {
		// WORKAROUND: iOS < 6.1.3 Android < 6.1.3 客户端使用 GET 方式请求，sound_id 为 Query 参数
		param.SoundID, err = c.GetParamInt64("sound_id")
	} else {
		err = c.Bind(param)
	}
	if err != nil || param.SoundID <= 0 || param.Num < 0 {
		return nil, actionerrors.ErrParams
	}
	if param.Num == 0 {
		// 默认值
		param.Num = TsPointNumOne
	}
	if param.Num != TsPointNumOne && param.Num != TsPointNumTen {
		return nil, actionerrors.ErrParams
	}
	err = param.getTsSound()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.sound == nil {
		return nil, actionerrors.ErrNotFound(200110001, "暂不能对当前内容进行此操作")
	}
	param.userID = c.UserID()
	pass, err := param.checkUserPointEnough()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !pass {
		return nil, actionerrors.ErrNotEnoughPoint
	}
	err = param.checkTsLimit()
	if err != nil {
		return nil, err
	}
	param.c = c
	return param, nil
}

func (param *tsParam) getTsSound() error {
	var sound msound.MSound
	err := service.DB.Table(msound.MSound{}.TableName()).
		Select("id, catalog_id, user_id, point").
		Where("id = ? AND checked IN (?)", param.SoundID, []int64{msound.CheckedPass, msound.CheckedPolice}).
		Take(&sound).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil
		}
		return err
	}
	param.sound = &sound
	return nil
}

// checkUserPointEnough 检查用户的鱼干余额是否 ≥ 要投食的鱼干数量，是则返回 true，否则返回 false
func (param *tsParam) checkUserPointEnough() (bool, error) {
	var userPoint int64
	err := service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", param.userID).Row().Scan(&userPoint)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return false, err
	}
	return userPoint >= int64(param.Num), nil
}

func getLimitKeyExpireDuration() time.Duration {
	now := util.TimeNow()
	endTime := util.BeginningOfDay(now).AddDate(0, 0, 1)
	// 多给 5 秒，避免时间上的误差
	return endTime.Add(5 * time.Second).Sub(now)
}

// checkTsLimit 检查用户每日投食限制
func (param *tsParam) checkTsLimit() error {
	pass := false
	defer func() {
		if !pass {
			param.rollbackFailedTsLimit()
		}
	}()
	passSoundLimit, err := param.checkTsSoundLimit()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if !passSoundLimit {
		return actionerrors.ErrForbidden(handler.CodeUnknownError, fmt.Sprintf("每天只能对同一音频投食 %d 小鱼干哦", TsSoundPointLimitOneDay))
	}
	passDramaLimit, err := param.checkTsDramaLimit()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if !passDramaLimit {
		return actionerrors.ErrForbidden(handler.CodeUnknownError, fmt.Sprintf("每天只能对同一剧集投食 %d 小鱼干哦", TsDramaPointLimitOneDay))
	}
	pass = true
	return nil
}

// checkTsSoundLimit 检查用户每日音频投食数量是否 ≤ 上限，是则返回 true，否则返回 false
func (param *tsParam) checkTsSoundLimit() (bool, error) {
	key := keys.LockUserTsSound3.Format(param.userID, util.TimeNow().Format(util.TimeFormatYMDWithNoSpace), param.SoundID)
	redis := service.Redis
	currentCount, err := redis.Get(key).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return false, err
	}
	if currentCount >= TsSoundPointLimitOneDay {
		return false, nil
	}
	pipe := redis.TxPipeline()
	incrCmd := pipe.IncrBy(key, int64(param.Num))
	if currentCount == 0 {
		pipe.Expire(key, getLimitKeyExpireDuration())
	}
	if _, err := pipe.Exec(); err != nil {
		return false, err
	}
	param.limitKey.soundKey = key
	return incrCmd.Val() <= TsSoundPointLimitOneDay, nil
}

// checkTsDramaLimit 检查用户每日剧集投食数量是否 ≤ 上限，是则返回 true，否则返回 false
func (param *tsParam) checkTsDramaLimit() (bool, error) {
	dramaID, err := dramainfo.GetDramaIDBySoundID(param.SoundID)
	if err != nil {
		return false, err
	}
	// 无需检查不在剧集中的音频
	if dramaID == 0 {
		return true, nil
	}
	key := keys.LockUserTsDrama3.Format(param.userID, util.TimeNow().Format(util.TimeFormatYMDWithNoSpace), dramaID)
	redis := service.Redis
	currentCount, err := redis.Get(key).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return false, err
	}
	if currentCount >= TsDramaPointLimitOneDay {
		return false, nil
	}
	pipe := redis.TxPipeline()
	incrCmd := pipe.IncrBy(key, int64(param.Num))
	if currentCount == 0 {
		pipe.Expire(key, getLimitKeyExpireDuration())
	}
	if _, err = pipe.Exec(); err != nil {
		return false, err
	}
	param.limitKey.dramaKey = key
	return incrCmd.Val() <= TsDramaPointLimitOneDay, nil
}

// rollbackFailedTsLimit 投食失败后，扣除记录在限制 key 中的鱼干数
func (param *tsParam) rollbackFailedTsLimit() {
	if param.limitKey.soundKey != "" {
		_, err := service.Redis.DecrBy(param.limitKey.soundKey, int64(param.Num)).Result()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	if param.limitKey.dramaKey != "" {
		_, err := service.Redis.DecrBy(param.limitKey.dramaKey, int64(param.Num)).Result()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
}

func (param *tsParam) ts() (*tsResp, error) {
	errNotEnoughPoint := errors.New("not enough point")
	// 判断数据库事务 commit 操作是否执行，未执行或执行失败后要扣除记录在限制 key 中的鱼干数
	var isCommit bool
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if param.sound.UserID != param.userID {
			pf := mpointfeed.NewMPointFeed(param.SoundID, param.userID, int64(param.sound.CatalogID), param.Num)
			err := tx.Table(pf.TableName()).Create(pf).Error
			if err != nil {
				return err
			}
			// 新收到的小鱼干数量满足 TsSoundTrigger 时，需要给 UP 主奖励
			if param.Num == TsSoundTrigger {
				// 一次性投食鱼干的数量正好是 TsSoundTrigger 时，直接给 UP 主奖励
				param.hasReward = true
			} else {
				var count int64
				err = tx.Table(mpointfeed.MPointFeed{}.TableName()).
					Where("sound_id = ? AND num = ?", param.SoundID, TsPointNumOne).Count(&count).Error
				if err != nil {
					return err
				}
				if count%TsSoundTrigger == 0 {
					param.hasReward = true
				}
			}
			if param.hasReward {
				err = tx.Table(user.MowangskUser{}.TableName()).Where("id = ?", param.sound.UserID).
					Update("point", gorm.Expr("point + ?", TsSoundReward)).Error
				if err != nil {
					return err
				}
			}
		}
		// 扣除用户消费的小鱼干
		query := tx.Table(user.MowangskUser{}.TableName()).Where("id = ? AND point >= ?", param.userID, param.Num).
			Update("point", gorm.Expr("point - ?", param.Num))
		if query.Error != nil {
			return query.Error
		}
		if query.RowsAffected == 0 {
			return errNotEnoughPoint
		}
		// 更新音频获取的小鱼干
		err := tx.Table(msound.MSound{}.TableName()).Where("id = ?", param.SoundID).
			Update("point", gorm.Expr("point + ?", param.Num)).Error
		if err != nil {
			return err
		}
		isCommit = true
		return nil
	})
	if !isCommit {
		// 扣除记录在限制 key 中的鱼干数
		param.rollbackFailedTsLimit()
	}
	if err != nil {
		if err == errNotEnoughPoint {
			return nil, actionerrors.ErrNotEnoughPoint
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	// 记录到 databus 日志中
	param.sendLog()
	return &tsResp{
		Point: param.sound.Point + int64(param.Num),
	}, nil
}

func (param *tsParam) sendLog() {
	e := util.NewEquipment(param.c.Request().UserAgent())
	var origin int
	switch e.OS {
	case util.Android, util.IOS, util.HarmonyOS:
		origin = user.TypeOriginApp
	case util.Web:
		origin = user.TypeOriginWeb
	case util.MobileWeb:
		origin = user.TypeOriginMobileWeb
	}
	// 记录用户投食小鱼干日志
	mpointfeed.SendDataBusLog(param.userID, param.SoundID, -param.Num, origin, user.TypeSoundTs)
	if param.hasReward {
		// 记录 UP 主收到小鱼干奖励日志
		mpointfeed.SendDataBusLog(param.sound.UserID, param.SoundID, TsSoundReward, origin, mpointfeed.DataBusLogTypeSoundTsReward)
	}
}

type tsOlderThan613Resp struct {
	Point int64  `json:"point"`
	Msg   string `json:"msg"`
}

// ActionTsOlderThan613 给音频投食
/**
 * @api {get} /sound/ts{?sound_id} 给音频投食
 * @apiDescription iOS < 6.1.3 Android < 6.1.3 版本音频投食使用
 *
 * @apiVersion 0.1.0
 * @apiName ts
 * @apiGroup sound
 *
 * @apiPermission user
 *
 * @apiParam {Number} sound_id 单音 ID
 *
 * @apiSuccessExample Success-Response: iOS < 4.8.3 Android < 5.7.1
 *     {
 *       "code": 0,
 *       "info": "投食成功，此音共收到 50 个小鱼干"
 *     }
 * @apiSuccessExample Success-Response: iOS < 6.1.3 Android < 6.1.3
 *     {
 *       "code": 0,
 *       "info": {
 *         "point": 50,
 *         "msg": "投食成功，此音共收到 50 个小鱼干"
 *       }
 *     }
 * @apiErrorExample Error-Response:
 *     {
 *       "code": 200360104,
 *       "info": "没有小鱼干可以投食了 T_T"
 *     }
 */
func ActionTsOlderThan613(c *handler.Context) (handler.ActionResponse, error) {
	e := c.Equip()
	// WORKAROUND: iOS ≥ 6.1.3 Android ≥ 6.1.3 投食音频请求新接口（/x/sound/ts）
	if !e.IsAppOlderThan("6.1.3", "6.1.3") {
		return nil, actionerrors.ErrBadRequest(handler.CodeUnknownError, "非法请求")
	}
	data, message, err := ActionTs(c)
	if err != nil {
		return nil, err
	}
	resp, ok := data.(*tsResp)
	if !ok {
		return nil, actionerrors.ErrServerInternal(errors.New("投食鱼干响应数据异常"), nil)
	}
	// WORKAROUND: iOS < 4.8.3 Android < 5.7.1 不下发 point 和 msg 字段
	if e.IsAppOlderThan("4.8.3", "5.7.1") {
		return message, nil
	}
	return tsOlderThan613Resp{
		Point: resp.Point,
		Msg:   message,
	}, nil
}
