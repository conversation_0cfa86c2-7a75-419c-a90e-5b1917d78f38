package drama

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramataginfo"
)

type dramaTagsResp struct {
	Tags []radiodramataginfo.DramaTags `json:"tags"`
}

// ActionUploadTags 创建剧集时需要筛选的分类标签、内容标签
/**
 * @api {get} /x/drama/upload-tags 剧集分类标签、内容标签
 *
 * @apiVersion 0.1.0
 * @apiName upload-tags
 * @apiGroup x/drama
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {  // 标签不存在时返回 null
 *         "tags": [ // 按数组下发的顺序显示
 *           {
 *             "label": "分类标签", // 标签类型名
 *             "type": 1, // 标签类型 1：分类标签；2：内容标签
 *             "items": [ // 按数组下发的顺序显示
 *               {
 *                 "id": 1,
 *                 "name": "古风"
 *               },
 *               {
 *                 "id": 2,
 *                 "name": "都市"
 *               }
 *             ]
 *           },
 *           {
 *             "label": "内容标签",
 *             "type": 2,
 *             "items": [ // 按数组下发的顺序显示
 *               {
 *                 "id": 1,
 *                 "name": "甜"
 *               },
 *               {
 *                 "id": 2,
 *                 "name": "虐"
 *               }
 *             ]
 *           }
 *         ]
 *       }
 *     }
 */
func ActionUploadTags(c *handler.Context) (handler.ActionResponse, string, error) {
	tags, err := radiodramataginfo.ListDramaUploadTags()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if len(tags) == 0 {
		return nil, "", nil
	}
	return dramaTagsResp{
		Tags: tags,
	}, "", nil
}
