package drama

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler 返回 drama handler
func Handler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "drama",
		Actions: map[string]*handler.ActionV2{
			"batch-subscribe": handler.NewActionV2(handler.POST, ActionBatchSubscribe, handler.ActionOption{LoginRequired: true}),
			"upload-tags":     handler.NewActionV2(handler.GET, ActionUploadTags, handler.ActionOption{LoginRequired: false}),
		},
	}
}
