package drama

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramasubscription"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestActionBatchSubscribe(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	api := "/x/drama/batch-subscribe"

	// 测试请求失败的情况
	errParam := []int64{70803, 71619}
	c := handler.NewTestContext(http.MethodPost, api, true, errParam)
	_, _, err := ActionBatchSubscribe(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试追剧成功
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	param := &batchSubscribeParam{DramaIDsStr: "6,2,4,4,1,2,3,4,5,6"}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	data, msg, err := ActionBatchSubscribe(c)
	require.NoError(err)
	resp, ok := data.(batchSubscribeResp)
	require.True(ok)
	// 部分剧集追剧失败原因：ID 为 3 的剧集已下架；ID 为 5 的剧集不存在；ID 为 6 的剧集已经追剧
	assert.ElementsMatch([]int64{6, 3, 5}, resp.FailedDramaIDs)
	assert.Equal("成功追剧 3 部剧集！", msg)
	var subscribedDramaIDs []int64
	err = radiodramasubscription.RadioDramaSubscription{}.DB().Select("drama_id").
		Where("user_id = ?", c.UserID()).
		Pluck("drama_id", &subscribedDramaIDs).Error
	require.NoError(err)
	assert.ElementsMatch([]int64{1, 2, 4, 6}, subscribedDramaIDs)
	// 验证正常写入 databus
	pubMsg := service.Databus.AppLogPub.DebugPubMsgs()
	// 成功追剧 3 部，ID 为 6 的剧集已经追剧
	require.Len(pubMsg, 3)

	// 测试本次新增追剧数量为 0
	param = &batchSubscribeParam{DramaIDsStr: "6,2,4,1,2,4,6"}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	data, msg, err = ActionBatchSubscribe(c)
	require.NoError(err)
	resp, ok = data.(batchSubscribeResp)
	require.True(ok)
	assert.ElementsMatch([]int64{1, 2, 4, 6}, resp.FailedDramaIDs)
	assert.Equal("成功追剧 0 部剧集！", msg)

	// 测试全部追剧成功
	require.NoError(radiodramasubscription.RadioDramaSubscription{}.DB().Delete("", "user_id = ?", c.UserID()).Error)
	param = &batchSubscribeParam{DramaIDsStr: "2,4,1,2,4"}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	data, msg, err = ActionBatchSubscribe(c)
	require.NoError(err)
	resp, ok = data.(batchSubscribeResp)
	require.True(ok)
	assert.Empty(resp.FailedDramaIDs)
	assert.NotNil(resp.FailedDramaIDs)
	assert.Equal("成功追剧 3 部剧集！", msg)
}
