package drama

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramasubscription"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// subscribeDramasLimit 单次最多可追剧数量
const subscribeDramasLimit = 100

// batchSubscribeParam 批量追剧参数
type batchSubscribeParam struct {
	DramaIDsStr string `json:"drama_ids" form:"drama_ids"`
}

// batchSubscribeResp 批量追剧返回值
type batchSubscribeResp struct {
	FailedDramaIDs []int64 `json:"failed_drama_ids"` // 未成功追剧的剧集 IDs
}

// ActionBatchSubscribe 批量追剧
/**
 * @api {post} /x/drama/batch-subscribe 批量追剧
 *
 * @apiVersion 0.1.0
 * @apiName batch-subscribe
 * @apiGroup x/drama
 *
 * @apiPermission user
 *
 * @apiParam {String} drama_ids 待追剧剧集 IDs 字符串，以半角逗号分隔
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 400
 *     {
 *       "code": 201010002,
 *       "message": "参数错误",
 *       "data": null
 *     }
 * @apiSuccessExample {json} Error-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "成功追剧 43 部剧集！",
 *       "data": {
 *         "failed_drama_ids": [39006, 43118] // 本次未成功追剧的剧集 IDs
 *       }
 *     }
 */
func ActionBatchSubscribe(c *handler.Context) (handler.ActionResponse, string, error) {
	param := new(batchSubscribeParam)
	err := c.Bind(param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	dramaIDs, err := util.SplitToInt64Array(param.DramaIDsStr, ",")
	if err != nil || len(dramaIDs) == 0 {
		return nil, "", actionerrors.ErrParams
	}
	dramaIDs = sets.Uniq(dramaIDs)
	if len(dramaIDs) > subscribeDramasLimit {
		return nil, "", actionerrors.ErrParams
	}

	userID := c.UserID()
	lock := keys.LockUserBatchSubscribeDramas1.Format(userID)
	ok, err := service.Redis.SetNX(lock, "1", 10*time.Second).Result()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)

	subscribedDramaIDs, err := radiodramasubscription.BatchSubscribe(dramaIDs, userID)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	subscribedDramaIDsNum := len(subscribedDramaIDs)
	if subscribedDramaIDsNum == 0 {
		return batchSubscribeResp{FailedDramaIDs: dramaIDs}, "成功追剧 0 部剧集！", nil
	}
	util.Go(func() {
		// TODO: 后续改为写入一条批量追剧消息
		for _, dramaID := range subscribedDramaIDs {
			radiodramasubscription.SendSubscribeLog(userID, dramaID, radiodramasubscription.LogTypeSubscribe)
		}
	})

	cacheKeyWeb := keys.KeyWebPersonHomepageSubscription1.Format(userID)
	cacheKeyApp := keys.KeyAppPersonHomepage1.Format(userID)
	err = service.LRURedis.Del(cacheKeyWeb, cacheKeyApp).Err()
	if err != nil {
		logger.WithField("user_id", userID).Errorf("批量追剧后删除 App 个人主页缓存或 Web 个人主页追剧信息第一页缓存出错：%v", err)
		// PASS
	}

	msg := fmt.Sprintf("成功追剧 %d 部剧集！", subscribedDramaIDsNum)
	failedDramaIDs := sets.Diff(dramaIDs, subscribedDramaIDs)
	return batchSubscribeResp{FailedDramaIDs: failedDramaIDs}, msg, nil
}
