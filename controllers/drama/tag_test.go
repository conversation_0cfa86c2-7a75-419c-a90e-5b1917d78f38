package drama

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramataginfo"
)

func TestActionUploadTags(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "/x/drama/upload-tags", true, nil)
	data, message, err := ActionUploadTags(c)
	require.NoError(err)
	assert.Equal("", message)
	require.NotNil(data)
	resp, ok := data.(dramaTagsResp)
	assert.True(ok)
	require.EqualValues(2, len(resp.Tags))
	assert.Equal("分类标签", resp.Tags[0].Label)
	assert.Equal(radiodramataginfo.TypeClassification, resp.Tags[0].Type)
	assert.NotEmpty(resp.Tags[0].Items)
	assert.Equal("内容标签", resp.Tags[1].Label)
	assert.Equal(radiodramataginfo.TypeContent, resp.Tags[1].Type)
	assert.NotEmpty(resp.Tags[1].Items)
}
