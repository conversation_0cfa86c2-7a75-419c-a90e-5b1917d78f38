package health

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/config"
)

// Handler returns the registered handler
func Handler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "health",
		Actions: map[string]*handler.Action{
			"": handler.NewAction(handler.GET, actionHealth, false),
		},
	}
}

func actionHealth(c *handler.Context) (handler.ActionResponse, error) {
	return "success", nil
}
