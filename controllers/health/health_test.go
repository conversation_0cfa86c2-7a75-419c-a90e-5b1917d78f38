package health

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionHealth(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	c := handler.NewTestContext(http.MethodGet, "/health", false, nil)
	r, err := actionHealth(c)
	require.NoError(err)
	assert.Equal("success", r)
}
