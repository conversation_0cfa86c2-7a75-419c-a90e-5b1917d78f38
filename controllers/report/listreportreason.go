package report

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/report/mreportreason"
)

const (
	sceneSound       = 1 // 稿件举报
	sceneInteraction = 2 // 互动举报
	sceneLive        = 3 // 直播举报
)

type listReportReasonResp struct {
	Reasons []*reportReason `json:"reasons"`
}

type reportReason struct {
	ID         int64       `json:"id"`
	Name       string      `json:"name"`
	SubReasons []subReason `json:"sub_reasons"`
}

type subReason struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	OtherReason *bool  `json:"other_reason,omitempty"`
}

// ActionListReportReason 获取举报原因分类列表
/**
 * @api {get} /x/report/list-report-reason 获取举报原因分类列表
 *
 * @apiVersion 0.1.0
 * @apiName list-report-reason
 * @apiGroup /x/report/
 *
 * @apiParam {number=1,2,3} scene 举报场景。1: 稿件举报；2: 互动举报；3: 直播举报
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "reasons": [
 *         {
 *           "id": 6,
 *           "name": "违反法律法规",
 *           "sub_reasons": [
 *             {
 *               "id": 111, // 该字段为举报原因 id
 *               "name": "违法违禁"
 *             },
 *             {
 *               "id": 112,
 *               "name": "赌博诈骗"
 *             },
 *             {
 *               "id": 119,
 *               "name": "其他"
 *               "other_reason": true // 举报原因为“其他”，只有为“其他”时才下发
 *             }
 *           ]
 *         },
 *         {
 *           "id": 7,
 *           "name": "谣言及不实信息",
 *           "sub_reasons": [
 *             {
 *               "id": 113,
 *               "name": "涉政谣言"
 *             }
 *           ]
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} message 服务器内部错误
 * @apiError (500) {Object} data null
 */
func ActionListReportReason(c *handler.Context) (handler.ActionResponse, string, error) {
	scene, _ := c.GetParamInt("scene")
	if scene <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	var sceneVal int
	switch scene {
	case sceneSound:
		sceneVal = mreportreason.SceneSound
	case sceneInteraction:
		sceneVal = mreportreason.SceneInteraction
	case sceneLive:
		sceneVal = mreportreason.SceneLive
	default:
		return nil, "", actionerrors.ErrParams
	}
	reasons, err := mreportreason.ListReportReasonsByScene(sceneVal)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	reasonMap := make(map[int64]*reportReason, len(reasons))
	for _, reason := range reasons {
		if reason.ParentID == 0 {
			reasonMap[reason.ID] = &reportReason{
				ID:   reason.ID,
				Name: reason.Name,
			}
		} else {
			if r, ok := reasonMap[reason.ParentID]; ok {
				subReasonInfo := subReason{
					ID:   reason.ID,
					Name: reason.Name,
				}
				if reason.Name == "其他" {
					subReasonInfo.OtherReason = util.NewBool(true)
				}
				r.SubReasons = append(r.SubReasons, subReasonInfo)
			} else {
				logger.WithFields(logger.Fields{
					"id":   reason.ID,
					"name": reason.Name,
				}).Error("该举报原因无主分类")
				// PASS
			}
		}
	}

	resp := listReportReasonResp{
		Reasons: make([]*reportReason, 0, len(reasonMap)),
	}
	for i := 0; i < len(reasons) && reasons[i].ParentID == 0; i++ {
		if r, ok := reasonMap[reasons[i].ID]; ok {
			resp.Reasons = append(resp.Reasons, r)
		} else {
			logger.WithFields(logger.Fields{
				"id":   reasons[i].ID,
				"name": reasons[i].Name,
			}).Error("不存在举报原因")
			// PASS
		}
	}
	return resp, "", nil
}
