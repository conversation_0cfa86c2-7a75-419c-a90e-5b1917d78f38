package report

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(listReportReasonResp{}, "reasons")
	kc.Check(reportReason{}, "id", "name", "sub_reasons")
	kc.Check(subReason{}, "id", "name", "other_reason")
	kc.CheckOmitEmpty(subReason{}, "other_reason")
}

func TestActionListReportReason(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/x/report/list-report-reason"
	c := handler.NewTestContext(http.MethodGet, api, false, nil)
	_, _, err := ActionListReportReason(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试场景不存在
	scene := 233
	uri := fmt.Sprintf("%s?scene=%d", api, scene)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	_, _, err = ActionListReportReason(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取举报原因分类列表
	scene = sceneSound
	uri = fmt.Sprintf("%s?scene=%d", api, scene)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	res, _, err := ActionListReportReason(c)
	require.NoError(err)
	data, ok := res.(listReportReasonResp)
	require.True(ok)
	require.Len(data.Reasons, 2)
	assert.EqualValues(2, data.Reasons[0].ID)
	assert.Equal("违法违规", data.Reasons[0].Name)
	require.Len(data.Reasons[0].SubReasons, 1)
	assert.EqualValues(3, data.Reasons[0].SubReasons[0].ID)
	assert.Equal("涉政谣言", data.Reasons[0].SubReasons[0].Name)
	assert.EqualValues(1, data.Reasons[1].ID)
	assert.Equal("谣言类不实信息", data.Reasons[1].Name)
	require.Len(data.Reasons[1].SubReasons, 3)
	assert.EqualValues(5, data.Reasons[1].SubReasons[0].ID)
	assert.Equal("违法违禁", data.Reasons[1].SubReasons[0].Name)
	assert.EqualValues(4, data.Reasons[1].SubReasons[1].ID)
	assert.Equal("赌博诈骗", data.Reasons[1].SubReasons[1].Name)
	assert.EqualValues(6, data.Reasons[1].SubReasons[2].ID)
	assert.Equal("其他", data.Reasons[1].SubReasons[2].Name)
	assert.True(*data.Reasons[1].SubReasons[2].OtherReason)
}
