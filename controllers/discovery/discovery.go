package discovery

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// HandlerV2 return handlerV2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "discovery",
		Actions: map[string]*handler.ActionV2{
			"rank":       handler.NewActionV2(handler.GET, ActionRank, handler.ActionOption{LoginRequired: false}),
			"hot-search": handler.NewActionV2(handler.GET, ActionHotSearch, handler.ActionOption{LoginRequired: false}),
		},
	}
}
