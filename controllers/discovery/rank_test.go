package discovery

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/hotsearch"
	"github.com/MiaoSiLa/missevan-main/models/mhomepagerank"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

func TestRankRespTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(rankResp{}, "data")
	kc.Check(dataItem{}, "type", "name", "active", "more", "element_type", "sound_elements", "drama_elements", "hot_search_elements", "live_elements", "elements")
	kc.Check(more{}, "url")
	kc.Check(soundElement{}, "id", "soundstr", "view_count", "front_cover", "video")
	kc.Check(dramaElement{}, "id", "name", "cover", "cover_color", "view_count")

	kc.CheckOmitEmpty(dataItem{}, "active", "more", "sound_elements", "drama_elements", "hot_search_elements", "live_elements")
}

func mockURIUserLiveRankRPC() func() {
	return mrpc.SetMock(userapi.URIUserLiveRank, func(input interface{}) (interface{}, error) {
		return userapi.UserLiveRankResp{
			Data: []*userapi.UserLiveRank{
				{
					Info: &userapi.Info{
						UserID:   9467681,
						Username: "测试用户",
						IconURL:  "https://test.com/test.png",
						Revenue:  100,
						Rank:     1,
					},
					Room: &userapi.RoomInfo{
						RoomID:          1111,
						CatalogID:       145,
						Name:            "测试直播间 1",
						Announcement:    "欢迎来到我的直播间！",
						CreatorID:       9467681,
						CreatorUsername: "测试用户",
						Status: &userapi.SimpleRoomStatus{
							Open: 1,
						},
						CoverURL: "https://test.com/cover.png",
					},
				},
			},
		}, nil
	})
}

func TestActionRank(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mockURIUserLiveRankRPC()
	defer cancel()

	// 删除热搜缓存数据
	key := keys.KeyAppDiscoveryRanks0.Format()
	require.NoError(service.LRURedis.Del(key).Err())
	defer service.Redis.Del(key)

	// 删除热搜缓存数据
	hotSearchKeywordKey := keys.KeyHotSearchKeyword0.Format()
	require.NoError(service.Redis.Del(hotSearchKeywordKey).Err())
	defer service.Redis.Del(hotSearchKeywordKey)
	jsonData := `[{"checked":0,"key":"天官赐福","level":1,"url":"missevan://search"},{"checked":0,"key":"吞海","level":0,"url":"missevan://search"}]`
	require.NoError(service.Redis.Set(hotSearchKeywordKey, jsonData, time.Minute).Err())

	// 测试成功
	api := "/x/discovery/rank"
	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?persona_id=%d", api, persona.TypeBoy), false, nil)
	c.Equip().FromApp = true
	data, message, err := ActionRank(c)
	require.NoError(err)
	require.Empty(message)
	require.NotNil(data)
	resp, ok := data.(rankResp)
	require.True(ok)
	require.NotNil(resp)
	require.NotEmpty(resp.Data)
	assert.Equal(mhomepagerank.TypeRankHotSearch, resp.Data[0].Type)
	assert.NotEmpty(resp.Data[0].Elements)
	hotSearchElement, ok := resp.Data[0].Elements[0].(*hotsearch.Item)
	require.True(ok)
	assert.Equal("天官赐福", hotSearchElement.Name)
	assert.Equal("missevan://search", hotSearchElement.OpenURL)
	assert.Equal("", hotSearchElement.URL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", hotSearchElement.IconURL)
	assert.Equal(mhomepagerank.TypeRankDramaNew, resp.Data[1].Type)
	assert.NotEmpty(resp.Data[1].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaPopularity, resp.Data[2].Type)
	assert.NotEmpty(resp.Data[2].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaFree, resp.Data[3].Type)
	assert.NotEmpty(resp.Data[3].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaRomantic, resp.Data[4].Type)
	assert.NotEmpty(resp.Data[4].Elements)
	assert.Equal(mhomepagerank.TypeRankLive, resp.Data[5].Type)
	assert.NotEmpty(resp.Data[5].Elements)
	assert.Equal(mhomepagerank.TypeRankSoundLover, resp.Data[6].Type)
	assert.NotEmpty(resp.Data[6].Elements)

	assert.Nil(resp.Data[0].SoundElements)
	assert.Nil(resp.Data[0].DramaElements)
	assert.Nil(resp.Data[0].HotSearchElements)
	assert.Nil(resp.Data[0].LiveElements)
}

func TestNewRankParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	api := "/x/discovery/rank"
	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?persona_id=%d", api, persona.TypeBoy), true, nil)
	param, err := newRankParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(persona.TypeBoy, param.personaID)
	assert.Equal(c.UserContext(), param.uc)
	assert.Equal(c.Equip(), param.equip)

	// 测试画像参数错误
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?persona_id=%d", api, -1), true, nil)
	_, err = newRankParam(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestRankParam_getDataFromCache(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mockURIUserLiveRankRPC()
	defer cancel()

	// 删除发现页榜单缓存数据
	key := keys.KeyAppDiscoveryRanks0.Format()
	require.NoError(service.LRURedis.Del(key).Err())
	defer service.Redis.Del(key)

	// 删除热搜缓存数据
	hotSearchKeywordKey := keys.KeyHotSearchKeyword0.Format()
	require.NoError(service.Redis.Del(hotSearchKeywordKey).Err())
	defer service.Redis.Del(hotSearchKeywordKey)
	jsonData := `[{"checked":0,"key":"天官赐福","level":1,"url":"missevan://search"},{"checked":0,"key":"吞海","level":0,"url":"missevan://search"}]`
	require.NoError(service.Redis.Set(hotSearchKeywordKey, jsonData, time.Minute).Err())

	// 测试没有缓存时
	param := &rankParam{
		personaID: persona.TypeGirl,
		equip:     &util.Equipment{FromApp: true},
	}
	err := param.getDataFromCache()
	require.NoError(err)
	require.NotEmpty(param.respData)
	require.Len(param.respData, 7)
	assert.NotNil(param.respData[0].More)
	assert.Equal("https://m.uat.missevan.com/ranking?from_search=1&navhide=1&persona_id=3&type=8", param.respData[0].More.URL)
	assert.Equal(mhomepagerank.TypeRankHotSearch, param.respData[0].Type)
	assert.NotEmpty(param.respData[0].Elements)
	hotSearchElement, ok := param.respData[0].Elements[0].(*hotsearch.Item)
	require.True(ok)
	assert.Equal("天官赐福", hotSearchElement.Name)
	assert.Equal("missevan://search", hotSearchElement.OpenURL)
	assert.Equal("", hotSearchElement.URL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", hotSearchElement.IconURL)
	assert.Equal(mhomepagerank.TypeRankDramaNew, param.respData[1].Type)
	assert.NotEmpty(param.respData[1].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaPopularity, param.respData[2].Type)
	assert.NotEmpty(param.respData[2].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaFree, param.respData[3].Type)
	assert.NotEmpty(param.respData[3].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaRomantic, param.respData[4].Type)
	assert.NotEmpty(param.respData[4].Elements)
	assert.Equal(mhomepagerank.TypeRankLive, param.respData[5].Type)
	assert.NotEmpty(param.respData[5].Elements)
	assert.Equal(mhomepagerank.TypeRankSoundLover, param.respData[6].Type)
	assert.NotEmpty(param.respData[6].Elements)

	// 验证生成的缓存数据
	dataStr, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	var rankDataList []*dataItem
	err = json.Unmarshal([]byte(dataStr), &rankDataList)
	require.NoError(err)
	require.NotEmpty(rankDataList)
	require.Len(rankDataList, 7)
	assert.Equal(mhomepagerank.TypeRankHotSearch, rankDataList[0].Type)
	assert.Nil(rankDataList[0].SoundElements)
	assert.Nil(rankDataList[0].DramaElements)
	assert.Nil(rankDataList[0].LiveElements)
	assert.NotNil(rankDataList[0].HotSearchElements)
	require.Len(rankDataList[0].HotSearchElements, 2)
	assert.Equal("天官赐福", rankDataList[0].HotSearchElements[0].Name)
	assert.Equal("", rankDataList[0].HotSearchElements[0].OpenURL)
	assert.Equal("missevan://search", rankDataList[0].HotSearchElements[0].URL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", rankDataList[0].HotSearchElements[0].IconURL)
	assert.Equal(mhomepagerank.TypeRankDramaNew, rankDataList[1].Type)
	assert.NotEmpty(rankDataList[1].DramaElements)
	assert.Equal(mhomepagerank.TypeRankDramaPopularity, rankDataList[2].Type)
	assert.NotEmpty(rankDataList[2].DramaElements)
	assert.Equal(mhomepagerank.TypeRankDramaFree, rankDataList[3].Type)
	assert.NotEmpty(rankDataList[3].DramaElements)
	assert.Equal(mhomepagerank.TypeRankDramaRomantic, rankDataList[4].Type)
	assert.NotEmpty(rankDataList[4].DramaElements)
	assert.Equal(mhomepagerank.TypeRankLive, rankDataList[5].Type)
	assert.NotEmpty(rankDataList[5].LiveElements)
	assert.Equal(mhomepagerank.TypeRankSoundLover, rankDataList[6].Type)
	assert.NotEmpty(rankDataList[6].SoundElements)

	// 测试有缓存时
	err = param.getDataFromCache()
	require.NoError(err)
	require.NotEmpty(param.respData)
	require.Len(param.respData, 7)
	assert.Equal(mhomepagerank.TypeRankHotSearch, param.respData[0].Type)
	assert.NotEmpty(param.respData[0].Elements)
	hotSearchElement, ok = param.respData[0].Elements[0].(*hotsearch.Item)
	require.True(ok)
	assert.Equal("天官赐福", hotSearchElement.Name)
	assert.Equal("missevan://search", hotSearchElement.OpenURL)
	assert.Equal("", hotSearchElement.URL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", hotSearchElement.IconURL)
	assert.Equal(mhomepagerank.TypeRankDramaNew, param.respData[1].Type)
	assert.NotEmpty(param.respData[1].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaPopularity, param.respData[2].Type)
	assert.NotEmpty(param.respData[2].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaFree, param.respData[3].Type)
	assert.NotEmpty(param.respData[3].Elements)
	assert.Equal(mhomepagerank.TypeRankDramaRomantic, param.respData[4].Type)
	assert.NotEmpty(param.respData[4].Elements)
	assert.Equal(mhomepagerank.TypeRankLive, param.respData[5].Type)
	assert.NotEmpty(param.respData[5].Elements)
	assert.Equal(mhomepagerank.TypeRankSoundLover, param.respData[6].Type)
	assert.NotEmpty(param.respData[6].Elements)
}

func TestRankParam_getData(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mockURIUserLiveRankRPC()
	defer cancel()

	key := keys.KeyHotSearchKeyword0.Format()
	err := service.Redis.Del(key).Err()
	require.NoError(err)
	defer service.Redis.Del(key)
	jsonData := `[{"checked":0,"key":"天官赐福","level":1,"url":"missevan://search"},{"checked":0,"key":"吞海","level":0,"url":"missevan://search"}]`
	require.NoError(service.Redis.Set(key, jsonData, time.Minute).Err())

	param := &rankParam{}
	data, err := param.getData()
	require.NoError(err)
	require.NotEmpty(data)
	require.Len(data, 7)
	assert.Equal(mhomepagerank.TypeRankHotSearch, data[0].Type)
	assert.NotEmpty(data[0].HotSearchElements)
	assert.Equal("天官赐福", data[0].HotSearchElements[0].Name)
	assert.Equal("", data[0].HotSearchElements[0].OpenURL)
	assert.Equal("missevan://search", data[0].HotSearchElements[0].URL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", data[0].HotSearchElements[0].IconURL)
	assert.Equal(mhomepagerank.TypeRankDramaNew, data[1].Type)
	assert.NotEmpty(data[1].DramaElements)
	assert.Equal(mhomepagerank.TypeRankDramaPopularity, data[2].Type)
	assert.NotEmpty(data[2].DramaElements)
	assert.Equal(mhomepagerank.TypeRankDramaFree, data[3].Type)
	assert.NotEmpty(data[3].DramaElements)
	assert.Equal(mhomepagerank.TypeRankDramaRomantic, data[4].Type)
	assert.NotEmpty(data[4].DramaElements)
	assert.Equal(mhomepagerank.TypeRankLive, data[5].Type)
	assert.NotEmpty(data[5].LiveElements)
	assert.Equal(mhomepagerank.TypeRankSoundLover, data[6].Type)
	assert.NotEmpty(data[6].SoundElements)
}

func TestRankParam_getHotSearchRank(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	key := keys.KeyHotSearchKeyword0.Format()
	err := service.Redis.Del(key).Err()
	require.NoError(err)
	defer service.Redis.Del(key)
	// 测试热搜榜 redis 数据不存在时
	param := &rankParam{}
	require.NoError(param.getHotSearchRank())
	require.Empty(param.hotSearchRankElements)

	// 测试热搜榜 redis 数据存在时
	jsonData := `[{"checked":0,"key":"天官赐福","level":1,"url":"missevan://search"},{"checked":0,"key":"吞海","level":0,"url":"missevan://search"}]`
	require.NoError(service.Redis.Set(key, jsonData, time.Minute).Err())
	param = &rankParam{}
	require.NoError(param.getHotSearchRank())
	require.NotEmpty(param.hotSearchRankElements)
	require.Len(param.hotSearchRankElements, 2)
	assert.Equal(param.hotSearchRankElements[0].Name, "天官赐福")
	assert.Equal(param.hotSearchRankElements[1].Name, "吞海")
}

func TestRankParam_getLiveRank(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mockURIUserLiveRankRPC()
	defer cancel()

	param := &rankParam{}
	require.NoError(param.getLiveRank())
	require.NotEmpty(param.liveRankElements)
	assert.Len(param.liveRankElements, 1)
	assert.EqualValues(param.liveRankElements[0].UserID, 9467681)
	assert.Equal(param.liveRankElements[0].Username, "测试用户")
}

func TestRankParam_getDramaAndSoundRank(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := &rankParam{}
	require.NoError(param.getDramaAndSoundRank())
	require.NotEmpty(param.soundRankTypeElementsMap)
	assert.Len(param.soundRankTypeElementsMap, 1)
	require.NotEmpty(param.soundRankTypeElementsMap[mhomepagerank.TypeRankSoundLover])
	assert.Len(param.soundRankTypeElementsMap[mhomepagerank.TypeRankSoundLover], 3)
	assert.EqualValues(1217695, param.soundRankTypeElementsMap[mhomepagerank.TypeRankSoundLover][0].ID)
	assert.Equal("测试推荐音频", param.soundRankTypeElementsMap[mhomepagerank.TypeRankSoundLover][0].SoundStr)
	assert.Equal("https://static-test.maoercdn.com/coversmini/201701/24/test.png", param.soundRankTypeElementsMap[mhomepagerank.TypeRankSoundLover][0].FrontCover)
	assert.True(param.soundRankTypeElementsMap[mhomepagerank.TypeRankSoundLover][0].Video)

	require.NotEmpty(param.dramaRankTypeElementsMap)
	assert.Len(param.dramaRankTypeElementsMap, 4)
	require.NotEmpty(param.dramaRankTypeElementsMap[mhomepagerank.TypeRankDramaFree])
	assert.Len(param.dramaRankTypeElementsMap[mhomepagerank.TypeRankDramaFree], 3)
	assert.EqualValues(11, param.dramaRankTypeElementsMap[mhomepagerank.TypeRankDramaFree][0].ID)
	assert.Equal("剧集名称（审核通过）", param.dramaRankTypeElementsMap[mhomepagerank.TypeRankDramaFree][0].Name)
	assert.Equal("https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg", param.dramaRankTypeElementsMap[mhomepagerank.TypeRankDramaFree][0].Cover)
	assert.Equal(12434877, param.dramaRankTypeElementsMap[mhomepagerank.TypeRankDramaFree][0].CoverColor)
}

func TestProcessRankURL(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 模拟配置
	originalURL := config.Conf.Params.URL.RankDetailsURL
	config.Conf.Params.URL.RankDetailsURL = "https://example.com/rank"
	defer func() {
		config.Conf.Params.URL.RankDetailsURL = originalURL
	}()

	// 测试正常情况
	actualURL, err := processRankURL(1, 12345)
	require.NoError(err)
	expectedURL := "https://example.com/rank?from_search=1&navhide=1&persona_id=12345&type=1"
	assert.Equal(expectedURL, actualURL)

	// 测试使用无效的 URL 配置，期望返回错误
	config.Conf.Params.URL.RankDetailsURL = "sdsd\naaa"
	_, err = processRankURL(1, 12345)
	require.Error(err)
}
