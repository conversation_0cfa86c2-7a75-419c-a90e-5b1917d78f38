package discovery

import (
	"encoding/json"
	"net/url"
	"slices"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	gourl "github.com/MiaoSiLa/missevan-go/util/url"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/hotsearch"
	"github.com/MiaoSiLa/missevan-main/models/mhomepagerank"
	"github.com/MiaoSiLa/missevan-main/models/mpersonarank"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/models/sound"
	"github.com/MiaoSiLa/missevan-main/models/soundvideo"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

type rankResp struct {
	Data []*dataItem `json:"data"`
}

type dataItem struct {
	Type        int       `json:"type"`             // 榜单类型（1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜；7: 直播榜；8: 热搜榜）
	Name        string    `json:"name"`             // 榜单名称
	Active      int       `json:"active,omitempty"` // 默认定位
	More        *more     `json:"more,omitempty"`   // 更多信息
	ElementType int       `json:"element_type"`     // 榜单元素类型
	Elements    []element `json:"elements"`         // 榜单元素信息

	SoundElements     []*soundElement         `json:"sound_elements,omitempty"`      // 音频类型榜元素
	DramaElements     []*dramaElement         `json:"drama_elements,omitempty"`      // 剧集类型榜元素
	HotSearchElements []*hotsearch.Item       `json:"hot_search_elements,omitempty"` // 热搜榜元素
	LiveElements      []*userapi.UserLiveRank `json:"live_elements,omitempty"`       // 主播收益榜元素
}

type more struct {
	URL string `json:"url"` // 跳转链接
}

type element interface{}

type soundElement struct {
	ID         int64  `json:"id"`          // 音频 ID
	SoundStr   string `json:"soundstr"`    // 音频标题
	FrontCover string `json:"front_cover"` // 音频封面
	ViewCount  int64  `json:"view_count"`  // 播放量
	Video      bool   `json:"video"`       // 音频是否包含对应的视频
}

type dramaElement struct {
	ID         int64  `json:"id"`          // 剧集 ID
	Name       string `json:"name"`        // 剧集名称
	Cover      string `json:"cover"`       // 剧集封面
	CoverColor int    `json:"cover_color"` // 背景图主颜色
	ViewCount  int64  `json:"view_count"`  // 播放量
}

type rankParam struct {
	personaID int64
	uc        mrpc.UserContext
	equip     *util.Equipment

	hotSearchRankElements    []*hotsearch.Item
	liveRankElements         []*userapi.UserLiveRank
	soundRankTypeElementsMap map[int][]*soundElement
	dramaRankTypeElementsMap map[int][]*dramaElement
	respData                 []*dataItem
}

// ActionRank 搜索页榜单
/**
 * @api {get} /x/discovery/rank 搜索页榜单
 *
 * @apiVersion 0.1.0
 * @apiName rank
 * @apiGroup /x/discovery
 *
 * @apiParam {Number} persona_id 画像 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [
 *           {
 *             "type": 8, // 榜单类型（1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜；7: 直播榜；8: 热搜榜）
 *             "name": "热门搜索", // 榜单名称
 *             "active": 1, // 默认定位到该榜单，不默认定位到该榜单时不下发
 *             "more": { // 没有 more 字段的时候不显示更多按钮
 *               "url": "https://test.com/aaa?type=8&from_search=1" // 跳转链接（跳转到榜单详情页，携带 from_search=1 参数，前端请求榜单类型列表接口时透传此参数，服务端根据此参数判断是否在榜单类型列表接口中增加热搜榜）
 *             },
 *             "element_type": 6, // 榜单元素类型（2: 剧集；3: 音频；5: 直播；6: 热搜词）
 *             "elements": [
 *               {
 *                 "name": "热搜词名",
 *                 "open_url": "missevan://search?keyword=%E5%B0%81%E5%BB%BA%E7%B3%9F%E7%B2%95", // 点击热搜词后的跳转地址
 *                 "icon_url": "https://static-test.test.com/icon.png" // 热搜词 icon，下发时需要展示
 *               }
 *             ]
 *           },
 *           {
 *             "type": 1, // 榜单类型（1: 新品榜）
 *             "name": "新品榜", // 榜单名称
 *             "more": {
 *               "url": "https://test.com/aaa?type=8&from_search=1"
 *             },
 *             "element_type": 2, // 榜单元素类型（2: 剧集；3: 音频；5: 直播；6: 热搜词）
 *             "elements": [
 *               {
 *                 "id": 9888, // 剧集 ID
 *                 "name": "《杀破狼》广播剧", // 剧集名称
 *                 "cover": "https://test.com/test.jpg", // 剧集封面
 *                 "cover_color": 5402961, // 背景图主颜色，十进制表示
 *                 "view_count": 12345 // 播放量
 *               }
 *             ]
 *           },
 *           {
 *             "type": 6, // 榜单类型（6: 声音恋人榜）
 *             "name": "声音恋人榜", // 榜单名称
 *             "more": {
 *               "url": "https://test.com/aaa?type=8&from_search=1"
 *             },
 *             "element_type": 3, // 榜单元素类型（2: 剧集；3: 音频；5: 直播；6: 热搜词）
 *             "elements": [
 *               {
 *                 "id": 1462573, // 音频 ID
 *                 "soundstr": "迪奥光宗登场铃声", // 音频标题
 *                 "view_count": 12345, // 播放量
 *                 "front_cover": "https://test.com/test.jpg", // 音频封面
 *                 "video": true // 音频是否包含对应的视频
 *               }
 *             ]
 *           },
 *           {
 *             "type": 7, // 榜单类型（7: 直播榜）
 *             "name": "直播榜",
 *             "more": {
 *               "url": "https://test.com/aaa?type=8&from_search=1"
 *             },
 *             "element_type": 5, // 榜单元素类型（2: 剧集；3: 音频；5: 直播；6: 热搜词）
 *             "elements": [
 *               {
 *                 "user_id": 9467681, // 用户 ID
 *                 "username": "2Fire", // 用户名称
 *                 "iconurl": "https://test.com/profile/icon01.png", // 用户头像
 *                 "revenue": 60927, // 收益（分数值）
 *                 "rank_up": 10131, // 前一名的收益 - 自己的收益 + 1
 *                 "room": { // 直播间信息
 *                   "room_id": 868858629,
 *                   "catalog_id": 145,
 *                   "name": "听歌进",
 *                   "announcement": "欢迎来到我的直播间！",
 *                   "creator_id": 9467681,
 *                   "creator_username": "2Fire",
 *                   "status": {
 *                     "open": 1
 *                   },
 *                   "cover_url": "https://test.com/fmcovers/test.jpg" // 直播间封面
 *                 }
 *               }
 *             ]
 *           }
 *         ]
 *       }
 *     }
 */
func ActionRank(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newRankParam(c)
	if err != nil {
		return nil, "", err
	}

	// 获取榜单数据
	err = param.getDataFromCache()
	if err != nil {
		return nil, "", err
	}

	return rankResp{Data: param.respData}, "", nil
}

func newRankParam(c *handler.Context) (*rankParam, error) {
	param := new(rankParam)
	personaID, err := c.GetParamInt64("persona_id")
	if err != nil || personaID < 0 {
		return nil, actionerrors.ErrParams
	}
	personaModule := personaID & persona.MaskPersonaModule
	if !persona.IsValidatePersona(personaID) {
		// 若用户画像不在定义的范围内，则默认为女用户
		personaModule = persona.TypeGirl
	}
	param.personaID = personaModule

	param.uc = c.UserContext()
	param.equip = c.Equip()

	return param, nil
}

func (param *rankParam) getDataFromCache() error {
	key := keys.KeyAppDiscoveryRanks0.Format()
	dataStr, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Errorf("从 redis 获取搜索页榜单缓存出错: %v", err)
		// PASS
		return nil
	}
	var rankDataList []*dataItem
	if err == nil {
		err = json.Unmarshal([]byte(dataStr), &rankDataList)
		if err != nil {
			logger.Errorf("解析搜索页榜单缓存数据时失败: %v", err)
			// PASS
			return nil
		}
	} else if serviceredis.IsRedisNil(err) {
		rankDataList, err = param.getData()
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
		rankDataBytes, err := json.Marshal(rankDataList)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			err = service.LRURedis.Set(key, string(rankDataBytes), time.Minute).Err()
			if err != nil {
				logger.Errorf("往 redis 设置搜索页排行榜数据失败: %v", err)
				// PASS
			}
		}
	}

	param.respData = make([]*dataItem, 0, len(rankDataList))
	for _, rankData := range rankDataList {
		var rankElements []element
		switch mhomepagerank.GetRankElementType(rankData.Type) {
		case mhomepagerank.ElementTypeHotSearch:
			// 热搜榜根据版本号转化 App 跳转链接为可用链接
			for _, elem := range rankData.HotSearchElements {
				openURL := elem.URL
				if param.equip.FromApp {
					openURL = gourl.GetUsableAppLink(openURL, param.equip)
				}
				elem.OpenURL = openURL
				// 将原始 URL 置为空，不下发原始 URL
				elem.URL = ""
				rankElements = append(rankElements, elem)
			}
			// 将对应类型的元素置为空，不下发对应类型的元素
			rankData.HotSearchElements = nil
		case mhomepagerank.ElementTypeLive:
			for _, elem := range rankData.LiveElements {
				rankElements = append(rankElements, elem)
			}
			// 将对应类型的元素置为空，不下发对应类型的元素
			rankData.LiveElements = nil
		case mhomepagerank.ElementTypeSound:
			for _, elem := range rankData.SoundElements {
				rankElements = append(rankElements, elem)
			}
			// 将对应类型的元素置为空，不下发对应类型的元素
			rankData.SoundElements = nil
		case mhomepagerank.ElementTypeDrama:
			for _, elem := range rankData.DramaElements {
				rankElements = append(rankElements, elem)
			}
			// 将对应类型的元素置为空，不下发对应类型的元素
			rankData.DramaElements = nil
		default:
			logger.Errorf("搜索页榜单类型错误: %d", rankData.Type)
			// PASS
		}
		if len(rankElements) == 0 {
			logger.Errorf("搜索页榜单数据不存在: %d", rankData.Type)
			// PASS: 榜单数据不存在时不返回该榜单数据
			continue
		}

		// 根据用户画像处理榜单跳转链接
		rankURL, err := processRankURL(rankData.Type, param.personaID)
		if err != nil {
			return err
		}
		if rankURL != "" {
			rankData.More = &more{URL: rankURL}
		}
		rankData.Elements = rankElements

		param.respData = append(param.respData, rankData)
	}

	return nil
}

// getData 获取榜单数据
func (param *rankParam) getData() ([]*dataItem, error) {
	personaRankList, err := mpersonarank.ListByPersonaID(persona.TypeDiscovery)
	if err != nil {
		return nil, err
	}
	if len(personaRankList) == 0 {
		logger.Errorf("搜索页未配置任何榜单")
		// PASS
		return nil, nil
	}

	var hotSearchRank bool
	var liveRank bool
	var soundRank bool
	var dramaRank bool
	for _, personaRank := range personaRankList {
		switch mhomepagerank.GetRankElementType(personaRank.RankType) {
		case mhomepagerank.ElementTypeHotSearch:
			hotSearchRank = true
		case mhomepagerank.ElementTypeLive:
			liveRank = true
		case mhomepagerank.ElementTypeSound:
			soundRank = true
		case mhomepagerank.ElementTypeDrama:
			dramaRank = true
		default:
			logger.Errorf("搜索页榜单类型错误: %d", personaRank.RankType)
			// PASS
		}
	}

	if hotSearchRank {
		// 获取热搜榜
		err = param.getHotSearchRank()
		if err != nil {
			// 热搜榜获取出错时，直接报错，其他榜单获取出错时隐藏其他榜单
			return nil, err
		}
	}

	if soundRank || dramaRank {
		// 获取剧集和音频榜数据
		err = param.getDramaAndSoundRank()
		if err != nil {
			logger.Errorf("获取搜索页音频和剧集榜出错: %v", err)
			// PASS
		}
	}

	if liveRank {
		// 获取直播榜
		err = param.getLiveRank()
		if err != nil {
			logger.Errorf("获取搜索页直播榜出错: %v", err)
			// PASS
		}
	}

	// 整合所有榜单数据
	respData := make([]*dataItem, 0, len(personaRankList))
	for _, personaRank := range personaRankList {
		elementType := mhomepagerank.GetRankElementType(personaRank.RankType)
		rankData := &dataItem{
			Type:        personaRank.RankType,
			Name:        personaRank.Name,
			Active:      personaRank.Active,
			ElementType: elementType,
		}
		switch elementType {
		case mhomepagerank.ElementTypeHotSearch:
			rankData.HotSearchElements = param.hotSearchRankElements
		case mhomepagerank.ElementTypeLive:
			rankData.LiveElements = param.liveRankElements
		case mhomepagerank.ElementTypeSound:
			if soundRankElements, ok := param.soundRankTypeElementsMap[personaRank.RankType]; ok {
				rankData.SoundElements = soundRankElements
			}
		case mhomepagerank.ElementTypeDrama:
			if dramaRankElements, ok := param.dramaRankTypeElementsMap[personaRank.RankType]; ok {
				rankData.DramaElements = dramaRankElements
			}
		default:
			logger.Errorf("搜索页榜单类型错误: %d", personaRank.RankType)
			// PASS
		}
		respData = append(respData, rankData)
	}

	return respData, nil
}

// getHotSearchRank 获取热搜榜
func (param *rankParam) getHotSearchRank() error {
	hotSearchRankElements := hotsearch.List(mhomepagerank.RankItemLengthDiscoveryShow10)
	if len(hotSearchRankElements) == 0 {
		logger.Errorf("搜索页热搜榜数据不存在")
		// PASS
		return nil
	}
	param.hotSearchRankElements = hotSearchRankElements

	return nil
}

// getLiveRank 获取直播榜
func (param *rankParam) getLiveRank() error {
	userLiveRanks, err := userapi.LiveRank(param.uc, userapi.RankTypeHour, mhomepagerank.RankItemLengthDiscoveryShow10)
	if err != nil {
		return err
	}
	if len(userLiveRanks) == 0 {
		logger.Errorf("搜索页直播榜数据不存在")
		// PASS
		return nil
	}
	param.liveRankElements = userLiveRanks

	return nil
}

// getDramaAndSoundRank 获取剧集和音频榜数据
func (param *rankParam) getDramaAndSoundRank() error {
	// 获取剧集和音频榜
	rankTypeDataMap, err := mhomepagerank.GetDiscoveryRanks()
	if err != nil {
		return err
	}
	if len(rankTypeDataMap) == 0 {
		logger.Errorf("搜索页剧集和音频类型榜单数据不存在")
		// PASS
		return nil
	}

	rankTypeDataMapLen := len(rankTypeDataMap)
	rankTypeSoundIDsMap := make(map[int][]int64, rankTypeDataMapLen)
	rankTypeDramaIDsMap := make(map[int][]int64, rankTypeDataMapLen)
	rankSoundIDs := make([]int64, 0, mhomepagerank.RankItemLengthDiscoverySelect20*rankTypeDataMapLen)
	rankDramaIDs := make([]int64, 0, mhomepagerank.RankItemLengthDiscoverySelect20*rankTypeDataMapLen)
	for rankType, ranks := range rankTypeDataMap {
		var rankItemIDs []int64
		// 截取需要查询的剧集或音频 ID 数量
		if len(ranks.DataInfo) > mhomepagerank.RankItemLengthDiscoverySelect20 {
			rankItemIDs = ranks.DataInfo[:mhomepagerank.RankItemLengthDiscoverySelect20]
		} else {
			rankItemIDs = ranks.DataInfo
		}
		rankElementType := mhomepagerank.GetRankElementType(rankType)
		switch rankElementType {
		case mhomepagerank.ElementTypeSound:
			rankSoundIDs = append(rankSoundIDs, rankItemIDs...)
			rankTypeSoundIDsMap[rankType] = rankItemIDs
		case mhomepagerank.ElementTypeDrama:
			rankDramaIDs = append(rankDramaIDs, rankItemIDs...)
			rankTypeDramaIDsMap[rankType] = rankItemIDs
		default:
			logger.Errorf("搜索页榜单类型错误: %d", rankType)
			// PASS
		}
	}
	rankSoundIDs = sets.Uniq(rankSoundIDs)
	rankDramaIDs = sets.Uniq(rankDramaIDs)

	if len(rankSoundIDs) > 0 {
		// 查询音频信息
		soundInfoList, err := sound.ListSoundInfoByIDs(rankSoundIDs)
		if err != nil {
			return err
		}
		// 查询已绑定视频并审核通过的音频 IDs
		videoSoundIDs, err := soundvideo.ListHasVideoSoundIDs(rankSoundIDs)
		if err != nil {
			return err
		}
		soundIDElementMap := make(map[int64]*soundElement, len(soundInfoList))
		for _, soundInfo := range soundInfoList {
			soundElementInfo := &soundElement{
				ID:         soundInfo.ID,
				SoundStr:   soundInfo.Soundstr,
				FrontCover: soundInfo.FrontCover,
				ViewCount:  soundInfo.ViewCount,
				Video:      slices.Contains(videoSoundIDs, soundInfo.ID),
			}
			soundIDElementMap[soundInfo.ID] = soundElementInfo
		}
		// 处理音频类型榜单需要返回的数据
		param.soundRankTypeElementsMap = make(map[int][]*soundElement, len(rankTypeSoundIDsMap))
		for rankType, soundIDs := range rankTypeSoundIDsMap {
			i := 0
			soundElements := make([]*soundElement, 0, mhomepagerank.RankItemLengthDiscoveryShow10)
			for _, soundID := range soundIDs {
				if soundElem, ok := soundIDElementMap[soundID]; ok {
					soundElements = append(soundElements, soundElem)
					i++
				}
				if i >= mhomepagerank.RankItemLengthDiscoveryShow10 {
					// 获取到搜索页榜单需要展示的数量时终止
					break
				}
			}
			param.soundRankTypeElementsMap[rankType] = soundElements
		}
	}

	if len(rankDramaIDs) > 0 {
		// 查询剧集信息
		dramaInfoList, err := dramainfo.ListRankDramaInfoByIDs(rankDramaIDs)
		if err != nil {
			return err
		}
		dramaIDElementMap := make(map[int64]*dramaElement, len(dramaInfoList))
		for _, dramaInfo := range dramaInfoList {
			dramaElementInfo := &dramaElement{
				ID:         dramaInfo.ID,
				Name:       *dramaInfo.Name,
				Cover:      dramaInfo.CoverURL,
				CoverColor: dramaInfo.CoverColor,
				ViewCount:  dramaInfo.ViewCount,
			}
			dramaIDElementMap[dramaInfo.ID] = dramaElementInfo
		}
		// 处理剧集类型榜单需要返回的数据
		param.dramaRankTypeElementsMap = make(map[int][]*dramaElement, len(rankTypeDramaIDsMap))
		for rankType, dramaIDs := range rankTypeDramaIDsMap {
			i := 0
			dramaElements := make([]*dramaElement, 0, mhomepagerank.RankItemLengthDiscoveryShow10)
			for _, dramaID := range dramaIDs {
				if dramaElem, ok := dramaIDElementMap[dramaID]; ok {
					dramaElements = append(dramaElements, dramaElem)
					i++
				}
				if i >= mhomepagerank.RankItemLengthDiscoveryShow10 {
					// 获取到搜索页榜单需要展示的数量时终止
					break
				}
			}
			param.dramaRankTypeElementsMap[rankType] = dramaElements
		}
	}

	return nil
}

// processRankURL 处理榜单跳转链接
func processRankURL(rankType int, personaID int64) (string, error) {
	parsedURL, err := url.Parse(config.Conf.Params.URL.RankDetailsURL)
	if err != nil {
		return "", err
	}
	q := url.Values{}
	q.Add("type", strconv.Itoa(rankType))
	q.Add("persona_id", strconv.FormatInt(personaID, 10))
	q.Add("from_search", "1")
	// navhide 用途参考文档：https://info.missevan.com/pages/viewpage.action?pageId=67240173
	q.Add("navhide", "1")
	parsedURL.RawQuery = q.Encode()
	return parsedURL.String(), nil
}
