package discovery

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)
	h := HandlerV2()
	checker := tutil.NewKeyChecker(t, tutil.Actions)

	assert.Equal("discovery", h.Name)
	checker.Check(h.Actions, "rank", "hot-search")
}
