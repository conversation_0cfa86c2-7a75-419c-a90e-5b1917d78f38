package discovery

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util/url"
	"github.com/MiaoSiLa/missevan-main/models/hotsearch"
)

// showCount50 热搜词展示数量，热搜榜使用
const showCount50 = 50

type hotSearchListResp struct {
	Data []*hotsearch.Item `json:"data"`
}

// ActionHotSearch 热搜榜详情
/**
 * @api {get} /x/discovery/hot-search 热搜榜详情
 *
 * @apiVersion 0.1.0
 * @apiName hot-search
 * @apiGroup /x/discovery
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [ // 没有数据时为空数组
 *           {
 *             "name": "热搜词名 1",
 *             "open_url": "missevan://search?keyword=%E5%B0%81%E5%BB%BA%E7%B3%9F%E7%B2%95", // 点击热搜词后的跳转地址
 *             "icon_url": "https://static-test.test.com/icon.png" // 热搜词 icon，下发时需要展示
 *           },
 *           {
 *             "name": "热搜词名 2",
 *             "open_url": "missevan://search?keyword=%E5%B0%81%E5%BB%BA%E7%B3%9F%E7",
 *             "icon_url": "https://static-test.test.com/icon.png"
 *           }
 *         ]
 *       }
 *     }
 */
func ActionHotSearch(c *handler.Context) (handler.ActionResponse, string, error) {
	hotSearchWordList := hotsearch.List(showCount50)
	equip := c.Equip()
	for _, hotSearchWord := range hotSearchWordList {
		// 根据版本号转化 App 跳转链接为可用链接
		openURL := hotSearchWord.URL
		if equip.FromApp {
			openURL = url.GetUsableAppLink(openURL, equip)
		}
		hotSearchWord.OpenURL = openURL
		// 将原始 URL 置为空，不下发原始 URL
		hotSearchWord.URL = ""
	}
	return hotSearchListResp{Data: hotSearchWordList}, "", nil
}
