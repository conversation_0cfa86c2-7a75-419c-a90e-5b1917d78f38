package discovery

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestActionHotSearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	// 测试没有热搜词
	key := keys.KeyHotSearchKeyword0.Format()
	require.NoError(service.Redis.Del(key).Err())
	defer service.Redis.Del(key)
	c := handler.NewTestContext(http.MethodGet, "/x/discovery/hot-search", false, nil)
	resp, message, err := ActionHotSearch(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok := resp.(hotSearchListResp)
	require.True(ok)
	assert.Empty(result.Data)

	// 设置缓存
	jsonData := `[{"checked":0,"key":"天官赐福","level":1,"url":"missevan://search"},{"checked":0,"key":"吞海","level":0,"url":"missevan://search"}]`
	require.NoError(service.Redis.Set(key, jsonData, time.Minute).Err())
	// 测试有热搜词
	c = handler.NewTestContext(http.MethodGet, "/x/discovery/hot-search", false, nil)
	c.Equip().FromApp = true
	resp, message, err = ActionHotSearch(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok = resp.(hotSearchListResp)
	require.True(ok)
	require.Len(result.Data, 2)
	assert.Equal("天官赐福", result.Data[0].Name)
	assert.Equal("missevan://search", result.Data[0].OpenURL)
	assert.Equal("", result.Data[0].URL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", result.Data[0].IconURL)
	assert.Equal("吞海", result.Data[1].Name)
	assert.Equal("missevan://search", result.Data[1].OpenURL)
	assert.Equal("", result.Data[1].URL)
	assert.Equal("", result.Data[1].IconURL)
}
