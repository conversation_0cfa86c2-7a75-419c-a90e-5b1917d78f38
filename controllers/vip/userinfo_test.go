package vip

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/config/params"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(-1, diamondClaimStatusCanNot)
	assert.EqualValues(0, diamondClaimStatusNotYet)
	assert.EqualValues(1, diamondClaimStatusClaimed)
}

func TestActionUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/x/vip/user-info"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)

	// 测试当前在会员有效期内
	data, message, err := ActionUserInfo(c)
	require.NoError(err)
	require.Equal("", message)
	resp, ok := data.(userInfoResp)
	require.True(ok)
	assert.Equal(muservip.VipStatusInEffect, resp.VipInfo.Status)
	assert.Equal(fmt.Sprintf("权益生效中，%s 到期", util.TimeNow().Format(util.TimeFormatYMD)), resp.VipInfo.Text)
	assert.Equal(5, resp.Privilege.Diamond.Num)
	assert.Equal(diamondClaimStatusNotYet, resp.Privilege.Diamond.ClaimStatus)
	expectList := []params.PrivilegeItem{
		{
			Title:   "会员剧免费畅听",
			IconURL: "https://static-test.maoercdn.com/image/a.png",
		},
		{
			Title:   "精品剧集 8 折起",
			IconURL: "https://static-test.maoercdn.com/image/b.png",
		},
	}
	assert.Equal(expectList, resp.Privilege.List)

	// 测试会员已过期
	err = muservip.MUserVip{}.DB().Where("user_id = ?", 12).
		Update("end_time", util.TimeNow().Add(-1*time.Second).Unix()).Error
	require.NoError(err)
	data, message, err = ActionUserInfo(c)
	require.NoError(err)
	require.Equal("", message)
	resp, ok = data.(userInfoResp)
	require.True(ok)
	assert.Equal(muservip.VipStatusExpired, resp.VipInfo.Status)
	assert.Equal("会员已过期，续费恢复会员权益", resp.VipInfo.Text)
	assert.Equal(diamondClaimStatusCanNot, resp.Privilege.Diamond.ClaimStatus)

	// 测试用户未开通过会员
	err = muservip.MUserVip{}.DB().Delete("", "user_id = ?", 12).Error
	require.NoError(err)
	data, message, err = ActionUserInfo(c)
	require.NoError(err)
	require.Equal("", message)
	resp, ok = data.(userInfoResp)
	require.True(ok)
	assert.Equal(muservip.VipStatusNotYet, resp.VipInfo.Status)
	assert.Equal("9.9 元开通立享多项专属特权", resp.VipInfo.Text)
	assert.Equal(diamondClaimStatusCanNot, resp.Privilege.Diamond.ClaimStatus)

	// 测试当前无会员免费领钻石权益时不下发免费钻石字段
	config.Conf.Params.Vip.Privilege.DiamondNum = 0
	data, message, err = ActionUserInfo(c)
	require.NoError(err)
	require.Equal("", message)
	resp, ok = data.(userInfoResp)
	require.True(ok)
	assert.Nil(resp.Privilege.Diamond)
}
