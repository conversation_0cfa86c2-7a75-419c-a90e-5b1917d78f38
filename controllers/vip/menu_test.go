package vip

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/models/vip/mvip"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(vipMenuResp{}, "menu", "pay_method", "user_vip_status", "trade_agreement_url")
	kc.Check(menu{}, "id", "title", "price", "original_price", "product_id", "corner_mark_text", "description", "active")
	kc.Check(payMethod{}, "title", "type", "icon_url", "active")

	kc.CheckOmitEmpty(vipMenuResp{}, "pay_method")
	kc.CheckOmitEmpty(menu{}, "original_price", "corner_mark_text", "active")
	kc.CheckOmitEmpty(payMethod{}, "active")
}

func TestActionMenu(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试 Web 请求
	api := "/x/vip/menu"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	data, message, err := ActionMenu(c)
	assert.Equal(actionerrors.ErrForbidden(handler.CodeUnknownError, "暂不支持的设备"), err)
	assert.Nil(data)
	assert.Empty(message)

	// 测试 iOS（用户未享受过连续包月首次优惠）
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (iOS;12.0.1;iPhone7,2)")
	data, message, err = ActionMenu(c)
	require.NoError(err)
	require.Empty(message)
	require.NotNil(data)
	resp, ok := data.(vipMenuResp)
	require.True(ok)
	require.NotNil(resp)
	require.Len(resp.Menus, 5)
	// 验证用户未享受过连续包月首次优惠时
	assert.Equal(resp.Menus[0].Price, "9.9")
	assert.Equal(resp.Menus[0].OriginalPrice, "19")
	assert.Equal(resp.Menus[0].Title, "连续包月首月")
	assert.Equal(resp.Menus[0].CornerMarkText, "限时")
	// 验证不下发支付方式
	require.Nil(resp.PayMethods)
	// 验证是正式会员
	assert.Equal(muservip.VipStatusInEffect, resp.UserVipStatus)

	// 测试 iOS（用户享受过连续包月首次优惠）
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (iOS;12.0.1;iPhone7,2)")
	c.User().ID = 3457181
	data, message, err = ActionMenu(c)
	require.NoError(err)
	require.Empty(message)
	require.NotNil(data)
	resp, ok = data.(vipMenuResp)
	require.True(ok)
	require.NotNil(resp)
	require.Len(resp.Menus, 5)
	// 验证用户享受过连续包月首次优惠时
	assert.Equal(resp.Menus[0].Price, "19")
	assert.Equal(resp.Menus[0].OriginalPrice, "25")
	assert.Equal(resp.Menus[0].Title, "连续包月")
	assert.Empty(resp.Menus[0].CornerMarkText)
	// 验证不下发支付方式
	require.Nil(resp.PayMethods)
	// 验证正式会员已过期
	assert.Equal(muservip.VipStatusExpired, resp.UserVipStatus)

	// 测试 google 渠道
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	c.C.Request.Header.Set("channel", "missevan_google")
	data, message, err = ActionMenu(c)
	require.NoError(err)
	require.Empty(message)
	require.NotNil(data)
	resp, ok = data.(vipMenuResp)
	require.True(ok)
	require.NotNil(resp)
	require.Len(resp.Menus, 5)
	// 验证用户未享受过连续包月首次优惠时
	assert.Equal(resp.Menus[0].Price, "9.9")
	assert.Equal(resp.Menus[0].OriginalPrice, "15")
	assert.Equal(resp.Menus[0].Title, "连续包月首月")
	assert.Equal(resp.Menus[0].CornerMarkText, "限时")
	// 验证不下发支付方式
	require.Nil(resp.PayMethods)
	// 验证是正式会员
	assert.Equal(muservip.VipStatusInEffect, resp.UserVipStatus)

	// 测试安卓（非 google 渠道）
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	data, message, err = ActionMenu(c)
	require.NoError(err)
	require.Empty(message)
	require.NotNil(data)
	resp, ok = data.(vipMenuResp)
	require.True(ok)
	require.NotNil(resp)
	require.Len(resp.Menus, 5)
	// 验证用户未享受过连续包月首次优惠时
	assert.Equal(resp.Menus[0].Price, "9.9")
	assert.Equal(resp.Menus[0].OriginalPrice, "15")
	assert.Equal(resp.Menus[0].Title, "连续包月首月")
	assert.Equal(resp.Menus[0].CornerMarkText, "限时")
	// 验证支付方式
	require.NotNil(resp.PayMethods)
	require.Len(resp.PayMethods, 2)
	assert.NotEmpty(resp.PayMethods[0].IconURL)
	// 验证是正式会员
	assert.Equal(muservip.VipStatusInEffect, resp.UserVipStatus)
}

func TestNewVipMenuParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试 Web
	api := "/x/vip/menu"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	params, err := newVipMenuParam(c)
	require.Equal(actionerrors.ErrForbidden(handler.CodeUnknownError, "暂不支持的设备"), err)
	require.Nil(params)

	// 测试 google 渠道
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	c.C.Request.Header.Set("channel", "missevan_google")
	params, err = newVipMenuParam(c)
	require.NoError(err)
	require.NotNil(params)
	assert.Equal(c.UserID(), params.userID)
	assert.Equal(util.NewEquipment(c.Request().UserAgent()), params.equip)
	assert.True(params.fromGoogleChannel)
	assert.Equal(mvip.PlatformGooglePlay, params.platform)

	// 测试安卓
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	params, err = newVipMenuParam(c)
	require.NoError(err)
	require.NotNil(params)
	assert.Equal(c.UserID(), params.userID)
	assert.Equal(util.NewEquipment(c.Request().UserAgent()), params.equip)
	assert.False(params.fromGoogleChannel)
	assert.Equal(mvip.PlatformAndroid, params.platform)
}

func TestVipMenuParam_getMenuList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params := &vipMenuParam{
		fromGoogleChannel: false,
		userID:            12,
		equip: &util.Equipment{
			OS: util.Android,
		},
		platform: mvip.PlatformAndroid,
	}

	// 测试正常情况（非 google 渠道）
	params.fromGoogleChannel = false
	require.NoError(params.getMenuList())
	require.NotNil(params.resp.Menus)
	require.Len(params.resp.Menus, 5)
	// 验证用户未享受过连续包月首次优惠时
	assert.Equal(params.resp.Menus[0].Price, "9.9")
	assert.Equal(params.resp.Menus[0].OriginalPrice, "15")
	assert.Equal(params.resp.Menus[0].Title, "连续包月首月")
	assert.Equal(params.resp.Menus[0].CornerMarkText, "限时")
}

func TestVipMenuParam_getPayMethod(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params := &vipMenuParam{
		userID: 12,
		equip: &util.Equipment{
			OS: util.IOS,
		},
		fromGoogleChannel: true,
	}

	// 测试 iOS（期望不下发支付方式）
	require.NoError(params.getPayMethod())
	require.Nil(params.resp.PayMethods)

	// 测试 google 渠道（期望不下发支付方式）
	params.equip.OS = util.Android
	params.fromGoogleChannel = true
	require.NoError(params.getPayMethod())
	require.Nil(params.resp.PayMethods)

	// 测试安卓（非 google 渠道）
	params.fromGoogleChannel = false
	require.NoError(params.getPayMethod())
	require.NotNil(params.resp.PayMethods)
	require.Len(params.resp.PayMethods, 2)
	assert.NotEmpty(params.resp.PayMethods[0].IconURL)
}

func TestVipMenuParam_getUserVipStatus(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试是正式会员
	params := &vipMenuParam{
		userID: 12,
	}
	require.NoError(params.getUserVipStatus())
	assert.Equal(muservip.VipStatusInEffect, params.resp.UserVipStatus)

	// 测试正式会员已过期
	params = &vipMenuParam{
		userID: 3457181,
	}
	require.NoError(params.getUserVipStatus())
	assert.Equal(muservip.VipStatusExpired, params.resp.UserVipStatus)
}

func TestFormatMenuPrice(t *testing.T) {
	assert := assert.New(t)

	// 测试价格为 0 时
	assert.Equal(formatMenuPrice(0), "")

	// 测试到元
	assert.Equal(formatMenuPrice(1900), "19")

	// 测试到角
	assert.Equal(formatMenuPrice(990), "9.9")

	// 测试分
	assert.Equal(formatMenuPrice(1), "0.01")
}

func TestVipMenuParam_getProductID(t *testing.T) {
	assert := assert.New(t)

	params := &vipMenuParam{
		userID:            12,
		fromGoogleChannel: true,
		equip: &util.Equipment{
			OS: util.Android,
		},
	}

	// 测试 google 渠道
	assert.Equal(params.getProductID(3), "cn.missevan.item.vip0003")

	// 测试安卓非 google 渠道
	params.equip.OS = util.Android
	params.fromGoogleChannel = false
	assert.Equal(params.getProductID(3), "")

	// 测试 iOS
	params.equip.OS = util.IOS
	assert.Equal(params.getProductID(3), "com.missevan.CatEarFM.vip0003")
}
