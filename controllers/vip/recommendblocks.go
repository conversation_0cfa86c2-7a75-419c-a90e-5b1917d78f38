package vip

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/mpersonamoduleelement"
	"github.com/MiaoSiLa/missevan-main/models/person/mappearance"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/models/personalrecommendblocks"
)

// 装扮类型
const (
	moduleTypeThemeSkin   = iota + 1 // APP 主题皮肤
	moduleTypeAvatarFrame            // 头像挂件
	moduleTypeAppearance             // IP 套装
)

// blockDressUp 会员装扮模块
type blockDressUp struct {
	BlockType int           `json:"block_type"`
	Title     string        `json:"title"`
	Modules   []interface{} `json:"modules"`
}

// recommendBlocksResp 推荐模块返回值
type recommendBlocksResp struct {
	Blocks []interface{} `json:"blocks"`
}

// ActionRecommendBlocks 会员专享模块，包含：剧集、音频、装扮列表等
/**
 * @api {get} /x/vip/recommend-blocks 会员专享模块，包含：剧集、音频、装扮列表等
 *
 * @apiVersion 0.1.0
 * @apiName x/vip
 * @apiGroup recommend-blocks
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "blocks": [ // 列表中下发自定义模块数量为 0 至多个
 *           {
 *             "block_type": 3, // 模块元素类型，和新人 tab 页下发值含义一致。3：自定义模块；5：外观
 *             "module_id": 233, // 模块 ID, 仅在自定义模块下发
 *             "title": "会员好剧免费听", // 模块名称
 *             "type": 2, // 自定义模块类型。1：音单；2：剧集；3：音频；5：直播，仅在自定义模块下发
 *             "style": 0, // 排版方式。0：竖版；1：横版；2：排行榜；3：滑动
 *             "more": { // 不下发或为空时跳转到默认的原生自定义模块详情页
 *               "url": "https://test.com/aaa?foo=bar" // “查看更多”按钮跳转链接
 *             },
 *             "elements": [ // 剧集元素列表
 *               {
 *                 "id": 26,
 *                 "name": "剧集名称",
 *                 "abstract": "剧集简介",
 *                 "cover_color": 534887, // 背景图主颜色，十进制表示
 *                 "integrity": 2, // 完结度。1：长篇未完结；2：长篇完结；3：全一期
 *                 "newest": "string abc",
 *                 "pay_type": 2, // 付费类型。0：免费；1：单集付费；2：整剧付费
 *                 "view_count": 81949,
 *                 "need_pay": 1,
 *                 "sort": 0,
 *                 "module_id": 233,
 *                 "front_cover": "https://static-test.maoercdn.com/dramacovers/202401/04/a.jpg",
 *                 "corner_mark": { // 无剧集角标时不返回该字段
 *                   "text": "会员",
 *                   "text_color": "#FFFFFF", // 文字颜色
 *                   "text_start_color": "#FFFFFF", // 文字渐变起始颜色，优先使用 text_start_color 和 text_end_color 来展示文字颜色，不存在的话使用 text_color
 *                   "text_end_color": "#FFFFFF", // 文字渐变结束颜色，优先使用 text_start_color 和 text_end_color 来展示文字颜色，不存在的话使用 text_color
 *                   "bg_start_color": "#F8A623",
 *                   "bg_end_color": "#F8A623",
 *                   "left_icon_url": "https://static-test.maoercdn.com/cornermark/corner_mark_new.png" // 无左侧图标时不返回该字段
 *                 }
 *               }
 *             ]
 *           },
 *           {
 *             "block_type": 5,
 *             "title": "会员专属外观",
 *             "modules": [ // 外观模块列表
 *               {
 *                 "type": 1, // 元素类型。1：APP 主题皮肤；2：头像挂件；3：IP 套装
 *                 "title": "APP 主题皮肤",
 *                 "more": { // 不下发或为空时不展示“更多”按钮
 *                   "url": "https://test.com/aaa?foo=bar" // “更多”按钮跳转链接
 *                 },
 *                 "elements": [ // 外观模块元素列表
 *                   {
 *                     "id": 1992,
 *                     "name": "圣诞限定套装",
 *                     "intro": "温暖圣诞，温暖你！穿上这一套，最靓的仔就是你！",
 *                     "image_url": "https://static-test.maoercdn.com/dramacovers/202401/04/a.jpg",
 *                     "bg_start_color": "#F8A623", // 背景色渐变起始颜色，仅在元素类型为 APP 主题皮肤时下发。注意此处起止颜色与详情页不同
 *                     "bg_end_color": "#F8A626" // 背景色渐变结束颜色，仅在元素类型为 APP 主题皮肤时下发
 *                   }
 *                 ]
 *               },
 *               {
 *                 "type": 2,
 *                 "title": "头像挂件",
 *                 "more": { // 不下发或为空时不展示“更多”按钮
 *                   "url": "https://test.com/aaa?foo=bar" // “更多”按钮跳转链接
 *                 },
 *                 "elements": [
 *                   {
 *                     "id": 7,
 *                     "name": "十刻相伴",
 *                     "intro": "猫耳十周年礼遇 · 任务奖励",
 *                     "avatar_frame_url": "https://static-test.maoercdn.com/dramacovers/202401/04/a.jpg"
 *                   }
 *                 ]
 *               },
 *               {
 *                 "type": 3,
 *                 "title": "IP 套装",
 *                 "more": { // 不下发或为空时不展示“更多”按钮
 *                   "url": "https://test.com/aaa?foo=bar" // “更多”按钮跳转链接
 *                 },
 *                 "elements": [
 *                   {
 *                     "id": 7,
 *                     "name": "魔道祖师限定套装",
 *                     "intro": "装扮说明",
 *                     "image_url": "https://static-test.maoercdn.com/dramacovers/202401/04/a.jpg", // 封面图
 *                     "bg_start_color": "#F8A623", // 背景色渐变起始颜色
 *                     "bg_end_color": "#F8A626", // 背景色渐变结束颜色
 *                     "is_vip": 1 // 是否会员免费 0：否；1：是
 *                   }
 *                 ]
 *               }
 *             ]
 *           }
 *         ]
 *       }
 *     }
 */
func ActionRecommendBlocks(c *handler.Context) (handler.ActionResponse, string, error) {
	// 会员专享剧集
	moduleDramas, err := personalrecommendblocks.RecommendDramaModules(persona.TypeVip, c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}

	var blocks []interface{}
	for _, item := range moduleDramas {
		blocks = append(blocks, item)
	}

	// 会员专属外观模块
	dressUp := blockDressUp{
		BlockType: mpersonamoduleelement.BlockTypeDressUP,
		Title:     "会员专属外观",
	}
	// APP 主题皮肤
	skinElements, err := mthemeskin.ListVipThemeSkin()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	skinElementsLen := len(skinElements)
	if skinElementsLen != 0 {
		// 会员专享主题皮肤
		elements := make([]interface{}, 0, skinElementsLen)
		for _, item := range skinElements {
			elements = append(elements, item)
		}
		moduleThemeSkin := dressUpModule{
			Type:     moduleTypeThemeSkin,
			Title:    "APP 主题皮肤",
			Elements: elements,
		}
		url := config.Conf.Params.Vip.MoreURLThemeSkin
		if url != "" {
			moduleThemeSkin.More = &more{
				URL: url,
			}
		}
		dressUp.Modules = append(dressUp.Modules, moduleThemeSkin)
	}
	// 会员头像挂件
	avatarFrames, err := mavatarframe.ListVipAvatarFrame()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	avatarFramesLen := len(avatarFrames)
	if avatarFramesLen != 0 {
		elements := make([]interface{}, 0, avatarFramesLen)
		for _, item := range avatarFrames {
			elements = append(elements, item)
		}
		moduleAvatarFrame := dressUpModule{
			Type:     moduleTypeAvatarFrame,
			Title:    "头像挂件",
			Elements: elements,
		}
		url := config.Conf.Params.Vip.MoreURLAvatarFrame
		if url != "" {
			moduleAvatarFrame.More = &more{
				URL: url,
			}
		}
		dressUp.Modules = append(dressUp.Modules, moduleAvatarFrame)
	}
	// WORKAROUND: iOS < 9.0.0 Android < 9.0.0 HarmonyOS < 9.0.0 暂不支持展示外观套装，确认上线版本后再放出
	v := util.AppVersions{IOS: "9.0.0", Android: "9.0.0", HarmonyOS: "9.0.0"}
	if c.Equip().IsOldApp(v) {
		if len(dressUp.Modules) != 0 {
			blocks = append(blocks, dressUp)
		}
		return recommendBlocksResp{
			Blocks: blocks,
		}, "", nil
	}
	// 会员 IP 套装
	appearanceModule := listVipAppearanceModule()
	if appearanceModule != nil {
		dressUp.Modules = append(dressUp.Modules, appearanceModule)
	}
	if len(dressUp.Modules) != 0 {
		blocks = append(blocks, dressUp)
	}
	resp := recommendBlocksResp{
		Blocks: blocks,
	}
	return resp, "", nil
}

// dressUpModule 会员专属外观模块
type dressUpModule struct {
	Type     int           `json:"type"`
	Title    string        `json:"title"`
	More     *more         `json:"more"`
	Elements []interface{} `json:"elements"`
}

// more 额外信息
type more struct {
	URL string `json:"url"` // "更多"跳转链接地址
}

// listVipAppearanceModule 会员 IP 套装模块
func listVipAppearanceModule() *dressUpModule {
	appearanceElements, err := mappearance.ListVipAppearanceElements()
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	appearanceElementsLen := len(appearanceElements)
	if appearanceElementsLen == 0 {
		return nil
	}
	module := dressUpModule{
		Type:  moduleTypeAppearance,
		Title: "IP 套装",
	}
	url := config.Conf.Params.Vip.MoreURLAppearance
	if url != "" {
		module.More = &more{
			URL: url,
		}
	}
	elements := make([]interface{}, 0, appearanceElementsLen)
	for _, item := range appearanceElements {
		elements = append(elements, item)
	}
	module.Elements = elements
	return &module
}
