package vip

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/models/vip/vipfeedeductedrecord"
)

func TestActionSubscribeInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户未登录的情况
	c := handler.NewTestContext(http.MethodGet, "/x/vip/subscribe-info", false, nil)
	resp, message, err := ActionSubscribeInfo(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok := resp.(subscribeInfoResp)
	require.True(ok)
	require.NotNil(result.SubscribeInfo)
	assert.Equal("9.9 元限时开通会员，免费畅听！", result.SubscribeInfo.ButtonText)
	assert.Equal("9.9", result.SubscribeInfo.PromotionPrice)
	assert.Equal(muservip.VipStatusNotYet, result.UserVipInfo.Status)
	assert.Equal(promotionalStatusNotSubscribed, result.UserVipInfo.PromotionalSubscribeStatus)

	// 测试用户是正式会员，享受过连续包月首月优惠
	c = handler.NewTestContext(http.MethodGet, "/x/vip/subscribe-info", true, nil)
	c.User().ID = 13
	resp, message, err = ActionSubscribeInfo(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok = resp.(subscribeInfoResp)
	require.True(ok)
	require.Nil(result.SubscribeInfo)
	assert.Equal(muservip.VipStatusInEffect, result.UserVipInfo.Status)
	assert.Equal(promotionalStatusSubscribed, result.UserVipInfo.PromotionalSubscribeStatus)

	// 测试用户是正式会员，没有享受过连续包月首月优惠
	err = vipfeedeductedrecord.VipFeeDeductedRecord{}.DB().Delete("", "user_id = ?", 13).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/x/vip/subscribe-info", true, nil)
	c.User().ID = 13
	resp, message, err = ActionSubscribeInfo(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok = resp.(subscribeInfoResp)
	require.True(ok)
	require.Nil(result.SubscribeInfo)
	assert.Equal(muservip.VipStatusInEffect, result.UserVipInfo.Status)
	assert.Equal(promotionalStatusNotSubscribed, result.UserVipInfo.PromotionalSubscribeStatus)

	// 测试用户是正式会员已过期，享受过连续包月首月优惠
	c = handler.NewTestContext(http.MethodGet, "/x/vip/subscribe-info", true, nil)
	c.User().ID = 3457181
	resp, message, err = ActionSubscribeInfo(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok = resp.(subscribeInfoResp)
	require.True(ok)
	require.NotNil(result.SubscribeInfo)
	assert.Equal("开通会员 免费畅听", result.SubscribeInfo.ButtonText)
	assert.Equal("0", result.SubscribeInfo.PromotionPrice)
	assert.Equal(muservip.VipStatusExpired, result.UserVipInfo.Status)
	assert.Equal(promotionalStatusSubscribed, result.UserVipInfo.PromotionalSubscribeStatus)

	// 测试用户是正式会员已过期，没有享受过连续包月首月优惠
	err = vipfeedeductedrecord.VipFeeDeductedRecord{}.DB().Delete("", "user_id = ?", 3457181).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/x/vip/subscribe-info", true, nil)
	c.User().ID = 3457181
	resp, message, err = ActionSubscribeInfo(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok = resp.(subscribeInfoResp)
	require.True(ok)
	require.NotNil(result.SubscribeInfo)
	assert.Equal("9.9 元限时开通会员，免费畅听！", result.SubscribeInfo.ButtonText)
	assert.Equal("9.9", result.SubscribeInfo.PromotionPrice)
	assert.Equal(muservip.VipStatusExpired, result.UserVipInfo.Status)
	assert.Equal(promotionalStatusNotSubscribed, result.UserVipInfo.PromotionalSubscribeStatus)

	// 测试用户未开通过正式会员
	err = muservip.MUserVip{}.DB().Delete("", "user_id = ?", 3457181).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/x/vip/subscribe-info", true, nil)
	c.User().ID = 3457181
	resp, message, err = ActionSubscribeInfo(c)
	require.NoError(err)
	assert.Equal("", message)
	result, ok = resp.(subscribeInfoResp)
	require.True(ok)
	require.NotNil(result.SubscribeInfo)
	assert.Equal("9.9 元限时开通会员，免费畅听！", result.SubscribeInfo.ButtonText)
	assert.Equal("9.9", result.SubscribeInfo.PromotionPrice)
	assert.Equal(muservip.VipStatusNotYet, result.UserVipInfo.Status)
	assert.Equal(promotionalStatusNotSubscribed, result.UserVipInfo.PromotionalSubscribeStatus)
}
