package vip

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/config/params"
	"github.com/MiaoSiLa/missevan-main/models/user"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/models/vip/mvip"
	"github.com/MiaoSiLa/missevan-main/models/vip/vipreceivecoinlog"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 钻石领取状态
const (
	diamondClaimStatusCanNot  = iota - 1 // 不可领取
	diamondClaimStatusNotYet             // 未领取
	diamondClaimStatusClaimed            // 已领取
)

// userInfoResp 用户信息接口返回信息
type userInfoResp struct {
	VipInfo   vipInfo   `json:"vip_info"`  // 当前登录用户会员信息
	Privilege privilege `json:"privilege"` // 会员专属特权模块
}

// privilege 会员特权信息
type privilege struct {
	Diamond *diamond               `json:"diamond,omitempty"`
	List    []params.PrivilegeItem `json:"list"`
}

// diamond 钻石信息
type diamond struct {
	Num         int `json:"num"`
	ClaimStatus int `json:"claim_status"`
}

// vipInfo 用户基本会员信息
type vipInfo struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
	Status   int    `json:"status"`
	EndTime  int64  `json:"end_time,omitempty"`
	Text     string `json:"text"`
}

// ActionUserInfo 用户信息
/**
 * @api {get} /x/vip/user-info 用户信息
 *
 * @apiVersion 0.1.0
 * @apiName x/vip
 * @apiGroup user-info
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "vip_info": { // 当前登录用户会员信息
 *           "user_id": 346287, // 用户 ID
 *           "username": "23336666", // 用户名
 *           "iconurl": "https://static-test.maoercdn/avatars/202312/08/a.png", // 用户头像
 *           "status": 1, // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
 *           "end_time": 1734883200, // 会员过期时间戳，单位：秒。仅在会员有效期内下发
 *           "text": "会员权益生效中，2024-12-23 到期"
 *         },
 *         "privilege": { // 会员专属特权模块
 *           "diamond": { // 会员领取免费钻石信息，不下发时表示当前无该权益，客户端应隐藏此模块
 *             "num": 5, // 会员每日可领取的钻石数量
 *             "claim_status": 0 // 钻石领取状态。-1：不可领取；0：未领取；1：已领取
 *           },
 *           "list": [ // 会员权益列表
 *             {
 *               "title": "会员剧免费畅听",
 *               "icon_url": "https://static-test.maoercdn/avatars/202312/08/a.png"
 *             },
 *             {
 *               "title": "精品剧集 8 折起",
 *               "icon_url": "https://static-test.maoercdn/avatars/202312/08/a.png"
 *             }
 *           ]
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code 100010006
 * @apiError (403) {String} message 请先登录
 * @apiError (403) {Object} data null
 */
func ActionUserInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	resp := userInfoResp{}

	// 需要保证拿到的信息都是最新的，所以不直接从 c.User() 中取用户信息
	userinfo, err := user.FindOne(c.UserID())
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if userinfo == nil {
		return nil, "", actionerrors.ErrUserNotFound
	}

	resp.VipInfo = vipInfo{
		UserID:   c.UserID(),
		Username: userinfo.UserName,
		IconURL:  userinfo.IconURL,
	}
	vip, err := muservip.GetUserVipInfo(c.UserID())
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	resp.VipInfo.Status = vip.Status
	canClaimDiamond := config.Conf.Params.Vip.Privilege.DiamondNum > 0
	if canClaimDiamond {
		resp.Privilege.Diamond = &diamond{
			Num:         config.Conf.Params.Vip.Privilege.DiamondNum,
			ClaimStatus: diamondClaimStatusCanNot,
		}
	}
	switch vip.Status {
	case muservip.VipStatusNotYet:
		resp.VipInfo.Text = fmt.Sprintf("%s 元开通立享多项专属特权", mvip.GetPromotionalSubscribePrice())
	case muservip.VipStatusExpired:
		resp.VipInfo.Text = "会员已过期，续费恢复会员权益"
	case muservip.VipStatusInEffect:
		resp.VipInfo.EndTime = vip.EndTime
		resp.VipInfo.Text = fmt.Sprintf("权益生效中，%s 到期", time.Unix(vip.EndTime, 0).Format(util.TimeFormatYMD))
		if canClaimDiamond {
			// 判断用户今天是否领取过钻石
			claimed, err := vipreceivecoinlog.IsReceivedToday(c.UserID())
			if err != nil {
				return nil, "", actionerrors.ErrServerInternal(err, nil)
			}
			if claimed {
				resp.Privilege.Diamond.ClaimStatus = diamondClaimStatusClaimed
			} else {
				resp.Privilege.Diamond.ClaimStatus = diamondClaimStatusNotYet
			}
		}
	}
	resp.Privilege.List = make([]params.PrivilegeItem, 0, len(config.Conf.Params.Vip.Privilege.List))
	for _, item := range config.Conf.Params.Vip.Privilege.List {
		item.IconURL = service.Storage.Parse(item.IconURL)
		resp.Privilege.List = append(resp.Privilege.List, item)
	}
	return resp, "", nil
}
