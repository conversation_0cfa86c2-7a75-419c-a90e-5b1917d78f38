package vip

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/mpersonamoduleelement"
	"github.com/MiaoSiLa/missevan-main/models/person/mappearance"
)

func TestActionRecommendBlocks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/x/vip/recommend-blocks"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/9.0.1 (iOS;12.0.1;iPhone7,2)")
	data, message, err := ActionRecommendBlocks(c)
	require.NoError(err)
	require.Equal("", message)
	resp, ok := data.(recommendBlocksResp)
	require.True(ok)
	assert.Len(resp.Blocks, 4)
	dressUp, ok := resp.Blocks[3].(blockDressUp)
	require.True(ok)
	assert.Equal(mpersonamoduleelement.BlockTypeDressUP, dressUp.BlockType)
	assert.Equal("会员专属外观", dressUp.Title)
	assert.Len(dressUp.Modules, 3)
	themeSkin, ok := dressUp.Modules[0].(dressUpModule)
	require.True(ok)
	assert.Equal(moduleTypeThemeSkin, themeSkin.Type)
	assert.Equal("APP 主题皮肤", themeSkin.Title)
	avatarFrama, ok := dressUp.Modules[1].(dressUpModule)
	require.True(ok)
	assert.Equal(moduleTypeAvatarFrame, avatarFrama.Type)
	assert.Equal("头像挂件", avatarFrama.Title)
	appearance, ok := dressUp.Modules[2].(*dressUpModule)
	require.True(ok)
	assert.Equal(moduleTypeAppearance, appearance.Type)
	assert.Equal("IP 套装", appearance.Title)
	assert.Len(appearance.Elements, 1)

	// 测试 iOS < 9.0.0 Android < 9.0.0 HarmonyOS < 9.0.0 不下发 IP 套装
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.3.6 (iOS;12.0.1;iPhone7,2)")
	data, message, err = ActionRecommendBlocks(c)
	require.NoError(err)
	require.Equal("", message)
	resp, ok = data.(recommendBlocksResp)
	require.True(ok)
	assert.Len(resp.Blocks, 4)
	dressUp, ok = resp.Blocks[3].(blockDressUp)
	require.True(ok)
	assert.Equal(mpersonamoduleelement.BlockTypeDressUP, dressUp.BlockType)
	assert.Equal("会员专属外观", dressUp.Title)
	assert.Len(dressUp.Modules, 2)
	// 断言不下发 IP 套装
	module, ok := dressUp.Modules[0].(dressUpModule)
	require.True(ok)
	assert.NotEqualValues(moduleTypeAppearance, module.Type)
	module, ok = dressUp.Modules[1].(dressUpModule)
	require.True(ok)
	assert.NotEqualValues(moduleTypeAppearance, module.Type)

	// 测试 IP 套装为空
	err = mappearance.MAppearance{}.DB().Delete("", "id = ?", 1).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/9.0.1 (iOS;12.0.1;iPhone7,2)")
	data, message, err = ActionRecommendBlocks(c)
	require.NoError(err)
	require.Equal("", message)
	resp, ok = data.(recommendBlocksResp)
	require.True(ok)
	assert.Len(resp.Blocks, 4)
	dressUp, ok = resp.Blocks[3].(blockDressUp)
	require.True(ok)
	assert.Equal(mpersonamoduleelement.BlockTypeDressUP, dressUp.BlockType)
	assert.Equal("会员专属外观", dressUp.Title)
	assert.Len(dressUp.Modules, 2)
	themeSkin, ok = dressUp.Modules[0].(dressUpModule)
	require.True(ok)
	assert.Equal(moduleTypeThemeSkin, themeSkin.Type)
	assert.Equal("APP 主题皮肤", themeSkin.Title)
	avatarFrama, ok = dressUp.Modules[1].(dressUpModule)
	require.True(ok)
	assert.Equal(moduleTypeAvatarFrame, avatarFrama.Type)
	assert.Equal("头像挂件", avatarFrama.Title)
}

func TestListVipAppearanceModule(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试 IP 套装为空
	err := mappearance.MAppearance{}.DB().Delete("", "id = ?", 1).Error
	require.NoError(err)
	data := listVipAppearanceModule()
	assert.Nil(data)

	// 测试 IP 套装不为空
	more, err := json.Marshal(mappearance.MoreInfo{
		ListCoverURL: "test://image/test.png",
	})
	require.NoError(err)
	a, err := json.Marshal(mappearance.AppearanceInfo{})
	require.NoError(err)
	appearance := mappearance.MAppearance{
		ID:           1,
		CreateTime:   1,
		ModifiedTime: 1,
		Name:         "测试外观套装",
		Intro:        "测试",
		Vip:          1,
		PayType:      0,
		Price:        0,
		More:         more,
		Appearance:   a,
	}
	err = appearance.DB().Create(&appearance).Error
	require.NoError(err)
	data = listVipAppearanceModule()
	require.NotNil(data)
	assert.Equal(moduleTypeAppearance, data.Type)
	assert.Equal("IP 套装", data.Title)
	assert.Len(data.Elements, 1)
}
