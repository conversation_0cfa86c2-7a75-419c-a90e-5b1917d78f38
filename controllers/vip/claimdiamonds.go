package vip

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// ActionClaimDiamond 领取会员专属钻石
/**
 * @api {post} /x/vip/claim-diamond 领取会员专属钻石
 * @apiDescription 会员有效期内每个自然日可领取一次
 *
 * @apiVersion 0.1.0
 * @apiName x/vip
 * @apiGroup claim-diamond
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "领取成功",
 *       "data": null
 *     }
 *
 * @apiError (403) {Number} code 100010006
 * @apiError (403) {String} message 请先登录
 * @apiError (403) {Object} data null
 *
 * @apiError (400) {Number} code 100010007
 * @apiError (400) {String} message 请勿重复领取
 * @apiError (400) {Object} data null
 */
func ActionClaimDiamond(c *handler.Context) (handler.ActionResponse, string, error) {
	// TODO: 补充业务逻辑
	return nil, "领取成功", nil
}
