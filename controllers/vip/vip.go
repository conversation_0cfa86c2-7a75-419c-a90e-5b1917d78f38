package vip

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// HandlerV2 return handlerV2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "vip",
		Actions: map[string]*handler.ActionV2{
			"menu":             handler.NewActionV2(handler.GET, ActionMenu, handler.ActionOption{LoginRequired: true}),
			"user-info":        handler.NewActionV2(handler.GET, ActionUserInfo, handler.ActionOption{LoginRequired: true}),
			"subscribe-info":   handler.NewActionV2(handler.GET, ActionSubscribeInfo, handler.ActionOption{LoginRequired: false}),
			"claim-diamond":    handler.NewActionV2(handler.POS<PERSON>, ActionClaimDiamond, handler.ActionOption{LoginRequired: true}),
			"recommend-blocks": handler.NewActionV2(handler.GET, ActionRecommendBlocks, handler.ActionOption{LoginRequired: true}),
		},
	}
}
