package vip

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/models/vip/mvip"
	"github.com/MiaoSiLa/missevan-main/models/vip/vipfeedeductedrecord"
)

// 是否促销价开通过会员状态 0：未开通过；1：开通过
const (
	promotionalStatusNotSubscribed = 0
	promotionalStatusSubscribed    = 1
)

type subscribeInfoResp struct {
	SubscribeInfo *subscribeInfo `json:"subscribe_info,omitempty"` // 订购会员信息
	UserVipInfo   userVipInfo    `json:"user_vip_info"`            // 用户会员信息
}

type subscribeInfo struct {
	ButtonText     string `json:"button_text"`     // 开通会员按钮文案
	PromotionPrice string `json:"promotion_price"` // 促销价
}

type userVipInfo struct {
	Status                     int `json:"status"`                       // 会员状态
	PromotionalSubscribeStatus int `json:"promotional_subscribe_status"` // 是否促销价开通过会员状态
}

// ActionSubscribeInfo 订购会员信息
/**
 * @api {get} /x/vip/subscribe-info 订购会员信息
 *
 * @apiVersion 0.1.0
 * @apiName subscribe-info
 * @apiGroup /x/vip
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "subscribe_info": { // 当用户是正式会员时，不返回该字段
 *           "button_text": "9.9 元限时开通会员，免费畅听！", // 开通会员按钮文案
 *           "promotion_price": "9.9" // 促销价，在需要展示促销价格时使用。单位：元
 *         },
 *         "user_vip_info": {
 *           "status": 0, // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
 *           "promotional_subscribe_status": 0 // 是否促销价开通过会员状态。0：未开通过；1：开通过
 *         }
 *       }
 *     }
 */
func ActionSubscribeInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	promotionPrice := mvip.GetPromotionalSubscribePrice()
	resp := subscribeInfoResp{
		SubscribeInfo: &subscribeInfo{
			ButtonText:     fmt.Sprintf("%s 元限时开通会员，免费畅听！", promotionPrice),
			PromotionPrice: promotionPrice,
		},
		UserVipInfo: userVipInfo{
			Status:                     muservip.VipStatusNotYet,
			PromotionalSubscribeStatus: promotionalStatusNotSubscribed,
		},
	}

	userID := c.UserID()
	if userID == 0 {
		return resp, "", nil
	}

	vip, err := muservip.GetUserVipInfo(userID)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	resp.UserVipInfo.Status = vip.Status
	if vip.Status != muservip.VipStatusNotYet {
		// 判断用户是否享受过连续包月首月优惠
		purchased, err := vipfeedeductedrecord.HasSubscribePromotional(userID)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		if purchased {
			resp.UserVipInfo.PromotionalSubscribeStatus = promotionalStatusSubscribed
			if vip.Status == muservip.VipStatusExpired {
				// 用户已经享受过连续包月首月优惠并且用户会员已过期
				resp.SubscribeInfo = &subscribeInfo{
					ButtonText:     "开通会员 免费畅听",
					PromotionPrice: "0",
				}
			}
		}
	}
	if vip.Status == muservip.VipStatusInEffect || vip.Status == muservip.VipStatusTrial {
		resp.SubscribeInfo = nil
	}
	return resp, "", nil
}
