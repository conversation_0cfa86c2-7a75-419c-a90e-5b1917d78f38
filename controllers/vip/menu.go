package vip

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/vip/mpaymethod"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/models/vip/mvip"
	"github.com/MiaoSiLa/missevan-main/models/vip/vipfeedeductedrecord"
	"github.com/MiaoSiLa/missevan-main/service"
)

type vipMenuParam struct {
	userID            int64
	fromGoogleChannel bool // 是否是 google 渠道
	equip             *util.Equipment
	platform          int
	resp              vipMenuResp
}

// vipMenuResp 会员价目响应信息
type vipMenuResp struct {
	Menus             []*menu      `json:"menu"`                 // 会员价目
	PayMethods        []*payMethod `json:"pay_method,omitempty"` // 支付方式
	UserVipStatus     int          `json:"user_vip_status"`      // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
	TradeAgreementURL string       `json:"trade_agreement_url"`  // 会员服务协议链接
}

// menu 会员价目信息
type menu struct {
	ID             int64  `json:"id"`                         // 会员价目 ID
	Title          string `json:"title"`                      // 会员价目标题
	Price          string `json:"price"`                      // 当前价格
	OriginalPrice  string `json:"original_price,omitempty"`   // 划线价格
	ProductID      string `json:"product_id"`                 // 产品 ID
	CornerMarkText string `json:"corner_mark_text,omitempty"` // 右上角角标
	Description    string `json:"description"`                // 套餐描述
	Active         int    `json:"active,omitempty"`           // 是否默认选中。1：是
}

// payMethod 支付方式
type payMethod struct {
	Title   string `json:"title"`            // 支付方式名称
	Type    string `json:"type"`             // 支付方式类型（wechatpay: 微信支付；alipay: 支付宝支付）
	IconURL string `json:"icon_url"`         // 支付图标 URL
	Active  int    `json:"active,omitempty"` // 是否默认选中。1：是
}

// ActionMenu 会员价目
/**
 * @api {get} /x/vip/menu 会员价目
 *
 * @apiVersion 0.1.0
 * @apiName menu
 * @apiGroup /x/vip
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "menu": [ // 会员价目
 *           {
 *             "id": 1, // 价目 ID
 *             "title": "连续包月首月",
 *             "price": "9.9", // 价格（单位：元）
 *             "original_price": "15", // 划线价格（单位：元）
 *             "product_id": "com.missevan.CatEarFM.vip0001",
 *             "corner_mark_text": "限时", // 右上角角标
 *             "description": "· 次月 ¥15/月 续费\n· 可随时取消自动续费，优惠仅限一次",
 *             "active": 1 // 是否默认选中。1：是（不默认选中时不下发）
 *           },
 *           {
 *             "id": 2,
 *             "title": "连续包季",
 *             "price": "39",
 *             "original_price": "54",
 *             "product_id": "com.missevan.CatEarFM.vip0002",
 *             "description": "· 每季 ¥54 续费\n· 可随时取消自动续费"
 *           },
 *           {
 *             "id": 3,
 *             "title": "年费",
 *             "price": "168",
 *             "original_price": "216",
 *             "product_id": "com.missevan.CatEarFM.vip0003",
 *             "description": "· 每年 ¥168 续费\n· 可随时取消自动续费"
 *           }
 *         ],
 *         "pay_method": [ // 支付方式（仅安卓（非 google 渠道）下发支付方式）
 *           {
 *             "icon_url": "https://xxx.png",
 *             "title": "微信支付",
 *             "active": 1, // 是否默认选中。1：是（不默认选中时不下发）
 *             "type": "wechatpay" // 支付方式类型（wechatpay: 微信支付；alipay: 支付宝支付）
 *           },
 *           {
 *             "icon_url": "https://xxx.png",
 *             "title": "支付宝支付",
 *             "type": "alipay"
 *           }
 *         ],
 *         "user_vip_status": 1, // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
 *         "trade_agreement_url": "https://link.missevan.com/rule/vip-agreement" // 会员服务协议
 *       }
 *     }
 */
func ActionMenu(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newVipMenuParam(c)
	if err != nil {
		return nil, "", err
	}

	// 获取价目列表
	err = param.getMenuList()
	if err != nil {
		return nil, "", err
	}

	// 获取支付方式
	err = param.getPayMethod()
	if err != nil {
		return nil, "", err
	}

	// 获取用户会员状态
	err = param.getUserVipStatus()
	if err != nil {
		return nil, "", err
	}

	// 会员服务协议
	param.resp.TradeAgreementURL = config.Conf.Params.Vip.TradeAgreementURL

	return param.resp, "", nil
}

func newVipMenuParam(c *handler.Context) (*vipMenuParam, error) {
	param := new(vipMenuParam)
	param.userID = c.UserID()
	param.fromGoogleChannel = util.IsFromGoogleChannel(c.Request())
	param.equip = c.Equip()
	switch param.equip.OS {
	case util.Android:
		param.platform = mvip.PlatformAndroid
		if param.fromGoogleChannel {
			param.platform = mvip.PlatformGooglePlay
		}
	case util.IOS:
		param.platform = mvip.PlatformIOS
	default:
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "暂不支持的设备")
	}

	return param, nil
}

// getMenuList 获取价目列表
func (param *vipMenuParam) getMenuList() error {
	vipMenuList, err := mvip.ListVipMenu(param.platform)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	if len(vipMenuList) == 0 {
		logger.Error("会员价目信息不存在")
		return nil
	}

	param.resp.Menus = make([]*menu, 0, len(vipMenuList))
	for _, vipMenu := range vipMenuList {
		respMenu := &menu{
			ID:            vipMenu.ID,
			Title:         vipMenu.More.Title,
			Price:         formatMenuPrice(vipMenu.Price),
			OriginalPrice: formatMenuPrice(vipMenu.More.OriginalPrice),
			ProductID:     param.getProductID(vipMenu.ID),
			Description:   vipMenu.More.Description,
			Active:        vipMenu.More.Active,
		}

		if vipMenu.DeductFeeSchedule == mvip.DeductFeeScheduleContinuousMonthly {
			// 连续包月时，判断用户是否享受过首次优惠
			var purchased bool
			var payType []int64
			if param.platform == mvip.PlatformIOS {  // iOS 包月首月优惠跟随苹果账户体系，需要单独区分
				payType = []int64{mvip.PlatformIOS}
			}
			purchased, err = vipfeedeductedrecord.HasSubscribePromotional(param.userID, payType...)
			if err != nil {
				return actionerrors.ErrServerInternal(err, nil)
			}
			if !purchased {
				// 未享受过连续包月首次优惠时，展示首次开通的标题、优惠价格、划线价格、说明文案和角标
				respMenu.Title = vipMenu.More.FirstTitle
				respMenu.Price = formatMenuPrice(vipMenu.More.FirstSubscribeDiscountPrice)
				respMenu.OriginalPrice = formatMenuPrice(vipMenu.More.FirstOriginalPrice)
				respMenu.Description = vipMenu.More.FirstDescription
				respMenu.CornerMarkText = vipMenu.More.CornerMarkText
			}
		}
		param.resp.Menus = append(param.resp.Menus, respMenu)
	}

	return nil
}

// getPayMethod 获取支付方式
func (param *vipMenuParam) getPayMethod() error {
	if param.equip.OS != util.Android || param.fromGoogleChannel {
		// 仅安卓（非 google 渠道）下发支付方式
		return nil
	}

	payMethodList, err := mpaymethod.ListVipPayMethod()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	param.resp.PayMethods = make([]*payMethod, 0, len(payMethodList))
	for _, p := range payMethodList {
		payMethodItem := &payMethod{
			Title:  p.Title,
			Type:   p.Type,
			Active: p.More.Active,
		}
		if p.Icon != "" {
			payMethodItem.IconURL = service.Storage.Parse(p.Icon)
		}
		param.resp.PayMethods = append(param.resp.PayMethods, payMethodItem)
	}

	return nil
}

// getUserVipStatus 获取用户会员状态
func (param *vipMenuParam) getUserVipStatus() error {
	vip, err := muservip.GetUserVipInfo(param.userID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	param.resp.UserVipStatus = vip.Status

	return nil
}

func formatMenuPrice(priceInFen int) string {
	if priceInFen == 0 {
		return ""
	}
	priceInYuan := float64(priceInFen) / 100
	if priceInFen%100 == 0 {
		// 最小单位为元
		return fmt.Sprintf("%.0f", priceInYuan)
	}
	if priceInFen%10 == 0 {
		// 最小单位为角
		return fmt.Sprintf("%.1f", priceInYuan)
	}

	// 最小单位为分
	return fmt.Sprintf("%.2f", priceInYuan)
}

func (param *vipMenuParam) getProductID(vipID int64) string {
	switch param.equip.OS {
	case util.Android:
		if param.fromGoogleChannel {
			return fmt.Sprintf("%s%04d", config.Conf.Params.Vip.IapProductIDsPrefix.MissevanGooglePlay, vipID)
		}
	case util.IOS:
		return fmt.Sprintf("%s%04d", config.Conf.Params.Vip.IapProductIDsPrefix.MissevanIOS, vipID)
	default:
		return ""
	}

	return ""
}
