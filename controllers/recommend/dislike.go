package recommend

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const dislikeSuccessMsg = "将会减少此类内容推荐 _(:з」∠)_"

// ActionDislike dislike 上报
/**
 * @api {post} /x/recommend/dislike dislike 上报
 *
 * @apiVersion 0.1.0
 * @apiName dislike
 * @apiGroup /x/recommend
 *
 * @apiParam {String} reason dislike 原因，JSON 字符串，例如：{"type":1,"drama_id":1}
 *
 * @apiParamExample {json} Request-Example:
 *   {
 *     "reason": "{\"type\":1,\"drama_id\":1}"
 *   }
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "将会减少此类内容推荐 _(:з」∠)_"
 *   }
 */
func ActionDislike(c *handler.Context) (handler.ActionResponse, string, error) {
	// 从请求中获取参数
	reason := c.GetDefaultParamString("reason", "")
	if reason == "" {
		return nil, "", actionerrors.ErrParams
	}

	// TODO: 实现 dislike 上报逻辑

	return nil, dislikeSuccessMsg, nil
}
