package recommend

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/controllers/recommend/homefeed"
)

func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "recommend",
		Actions: map[string]*handler.ActionV2{
			"home-feed": handler.NewActionV2(handler.GET, homefeed.ActionHomeFeed, handler.ActionOption{LoginRequired: false}),
			"get-popup": handler.NewActionV2(handler.GET, ActionGetPopup, handler.ActionOption{LoginRequired: false}),
			"dislike":   handler.NewActionV2(handler.POST, ActionDislike, handler.ActionOption{LoginRequired: false}),
		},
	}
}
