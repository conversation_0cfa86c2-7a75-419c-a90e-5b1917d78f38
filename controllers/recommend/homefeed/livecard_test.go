package homefeed

import (
	"testing"

	"github.com/MiaoSiLa/missevan-main/service/liveapi"
	"github.com/stretchr/testify/assert"
)

func TestNewLiveCardInfo(t *testing.T) {
	input := &liveapi.LiveCard{
		RoomID:          123,
		Name:            "测试直播间",
		CreatorID:       456,
		CreatorUsername: "测试用户",
		CreatorIconURL:  "http://example.com/avatar.png",
		CoverURL:        "http://example.com/cover.png",
		CatalogID:       12,
		CustomTagID:     34,
		Announcement:    "测试公告",
		Statistics: &struct {
			Score int64 `json:"score"`
		}{
			Score: 789,
		},
		ExtraInfo: &struct {
			IconURL string `json:"icon_url"`
			Title   string `json:"title"`
		}{
			IconURL: "http://example.com/icon.png",
			Title:   "测试标题",
		},
		RecommendTag: &liveapi.Tag{
			Type: 6,
			Text: "推荐",
		},
		CatalogTag: &liveapi.Tag{
			Type: 7,
			Text: "分类",
		},
		CustomTag: &liveapi.Tag{
			Type: 4,
			Text: "自定义",
		},
	}

	expected := &liveCardInfo{
		ElementType:     elementTypeLive,
		RoomID:          123,
		Name:            "测试直播间",
		CreatorID:       456,
		CreatorUsername: "测试用户",
		CreatorIconURL:  "http://example.com/avatar.png",
		CoverURL:        "http://example.com/cover.png",
		Announcement:    "测试公告",
		Statistics: liveCardStatistics{
			Score: 789,
		},
		ExtraInfo: &liveCardExtraInfo{
			IconURL: "http://example.com/icon.png",
			Text:    "测试标题",
		},
		Tags: []Tag{
			{
				Type:  tagTypeLiveRecommend,
				Style: tagStyleRedOnFoundation,
				Text:  "推荐",
			},
			{
				ID:    12,
				Type:  tagTypeLiveCatalog,
				Style: tagStyleBlackOnGray,
				Text:  "分类",
			},
			{
				ID:    34,
				Type:  tagTypeLiveCustom,
				Style: tagStyleBlackOnGray,
				Text:  "自定义",
			},
		},
	}

	result := newLiveCardInfo(input)

	assert.Equal(t, expected.ElementType, result.ElementType)
	assert.Equal(t, expected.RoomID, result.RoomID)
	assert.Equal(t, expected.Name, result.Name)
	assert.Equal(t, expected.CreatorID, result.CreatorID)
	assert.Equal(t, expected.CreatorUsername, result.CreatorUsername)
	assert.Equal(t, expected.CreatorIconURL, result.CreatorIconURL)
	assert.Equal(t, expected.CoverURL, result.CoverURL)
	assert.Equal(t, expected.Announcement, result.Announcement)
	assert.Equal(t, expected.Statistics.Score, result.Statistics.Score)
	assert.NotNil(t, result.ExtraInfo)
	assert.Equal(t, expected.ExtraInfo.IconURL, result.ExtraInfo.IconURL)
	assert.Equal(t, expected.ExtraInfo.Text, result.ExtraInfo.Text)
	assert.Equal(t, len(expected.Tags), len(result.Tags))
	for i, tag := range expected.Tags {
		assert.Equal(t, tag.ID, result.Tags[i].ID)
		assert.Equal(t, tag.Type, result.Tags[i].Type)
		assert.Equal(t, tag.Style, result.Tags[i].Style)
		assert.Equal(t, tag.Text, result.Tags[i].Text)
	}
}
