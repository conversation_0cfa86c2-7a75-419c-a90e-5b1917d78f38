package homefeed

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetDramaFirstSoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试空输入列表
	emptyMap, err := GetDramaFirstSoundIDs([]int64{})
	require.NoError(err)
	assert.Nil(emptyMap)

	// 测试所有记录不存在的情况
	nonExistDramaIDs := []int64{2001, 2002, 2003}
	result, err := GetDramaFirstSoundIDs(nonExistDramaIDs)
	require.NoError(err)
	assert.Empty(result)

	// 测试正常查询到记录的情况
	// 正常查询到记录
	testDramaIDs := []int64{101, 102, 103, 105}
	result, err = GetDramaFirstSoundIDs(testDramaIDs)
	require.NoError(err)

	// 预期的结果
	expected := map[int64]int64{
		101: 1003,
		102: 2002,
		103: 3001,
	}
	assert.Equal(expected, result)

	// 验证 dramaID 105 没有对应的 soundID
	_, exists := result[105]
	assert.False(exists)
}
