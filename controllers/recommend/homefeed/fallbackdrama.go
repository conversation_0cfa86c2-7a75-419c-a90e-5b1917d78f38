package homefeed

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

const (
	// 兜底数据缓存时间
	fallbackCacheTTL = time.Hour
	// 兜底数据返回的广播剧数量
	fallbackDramaCount = 200
)

// KeyHomeFeedFallbackDramas0 首页 Feed 流兜底剧集 ID 缓存
const KeyHomeFeedFallbackDramas0 = "homefeed:fallback_dramas"

// GetFallbackDramas 获取兜底剧集 ID 列表
func GetFallbackDramas() ([]int64, error) {
	var dramaIDs []int64
	// 先从缓存中获取
	dataStr, err := service.LRURedis.Get(keys.KeyAppHomepageFeedFallbackDramas0.Format()).Result()
	if err == nil && dataStr != "" {
		err = json.Unmarshal([]byte(dataStr), &dramaIDs)
		if err == nil && len(dramaIDs) > 0 {
			return dramaIDs, nil
		}
	}

	// 缓存不存在或者过期，从数据库查询
	dramaIDs, err = queryFallbackDramas()
	if err != nil {
		return nil, err
	}

	// 写入缓存
	if len(dramaIDs) > 0 {
		dataBytes, err := json.Marshal(dramaIDs)
		if err != nil {
			logger.Errorf("序列化兜底剧集 ID 列表失败: %v", err)
		} else {
			err = service.LRURedis.Set(keys.KeyAppHomepageFeedFallbackDramas0.Format(), string(dataBytes), fallbackCacheTTL).Err()
			if err != nil {
				logger.Errorf("缓存兜底剧集 ID 列表失败: %v", err)
				// PASS
			}
		}
	}

	return dramaIDs, nil
}

// queryFallbackDramas 查询兜底剧集 ID 列表
func queryFallbackDramas() ([]int64, error) {
	var dramaIDs []int64

	// 兜底数据条件：
	// 1. 版权类型 = 精品版权 (ip_id > 0)
	// 2. 剧集类型：中文广播剧(89)、有声故事（听书）(86)、有声漫画(95)
	// 3. 取播放量 TOP 200 按播放量倒序排序
	// 4. 已过审状态 (checked = 1)
	// 5. 未被报警 (police = 0)
	// 6. 不是擦边球 (refined & 1 = 0)
	err := dramainfo.RadioDramaDramainfo{}.DB().
		Table(dramainfo.RadioDramaDramainfo{}.TableName()+" FORCE INDEX (idx_checked_catalog_lastupdate_time)").
		Select("id").
		Where(
			"catalog IN (?, ?, ?) AND police = ? AND checked = ? AND ip_id > 0 AND NOT refined & ?",
			dramainfo.CatalogIDCnRadioDrama,
			dramainfo.CatalogIDAudioBook,
			dramainfo.CatalogIDCartoon,
			dramainfo.PoliceNo,
			dramainfo.CheckedPass,
			dramainfo.RefinedBitMaskRisking,
		).
		Order("view_count DESC").
		Limit(fallbackDramaCount).
		Pluck("id", &dramaIDs).
		Error

	if err != nil {
		return nil, fmt.Errorf("查询兜底剧集失败: %w", err)
	}

	return dramaIDs, nil
}

// GetDramaFirstSoundIDs 获取剧集列表下第一个单集对应的音频 ID 映射关系
func GetDramaFirstSoundIDs(dramaIDs []int64) (map[int64]int64, error) {
	if len(dramaIDs) == 0 {
		return nil, nil
	}

	// 获取剧集对应的第一个单集信息
	episodes, err := dramaepisode.ListDramaFirstSoundIDs(dramaIDs)
	if err != nil {
		return nil, err
	}

	// 构建剧集 ID 到音频 ID 的映射关系
	dramaIDSoundIDMap := make(map[int64]int64, len(episodes))
	for _, episode := range episodes {
		dramaIDSoundIDMap[episode.DramaID] = episode.SoundID
	}

	if len(dramaIDSoundIDMap) != len(dramaIDs) {
		missingDramaIDs := make([]int64, 0, len(dramaIDs)-len(dramaIDSoundIDMap))
		for _, dramaID := range dramaIDs {
			if _, ok := dramaIDSoundIDMap[dramaID]; !ok {
				missingDramaIDs = append(missingDramaIDs, dramaID)
			}
		}
		logger.WithFields(logger.Fields{
			"drama_ids": missingDramaIDs,
		}).Error("存在没有获取到音频 ID 的剧集")
	}

	return dramaIDSoundIDMap, nil
}
