package homefeed

import (
	"slices"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermarkstyle"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisodecv"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfoaddendum"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramasubscription"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramataginfo"
	"github.com/MiaoSiLa/missevan-main/models/mowangsksoundseiy"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

// 版权类型 1：精品版权，2：非精品版权
const (
	iprTypeQualityYes = iota + 1
	iprTypeQualityNo
)

// 标签样式，1：粉底红字，2：灰底黑字
const (
	tagStyleRedOnFoundation = iota + 1
	tagStyleBlackOnGray
)

type dramaCardParams struct {
	SoundIDs []int64 // 算法侧下发的剧集卡片的音频 ID 数组
	UserID   int64   // 当前用户 ID
	Uc       mrpc.UserContext

	dramaIDs                   []int64                         // 剧集卡片音频 ID 对应的剧集 ID 数组
	dramaInfoList              []dramainfo.RadioDramaDramainfo // 剧集卡片音频 ID 对应的剧集信息列表
	myCatchUpTagDramaIDTagMap  map[int64]Tag                   // 符合【我的追剧标签】的剧集对应的标签
	rankTagTagDramaIDTagMap    map[int64]Tag                   // 符合【排行榜标签】的剧集对应的标签
	followedCvTagDramaIDTagMap map[int64]Tag                   // 符合【已关注声优作品标签】的剧集对应的标签
	adaptedTagTagDramaIDTagMap map[int64]Tag                   // 符合【{原著作者}原著改编作品标签】的剧集对应的标签
	commentTagDramaIDTagMap    map[int64]Tag                   // 符合【超 x 万讨论标签】的剧集对应的标签
}

// DramaCardInfo 剧集卡片信息
type DramaCardInfo struct {
	ElementType elementType                      `json:"element_type"`          // 元素类型
	DramaID     int64                            `json:"drama_id"`              // 剧集 ID
	SoundID     int64                            `json:"sound_id"`              // 音频 ID
	Title       string                           `json:"title"`                 // 剧集标题
	CoverURL    string                           `json:"cover_url"`             // 剧集封面
	CoverColor  int                              `json:"cover_color"`           // 背景图主颜色，十进制表示
	Intro       string                           `json:"intro"`                 // 剧集简介，优先下发运营配置的一行简介
	IPRType     int                              `json:"ipr_type"`              // 版权类型，1：精品版权，2：非精品版权
	Cvs         []string                         `json:"cvs"`                   // 声优列表
	ViewCount   int64                            `json:"view_count"`            // 播放量
	PayType     int                              `json:"pay_type"`              // 付费类型（0: 免费；1: 单集付费；2: 整剧付费）
	Newest      string                           `json:"newest"`                // 最近更新
	Integrity   int                              `json:"integrity"`             // 完结度（1: 长篇未完结；2: 长篇完结；3: 全一期）
	Tags        []Tag                            `json:"tags"`                  // 标签列表
	CornerMark  *dramacornermarkstyle.CornerMark `json:"corner_mark,omitempty"` // 角标（可选）
}

// Tag 标签
type Tag struct {
	ID    int64  `json:"-"`             // 标签 ID
	Type  int    `json:"-"`             // 标签类型
	Style int    `json:"style"`         // 标签样式，1：粉底红字，2：灰底黑字
	Text  string `json:"text"`          // 标签文本
	Url   string `json:"url,omitempty"` // 标签跳转链接
}

const (
	tagTypeDramaSpecial = iota + 1
	tagTypeDramaNormal
	tagTypeLiveRecommend
	tagTypeLiveCatalog
	tagTypeLiveCustom
)

// getSoundIDDramaCardMap 根据音频 ID 获取剧集卡片信息 map
func (param *dramaCardParams) getSoundIDDramaCardMap() (map[int64]*DramaCardInfo, error) {
	// 根据音频 ID 获取剧集 ID
	soundIDDramaIDMap, err := dramainfo.GetDramaIDsBySoundIDs(param.SoundIDs)
	if err != nil {
		return nil, err
	}
	if len(soundIDDramaIDMap) <= 0 {
		return nil, nil
	}

	dramaIDs := make([]int64, 0, len(soundIDDramaIDMap))
	for _, dramaID := range soundIDDramaIDMap {
		dramaIDs = append(dramaIDs, dramaID)
	}
	dramaIDs = sets.Uniq(dramaIDs)
	param.dramaIDs = dramaIDs

	dramaInfos, err := dramainfo.ListDramaInfoByIDs(dramaIDs)
	if err != nil {
		return nil, err
	}
	dramaLen := len(dramaInfos)
	if dramaLen <= 0 {
		return nil, nil
	}
	param.dramaInfoList = make([]dramainfo.RadioDramaDramainfo, 0, dramaLen)
	// 属于精品版权的剧集 ID（有 ipr_id 的剧集）
	qualityDramaIDs := make([]int64, 0, dramaLen)
	for _, drama := range dramaInfos {
		param.dramaInfoList = append(param.dramaInfoList, *drama)
		if drama.IPID != 0 {
			qualityDramaIDs = append(qualityDramaIDs, drama.ID)
		}
	}
	// 获取精品版权剧集对应的主役声优信息
	qualityDramaIDCvNamesMap, err := getQualityDramaCvNames(qualityDramaIDs)
	if err != nil {
		return nil, err
	}

	// 获取剧集角标信息，该方法同时会对 dramasInfoList 中剧集加上 need_pay 信息
	cornerMarkMap, err := dramacornermarkstyle.GetDramaCornerMark(param.UserID, param.dramaInfoList)
	if err != nil {
		return nil, err
	}
	// 获取剧集更多信息
	moreDramaInfoMap, err := dramainfoaddendum.MapDramaInfoAddendum(dramaIDs)
	if err != nil {
		return nil, err
	}
	// 获取剧集标签信息
	dramaIDTagsMap, err := param.getDramaCardTags()
	if err != nil {
		return nil, err
	}
	dramaInfoMap := util.ToMap(dramaInfos, "ID").(map[int64]*dramainfo.RadioDramaDramainfo)
	soundIDDramaCardMap := make(map[int64]*DramaCardInfo, len(soundIDDramaIDMap))
	for soundID, dramaID := range soundIDDramaIDMap {
		dramaInfo, ok := dramaInfoMap[dramaID]
		if !ok {
			continue
		}
		// 算法推荐的剧集数据为 T+1 更新，业务侧需要针对审核状态、是否擦边等重要条件做兜底过滤
		if dramaInfo.Checked != dramainfo.CheckedPass || dramaInfo.Refined.IsSet(dramainfo.RefinedRisking) {
			continue
		}
		dramaCardInfo := &DramaCardInfo{
			ElementType: elementTypeSound,
			DramaID:     dramaInfo.ID,
			SoundID:     soundID,
			CoverURL:    dramaInfo.CoverURL,
			CoverColor:  dramaInfo.CoverColor,
			IPRType:     iprTypeQualityNo,
			ViewCount:   dramaInfo.ViewCount,
			Newest:      dramaInfo.Newest,
			Integrity:   int(*dramaInfo.Integrity),
			PayType:     int(dramaInfo.PayType),
		}
		if dramaInfo.Name != nil {
			dramaCardInfo.Title = *dramaInfo.Name
		}
		if dramaInfo.Abstract != nil {
			dramaCardInfo.Intro = util.ExtractHTMLPlainText(*dramaInfo.Abstract)
		}
		if slices.Contains(qualityDramaIDs, dramaInfo.ID) {
			// 版权类型 1：精品版权，2：非精品版权
			dramaCardInfo.IPRType = iprTypeQualityYes
			if cvNames, ok := qualityDramaIDCvNamesMap[dramaInfo.ID]; ok {
				// 精品版权对应的主役声优名称
				dramaCardInfo.Cvs = cvNames
			}
		}
		if cornerMark, ok := cornerMarkMap[dramaInfo.ID]; ok {
			// 剧集角标
			dramaCardInfo.CornerMark = &cornerMark
		}
		if moreDramaInfo, ok := moreDramaInfoMap[dramaInfo.ID]; ok {
			if moreDramaInfo.ShortIntro != "" {
				// 优先下发运营配置的一句话简介
				dramaCardInfo.Intro = moreDramaInfo.ShortIntro
				// 存在一句话简介时不下发声优信息
				dramaCardInfo.Cvs = nil
			}
		}
		if dramaIDTags, ok := dramaIDTagsMap[dramaInfo.ID]; ok {
			dramaCardInfo.Tags = dramaIDTags
		}
		soundIDDramaCardMap[soundID] = dramaCardInfo
	}

	return soundIDDramaCardMap, nil
}

// getQualityDramaCvNames 获取精品版权剧集对应的主役声优名称
func getQualityDramaCvNames(qualityDramaIDs []int64) (map[int64][]string, error) {
	if len(qualityDramaIDs) == 0 {
		return nil, nil
	}

	// 获取精品版权剧集对应的主役声优
	listDramaCvsMain, err := dramaepisodecv.ListDramaCvsMain(qualityDramaIDs)
	if err != nil {
		return nil, err
	}
	listDramaCvsMainLen := len(listDramaCvsMain)
	if listDramaCvsMainLen == 0 {
		return nil, nil
	}
	// 剧集对应的 cv IDs map
	dramaIDCvIDsMap := make(map[int64][]int64, listDramaCvsMainLen)
	// 所有的 cv ID
	cvIDs := make([]int64, 0, listDramaCvsMainLen)
	for _, cv := range listDramaCvsMain {
		cvIDs = append(cvIDs, cv.CvID)
		if dramaIDCvIDs, ok := dramaIDCvIDsMap[cv.DramaID]; ok {
			if slices.Contains(dramaIDCvIDs, cv.CvID) {
				// 保证每个剧集下的 cvID 不重复
				continue
			}
		}
		dramaIDCvIDsMap[cv.DramaID] = append(dramaIDCvIDsMap[cv.DramaID], cv.CvID)
	}

	// 获取所有声优信息
	cvs, err := mowangsksoundseiy.ListCvsByIDs(sets.Uniq(cvIDs))
	if err != nil {
		return nil, err
	}
	cvMap := util.ToMap(cvs, "ID").(map[int64]mowangsksoundseiy.MowangskSoundSeiy)

	dramaIDCvNamesMap := make(map[int64][]string, len(dramaIDCvIDsMap))
	for dramaID, dramaCvIDs := range dramaIDCvIDsMap {
		cvNameList := make([]string, 0, len(dramaCvIDs))
		for _, dramaCvID := range dramaCvIDs {
			if dramaCv, ok := cvMap[dramaCvID]; ok {
				cvNameList = append(cvNameList, dramaCv.Name)
			}
		}
		if len(cvNameList) > 0 {
			dramaIDCvNamesMap[dramaID] = cvNameList
		}
	}
	return dramaIDCvNamesMap, nil
}

// getDramaCardTags 获取剧集卡片标签
func (param *dramaCardParams) getDramaCardTags() (map[int64][]Tag, error) {
	// 特殊标签
	dramaIDSpecialTagMap := param.getDramaIDSpecialTagMap()
	// 普通标签
	dramaIDNormalTagsMap, err := radiodramataginfo.MapUserVisibleDramaTags(param.dramaIDs, true)
	if err != nil {
		return nil, err
	}

	dramaIDTagsMap := make(map[int64][]Tag, len(param.dramaIDs))
	for _, dramaID := range param.dramaIDs {
		// 每个剧集最多展示 1 个特殊标签 + 2 个普通标签
		dramsTags := make([]Tag, 0, 3)
		if specialTags, ok := dramaIDSpecialTagMap[dramaID]; ok {
			// 最多展示 1 个特殊标签
			if len(specialTags) > 0 {
				tag := specialTags[0]
				tag.Type = tagTypeDramaSpecial
				dramsTags = append(dramsTags, tag)
			}
		}
		if normalTags, ok := dramaIDNormalTagsMap[dramaID]; ok {
			i := 1
			for _, normalTag := range normalTags {
				// 截取 2 个普通标签（普通标签最多展示 2 个）
				if i > 2 {
					break
				}
				dramsTags = append(dramsTags, Tag{
					ID:    normalTag.ID,
					Type:  tagTypeDramaNormal,
					Style: tagStyleBlackOnGray,
					Text:  normalTag.Name,
				})
				i++
			}
		}
		if len(dramsTags) > 0 {
			dramaIDTagsMap[dramaID] = dramsTags
		}
	}
	return dramaIDTagsMap, nil
}

// getDramaIDSpecialTagMap 获取剧集 ID 特殊标签 map
func (param *dramaCardParams) getDramaIDSpecialTagMap() map[int64][]Tag {
	// 特殊标签优先级：我的追剧 > 排行榜标签 > 已关注声优作品 > {原著作者}原著改编作品 > 超 x 万讨论
	// 获取符合【我的追剧标签】的剧集
	err := param.processMyCatchUpTag()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": param.UserID}).Errorf("获取我的追剧标签出错，error: %v", err)
		// PASS
	}
	// 获取符合【排行榜标签】的剧集
	err = param.processRankTag()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": param.UserID}).Errorf("获取排行榜标签出错，error: %v", err)
		// PASS
	}
	// 获取符合【已关注声优作品标签】的剧集
	err = param.processFollowedCvTag()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": param.UserID}).Errorf("获取我的已关注声优作品标签出错，error: %v", err)
		// PASS
	}
	// 获取符合【{原著作者}原著改编作品标签】的剧集
	err = param.processAdaptedTag()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": param.UserID}).Errorf("获取原著改编作品标签出错，error: %v", err)
		// PASS
	}
	// 获取符合【超 x 万讨论标签】的剧集
	err = param.processCommentTag()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": param.UserID}).Errorf("获取超 x 万讨论标签出错，error: %v", err)
		// PASS
	}
	if len(param.myCatchUpTagDramaIDTagMap) <= 0 && len(param.rankTagTagDramaIDTagMap) <= 0 &&
		len(param.followedCvTagDramaIDTagMap) <= 0 && len(param.adaptedTagTagDramaIDTagMap) <= 0 &&
		len(param.commentTagDramaIDTagMap) <= 0 {
		// 如果 5 个特殊标签都为空，则直接返回
		return nil
	}

	dramaIDTags := make(map[int64][]Tag, len(param.dramaIDs))
	for _, dramaID := range param.dramaIDs {
		// 目前有 5 种特殊标签
		tags := make([]Tag, 0, 5)
		if myCatchUpTag, ok := param.myCatchUpTagDramaIDTagMap[dramaID]; ok {
			tags = append(tags, myCatchUpTag)
		}
		if rankTag, ok := param.rankTagTagDramaIDTagMap[dramaID]; ok {
			tags = append(tags, rankTag)
		}
		if followedCvTag, ok := param.followedCvTagDramaIDTagMap[dramaID]; ok {
			tags = append(tags, followedCvTag)
		}
		if adaptedTagTag, ok := param.adaptedTagTagDramaIDTagMap[dramaID]; ok {
			tags = append(tags, adaptedTagTag)
		}
		if commentTag, ok := param.commentTagDramaIDTagMap[dramaID]; ok {
			tags = append(tags, commentTag)
		}

		if len(tags) > 0 {
			dramaIDTags[dramaID] = tags
		}
	}

	return dramaIDTags
}

// processMyCatchUpTag 获取符合【我的追剧标签】的剧集
func (param *dramaCardParams) processMyCatchUpTag() error {
	if len(param.dramaIDs) <= 0 || param.UserID <= 0 {
		return nil
	}
	// 订阅剧集
	subscribeDramaIDs, err := radiodramasubscription.ListUserSubscribeDramaIDs(param.UserID)
	if err != nil {
		return err
	}
	// 已购剧集
	boughtDramaIDs, err := userapi.GetPaidDramaIDs(param.Uc, param.UserID)
	if err != nil {
		return err
	}
	if len(subscribeDramaIDs) <= 0 && len(boughtDramaIDs) <= 0 {
		return nil
	}

	// 去重剧集 IDs
	subscribeAndPaidDramaIDs := make([]int64, len(subscribeDramaIDs)+len(boughtDramaIDs))
	subscribeAndPaidDramaIDs = append(subscribeAndPaidDramaIDs, subscribeDramaIDs...)
	subscribeAndPaidDramaIDs = append(subscribeAndPaidDramaIDs, boughtDramaIDs...)
	subscribeAndPaidDramaIDs = sets.Uniq(subscribeAndPaidDramaIDs)

	// 获取 subscribeAndPaidDramaIDs 同 IPR ID 的剧集 IDs
	sameIPRIDDramaIDs, err := dramainfo.FindSameIPRIDDramaIDs(subscribeAndPaidDramaIDs)
	if err != nil {
		return nil
	}
	allDramaIDs := make([]int64, len(subscribeAndPaidDramaIDs)+len(sameIPRIDDramaIDs))
	allDramaIDs = append(allDramaIDs, subscribeAndPaidDramaIDs...)
	allDramaIDs = append(allDramaIDs, sameIPRIDDramaIDs...)
	allDramaIDs = sets.Uniq(allDramaIDs)

	// 符合【我的追剧标签】的剧集
	param.myCatchUpTagDramaIDTagMap = make(map[int64]Tag, len(param.dramaIDs))
	for _, dramaID := range param.dramaIDs {
		if slices.Contains(allDramaIDs, dramaID) {
			param.myCatchUpTagDramaIDTagMap[dramaID] = Tag{Style: tagStyleRedOnFoundation, Text: "我的追剧"}
		}
	}
	return nil
}

// processRankTag 获取符合【排行榜标签】的剧集
func (param *dramaCardParams) processRankTag() error {
	// TODO: 相关逻辑待补充
	return nil
}

// processFollowedCvTag 获取符合【已关注声优作品标签】的剧集
func (param *dramaCardParams) processFollowedCvTag() error {
	// TODO: 相关逻辑待补充
	return nil
}

// processAdaptedTag 获取符合【{原著作者}原著改编作品标签】的剧集
func (param *dramaCardParams) processAdaptedTag() error {
	// TODO: 相关逻辑待补充
	return nil
}

// processCommentTag 获取符合【超 x 万讨论标签】的剧集
func (param *dramaCardParams) processCommentTag() error {
	// TODO: 相关逻辑待补充
	return nil
}
