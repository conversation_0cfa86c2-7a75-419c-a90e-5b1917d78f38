package homefeed

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/url"
	mre "github.com/MiaoSiLa/missevan-main/models/mrecommendedelements"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
	"github.com/MiaoSiLa/missevan-main/service/liveapi"
)

// ExtraBannerItem 副通栏元素
type ExtraBannerItem struct {
	URL string `json:"url"` // 跳转链接
	Pic string `json:"pic"` // 图片链接
}

// ExtraBanner 副通栏
type ExtraBanner []mre.MRecommendedElement

// ExtraBannerMap module_id -> 副通栏
type ExtraBannerMap map[int64]ExtraBanner

// 副通栏位置
const (
	ExtraBannerModuleID1 = iota + 1
	ExtraBannerModuleID2
	ExtraBannerModuleID3
	ExtraBannerModuleID4
)

// ExtraBannerModuleIDs 副通栏位置列表
var ExtraBannerModuleIDs = []int64{
	ExtraBannerModuleID1,
	ExtraBannerModuleID2,
	ExtraBannerModuleID3,
	ExtraBannerModuleID4,
}

// ExtraBannerProvider 提供获取副通栏信息的功能
type ExtraBannerProvider struct {
	NowTime     time.Time
	UserID      int64
	UserContext mrpc.UserContext
	Equipment   *util.Equipment
}

// GetBanner 获取指定 moduleID 的副通栏信息
func (p *ExtraBannerProvider) GetBanner(moduleID int64) ([]ExtraBannerItem, error) {
	banners, err := p.listBanners()
	if err != nil {
		return nil, err
	}
	if banner, ok := banners[moduleID]; ok {
		return util.SliceMap(banner, func(item mre.MRecommendedElement) ExtraBannerItem {
			// 处理 URL，使用 GetUsableAppLink 进行转换
			itemURL := url.GetUsableAppLink(item.URL, p.Equipment)

			return ExtraBannerItem{
				URL: itemURL,
				Pic: item.Cover,
			}
		}), nil
	}
	return nil, nil
}

func (p *ExtraBannerProvider) listBanners() (ExtraBannerMap, error) {
	liveExtraBanners, err := p.listLiveBanners()
	if err != nil {
		return nil, err
	}

	aodExtraBanners, err := p.listAodBanners()
	if err != nil {
		return nil, err
	}

	return p.combineBanners(liveExtraBanners, aodExtraBanners), nil
}

func (p *ExtraBannerProvider) listAodBanners() (ExtraBannerMap, error) {
	banners, ok, err := p.listAodBannersFromCache()
	if err != nil {
		return nil, err
	}
	if ok {
		return banners, nil
	}
	banners, err = p.listAodBannersFromDB()
	if err != nil {
		return nil, err
	}
	if err := p.setAodBannersToCache(banners); err != nil {
		logger.WithFields(logger.Fields{
			"user_id": p.UserID,
			"error":   err,
		}).Error("缓存副通栏信息失败")
		// PASS
	}
	return banners, nil
}

func (p *ExtraBannerProvider) listAodBannersFromCache() (ExtraBannerMap, bool, error) {
	key := p.keyAodBanners()
	bannersJson, err := service.LRURedis.Get(key).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return nil, false, nil
		}
		return nil, false, err
	}
	var banners ExtraBannerMap
	err = json.Unmarshal([]byte(bannersJson), &banners)
	if err != nil {
		return nil, false, err
	}
	return banners, true, nil
}

func (p *ExtraBannerProvider) listAodBannersFromDB() (ExtraBannerMap, error) {
	now := p.NowTime.Unix()
	db := mre.MRecommendedElement{}.DB()
	var elements []mre.MRecommendedElement
	client := p.Equipment.OS
	if p.Equipment.OS == util.HarmonyOS {
		client = util.Android
	}
	err := db.
		Where(map[string]any{
			"client":      client,
			"module_type": mre.ModuleTypeExtraBanner,
			"archive":     mre.ArchiveOnline,
		}).
		Where("module_id IN (?)", ExtraBannerModuleIDs).
		Where("start_time <= ? AND end_time > ?", now, now).
		Order("module_id ASC, sort ASC").
		Find(&elements).Error
	if err != nil {
		return nil, err
	}
	return p.fromRecommendedElements(elements), nil
}

func (p *ExtraBannerProvider) setAodBannersToCache(banners ExtraBannerMap) error {
	bannersBytes, err := json.Marshal(banners)
	if err != nil {
		return err
	}
	key := p.keyAodBanners()
	expiration := p.calculateCacheDuration(banners)
	return service.LRURedis.Set(key, bannersBytes, expiration).Err()
}

func (p *ExtraBannerProvider) keyAodBanners() string {
	return keys.KeyAppHomepageExtraBanner1.Format(int(p.Equipment.OS))
}

func (p *ExtraBannerProvider) calculateCacheDuration(banners ExtraBannerMap) time.Duration {
	calculator := mre.CacheDurationCalculator{
		Client:     int(p.Equipment.OS),
		ModuleType: mre.ModuleTypeExtraBanner,
		ModuleIDs:  ExtraBannerModuleIDs,
		NowTime:    p.NowTime,
	}
	flattenedElements := make([]mre.MRecommendedElement, 0, len(banners))
	for _, elements := range banners {
		flattenedElements = append(flattenedElements, elements...)
	}
	return calculator.Calculate(flattenedElements)
}

func (p *ExtraBannerProvider) listLiveBanners() (ExtraBannerMap, error) {
	resp, err := liveapi.GetLiveExtraBanners(p.UserContext, liveapi.ExtraBannersParam{
		UserID:    p.UserID,
		Positions: ExtraBannerModuleIDs,
	})
	if err != nil {
		return nil, err
	}
	banners := make(ExtraBannerMap, len(resp.Data))
	for moduleID, items := range resp.Data {
		elements := util.SliceMap(items, func(item liveapi.ExtraBannerItem) mre.MRecommendedElement {
			return mre.MRecommendedElement{
				ModuleID: moduleID,
				Cover:    item.Pic,
				URL:      item.URL,
			}
		})
		banners[moduleID] = elements
	}
	return banners, nil
}

func (p *ExtraBannerProvider) fromRecommendedElements(elements []mre.MRecommendedElement) ExtraBannerMap {
	banners := make(ExtraBannerMap, len(elements))
	for _, element := range elements {
		banners[element.ModuleID] = append(
			banners[element.ModuleID],
			element,
		)
	}
	return banners
}

func (p *ExtraBannerProvider) combineBanners(
	liveExtraBanners ExtraBannerMap,
	aodExtraBanners ExtraBannerMap,
) ExtraBannerMap {
	if len(liveExtraBanners) == 0 && len(aodExtraBanners) == 0 {
		return nil
	}
	result := make(ExtraBannerMap)
	for moduleID, banners := range liveExtraBanners {
		if len(banners) > 0 {
			result[moduleID] = banners
		}
	}
	for moduleID, banners := range aodExtraBanners {
		if len(banners) > 0 {
			result[moduleID] = append(result[moduleID], banners...)
		}
	}
	return result
}
