package homefeed

import (
	"fmt"
	"testing"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/stretchr/testify/assert"
)

func createMockContext() *handler.Context {
	return handler.NewTestContext("GET", "/test", true, nil)
}

func createMockFeedCards() []*feedCard {
	return []*feedCard{
		{
			ID:        12345,
			Goto:      tianma.GotoSound,
			Source:    "mock_source",
			AvFeature: "{\"card_type\":1}",
			TrackID:   "mock_track_id",
		},
	}
}

func TestBuildKey(t *testing.T) {
	c := createMockContext()
	cards := createMockFeedCards()

	sender := exposureLogSender{
		Context:     c,
		Cards:       cards,
		Network:     1,
		RefreshType: tianma.FreshTypeAuto,
		RefreshNum:  1,
	}

	key := sender.buildKey()
	// 这里使用 Log 替代直接测试，因为 UserID 可能是从上下文中动态获取的
	fmt.Println("Generated key:", key)
	assert.Contains(t, key, "recommend_exposure_log:")
}

func TestBuildLogData(t *testing.T) {
	c := createMockContext()
	cards := createMockFeedCards()

	sender := exposureLogSender{
		Context:     c,
		Cards:       cards,
		Network:     1,
		RefreshType: tianma.FreshTypeAuto,
		RefreshNum:  1,
		UserFeature: "mock_user_feature",
	}

	log := sender.buildLogData()

	// 验证日志内容
	assert.Equal(t, c.UserID(), log.UserID)
	assert.Equal(t, 1, log.Network)
	assert.Equal(t, tianma.FreshTypeAuto, log.RefreshType)
	assert.Equal(t, int64(1), log.RefreshNum)
	assert.Equal(t, "mock_track_id", log.TrackID)
	assert.Equal(t, "mock_user_feature", log.UserFeature)

	// 验证曝光项
	assert.Len(t, log.ShowList, 1)
	assert.Equal(t, int64(12345), log.ShowList[0].ID)
	assert.Equal(t, tianma.GotoSound, log.ShowList[0].Goto)
	assert.Equal(t, 1, log.ShowList[0].Pos)
	assert.Equal(t, "mock_source", log.ShowList[0].Source)
	assert.Equal(t, "{\"card_type\":1}", log.ShowList[0].AvFeature)
}
