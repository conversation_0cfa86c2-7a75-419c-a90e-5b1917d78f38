package homefeed

import "github.com/MiaoSiLa/missevan-main/service/liveapi"

type liveCardInfo struct {
	ElementType     elementType        `json:"element_type"`         // 元素类型
	RoomID          int64              `json:"room_id"`              // 直播间 ID
	Name            string             `json:"name"`                 // 直播间名称
	CreatorID       int64              `json:"creator_id"`           // 主播 ID
	CreatorUsername string             `json:"creator_username"`     // 主播用户名
	CreatorIconURL  string             `json:"creator_iconurl"`      // 主播头像
	CoverURL        string             `json:"cover_url"`            // 直播间封面
	Announcement    string             `json:"announcement"`         // 直播间公告
	Statistics      liveCardStatistics `json:"statistics"`           // 直播间统计信息
	ExtraInfo       *liveCardExtraInfo `json:"extra_info,omitempty"` // 直播间补充信息
	Tags            []Tag              `json:"tags"`                 // 卡片标签
}

type liveCardStatistics struct {
	Score int64 `json:"score"` // 直播间热度
}

type liveCardExtraInfo struct {
	IconURL string `json:"icon_url"` // 图标 URL
	Text    string `json:"text"`     // 文本
}

func newLiveCardInfo(card *liveapi.LiveCard) *liveCardInfo {
	liveCard := &liveCardInfo{
		ElementType:     elementTypeLive,
		RoomID:          card.RoomID,
		Name:            card.Name,
		CreatorID:       card.CreatorID,
		CreatorUsername: card.CreatorUsername,
		CreatorIconURL:  card.CreatorIconURL,
		CoverURL:        card.CoverURL,
		Announcement:    card.Announcement,
	}
	if card.Statistics != nil {
		liveCard.Statistics = liveCardStatistics{
			Score: card.Statistics.Score,
		}
	}
	if card.ExtraInfo != nil {
		liveCard.ExtraInfo = &liveCardExtraInfo{
			IconURL: card.ExtraInfo.IconURL,
			Text:    card.ExtraInfo.Title,
		}
	}
	if card.RecommendTag != nil {
		liveCard.Tags = append(liveCard.Tags, Tag{
			Type:  tagTypeLiveRecommend,
			Style: tagStyleRedOnFoundation,
			Text:  card.RecommendTag.Text,
		})
	}
	if card.CatalogTag != nil {
		liveCard.Tags = append(liveCard.Tags, Tag{
			ID:    card.CatalogID,
			Type:  tagTypeLiveCatalog,
			Style: tagStyleBlackOnGray,
			Text:  card.CatalogTag.Text,
		})
	}
	if card.CustomTag != nil {
		liveCard.Tags = append(liveCard.Tags, Tag{
			ID:    card.CustomTagID,
			Type:  tagTypeLiveCustom,
			Style: tagStyleBlackOnGray,
			Text:  card.CustomTag.Text,
		})
	}
	return liveCard
}
