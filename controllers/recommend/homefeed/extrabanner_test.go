package homefeed

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
	mre "github.com/MiaoSiLa/missevan-main/models/mrecommendedelements"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
	"github.com/MiaoSiLa/missevan-main/service/liveapi"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestExtraBannerProvider_listBanners(t *testing.T) {
	// 设置空的直播通栏响应
	liveapi.SetMockGetLiveExtraBanners(t, liveapi.ExtraBannersResponse{}, nil)

	now := util.TimeNow()
	defaultTestBanners := ExtraBannerMap{
		ExtraBannerModuleID1: {
			{
				URL:   "https://test.com/aaa?foo=bar",
				Cover: "https://test.com/aaa?foo=bar",
			},
		},
	}

	t.Run("NoBanners", func(t *testing.T) {
		// 清理测试前的环境
		cleanupCache(t, util.Android)
		cleanupDBData(t, util.Android, now)

		p := ExtraBannerProvider{
			Equipment:   &util.Equipment{OS: util.Android},
			NowTime:     now,
			UserID:      1,
			UserContext: mrpc.UserContext{},
		}
		banners, err := p.listBanners()
		require.NoError(t, err)
		assert.Len(t, banners, 0)
	})

	t.Run("ShouldReturnBannersFromCache", func(t *testing.T) {
		// 清理测试前的环境
		cleanupCache(t, util.Android)
		cleanupDBData(t, util.Android, now)

		createLRUCache(t, util.Android, defaultTestBanners)

		p := ExtraBannerProvider{
			Equipment:   &util.Equipment{OS: util.Android},
			NowTime:     now,
			UserID:      1,
			UserContext: mrpc.UserContext{},
		}
		banners, err := p.listBanners()
		require.NoError(t, err)
		assert.Equal(t, defaultTestBanners, banners)
	})

	t.Run("ShouldReturnOnlineBannersFromDB", func(t *testing.T) {
		// 清理测试前的环境
		cleanupCache(t, util.Android)
		cleanupDBData(t, util.Android, now)

		previousBanners := ExtraBannerMap{
			ExtraBannerModuleID2: {
				{
					URL:   "https://test.com/bbb?foo=bar",
					Cover: "https://test.com/bbb?foo=bar",
				},
			},
		}
		nextBanners := ExtraBannerMap{
			ExtraBannerModuleID3: {
				{
					URL:   "https://test.com/ccc?foo=bar",
					Cover: "https://test.com/ccc?foo=bar",
				},
			},
		}
		createDBData(t, util.Android, previousBanners, now.Add(-time.Hour*24), now)
		createDBData(t, util.Android, defaultTestBanners, now, now.Add(time.Hour*24))
		createDBData(t, util.Android, nextBanners, now.Add(time.Hour*24), now.Add(time.Hour*48))

		p := ExtraBannerProvider{
			Equipment:   &util.Equipment{OS: util.Android},
			NowTime:     now,
			UserID:      1,
			UserContext: mrpc.UserContext{},
		}
		banners, err := p.listBanners()
		require.NoError(t, err)
		assert.Len(t, banners, 1)
		banner, ok := banners[ExtraBannerModuleID1]
		assert.True(t, ok)
		assert.Len(t, banner, 1)
	})

	t.Run("CombineBanners", func(t *testing.T) {
		// 清理测试前的环境
		cleanupCache(t, util.Android)
		cleanupDBData(t, util.Android, now)

		p := ExtraBannerProvider{
			Equipment:   &util.Equipment{OS: util.Android},
			NowTime:     now,
			UserID:      1,
			UserContext: mrpc.UserContext{},
		}

		liveBanners := ExtraBannerMap{
			ExtraBannerModuleID1: {
				{
					ModuleID: ExtraBannerModuleID1,
					Cover:    "https://live.com/cover1",
					URL:      "https://live.com/1",
				},
			},
			ExtraBannerModuleID3: {
				{
					ModuleID: ExtraBannerModuleID3,
					Cover:    "https://live.com/cover3",
					URL:      "https://live.com/3",
				},
			},
		}

		aodBanners := ExtraBannerMap{
			ExtraBannerModuleID1: {
				{
					ModuleID: ExtraBannerModuleID1,
					Cover:    "https://aod.com/cover1",
					URL:      "https://aod.com/1",
				},
			},
			ExtraBannerModuleID2: {
				{
					ModuleID: ExtraBannerModuleID2,
					Cover:    "https://aod.com/cover2",
					URL:      "https://aod.com/2",
				},
			},
		}

		result := p.combineBanners(liveBanners, aodBanners)
		assert.Len(t, result, 3)

		// 验证 moduleID 1 的 banner，直播 banner 应该在前面
		assert.Len(t, result[ExtraBannerModuleID1], 2)
		assert.Equal(t, "https://live.com/1", result[ExtraBannerModuleID1][0].URL)
		assert.Equal(t, "https://aod.com/1", result[ExtraBannerModuleID1][1].URL)

		// 验证 moduleID 2 的 banner，只有 aod banner
		assert.Len(t, result[ExtraBannerModuleID2], 1)
		assert.Equal(t, "https://aod.com/2", result[ExtraBannerModuleID2][0].URL)

		// 验证 moduleID 3 的 banner，只有直播 banner
		assert.Len(t, result[ExtraBannerModuleID3], 1)
		assert.Equal(t, "https://live.com/3", result[ExtraBannerModuleID3][0].URL)
	})
}

func TestExtraBannerProvider_GetBanner(t *testing.T) {
	// 设置空的直播通栏响应
	liveapi.SetMockGetLiveExtraBanners(t, liveapi.ExtraBannersResponse{}, nil)

	now := util.TimeNow()
	defaultTestBanners := ExtraBannerMap{
		ExtraBannerModuleID1: {
			{
				URL:   "https://test.com/aaa?foo=bar",
				Cover: "https://test.com/aaa?foo=bar",
			},
		},
	}

	t.Run("NoBannerForModuleID", func(t *testing.T) {
		// 清理测试前的缓存数据
		cleanupCache(t, util.Android)

		// 清理测试前的数据库数据
		cleanupDBData(t, util.Android, now)

		p := ExtraBannerProvider{
			Equipment:   &util.Equipment{OS: util.Android},
			NowTime:     now,
			UserID:      1,
			UserContext: mrpc.UserContext{},
		}
		banner, err := p.GetBanner(ExtraBannerModuleID1)
		require.NoError(t, err)
		assert.Nil(t, banner)
	})

	t.Run("GetBannerFromCache", func(t *testing.T) {
		// 清理测试前的环境
		cleanupCache(t, util.Android)
		cleanupDBData(t, util.Android, now)

		createLRUCache(t, util.Android, defaultTestBanners)

		p := ExtraBannerProvider{
			Equipment:   &util.Equipment{OS: util.Android},
			NowTime:     now,
			UserID:      1,
			UserContext: mrpc.UserContext{},
		}
		banner, err := p.GetBanner(ExtraBannerModuleID1)
		require.NoError(t, err)
		assert.NotNil(t, banner)
		assert.Len(t, banner, 1)
		assert.Equal(t, "https://test.com/aaa?foo=bar", banner[0].URL)
		assert.Equal(t, "https://test.com/aaa?foo=bar", banner[0].Pic)
	})

	t.Run("GetBannerFromDB", func(t *testing.T) {
		// 清理测试前的环境
		cleanupCache(t, util.Android)
		cleanupDBData(t, util.Android, now)

		previousBanners := ExtraBannerMap{
			ExtraBannerModuleID2: {
				{
					URL:   "https://test.com/bbb?foo=bar",
					Cover: "https://test.com/bbb?foo=bar",
				},
			},
		}
		nextBanners := ExtraBannerMap{
			ExtraBannerModuleID3: {
				{
					URL:   "https://test.com/ccc?foo=bar",
					Cover: "https://test.com/ccc?foo=bar",
				},
			},
		}
		createDBData(t, util.Android, previousBanners, now.Add(-time.Hour*24), now)
		createDBData(t, util.Android, defaultTestBanners, now, now.Add(time.Hour*24))
		createDBData(t, util.Android, nextBanners, now.Add(time.Hour*24), now.Add(time.Hour*48))

		p := ExtraBannerProvider{
			Equipment:   &util.Equipment{OS: util.Android},
			NowTime:     now,
			UserID:      1,
			UserContext: mrpc.UserContext{},
		}
		banner, err := p.GetBanner(ExtraBannerModuleID1)
		require.NoError(t, err)
		assert.NotNil(t, banner)
		assert.Len(t, banner, 1)
		assert.Equal(t, "https://test.com/aaa?foo=bar", banner[0].URL)
		assert.Equal(t, "https://test.com/aaa?foo=bar", banner[0].Pic)
	})
}

func createLRUCache(t *testing.T, platform util.Platform, banners ExtraBannerMap) {
	bannersJSON, err := json.Marshal(banners)
	require.NoError(t, err)
	key := keys.KeyAppHomepageExtraBanner1.Format(int(platform))
	t.Cleanup(func() {
		require.NoError(t, service.LRURedis.Del(key).Err())
	})
	require.NoError(t, service.LRURedis.Set(key, string(bannersJSON), 10*time.Minute).Err())
}

func createDBData(t *testing.T, platform util.Platform, banners ExtraBannerMap, startTime, endTime time.Time) {
	ids := make([]int64, 0, len(banners))
	t.Cleanup(func() {
		require.NoError(t, mre.MRecommendedElement{}.DB().Delete(mre.MRecommendedElement{}, "id IN (?)", ids).Error)
	})
	for position, banner := range banners {
		for i, item := range banner {
			element := mre.MRecommendedElement{
				Cover:      item.Cover,
				URL:        item.URL,
				Client:     int(platform),
				ModuleType: mre.ModuleTypeExtraBanner,
				ModuleID:   int64(position),
				Sort:       int64(i),
				StartTime:  startTime.Unix(),
				EndTime:    endTime.Unix(),
				Archive:    mre.ArchiveOnline,
			}
			require.NoError(t, element.DB().Save(&element).Error)
			ids = append(ids, element.ID)
		}
	}
}

func cleanupCache(t *testing.T, platform util.Platform) {
	key := keys.KeyAppHomepageExtraBanner1.Format(int(platform))
	err := service.LRURedis.Del(key).Err()
	require.NoError(t, err)
}

func cleanupDBData(t *testing.T, platform util.Platform, now time.Time) {
	db := mre.MRecommendedElement{}.DB()
	err := db.
		Where(map[string]any{
			"client":      platform,
			"module_type": mre.ModuleTypeExtraBanner,
		}).
		Where("module_id IN (?)", ExtraBannerModuleIDs).
		Where("start_time <= ? AND end_time > ?", now.Unix(), now.Unix()).
		Delete(&mre.MRecommendedElement{}).Error
	require.NoError(t, err)
}
