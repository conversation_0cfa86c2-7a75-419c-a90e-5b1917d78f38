package homefeed

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// MockRecommender 模拟推荐器
type MockRecommender struct {
	timeout time.Duration
}

// NewMockRecommender 创建模拟推荐器
func NewMockRecommender() *MockRecommender {
	return &MockRecommender{
		timeout: 15 * time.Second,
	}
}

// Timeout 获取超时时间
func (m *MockRecommender) Timeout() time.Duration {
	return m.timeout
}

// Recommend 获取推荐结果
func (m *MockRecommender) Recommend(params tianma.RecommendParams) (*tianma.RecommendResult, error) {
	// 从 Redis 中获取结果
	redisData, err := service.Redis.Get(keys.KeyAppHomeFeedRecommendMock0.Format()).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, fmt.Errorf("从 Redis 获取模拟数据失败: %w", err)
	}

	if serviceredis.IsRedisNil(err) || redisData == "" {
		// 如果 Redis 中没有数据，返回空结果
		return &tianma.RecommendResult{
			Code: tianma.CodeNoResult,
			Data: []tianma.RecommendItem{},
		}, nil
	}

	// 解析 Redis 中的数据
	var result tianma.RecommendResult
	if err := json.Unmarshal([]byte(redisData), &result); err != nil {
		return nil, fmt.Errorf("解析 Redis 模拟数据失败: %w", err)
	}

	// 根据 display_id 和 request_cnt 截断数据
	startIdx := max((params.Page-1)*params.RequestCnt, 0)

	// 如果起始索引超出范围，返回无结果
	if int64(len(result.Data)) <= startIdx {
		result.Code = tianma.CodeNoResult
		result.Data = []tianma.RecommendItem{}
		result.ExtraCards = []tianma.ExtraCards{}
		return &result, nil
	}

	endIdx := startIdx + params.RequestCnt

	// 如果结束索引超出范围，调整为数组长度
	if int64(len(result.Data)) < endIdx {
		endIdx = int64(len(result.Data))
	}

	result.ExtraCards = []tianma.ExtraCards{
		{
			Type:     "drama",
			Position: 5,
		},
		{
			Type:     "drama",
			Position: 11,
		},
	}

	// 截取对应范围的数据
	result.Data = result.Data[startIdx:endIdx]

	// 如果还有更多数据，设置状态码为正常
	if int64(len(result.Data)) > 0 {
		result.Code = tianma.CodeOK
	} else {
		result.Code = tianma.CodeItemReqCntNotEnough
	}

	return &result, nil
}

// GetRecommenderForEnv 根据环境获取合适的推荐器
func GetRecommenderForEnv() recommender {
	if goutil.IsProdEnv() {
		return tianma.NewClient()
	}
	return NewMockRecommender()
}

// recommender 推荐器接口
type recommender interface {
	Recommend(params tianma.RecommendParams) (*tianma.RecommendResult, error)
	Timeout() time.Duration
}
