package homefeed

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/appapi"
	"github.com/MiaoSiLa/missevan-main/service/keys"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func createMarkerString(displayID, customCardIDX, extraBannerIDX int64) string {
	marker := marker{
		RefreshNum:     displayID,
		CustomCardIDX:  int(customCardIDX),
		ExtraBannerIDX: int(extraBannerIDX),
	}
	markerJSON, _ := json.Marshal(marker)
	return string(markerJSON)
}

func createTestContext(query string) *handler.Context {
	return handler.NewTestContext("GET", "/x/recommend/home-feed?"+query, true, nil)
}

func TestNewHomeFeedParams(t *testing.T) {
	tests := []struct {
		name     string
		query    string
		expected *params
		wantErr  bool
		errType  error
	}{
		{
			name:  "ValidParams",
			query: "refresh_type=0&feed_type=1",
			expected: &params{
				RefreshType: 0,
				FeedType:    1,
				Network:     0,
				marker: &marker{
					RefreshNum:     1,
					CustomCardIDX:  0,
					ExtraBannerIDX: 0,
				},
			},
			wantErr: false,
		},
		{
			name:  "ValidParamsWithMarker",
			query: "refresh_type=0&feed_type=1&marker=" + createMarkerString(1, 2, 3),
			expected: &params{
				RefreshType: 0,
				FeedType:    1,
				Network:     0,
				Marker:      createMarkerString(1, 2, 3),
				marker: &marker{
					RefreshNum:     2,
					CustomCardIDX:  2,
					ExtraBannerIDX: 3,
				},
			},
			wantErr: false,
		},
		{
			name:    "InvalidRefreshType",
			query:   "refresh_type=-1&feed_type=1",
			wantErr: true,
			errType: actionerrors.ErrParams,
		},
		{
			name:    "InvalidFeedType",
			query:   "refresh_type=0&feed_type=-1",
			wantErr: true,
			errType: actionerrors.ErrParams,
		},
		{
			name:    "InvalidMarker",
			query:   "refresh_type=0&feed_type=1&marker=invalid-json",
			wantErr: true,
			errType: actionerrors.ErrParams,
		},
		{
			name:    "BindError",
			query:   "refresh_type=abc&feed_type=1", // 非整数值将导致 Bind 失败
			wantErr: true,
			errType: actionerrors.ErrParams,
		},
		{
			name:    "InvalidMarkerValues",
			query:   "refresh_type=0&feed_type=1&marker=" + createMarkerString(-1, -2, -1), // 解析后 RefreshNum=0, CustomCardIDX=-1 会导致验证失败
			wantErr: true,
			errType: actionerrors.ErrParams,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createTestContext(tt.query)

			params, err := newParams(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != nil {
					assert.Equal(t, tt.errType, err)
				}
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, params)

			if tt.expected != nil {
				assert.Equal(t, tt.expected.RefreshType, params.RefreshType)
				assert.Equal(t, tt.expected.FeedType, params.FeedType)
				assert.Equal(t, tt.expected.Network, params.Network)

				if tt.expected.marker != nil {
					assert.Equal(t, tt.expected.marker.RefreshNum, params.marker.RefreshNum)
					assert.Equal(t, tt.expected.marker.CustomCardIDX, params.marker.CustomCardIDX)
					assert.Equal(t, tt.expected.marker.ExtraBannerIDX, params.marker.ExtraBannerIDX)
				}
			}
		})
	}
}

func TestParams_GetIPInfo(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func(ctx goutil.UserContext, ip string, opts ...goclient.GetIPOptions) (goclient.IPInfo, error)
		expected goclient.IPInfo
	}{
		{
			name: "SuccessGetIPInfo",
			mockFunc: func(ctx goutil.UserContext, ip string, opts ...goclient.GetIPOptions) (goclient.IPInfo, error) {
				return goclient.IPInfo{
					CityName:    "测试城市",
					RegionName:  "测试省份",
					CountryCode: "CN",
					CountryName: "测试国家",
					ISP:         "测试 ISP",
				}, nil
			},
			expected: goclient.IPInfo{
				CityName:    "测试城市",
				RegionName:  "测试省份",
				CountryCode: "CN",
				CountryName: "测试国家",
				ISP:         "测试 ISP",
			},
		},
		{
			name: "FailedGetIPInfo",
			mockFunc: func(ctx goutil.UserContext, ip string, opts ...goclient.GetIPOptions) (goclient.IPInfo, error) {
				return goclient.IPInfo{}, fmt.Errorf("测试错误")
			},
			expected: goclient.IPInfo{
				CityName:    "",
				RegionName:  "",
				CountryCode: "",
				CountryName: "",
				ISP:         "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createTestContext("refresh_type=0&feed_type=1")

			p := &params{
				c:             ctx,
				getIPInfoFunc: tt.mockFunc,
			}

			info := p.getIPInfo()

			assert.Equal(t, tt.expected.CityName, info.CityName)
			assert.Equal(t, tt.expected.RegionName, info.RegionName)
			assert.Equal(t, tt.expected.CountryCode, info.CountryCode)
			assert.Equal(t, tt.expected.CountryName, info.CountryName)
			assert.Equal(t, tt.expected.ISP, info.ISP)
		})
	}
}

func TestParams_GetTianmaNetwork(t *testing.T) {
	tests := []struct {
		name     string
		network  int
		expected tianma.Network
	}{
		{
			name:     "WifiNetwork",
			network:  NetworkWifi,
			expected: tianma.NetworkWifi,
		},
		{
			name:     "CellularNetwork",
			network:  NetworkCellular,
			expected: tianma.NetworkCellular,
		},
		{
			name:     "OtherNetwork",
			network:  NetworkOther,
			expected: tianma.NetworkOtherNet,
		},
		{
			name:     "OfflineNetwork",
			network:  NetworkOffline,
			expected: tianma.UnknownStringValue,
		},
		{
			name:     "UnknownNetwork",
			network:  NetworkUnknown,
			expected: tianma.UnknownStringValue,
		},
		{
			name:     "InvalidNetwork",
			network:  99, // 一个不存在的网络类型
			expected: tianma.UnknownStringValue,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &params{
				Network: tt.network,
			}

			network := p.getTianmaNetwork()

			assert.Equal(t, tt.expected, network)
		})
	}
}

func TestParams_GetPlatform(t *testing.T) {
	tests := []struct {
		name           string
		os             goutil.Platform
		expectedResult tianma.Platform
	}{
		{
			name:           "AndroidPlatform",
			os:             goutil.Android,
			expectedResult: tianma.PlatformAndroid,
		},
		{
			name:           "IOSPlatform",
			os:             goutil.IOS,
			expectedResult: tianma.PlatformIOS,
		},
		{
			name:           "WebPlatform",
			os:             goutil.Web,
			expectedResult: tianma.PlatformWeb,
		},
		{
			name:           "MobileWebPlatform",
			os:             goutil.MobileWeb,
			expectedResult: tianma.PlatformMobileWeb,
		},
		{
			name:           "WindowsPlatform",
			os:             goutil.Windows,
			expectedResult: tianma.PlatformWindows,
		},
		{
			name:           "HarmonyOSPlatform",
			os:             goutil.HarmonyOS,
			expectedResult: tianma.PlatformHarmonyOS,
		},
		{
			name:           "UnknownPlatform",
			os:             99, // 未知平台
			expectedResult: tianma.UnknownStringValue,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createTestContext("refresh_type=0&feed_type=1")

			// 设置操作系统
			ctx.Equip().OS = tt.os

			p := &params{
				c: ctx,
			}

			platform := p.getPlatform()

			assert.Equal(t, tt.expectedResult, platform)
		})
	}
}

func TestParams_GetChannel(t *testing.T) {
	tests := []struct {
		name          string
		channelHeader string
		expected      string
	}{
		{
			name:          "ChannelExists",
			channelHeader: "test-channel",
			expected:      "test-channel",
		},
		{
			name:          "EmptyChannel",
			channelHeader: "",
			expected:      "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createTestContext("refresh_type=0&feed_type=1")

			// 设置 channel header
			ctx.Request().Header.Set("channel", tt.channelHeader)

			p := &params{
				c: ctx,
			}

			channel := p.getChannel()

			assert.Equal(t, tt.expected, channel)
		})
	}
}

func TestParams_GetDeviceModel(t *testing.T) {
	tests := []struct {
		name        string
		deviceModel string
		expected    string
	}{
		{
			name:        "ModelExists",
			deviceModel: "iPhone 13",
			expected:    "iPhone 13",
		},
		{
			name:        "EmptyModel",
			deviceModel: "",
			expected:    "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试上下文
			ctx := createTestContext("refresh_type=0&feed_type=1")

			// 设置设备模型
			ctx.Equip().DeviceModel = tt.deviceModel

			p := &params{
				c: ctx,
			}

			model := p.getDeviceModel()

			assert.Equal(t, tt.expected, model)
		})
	}
}

func TestParams_GetAppVersion(t *testing.T) {
	tests := []struct {
		name       string
		appVersion string
		expected   string
	}{
		{
			name:       "VersionExists",
			appVersion: "6.13.0",
			expected:   "6.13.0",
		},
		{
			name:       "EmptyVersion",
			appVersion: "",
			expected:   "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createTestContext("refresh_type=0&feed_type=1")

			// 设置应用版本
			ctx.Equip().AppVersion = tt.appVersion

			p := &params{
				c: ctx,
			}

			version := p.getAppVersion()

			assert.Equal(t, tt.expected, version)
		})
	}
}

func TestParams_GeneratePagination(t *testing.T) {
	tests := []struct {
		name            string
		feedsCode       int32
		marker          *marker
		expectedHasMore bool
		expectedMarker  string
		expectErr       bool
		expectErrType   string
	}{
		{
			name:            "HasMoreDataWithValidMarker",
			feedsCode:       tianma.CodeOK,
			marker:          &marker{RefreshNum: 100, CustomCardIDX: 5, ExtraBannerIDX: 2},
			expectedHasMore: true,
			expectedMarker:  `{"page":0,"refresh_num":100,"custom_card_idx":5,"extra_banner_idx":2,"extra_card_type_idx":0}`,
			expectErr:       false,
		},
		{
			name:            "NoMoreData",
			feedsCode:       999, // 非 tianma.CodeOK 的值
			marker:          &marker{RefreshNum: 100, CustomCardIDX: 5, ExtraBannerIDX: 2},
			expectedHasMore: false,
			expectedMarker:  "",
			expectErr:       false,
		},
		{
			name:            "NilMarker",
			feedsCode:       tianma.CodeOK,
			marker:          nil,
			expectedHasMore: false,
			expectedMarker:  "",
			expectErr:       true,
			expectErrType:   "marker 为 nil",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feeds := &tianma.RecommendResult{
				Code: tt.feedsCode,
			}

			p := &params{
				marker: tt.marker,
			}

			hasMore, marker, err := p.generatePagination(feeds)

			assert.Equal(t, tt.expectedHasMore, hasMore)

			if tt.expectErr {
				assert.Error(t, err)
				if tt.expectErrType != "" {
					assert.Contains(t, err.Error(), tt.expectErrType)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedMarker, marker)
			}
		})
	}
}

type mockRecommender struct {
	recommendFunc func(params tianma.RecommendParams) (*tianma.RecommendResult, error)
}

func (m *mockRecommender) Recommend(params tianma.RecommendParams) (*tianma.RecommendResult, error) {
	return m.recommendFunc(params)
}

func (m *mockRecommender) Timeout() time.Duration {
	return 5 * time.Second
}

func TestRequestAlgorithms(t *testing.T) {
	ctx := createTestContext("refresh_type=0&feed_type=1")

	ctx.Equip().BUVID = "test-buvid"
	ctx.Equip().DeviceModel = "iPhone 13"
	ctx.Equip().OS = goutil.IOS
	ctx.Equip().AppVersion = "6.13.0"

	ctx.Request().Header.Set("channel", "app-store")

	p := &params{
		RefreshType: 0,
		FeedType:    1,
		Network:     NetworkWifi,
		PersonaID:   2,
		marker: &marker{
			RefreshNum:     1,
			CustomCardIDX:  0,
			ExtraBannerIDX: 0,
		},
		c: ctx,
		getIPInfoFunc: func(ctx goutil.UserContext, ip string, opts ...goclient.GetIPOptions) (goclient.IPInfo, error) {
			return goclient.IPInfo{
				CityName:    "北京",
				RegionName:  "北京",
				CountryCode: "CN",
				CountryName: "中国",
			}, nil
		},
	}

	expectedResult := &tianma.RecommendResult{
		Code: tianma.CodeOK,
		Data: []tianma.RecommendItem{
			{
				ID:      123,
				Goto:    tianma.GotoDrama,
				TrackID: "track-123",
			},
			{
				ID:      456,
				Goto:    tianma.GotoLive,
				TrackID: "track-456",
			},
		},
	}

	var capturedParams tianma.RecommendParams
	p.recommender = &mockRecommender{
		recommendFunc: func(params tianma.RecommendParams) (*tianma.RecommendResult, error) {
			capturedParams = params
			return expectedResult, nil
		},
	}
	p.timeout = 5 * time.Second

	result, err := requestAlgorithms(p)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	assert.Equal(t, tianma.RecommendCmdHomeFeeds, capturedParams.Cmd)
	assert.Equal(t, int64(12), capturedParams.MID)
	assert.Equal(t, "test-buvid", capturedParams.Buvid)
	assert.Equal(t, int64(10), capturedParams.RequestCnt) // 期望是 requestCount 常量的值 10
	assert.Equal(t, int64(5000), capturedParams.Timeout)
	assert.Equal(t, int64(1), capturedParams.DisplayID)
	assert.Equal(t, tianma.FreshType(0), capturedParams.FreshType)
	assert.Equal(t, tianma.NetworkWifi, capturedParams.Network)
	assert.Equal(t, "中国", capturedParams.Country)
	assert.Equal(t, int32(-1), capturedParams.Sex)
	assert.Equal(t, int64(2), capturedParams.Persona)
	assert.Equal(t, "app-store", capturedParams.Chid)
	assert.Equal(t, "iPhone 13", capturedParams.Model)
	assert.Equal(t, tianma.PlatformIOS, capturedParams.Platform)
	assert.Equal(t, "6.13.0", capturedParams.Version)
	assert.Equal(t, "北京", capturedParams.Province)
	assert.Equal(t, "北京", capturedParams.City)
}

func TestParams_GetFavorsInfo(t *testing.T) {
	tests := []struct {
		name     string
		testResp *appapi.GetMyFavorsResponse
		wantErr  bool
	}{
		{
			name: "SuccessGetFavorsInfo",
			testResp: &appapi.GetMyFavorsResponse{
				VideoCard: map[string]appapi.VideoCard{
					"1": {
						Type:    1,
						ID:      21,
						Title:   "视频标题",
						Cover:   "http://static-test.maoercdn.com/test/test.jpg",
						SoundID: 2333,
					},
				},
				Modules: []appapi.Module{
					{
						ModuleID: 9,
						Title:    "音频模块标题",
						Type:     3,
					},
				},
				Marker: "233:1,234:1",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := createTestContext("refresh_type=0&feed_type=1")
			userID := ctx.UserID()

			// 准备测试数据写入Redis缓存
			key := keys.KeyAppHomepageFavors1.Format(userID)

			// 清理测试前后的缓存
			require.NoError(t, service.LRURedis.Del(key).Err())
			t.Cleanup(func() {
				require.NoError(t, service.LRURedis.Del(key).Err())
			})

			// 只有成功用例才需要写入缓存
			if !tt.wantErr && tt.testResp != nil {
				// 将测试数据序列化并写入缓存
				respBytes, err := json.Marshal(tt.testResp)
				require.NoError(t, err)
				require.NoError(t, service.LRURedis.Set(key, respBytes, 10*time.Minute).Err())
			}

			p := &params{
				c:         ctx,
				PersonaID: 2,
			}

			resp, err := p.getFavorsInfo()

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, resp)

			assert.Equal(t, tt.testResp.VideoCard["1"].ID, resp.VideoCard["1"].ID)
			assert.Equal(t, tt.testResp.VideoCard["1"].Title, resp.VideoCard["1"].Title)
			assert.Equal(t, tt.testResp.VideoCard["1"].Cover, resp.VideoCard["1"].Cover)
			assert.Equal(t, tt.testResp.VideoCard["1"].SoundID, resp.VideoCard["1"].SoundID)

			assert.Equal(t, tt.testResp.Modules[0].ModuleID, resp.Modules[0].ModuleID)
			assert.Equal(t, tt.testResp.Modules[0].Title, resp.Modules[0].Title)
			assert.Equal(t, tt.testResp.Modules[0].Type, resp.Modules[0].Type)

			assert.Equal(t, tt.testResp.Marker, resp.Marker)
		})
	}
}
