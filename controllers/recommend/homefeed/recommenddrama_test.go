package homefeed

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, iprTypeQualityYes)
	assert.EqualValues(2, iprTypeQualityNo)

	assert.EqualValues(1, tagStyleRedOnFoundation)
	assert.EqualValues(2, tagStyleBlackOnGray)
}

func TestDramaCardInfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(DramaCardInfo{}, "element_type", "drama_id", "sound_id", "title", "cover_url", "cover_color", "intro",
		"ipr_type", "cvs", "view_count", "newest", "integrity", "tags", "corner_mark", "pay_type")

	kc.Check(Tag{}, "style", "text", "url")

	kc.CheckOmitEmpty(DramaCardInfo{}, "corner_mark")
	kc.CheckOmitEmpty(Tag{}, "url")
}

func TestDramaCardParams_getSoundIDDramaCardMap(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{52355, 52356}, nil
	})
	defer cancel()

	param := dramaCardParams{
		SoundIDs: []int64{1217771, 1217772, 1217773, 1217774, 1000000},
		UserID:   3457181,
		Uc:       mrpc.UserContext{},
	}
	soundIDDramaCardMap, err := param.getSoundIDDramaCardMap()
	require.NoError(err)
	require.NotEmpty(soundIDDramaCardMap)
	require.Len(soundIDDramaCardMap, 4)
	require.NotNil(soundIDDramaCardMap[1217771])
	assert.EqualValues(52355, soundIDDramaCardMap[1217771].DramaID)
	assert.EqualValues(1217771, soundIDDramaCardMap[1217771].SoundID)
	assert.Equal("剧集名称（审核通过）", soundIDDramaCardMap[1217771].Title)
	assert.NotEmpty(soundIDDramaCardMap[1217771].CoverURL)
	assert.Equal(12434877, soundIDDramaCardMap[1217771].CoverColor)
	assert.Equal("测试一句话简介 52355", soundIDDramaCardMap[1217771].Intro)
	assert.Equal(iprTypeQualityYes, soundIDDramaCardMap[1217771].IPRType)
	assert.EqualValues(50, soundIDDramaCardMap[1217771].ViewCount)
	assert.Equal("", soundIDDramaCardMap[1217771].Newest)
	assert.Nil(soundIDDramaCardMap[1217771].CornerMark)

	// 验证音频所属剧集相同时
	require.NotNil(soundIDDramaCardMap[1217772])
	assert.Equal(soundIDDramaCardMap[1217771].DramaID, soundIDDramaCardMap[1217772].DramaID)

	// 验证有剧集角标时
	require.NotNil(soundIDDramaCardMap[1217773])
	assert.EqualValues(52356, soundIDDramaCardMap[1217773].DramaID)
	assert.EqualValues(1217773, soundIDDramaCardMap[1217773].SoundID)
	assert.Equal("测试一句话简介 52356", soundIDDramaCardMap[1217773].Intro)
	assert.Equal(iprTypeQualityYes, soundIDDramaCardMap[1217773].IPRType)
	assert.NotEmpty(soundIDDramaCardMap[1217773].CornerMark)

	require.NotNil(soundIDDramaCardMap[1217774])
	assert.EqualValues(52357, soundIDDramaCardMap[1217774].DramaID)
	assert.EqualValues(1217774, soundIDDramaCardMap[1217774].SoundID)
	// 验证没有一句话简介时
	assert.Equal("剧集简介", soundIDDramaCardMap[1217774].Intro)
	// 验证剧集为非精品版权剧集时不返回剧集声优信息
	assert.Equal(iprTypeQualityNo, soundIDDramaCardMap[1217774].IPRType)
	assert.Empty(soundIDDramaCardMap[1217774].Cvs)

	// 测试剧集不存在
	require.Nil(soundIDDramaCardMap[1000000])
}

func TestGetQualityDramaCvNames(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	qualityDramaIDCvNamesMap, err := getQualityDramaCvNames([]int64{52355, 52356, 52357, 10000})
	require.NoError(err)
	require.NotEmpty(qualityDramaIDCvNamesMap)
	require.Len(qualityDramaIDCvNamesMap, 3)
	require.NotEmpty(qualityDramaIDCvNamesMap[52355])
	assert.Len(qualityDramaIDCvNamesMap[52355], 1)
	assert.Equal("测试声优 5", qualityDramaIDCvNamesMap[52355][0])

	require.NotNil(qualityDramaIDCvNamesMap[52356])
	assert.Len(qualityDramaIDCvNamesMap[52356], 2)
	assert.Equal("测试声优 5", qualityDramaIDCvNamesMap[52356][0])
	assert.Equal("测试声优 6", qualityDramaIDCvNamesMap[52356][1])

	require.NotNil(qualityDramaIDCvNamesMap[52357])
	assert.Len(qualityDramaIDCvNamesMap[52357], 1)
	assert.Equal("测试声优 7", qualityDramaIDCvNamesMap[52357][0])

	// 测试剧集不存在
	require.Nil(qualityDramaIDCvNamesMap[10000])
}

func TestDramaCardParams_getDramaCardTags(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{52355, 52356}, nil
	})
	defer cancel()

	param := dramaCardParams{
		UserID:   3457181,
		Uc:       mrpc.UserContext{},
		dramaIDs: []int64{52355, 52356},
	}
	dramaIDTagsMap, err := param.getDramaCardTags()
	require.NoError(err)
	require.NotEmpty(dramaIDTagsMap)
	assert.Len(dramaIDTagsMap, 2)
}

func TestDramaCardParams_getDramaIDSpecialTagMap(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{52355, 52356}, nil
	})
	defer cancel()

	// 测试剧集 ID 为空时
	param := dramaCardParams{
		UserID:   3457181,
		Uc:       mrpc.UserContext{},
		dramaIDs: []int64{},
	}
	dramaIDSpecialTagMap := param.getDramaIDSpecialTagMap()
	require.Nil(dramaIDSpecialTagMap)

	// 测试有【我的追剧标签】时
	param.dramaIDs = []int64{52355, 52356, 52357}
	dramaIDSpecialTagMap = param.getDramaIDSpecialTagMap()
	require.NotEmpty(dramaIDSpecialTagMap)
	require.Len(dramaIDSpecialTagMap, 2)
	assert.NotEmpty(dramaIDSpecialTagMap[52355])
	assert.Equal(tagStyleRedOnFoundation, dramaIDSpecialTagMap[52355][0].Style)
	assert.EqualValues("我的追剧", dramaIDSpecialTagMap[52355][0].Text)
}

func TestDramaCardParams_processMyCatchUpTag(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{52355, 52356}, nil
	})
	defer cancel()

	// 测试剧集 ID 为空时
	param := dramaCardParams{
		UserID:   3457181,
		Uc:       mrpc.UserContext{},
		dramaIDs: []int64{},
	}
	err := param.processMyCatchUpTag()
	require.NoError(err)
	require.Nil(param.myCatchUpTagDramaIDTagMap)

	// 测试剧集 ID 包含我购买和订阅的剧集时
	param.dramaIDs = []int64{52355, 52356, 52357}
	err = param.processMyCatchUpTag()
	require.NoError(err)
	require.NotEmpty(param.myCatchUpTagDramaIDTagMap)
	require.Len(param.myCatchUpTagDramaIDTagMap, 2)
	assert.EqualValues(Tag{Style: tagStyleRedOnFoundation, Text: "我的追剧"}, param.myCatchUpTagDramaIDTagMap[52355])
	assert.EqualValues(Tag{Style: tagStyleRedOnFoundation, Text: "我的追剧"}, param.myCatchUpTagDramaIDTagMap[52356])

	// 测试剧集 ID 包含我购买和订阅的剧集的同 IPR ID 的剧集时
	param.dramaIDs = []int64{52358}
	err = param.processMyCatchUpTag()
	require.NotEmpty(param.myCatchUpTagDramaIDTagMap)
	require.Len(param.myCatchUpTagDramaIDTagMap, 1)
	assert.EqualValues(Tag{Style: tagStyleRedOnFoundation, Text: "我的追剧"}, param.myCatchUpTagDramaIDTagMap[52358])
}
