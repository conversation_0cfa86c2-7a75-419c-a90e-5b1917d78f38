package recommend

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/appapi"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestActionGetPopup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误 - 缺少所有参数
	c := handler.NewTestContext(http.MethodGet, "/x/recommend/get-popup", false, nil)
	// 设置 User-Agent 以通过设备类型检查
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	resp, message, err := ActionGetPopup(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Equal("", message)
	assert.Nil(resp)

	// 测试参数错误 - 缺少部分参数
	c = handler.NewTestContext(http.MethodGet, "/x/recommend/get-popup?channel=test_channel", false, nil)
	// 设置 User-Agent 以通过设备类型检查
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	resp, message, err = ActionGetPopup(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Equal("", message)
	assert.Nil(resp)

	// 设置模拟 RPC 响应
	mockResp := appapi.RecommendGetPopupResponse{
		PopupURL: "https://example.com/popup.png",
		OpenURL:  "https://example.com/target",
	}
	cancel := mrpc.SetMock(appapi.URIRecommendGetPopup, func(input interface{}) (interface{}, error) {
		return mockResp, nil
	})
	defer cancel()

	// 测试正常请求 - 使用 cookie 设置 buvid 和 equip_id
	c = handler.NewTestContext(
		http.MethodGet,
		"/x/recommend/get-popup?channel=test_channel",
		false,
		nil,
	)
	// 设置 User-Agent 以通过设备类型检查
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")

	// 设置 Cookie 来提供 buvid 和 equip_id
	buvidCookie := &http.Cookie{
		Name:  "buvid",
		Value: "test_buvid",
	}
	equipIDCookie := &http.Cookie{
		Name:  "equip_id",
		Value: "test_equip_id",
	}
	c.C.Request.AddCookie(buvidCookie)
	c.C.Request.AddCookie(equipIDCookie)

	resp, message, err = ActionGetPopup(c)
	require.NoError(err)
	assert.Equal("", message)
	require.NotNil(resp)

	popupResp, ok := resp.(*appapi.RecommendGetPopupResponse)
	require.True(ok)
	assert.Equal("https://example.com/popup.png", popupResp.PopupURL)
	assert.Equal("https://example.com/target", popupResp.OpenURL)

	// 测试没有弹窗和跳转链接的情况
	mockEmptyResp := appapi.RecommendGetPopupResponse{}
	cancel = mrpc.SetMock(appapi.URIRecommendGetPopup, func(input interface{}) (interface{}, error) {
		return mockEmptyResp, nil
	})
	defer cancel()

	c = handler.NewTestContext(
		http.MethodGet,
		"/x/recommend/get-popup?channel=test_channel",
		false,
		nil,
	)
	// 设置 User-Agent 以通过设备类型检查
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.8 (Android;8.0.0;honor FRD-DL00 HWFRD)")

	// 设置 Cookie 来提供 buvid 和 equip_id
	c.C.Request.AddCookie(buvidCookie)
	c.C.Request.AddCookie(equipIDCookie)

	resp, message, err = ActionGetPopup(c)
	require.NoError(err)
	assert.Equal("", message)
	require.NotNil(resp)

	emptyResp, ok := resp.(*appapi.RecommendGetPopupResponse)
	require.True(ok)
	assert.Equal("", emptyResp.PopupURL)
	assert.Equal("", emptyResp.OpenURL)
}
