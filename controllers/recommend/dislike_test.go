package recommend

import (
	"net/url"
	"testing"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/stretchr/testify/assert"
)

// 创建测试上下文
func createTestContext(method, path string, params map[string]string) *handler.Context {
	// 构造查询参数
	queryValues := url.Values{}
	for key, value := range params {
		queryValues.Add(key, value)
	}

	// 如果有参数，添加到 URL 中
	uri := path
	if len(params) > 0 {
		uri = path + "?" + queryValues.Encode()
	}

	// 创建测试上下文
	return handler.NewTestContext(method, uri, true, nil)
}

func TestActionDislike(t *testing.T) {
	// 测试用例
	tests := []struct {
		name        string
		params      map[string]string
		wantErr     bool
		expectedErr error
		wantMsg     string
	}{
		{
			name:        "NoParameters",
			params:      map[string]string{},
			wantErr:     true,
			expectedErr: actionerrors.ErrParams,
			wantMsg:     "",
		},
		{
			name:        "EmptyParameter",
			params:      map[string]string{"reason": ""},
			wantErr:     true,
			expectedErr: actionerrors.ErrParams,
			wantMsg:     "",
		},
		{
			name:        "ValidParameter",
			params:      map[string]string{"reason": `{"type":1,"drama_id":1}`},
			wantErr:     false,
			expectedErr: nil,
			wantMsg:     dislikeSuccessMsg,
		},
		{
			name:        "ComplexValidParameter",
			params:      map[string]string{"reason": `{"type":2,"sound_id":123,"user_id":456,"source":"homefeed"}`},
			wantErr:     false,
			expectedErr: nil,
			wantMsg:     dislikeSuccessMsg,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试上下文
			ctx := createTestContext("POST", "/x/recommend/dislike", tt.params)

			// 调用测试函数
			data, msg, err := ActionDislike(ctx)

			// 验证结果
			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErr != nil {
					assert.Equal(t, tt.expectedErr, err)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantMsg, msg)
				// data 应该为 nil，因为当前实现中 ActionDislike 总是返回 nil 作为 data
				assert.Nil(t, data)
			}
		})
	}
}
