package recommend

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service/appapi"
)

// ActionGetPopup 获取推荐弹窗和跳转链接
/**
 * @api {get} /x/recommend/get-popup 获取推荐弹窗和跳转链接
 *
 * @apiVersion 0.1.0
 * @apiName get-popup
 * @apiGroup /x/recommend
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "popup_url": "https://example.com/popup.png", // 推荐弹窗 URL，无推荐弹窗的情况下不含该参数
 *       "open_url": "https://example.com/target" // 跳转地址，无跳转地址时不含该参数
 *     }
 *   }
 *
 * @apiError {Number} code 错误码
 * @apiError {String} message 错误信息
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 40001,
 *     "message": "参数错误"
 *   }
 */
func ActionGetPopup(c *handler.Context) (handler.ActionResponse, string, error) {
	os := c.Equip().OS
	switch os {
	case util.Android, util.IOS, util.HarmonyOS:
		// PASS
	default:
		return nil, "", actionerrors.ErrParams
	}

	// 获取请求参数
	channel := c.Request().Header.Get("channel")
	buvid := c.BUVID()
	equipID := c.EquipID()

	if buvid == "" || equipID == "" {
		return nil, "", actionerrors.ErrParams
	}

	// 调用 RPC 服务
	resp, err := appapi.GetRecommendPopup(c.UserContext(), appapi.RecommendGetPopupParam{
		Channel: channel,
		Buvid:   buvid,
		EquipID: equipID,
	})
	if err != nil {
		return nil, "", err
	}

	return resp, "", nil
}
