package person

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler()
	checker := tutil.NewKeyChecker(t, tutil.Actions)

	assert.Equal("person", h.Name)
	checker.Check(h.Actions, "get-user-point")
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)
	h := HandlerV2()
	checker := tutil.NewKeyChecker(t, tutil.Actions)

	assert.Equal("person", h.Name)
	checker.Check(h.Actions, "get-user-point", "batch-follow", "get-avatar-frame", "homepage-info",
		"list-avatar-frame", "takeoff-avatar-frame", "wear-avatar-frame", "get-theme-skin", "theme-skin-detail",
		"wear-theme-skin", "takeoff-theme-skin", "task")
}
