package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

type setThemeSkinParams struct {
	ThemeSkinID int64 `form:"theme_skin_id" json:"theme_skin_id"` // 主题皮肤 ID

	userID         int64
	isVipThemeSkin bool
	isVipUser      bool
	themeSkin      *mthemeskin.MThemeSkin
	userThemeSkin  *muserthemeskin.MUserThemeSkin
}

type setThemeSkinResp struct {
	ThemeSkin *mthemeskin.UserThemeSkinInfo `json:"theme_skin"`
}

// ActionWearThemeSkin 装扮主题皮肤
/**
 * @api {post} /x/person/wear-theme-skin 装扮主题皮肤
 *
 * @apiVersion 0.1.0
 * @apiName wear-theme-skin
 * @apiGroup /x/person/
 *
 * @apiPermission user
 *
 * @apiParam {Number} theme_skin_id 主题皮肤 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "装扮成功",
 *     "data": {
 *       "theme_skin": {
 *         "id": 1,  // 当前使用的主题皮肤 ID
 *         "package_url": "https://static-test.maoercdn.com/app/themeskin/202412/12/tcu8461ccs2497829dbe9541b55bdc8d170256.zip"  // 主题皮肤压缩包地址
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null
 */
func ActionWearThemeSkin(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newSetThemeSkinParam(c, muserthemeskin.StatusWearing)
	if err != nil {
		return nil, "", err
	}
	if param.userThemeSkin == nil {
		param.userThemeSkin = &muserthemeskin.MUserThemeSkin{
			UserID:      param.userID,
			ThemeSkinID: param.ThemeSkinID,
		}
	}
	if param.userThemeSkin.Status != muserthemeskin.StatusWearing {
		// 装扮主题
		err = param.userThemeSkin.SetThemeSkin(muserthemeskin.StatusWearing)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
	}
	resp := setThemeSkinResp{
		ThemeSkin: &mthemeskin.UserThemeSkinInfo{
			ID:         param.ThemeSkinID,
			PackageURL: param.themeSkin.PackageURL,
		},
	}
	return resp, "装扮成功", nil
}

func newSetThemeSkinParam(c *handler.Context, action int) (*setThemeSkinParams, error) {
	param := new(setThemeSkinParams)
	err := c.Bind(param)
	if err != nil || param.ThemeSkinID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.themeSkin, err = mthemeskin.FindOne(param.ThemeSkinID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.themeSkin == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeUnknownError, "主题皮肤不存在")
	}
	param.isVipThemeSkin = param.themeSkin.Vip == mthemeskin.VipLimit
	param.userID = c.UserID()
	isVipUser, err := muservip.IsVip(param.userID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	param.isVipUser = isVipUser
	if param.isVipThemeSkin && !param.isVipUser {
		description := "装扮失败"
		if action == muserthemeskin.StatusTakeoff {
			description = "停用失败"
		}
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, description)
	}
	userThemeSkin, err := muserthemeskin.FindUserThemeSkin(param.userID, param.ThemeSkinID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	param.userThemeSkin = userThemeSkin
	return param, nil
}
