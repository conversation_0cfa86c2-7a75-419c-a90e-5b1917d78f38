package person

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

func TestActionThemeSkinDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/x/person/theme-skin-detail"
	c := handler.NewTestContext(http.MethodGet, api, false, nil)
	_, _, err := ActionThemeSkinDetail(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试主题皮肤不存在
	themeSkinID := int64(1999999)
	uri := fmt.Sprintf("%s?theme_skin_id=%d", api, themeSkinID)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	_, _, err = ActionThemeSkinDetail(c)
	assert.Equal(actionerrors.ErrNotFound(handler.CodeUnknownError, "主题皮肤不存在"), err)

	// 测试主题皮肤存在、用户未登录
	themeSkinID = 1
	uri = fmt.Sprintf("%s?theme_skin_id=%d", api, themeSkinID)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	data, msg, err := ActionThemeSkinDetail(c)
	require.NoError(err)
	assert.Equal("", msg)
	resp, ok := data.(themeSkinDetailResp)
	require.True(ok)
	expectResp := themeSkinDetailResp{
		ThemeSkin: themeSkinInfo{
			ID:    1,
			Name:  "椰子的假日",
			Intro: "和小猫一起，享受假日！",
			ImageURLList: []string{
				"https://static-test.maoercdn.com/themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.jpg",
				"https://static-test.maoercdn.com/themeskin/202412/25/2e1646155d2497829dbe9541b55bdc8d1lk942.jpg",
				"https://static-test.maoercdn.com/themeskin/202412/25/ye1646155d2497829dbe9541b55bdc88hb0943.jpg",
			},
			BgStartColor: "#FFA623",
			BgEndColor:   "#FFA629",
			Status:       muserthemeskin.StatusTakeoff,
		},
	}
	assert.Equal(expectResp, resp)

	// 测试主题皮肤存在、用户登录、用户非会员
	now := util.TimeNow().Unix()
	err = muservip.MUserVip{}.DB().Where("user_id = ?", 12).
		Update("end_time", now-1000).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	data, msg, err = ActionThemeSkinDetail(c)
	require.NoError(err)
	assert.Equal("", msg)
	resp, ok = data.(themeSkinDetailResp)
	require.True(ok)
	expectResp.User = &themeSkinUserInfo{
		IsVip: 0,
	}
	expectResp.ThemeSkin.Status = muserthemeskin.StatusNotOwned
	assert.Equal(expectResp, resp)

	// 测试主题皮肤存在、用户登录、用户为会员、未装扮该主题皮肤
	err = muservip.MUserVip{}.DB().Where("user_id = ?", 12).
		Update("end_time", now+100000).Error
	require.NoError(err)
	err = muserthemeskin.MUserThemeSkin{}.DB().Where("id = ?", 5).
		Update("status", muserthemeskin.StatusTakeoff).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	data, msg, err = ActionThemeSkinDetail(c)
	require.NoError(err)
	assert.Equal("", msg)
	resp, ok = data.(themeSkinDetailResp)
	require.True(ok)
	expectResp.User.IsVip = 1
	expectResp.ThemeSkin.Status = muserthemeskin.StatusTakeoff
	assert.Equal(expectResp, resp)

	// 测试主题皮肤存在、用户登录、用户为会员、装扮该主题皮肤
	err = muserthemeskin.MUserThemeSkin{}.DB().Delete("", "id = 5").Error
	require.NoError(err)
	userTS := muserthemeskin.MUserThemeSkin{
		ID:          5,
		UserID:      12,
		ThemeSkinID: themeSkinID,
		ExpireTime:  muserthemeskin.ExpireTimeForeverEffective,
		Status:      muserthemeskin.StatusWearing,
	}
	err = muserthemeskin.MUserThemeSkin{}.DB().Create(&userTS).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	data, msg, err = ActionThemeSkinDetail(c)
	require.NoError(err)
	assert.Equal("", msg)
	resp, ok = data.(themeSkinDetailResp)
	require.True(ok)
	expectResp.ThemeSkin.Status = muserthemeskin.StatusWearing
	assert.Equal(expectResp, resp)
}
