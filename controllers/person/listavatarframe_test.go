package person

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(listAvatarFrameResp{}, "data", "pagination", "current")
	kc.CheckOmitEmpty(listAvatarFrameResp{}, "current")
	kc.Check(listAvatarFrameItem{}, "id", "name", "intro", "avatar_frame_url", "icon_url", "expire_time", "status")
}

func TestActionListAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, "/x/person/list-avatar-frame?page_size=-2", true, nil)
	_, _, err := ActionListAvatarFrame(c)
	assert.EqualError(err, "参数错误")

	c = handler.NewTestContext(http.MethodGet, "/x/person/list-avatar-frame?page_size=2&marker=a", true, nil)
	_, _, err = ActionListAvatarFrame(c)
	assert.EqualError(err, "参数错误")

	// 测试获取用户头像框
	testAvatarFrameIDs := []int64{233, 234, 235, 236, 237, 238}
	avatarFrames := make([]mavatarframe.MAvatarFrame, 0, len(testAvatarFrameIDs))
	userAvatarFrames := make([]museravatarframemap.MUserAvatarFrameMap, 0, len(testAvatarFrameIDs))
	for i, avatarFrameID := range testAvatarFrameIDs {
		avatarFrames = append(avatarFrames, mavatarframe.MAvatarFrame{
			ID:    avatarFrameID,
			Name:  "测试头像框 " + strconv.Itoa(i),
			Intro: "测试头像框简介",
			Frame: "test://test/202309/27/test.png",
		})

		userAvatarFrame := museravatarframemap.MUserAvatarFrameMap{
			CreateTime:    util.TimeNow().Add(time.Hour * time.Duration(i)).Unix(),
			AvatarFrameID: avatarFrameID,
			UserID:        testUserID,
			ExpireTime:    util.TimeNow().Add(time.Hour).Unix(),
			Status:        museravatarframemap.StatusTakeoff,
		}
		if i == 1 {
			userAvatarFrame.ExpireTime = 0
			userAvatarFrame.Status = museravatarframemap.StatusWearing
		}
		if i == 2 {
			userAvatarFrame.ExpireTime = util.TimeNow().Add(-time.Hour).Unix()
		}
		if i == 3 {
			userAvatarFrame.ExpireTime = util.BeginningOfDay(util.TimeNow()).Unix()
		}
		if i == 4 || i == 5 {
			userAvatarFrame.Status = museravatarframemap.StatusTakeoff
		}
		userAvatarFrames = append(userAvatarFrames, userAvatarFrame)
	}
	require.NoError(avatarFrames[0].DB().Delete("", "id IN (?)", testAvatarFrameIDs).Error)
	require.NoError(servicedb.BatchInsert(service.MainDB, avatarFrames[0].TableName(), avatarFrames))
	require.NoError(userAvatarFrames[0].DB().Delete("", "user_id = ?", testUserID).Error)
	require.NoError(servicedb.BatchInsert(service.MainDB, userAvatarFrames[0].TableName(), userAvatarFrames))

	// 测试获取第一页数据，第一页数据中包含用户佩戴中的头像框
	c = handler.NewTestContext(http.MethodGet, "/x/person/list-avatar-frame?page_size=3", true, nil)
	res, _, err := ActionListAvatarFrame(c)
	require.NoError(err)
	data, ok := res.(listAvatarFrameResp)
	require.True(ok)
	assert.Equal("1", data.Pagination.Marker)
	assert.True(data.Pagination.HasMore)
	require.Len(data.Data, 3)
	assert.EqualValues(238, data.Data[0].ID)
	assert.Equal(museravatarframemap.StatusTakeoff, data.Data[0].Status)
	assert.EqualValues(237, data.Data[1].ID)
	assert.Equal(museravatarframemap.StatusTakeoff, data.Data[1].Status)
	assert.EqualValues(234, data.Data[2].ID)
	assert.Zero(data.Data[2].ExpireTime)
	require.NotNil(data.Current)
	assert.EqualValues(234, data.Current.ID)
	assert.Equal(museravatarframemap.StatusWearing, data.Current.Status)

	// 测试第一页数据中不包含用户佩戴中的头像框
	require.NoError(museravatarframemap.MUserAvatarFrameMap{}.DB().Where("avatar_frame_id = ?", 234).
		Update("status", museravatarframemap.StatusTakeoff).Error)
	require.NoError(museravatarframemap.MUserAvatarFrameMap{}.DB().Where("avatar_frame_id = ?", 233).
		Update("status", museravatarframemap.StatusWearing).Error)
	c = handler.NewTestContext(http.MethodGet, "/x/person/list-avatar-frame?page_size=3", true, nil)
	res, _, err = ActionListAvatarFrame(c)
	require.NoError(err)
	data, ok = res.(listAvatarFrameResp)
	require.True(ok)
	assert.Equal("1", data.Pagination.Marker)
	assert.True(data.Pagination.HasMore)
	require.Len(data.Data, 3)
	assert.EqualValues(238, data.Data[0].ID)
	assert.Equal(museravatarframemap.StatusTakeoff, data.Data[0].Status)
	assert.EqualValues(237, data.Data[1].ID)
	assert.Equal(museravatarframemap.StatusTakeoff, data.Data[1].Status)
	assert.EqualValues(234, data.Data[2].ID)
	assert.Zero(data.Data[2].ExpireTime)
	require.NotNil(data.Current)
	assert.EqualValues(233, data.Current.ID)
	assert.Equal(museravatarframemap.StatusWearing, data.Current.Status)

	// 测试获取最后一页数据
	c = handler.NewTestContext(http.MethodGet, "/x/person/list-avatar-frame?page_size=3&marker=1", true, nil)
	res, _, err = ActionListAvatarFrame(c)
	require.NoError(err)
	data, ok = res.(listAvatarFrameResp)
	require.True(ok)
	assert.Equal("2", data.Pagination.Marker)
	assert.False(data.Pagination.HasMore)
	require.Len(data.Data, 3)
	assert.EqualValues(233, data.Data[0].ID)
	assert.Equal(museravatarframemap.StatusWearing, data.Data[0].Status)
	assert.EqualValues(236, data.Data[1].ID)
	assert.Equal(museravatarframemap.StatusExpired, data.Data[1].Status)
	assert.Equal(util.BeginningOfDay(util.TimeNow()), time.Unix(data.Data[1].ExpireTime+1, 0))
	assert.EqualValues(235, data.Data[2].ID)
	assert.Equal(museravatarframemap.StatusExpired, data.Data[2].Status)
	assert.Nil(data.Current)
}

func TestGetUserAvatarFrame(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	avatarFrame := &museravatarframemap.UserAvatarFrame{
		AvatarFrameID:  233,
		Name:           "测试头像框名称",
		Intro:          "测试头像框简介",
		AvatarFrameURL: "test://test/202309/27/test.png",
		IconURL:        "test://test/202309/27/icon/test.png",
		Status:         museravatarframemap.StatusTakeoff,
		ExpireTime:     now.Add(-time.Minute).Unix(),
	}

	// 测试获取过期头像框
	userAvatarFrame := getUserAvatarFrame(avatarFrame)
	assert.Equal(avatarFrame.AvatarFrameID, userAvatarFrame.ID)
	assert.Equal(avatarFrame.Name, userAvatarFrame.Name)
	assert.Equal(avatarFrame.Intro, userAvatarFrame.Intro)
	assert.Equal(avatarFrame.AvatarFrameURL, userAvatarFrame.AvatarFrameURL)
	assert.Equal(avatarFrame.IconURL, userAvatarFrame.IconURL)
	assert.Equal(museravatarframemap.StatusExpired, userAvatarFrame.Status)
	assert.Equal(avatarFrame.ExpireTime-1, userAvatarFrame.ExpireTime)

	// 测试获取未过期头像框
	avatarFrame.ExpireTime = now.Add(time.Minute).Unix()
	userAvatarFrame = getUserAvatarFrame(avatarFrame)
	assert.Equal(avatarFrame.AvatarFrameID, userAvatarFrame.ID)
	assert.Equal(avatarFrame.Name, userAvatarFrame.Name)
	assert.Equal(avatarFrame.Intro, userAvatarFrame.Intro)
	assert.Equal(avatarFrame.AvatarFrameURL, userAvatarFrame.AvatarFrameURL)
	assert.Equal(avatarFrame.IconURL, userAvatarFrame.IconURL)
	assert.Equal(avatarFrame.Status, userAvatarFrame.Status)
	assert.Equal(avatarFrame.ExpireTime-1, userAvatarFrame.ExpireTime)
}
