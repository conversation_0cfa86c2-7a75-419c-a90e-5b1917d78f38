package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
)

// getThemeSkinResp 获取用户当前正使用的主题皮肤接口返回值
type getThemeSkinResp struct {
	ThemeSkin *mthemeskin.UserThemeSkinInfo `json:"theme_skin"`
}

// ActionGetThemeSkin 获取用户当前正使用的主题皮肤
/**
 * @api {get} /x/person/get-theme-skin 获取用户当前正使用的主题皮肤
 *
 * @apiVersion 0.1.0
 * @apiName get-theme-skin
 * @apiGroup /x/person/
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "theme_skin": { // 主题皮肤，下发 null 时使用默认主题
 *         "id": 1, // 当前使用的主题皮肤 ID
 *         "package_url": "https://static-test.maoercdn.com/app/themeskin/202412/12/tcu8461ccs2497829dbe9541b55bdc8d170256.zip" // 主题皮肤压缩包地址，其中 package.json 中的 package_id 与下发的 theme_skin.id 字段值一致
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null
 */
func ActionGetThemeSkin(c *handler.Context) (handler.ActionResponse, string, error) {
	themeSkin, err := mthemeskin.GetUserThemeSkin(c.UserID())
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return getThemeSkinResp{ThemeSkin: themeSkin}, "", nil
}
