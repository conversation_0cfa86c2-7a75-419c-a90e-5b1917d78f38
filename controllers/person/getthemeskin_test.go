package person

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
)

func TestActionGetThemeSkin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uri := "/x/person/get-theme-skin"
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	res, msg, err := ActionGetThemeSkin(c)
	require.NoError(err)
	assert.Equal("", msg)
	data, ok := res.(getThemeSkinResp)
	require.True(ok)
	expectData := getThemeSkinResp{
		ThemeSkin: &mthemeskin.UserThemeSkinInfo{
			ID:         1,
			Vip:        mthemeskin.VipLimit,
			Package:    "test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.zip",
			PackageURL: "https://static-test.maoercdn.com/themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.zip",
		},
	}
	assert.Equal(expectData, data)
}
