package person

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	testUserID = 12
)

func TestActionGetUserPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID).UpdateColumn("point", 50).Error
	require.NoError(err)
	c := handler.NewTestContext(http.MethodGet, "/x/person/get-user-point", true, nil)
	c.User().ID = testUserID
	data, message, err := ActionGetUserPoint(c)
	require.NoError(err)
	assert.Equal("", message)
	resp, ok := data.(userPointResp)
	require.True(ok)
	assert.Equal(userPointResp{
		Point: 50,
	}, resp)

	c.User().ID = 999999
	data, message, err = ActionGetUserPoint(c)
	assert.Equal(actionerrors.ErrUserNotFound, err)
	assert.Equal("", message)
	assert.Nil(data)
}

func TestActionGetUserPointLegacy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/person/get-user-point"

	// 测试非法请求
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.4 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	_, err := ActionGetUserPointLegacy(c)
	assert.EqualError(err, "非法请求")

	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.4 (iOS;12.0.1;iPhone7,2)")
	_, err = ActionGetUserPointLegacy(c)
	assert.EqualError(err, "非法请求")

	// 测试获取小鱼干数
	require.NoError(service.DB.Table(user.MowangskUser{}.TableName()).
		Where("id = ?", testUserID).UpdateColumn("point", 10).Error)
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.1.3 (iOS;12.0.1;iPhone7,2)")
	res, err := ActionGetUserPointLegacy(c)
	require.NoError(err)
	data, ok := res.(userPointResp)
	require.True(ok)
	assert.EqualValues(10, data.Point)
}
