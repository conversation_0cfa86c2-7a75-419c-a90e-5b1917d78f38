package person

import (
	"errors"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

type userPointResp struct {
	Point int64 `json:"point"`
}

// ActionGetUserPoint 获取当前登录用户拥有的小鱼干数
/**
 * @api {get} /x/person/get-user-point 获取当前登录用户拥有的小鱼干数
 *
 * @apiVersion 0.1.0
 * @apiName get-user-point
 * @apiGroup /x/person
 *
 * @apiPermission user
 *
 * @apiSuccessExample {json}
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "point": 50
 *       }
 *     }
 */
func ActionGetUserPoint(c *handler.Context) (handler.ActionResponse, string, error) {
	var userPoint int64
	err := service.DB.Table(user.MowangskUser{}.TableName()).Select("point").
		Where("id = ?", c.UserID()).Row().Scan(&userPoint)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, "", actionerrors.ErrUserNotFound
		}
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return userPointResp{
		Point: userPoint,
	}, "", nil
}

// ActionGetUserPointLegacy 获取当前登录用户拥有的小鱼干数
/**
 * @api {get} /person/get-user-point 获取当前登录用户拥有的小鱼干数
 * @apiDescription iOS <= 6.1.3 Android <= 6.1.3 使用 GET 方式请求 /person/get-user-point 获取当前登录用户拥有的小鱼干数
 * @apiVersion 0.1.0
 * @apiName get-user-point
 * @apiGroup /person
 *
 * @apiPermission user
 *
 * @apiSuccessExample {json}
 *     {
 *       "code": 0,
 *       "info": {
 *         "point": 50
 *       }
 *     }
 */
func ActionGetUserPointLegacy(c *handler.Context) (handler.ActionResponse, error) {
	e := c.Equip()
	// WORKAROUND: iOS ≥ 6.1.4 Android ≥ 6.1.4 获取当前登录用户拥有的小鱼干数请求新接口（/x/person/get-user-point）
	if !e.IsAppOlderThan("6.1.4", "6.1.4") {
		return nil, actionerrors.ErrBadRequest(handler.CodeUnknownError, "非法请求")
	}
	data, _, err := ActionGetUserPoint(c)
	if err != nil {
		return nil, err
	}
	resp, ok := data.(userPointResp)
	if !ok {
		return nil, actionerrors.ErrServerInternal(errors.New("获取当前登录用户拥有的小鱼干数响应数据异常"), nil)
	}
	return resp, nil
}
