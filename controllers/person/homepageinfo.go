package person

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

type homepageInfoResp struct {
	Vip vipInfo `json:"vip"`
}

type vipInfo struct {
	Title    string `json:"title"`
	Subtitle string `json:"subtitle"`
	Status   int    `json:"status"`
	URL      string `json:"url"`
}

// ActionHomepageInfo 我的页信息
/**
 * @api {get} /x/person/homepage-info 我的页信息
 *
 * @apiVersion 0.1.0
 * @apiName homepage-info
 * @apiGroup /x/person
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "icons": [
 *           {
 *             "id": 1,
 *             "title": "测试图标",
 *             "icon": "https://static.missevan.com/test.jpg",
 *             "dark_icon": "https://static.missevan.com/test.jpg",
 *             "url": "missevan://homepage"
 *           },
 *           {
 *             "id": 2,
 *             "title": "测试图标 2",
 *             "icon": "https://static.missevan.com/test.jpg",
 *             "dark_icon": "https://static.missevan.com/test.jpg",
 *             "url": "missevan://message",
 *             "name": "message"  // 用于标记 icon 的唯一标识
 *           },
 *           {
 *             "id": 3,
 *             "title": "我的钱包",
 *             "icon": "https://static.missevan.com/test.jpg",
 *             "dark_icon": "https://static.missevan.com/test.jpg",
 *             "url": "missevan://wallet",
 *             "name": "wallet",
 *             "is_new_user": true  // 标记新人用户，当「name = "wallet"」并且「新用户」或「新设备」时会返回，用于显示「首充福利」角标
 *           }
 *         ],
 *         "vip": {
 *           "title": "会员权益生效中",
 *           "subtitle": "2014-10-15 到期，每日可领 5 钻石",
 *           "status": 1, // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
 *           "url": "" // 下发空字符串跳转到会员中心页面，否则跳转到 url 中的页面
 *         }
 *       }
 *     }
 *
 */
// TODO: 我的页图标
func ActionHomepageInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	userID := c.UserID()
	enableVipCenter := config.Conf.Params.Vip.EnableVipCenter
	if !enableVipCenter {
		return nil, "", nil
	}
	vipInfo, err := getVipInfo(userID)
	if err != nil {
		return nil, "", err
	}
	return homepageInfoResp{
		Vip: vipInfo,
	}, "", nil
}

func getVipInfo(userID int64) (vipInfo, error) {
	userHomePageInfo := config.Conf.Params.Vip.UserHomePageInfo
	vipCenterURL := config.Conf.Params.Vip.VipCenterURL
	if userID <= 0 {
		return vipInfo{
			Title:    userHomePageInfo.Guest.Title,
			Subtitle: userHomePageInfo.Guest.Subtitle,
			Status:   muservip.VipStatusNotYet,
			URL:      vipCenterURL,
		}, nil
	}

	userVipInfo, err := muservip.GetUserVipInfo(userID)
	if err != nil {
		return vipInfo{}, actionerrors.ErrServerInternal(err, nil)
	}
	info := vipInfo{
		Status: userVipInfo.Status,
		URL:    vipCenterURL,
	}
	switch info.Status {
	case muservip.VipStatusNotYet:
		info.Title = userHomePageInfo.UserVipNotYet.Title
		info.Subtitle = userHomePageInfo.UserVipNotYet.Subtitle
	case muservip.VipStatusInEffect:
		info.Title = userHomePageInfo.UserVipInEffect.Title
		info.Subtitle = fmt.Sprintf(userHomePageInfo.UserVipInEffect.Subtitle,
			time.Unix(userVipInfo.EndTime, 0).Format(goutil.TimeFormatYMD))
	case muservip.VipStatusExpired:
		info.Title = userHomePageInfo.UserVipExpired.Title
		info.Subtitle = userHomePageInfo.UserVipExpired.Subtitle
	default:
		// 目前个人主页不做体验会员信息下发
		return vipInfo{}, nil
	}
	return info, nil
}
