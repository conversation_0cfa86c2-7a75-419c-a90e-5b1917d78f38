package person

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(getAvatarFrameResp{}, "avatar_frame", "user")
	kc.CheckOmitEmpty(getAvatarFrameResp{}, "user")
	kc.Check(avatarFrameInfo{}, "id", "name", "intro", "image_url", "type", "status", "detail_url")
	kc.CheckOmitEmpty(avatarFrameInfo{}, "status", "detail_url")
	kc.Check(userInfo{}, "is_vip")
}

func TestActionGetAvatarFrame(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	url := "/x/person/get-avatar-frame"

	// 测试请求失败的情况
	c := handler.NewTestContext(http.MethodGet, url, true, nil)
	_, _, err := ActionGetAvatarFrame(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试头像框不存在的情况
	testAvatarFrameID, testVipAvatarFrameID := int64(6354564), int64(6354565)
	require.NoError(mavatarframe.MAvatarFrame{}.DB().Delete("", "id IN (?)", []int64{testAvatarFrameID, testVipAvatarFrameID}).Error)
	api := fmt.Sprintf("%s?avatar_frame_id=%d", url, testAvatarFrameID)
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	_, _, err = ActionGetAvatarFrame(c)
	assert.Equal(actionerrors.ErrAvatarFrameNotFound, err)

	// 测试请求成功，未登录的情况
	more := "{\"event_id\": 999}"
	avatarFrames := []mavatarframe.MAvatarFrame{
		{
			ID:    testAvatarFrameID,
			Name:  "test name",
			Intro: "test intro",
			Frame: "test://test/test.png",
			More:  []byte(more),
			Type:  mavatarframe.TypeNormal,
		},
		{
			ID:    testVipAvatarFrameID,
			Name:  "test name",
			Intro: "test intro",
			Frame: "test://test/test.png",
			Type:  mavatarframe.TypeVip,
		},
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, avatarFrames[0].TableName(), avatarFrames))
	c = handler.NewTestContext(http.MethodGet, api, false, nil)
	resp, _, err := ActionGetAvatarFrame(c)
	require.NoError(err)
	data, ok := resp.(getAvatarFrameResp)
	require.True(ok)
	assert.Equal(getAvatarFrameResp{
		AvatarFrame: avatarFrameInfo{
			ID:        avatarFrames[0].ID,
			Name:      avatarFrames[0].Name,
			Intro:     avatarFrames[0].Intro,
			ImageURL:  "https://static-test.maoercdn.com/test/test.png",
			Type:      mavatarframe.TypeNormal,
			Status:    nil,
			DetailURL: "",
		},
		User: nil,
	}, data)

	// 测试请求成功，登录的用户未拥有头像框的情况
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	resp, _, err = ActionGetAvatarFrame(c)
	require.NoError(err)
	data, ok = resp.(getAvatarFrameResp)
	require.True(ok)
	assert.Equal(getAvatarFrameResp{
		AvatarFrame: avatarFrameInfo{
			ID:        avatarFrames[0].ID,
			Name:      avatarFrames[0].Name,
			Intro:     avatarFrames[0].Intro,
			ImageURL:  "https://static-test.maoercdn.com/test/test.png",
			Type:      mavatarframe.TypeNormal,
			Status:    util.NewInt(statusNotOwned),
			DetailURL: "",
		},
		User: &userInfo{
			IsVip: 1,
		},
	}, data)

	// 测试请求成功，登录的非 vip 用户未拥有 vip 头像框的情况
	api = fmt.Sprintf("%s?avatar_frame_id=%d", url, testVipAvatarFrameID)
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	c.User().ID = int64(233)
	resp, _, err = ActionGetAvatarFrame(c)
	require.NoError(err)
	data, ok = resp.(getAvatarFrameResp)
	require.True(ok)
	assert.Equal(getAvatarFrameResp{
		AvatarFrame: avatarFrameInfo{
			ID:        avatarFrames[1].ID,
			Name:      avatarFrames[1].Name,
			Intro:     avatarFrames[1].Intro,
			ImageURL:  "https://static-test.maoercdn.com/test/test.png",
			Type:      mavatarframe.TypeVip,
			Status:    util.NewInt(statusNotOwned),
			DetailURL: "",
		},
		User: &userInfo{
			IsVip: 0,
		},
	}, data)

	// 测试请求成功，登录的用户为 vip，但未佩戴过头像框的情况
	api = fmt.Sprintf("%s?avatar_frame_id=%d", url, testVipAvatarFrameID)
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	resp, _, err = ActionGetAvatarFrame(c)
	require.NoError(err)
	data, ok = resp.(getAvatarFrameResp)
	require.True(ok)
	assert.Equal(getAvatarFrameResp{
		AvatarFrame: avatarFrameInfo{
			ID:        avatarFrames[1].ID,
			Name:      avatarFrames[1].Name,
			Intro:     avatarFrames[1].Intro,
			ImageURL:  "https://static-test.maoercdn.com/test/test.png",
			Type:      mavatarframe.TypeVip,
			Status:    util.NewInt(museravatarframemap.StatusTakeoff),
			DetailURL: "",
		},
		User: &userInfo{
			IsVip: 1,
		},
	}, data)

	// 测试用户佩戴了头像框，且配置的（发放）活动 ID 在生效时间的情况
	userAvatarFrame := museravatarframemap.MUserAvatarFrameMap{
		AvatarFrameID: testAvatarFrameID,
		UserID:        12,
		Status:        museravatarframemap.StatusWearing,
		ExpireTime:    util.TimeNow().Add(time.Hour).Unix(),
	}
	require.NoError(userAvatarFrame.DB().Create(&userAvatarFrame).Error)
	more = "{\"event_id\": 233}"
	require.NoError(avatarFrames[0].DB().Where("id = ? ", testAvatarFrameID).Update("more", more).Error)
	api = fmt.Sprintf("%s?avatar_frame_id=%d", url, testAvatarFrameID)
	c = handler.NewTestContext(http.MethodGet, api, true, nil)
	resp, _, err = ActionGetAvatarFrame(c)
	require.NoError(err)
	data, ok = resp.(getAvatarFrameResp)
	require.True(ok)
	assert.Equal(getAvatarFrameResp{
		AvatarFrame: avatarFrameInfo{
			ID:        avatarFrames[0].ID,
			Name:      avatarFrames[0].Name,
			Intro:     avatarFrames[0].Intro,
			ImageURL:  "https://static-test.maoercdn.com/test/test.png",
			Status:    util.NewInt(museravatarframemap.StatusWearing),
			DetailURL: "https://www.missevan.com/mevent/233",
			Type:      mavatarframe.TypeNormal,
		},
		User: &userInfo{
			IsVip: 1,
		},
	}, data)
}

func TestNewGetAvatarFrameParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	api := "/x/person/get-avatar-frame"

	// 测试参数为空
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	_, err := newGetAvatarFrameParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数有误
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?avatar_frame_id=%d", api, -1), true, nil)
	_, err = newGetAvatarFrameParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试头像框不存在
	testAvatarFrameID := int64(85676865)
	avatarFrame := &mavatarframe.MAvatarFrame{
		ID: testAvatarFrameID,
	}
	require.NoError(avatarFrame.DB().Delete("", "id = ?", testAvatarFrameID).Error)
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?avatar_frame_id=%d", api, testAvatarFrameID), true, nil)
	_, err = newGetAvatarFrameParam(c)
	assert.Equal(actionerrors.ErrAvatarFrameNotFound, err)

	// 测试头像框存在
	require.NoError(avatarFrame.DB().Create(avatarFrame).Error)
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?avatar_frame_id=%d", api, testAvatarFrameID), true, nil)
	res, err := newGetAvatarFrameParam(c)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(testAvatarFrameID, res.avatarFrame.ID)
	assert.NotZero(res.userID)
}
