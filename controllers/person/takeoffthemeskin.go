package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskin"
)

// ActionTakeoffThemeSkin 卸下主题皮肤
/**
 * @api {post} /x/person/takeoff-theme-skin 卸下主题皮肤
 *
 * @apiVersion 0.1.0
 * @apiName wear-theme-skin
 * @apiGroup /x/person/
 *
 * @apiPermission user
 *
 * @apiParam {Number} theme_skin_id 主题皮肤 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "停用成功",
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null
 */
func ActionTakeoffThemeSkin(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newSetThemeSkinParam(c, muserthemeskin.StatusTakeoff)
	if err != nil {
		return nil, "", err
	}
	if param.userThemeSkin == nil {
		return nil, "", actionerrors.ErrForbidden(handler.CodeUnknownError, "停用失败")
	}
	if param.userThemeSkin.Status != muserthemeskin.StatusTakeoff {
		// 卸下主题
		err = param.userThemeSkin.SetThemeSkin(muserthemeskin.StatusTakeoff)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
	}
	return nil, "停用成功", nil
}
