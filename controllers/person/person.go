package person

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// HandlerV2 return handler
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "person",
		Actions: map[string]*handler.ActionV2{
			"get-user-point":       handler.NewActionV2(handler.GET, ActionGetUserPoint, handler.ActionOption{LoginRequired: true}),
			"batch-follow":         handler.NewActionV2(handler.POST, ActionBatchFollow, handler.ActionOption{LoginRequired: true}),
			"get-avatar-frame":     handler.NewActionV2(handler.GET, ActionGetAvatarFrame, handler.ActionOption{LoginRequired: false}),
			"homepage-info":        handler.NewActionV2(handler.GET, ActionHomepageInfo, handler.ActionOption{LoginRequired: false}),
			"list-avatar-frame":    handler.NewActionV2(handler.GET, ActionListAvatarFrame, handler.ActionOption{LoginRequired: true}),
			"takeoff-avatar-frame": handler.NewActionV2(handler.POST, ActionTakeoffAvatarFrame, handler.ActionOption{LoginRequired: true}),
			"wear-avatar-frame":    handler.NewActionV2(handler.POST, ActionWearAvatarFrame, handler.ActionOption{LoginRequired: true}),
			"get-theme-skin":       handler.NewActionV2(handler.GET, ActionGetThemeSkin, handler.ActionOption{LoginRequired: true}),
			"theme-skin-detail":    handler.NewActionV2(handler.GET, ActionThemeSkinDetail, handler.ActionOption{LoginRequired: false}),
			"wear-theme-skin":      handler.NewActionV2(handler.POST, ActionWearThemeSkin, handler.ActionOption{LoginRequired: true}),
			"takeoff-theme-skin":   handler.NewActionV2(handler.POST, ActionTakeoffThemeSkin, handler.ActionOption{LoginRequired: true}),
			"task":                 handler.NewActionV2(handler.POST, ActionTask, handler.ActionOption{LoginRequired: true}),
		},
	}
}

// Handler return handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "person",
		Actions: map[string]*handler.Action{
			// iOS <= 6.1.3 Android <= 6.1.3 使用 GET 方式请求 /person/get-user-point 获取当前登录用户拥有的小鱼干数
			"get-user-point": handler.NewAction(handler.GET, ActionGetUserPointLegacy, true),
		},
	}
}
