package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

type themeSkinDetailParam struct {
	themeSkinID int64
	userID      int64
	themeSkin   *mthemeskin.MThemeSkin
}

type themeSkinUserInfo struct {
	IsVip int `json:"is_vip"` // 当前登录用户是否为会员。0: 否；1: 是
}

type themeSkinDetailResp struct {
	ThemeSkin themeSkinInfo      `json:"theme_skin"`
	User      *themeSkinUserInfo `json:"user,omitempty"` // 未登录时不下发该字段
}

type themeSkinInfo struct {
	ID           int64    `json:"id"` // 主题皮肤 ID ID
	Name         string   `json:"name"`
	Intro        string   `json:"intro"`
	ImageURLList []string `json:"image_url_list"` // 预览图地址列表
	BgStartColor string   `json:"bg_start_color"` // 背景色渐变起始颜色
	BgEndColor   string   `json:"bg_end_color"`   // 背景色渐变结束颜色
	Status       int      `json:"status"`         // 当前登录用户装扮状态。-1: 无权限，0: 未装扮，1: 装扮中。未登录时固定下发 0
}

// ActionThemeSkinDetail 主题皮肤详情页
/**
 * @api {get} /x/person/theme-skin-detail 主题皮肤详情页
 *
 * @apiVersion 0.1.0
 * @apiName theme-skin-detail
 * @apiGroup /x/person/
 *
 * @apiParam {Number} theme_skin_id 主题皮肤 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "theme_skin": { // 主题皮肤
 *         "id": 1, // 主题皮肤 ID
 *         "name": "圣诞限定套装",
 *         "intro": "温暖圣诞，温暖你！穿上这一套，最靓的仔就是你！",
 *         "image_url_list": [ // 预览图，按顺序展示
 *           "https://static-test.maoercdn.com/theme/202401/04/a.jpg",
 *           "https://static-test.maoercdn.com/theme/202401/04/b.jpg",
 *           "https://static-test.maoercdn.com/theme/202401/04/c.jpg"
 *         ],
 *         "bg_start_color": "#F8A623", // 背景色渐变起始颜色
 *         "bg_end_color": "#F8A626", // 背景色渐变结束颜色
 *         "status": 1 // -1：无权限装扮，0：未装扮，1：装扮中。未登录时固定返回 0
 *       },
 *       "user": { // 用户信息，只对登录用户下发
 *         "is_vip" 1 // 0: 非会员，1: 会员
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null
 */
func ActionThemeSkinDetail(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newThemeSkinDetailParam(c)
	if err != nil {
		return nil, "", err
	}
	resp := themeSkinDetailResp{
		ThemeSkin: themeSkinInfo{
			ID:           param.themeSkin.ID,
			Name:         param.themeSkin.Title,
			Intro:        param.themeSkin.Intro,
			ImageURLList: param.themeSkin.MoreInfo.PreviewCoverUrls,
			BgStartColor: param.themeSkin.MoreInfo.PreviewBgStartColor,
			BgEndColor:   param.themeSkin.MoreInfo.PreviewBgEndColor,
			Status:       muserthemeskin.StatusTakeoff,
		},
	}
	if param.userID != 0 {
		isVipUser, err := muservip.IsVip(param.userID)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		resp.User = &themeSkinUserInfo{
			IsVip: util.BoolToInt(isVipUser),
		}
		resp.ThemeSkin.Status = muserthemeskin.StatusNotOwned
		// TODO: 装扮逻辑暂未考虑购买主题皮肤的情况
		if isVipUser {
			isWear, err := muserthemeskin.IsWear(param.userID, param.themeSkinID)
			if err != nil {
				return nil, "", actionerrors.ErrServerInternal(err, nil)
			}
			resp.ThemeSkin.Status = muserthemeskin.StatusTakeoff
			if isWear {
				resp.ThemeSkin.Status = muserthemeskin.StatusWearing
			}
		}
	}
	return resp, "", nil
}

func newThemeSkinDetailParam(c *handler.Context) (*themeSkinDetailParam, error) {
	themeSkinID, err := c.GetParamInt64("theme_skin_id")
	if err != nil || themeSkinID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param := &themeSkinDetailParam{
		themeSkinID: themeSkinID,
	}
	param.themeSkin, err = mthemeskin.FindOne(param.themeSkinID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.themeSkin == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeUnknownError, "主题皮肤不存在")
	}
	param.userID = c.UserID()
	return param, nil
}
