package person

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
)

type listAvatarFrameResp struct {
	Data       []listAvatarFrameItem `json:"data"`
	Pagination util.MarkerPagination `json:"pagination"`
	Current    *listAvatarFrameItem  `json:"current,omitempty"`
}

// listAvatarFrameItem 用户头像框信息
type listAvatarFrameItem struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	Intro          string `json:"intro"`
	AvatarFrameURL string `json:"avatar_frame_url"`
	IconURL        string `json:"icon_url"`
	ExpireTime     int64  `json:"expire_time"`
	Status         int    `json:"status"`
}

// ActionListAvatarFrame 获取用户普通头像框列表
/**
 * @api {get} /x/person/list-avatar-frame 获取用户普通头像框列表
 *
 * @apiVersion 0.1.0
 * @apiName list-avatar-frame
 * @apiGroup /x/person/
 *
 * @apiPermission user
 *
 * @apiParam {String} [marker=""] 分页标识，第一次请求时不需要传
 * @apiParam {Number} [page_size=20] 每页数量
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "id": 1, // 头像框 ID
 *           "name": "头像框名称",
 *           "intro": "头像框简介 <a href=\"test.com\">超链接</a>", // 头像框简介，支持 HTML
 *           "avatar_frame_url": "https://test.com/test.png", // 头像框地址
 *           "icon_url": "https://test.com/icon/test.png", // icon 地址
 *           "expire_time": 1698888888, // 过期时间戳，为 0 时表示不会过期，单位：秒
 *           "status": 0 // 佩戴状态。0: 未佩戴，1: 佩戴中，2: 已过期
 *         }
 *       ],
 *       "pagination": {
 *         "marker": "1", // 分页标识，客户端下次请求接口时回传
 *         "has_more": true // 是否有下一页
 *       },
 *       "current": { // 仅第一页下发用户佩戴中的头像框，若用户没有佩戴中的头像框时不下发该字段
 *         "id": 2,
 *         "name": "头像框名称 2",
 *         "intro": "头像框简介 2 <a href=\"test.com\">超链接</a>",
 *         "avatar_frame_url": "https://test.com/test.png",
 *         "expire_time": 1698888888,
 *         "status": 1
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 * @apiError (404) {Object} data null
 */
func ActionListAvatarFrame(c *handler.Context) (handler.ActionResponse, string, error) {
	marker, pageSize, err := c.GetParamMarker()
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}

	// 解析 marker 获取页码参数，若 marker 参数为空，页码默认为 1
	page := int64(1)
	if marker != "" {
		markerIndex, err := strconv.ParseInt(marker, 10, 64)
		if err != nil || page <= 0 {
			return nil, "", actionerrors.ErrParams
		}
		page = markerIndex + 1
	}

	// 获取用户头像框列表
	userID := c.UserID()
	userAvatarFrameLister := museravatarframemap.UserAvatarFrameLister{
		Page:     page,
		PageSize: pageSize,
		UserID:   userID,
	}
	avatarFrames, hasMore, err := userAvatarFrameLister.ListUserNormalAvatarFrame()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	var currentAvatarFrame *listAvatarFrameItem
	userAvatarFrames := make([]listAvatarFrameItem, 0, len(avatarFrames))
	for _, avatarFrame := range avatarFrames {
		userAvatarFrame := getUserAvatarFrame(&avatarFrame)
		if page == 1 && userAvatarFrame.Status == museravatarframemap.StatusWearing {
			currentAvatarFrame = userAvatarFrame
		}
		userAvatarFrames = append(userAvatarFrames, *userAvatarFrame)
	}

	if page == 1 && currentAvatarFrame == nil && hasMore {
		// 若第一页没有用户佩戴中的头像框，且下页有数据时查询用户当前佩戴中的头像框
		userWearingAvatarFrames, err := museravatarframemap.ListWearingByUserIDs([]int64{userID}, true)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		if len(userWearingAvatarFrames) > 0 {
			currentAvatarFrame = getUserAvatarFrame(userWearingAvatarFrames[0])
		}
	}

	return listAvatarFrameResp{
		Data: userAvatarFrames,
		Pagination: util.MarkerPagination{
			HasMore: hasMore,
			Marker:  strconv.FormatInt(page, 10),
		},
		Current: currentAvatarFrame,
	}, "", nil
}

func getUserAvatarFrame(avatarFrame *museravatarframemap.UserAvatarFrame) *listAvatarFrameItem {
	userAvatarFrame := &listAvatarFrameItem{
		ID:             avatarFrame.AvatarFrameID,
		Name:           avatarFrame.Name,
		Intro:          avatarFrame.Intro,
		AvatarFrameURL: avatarFrame.AvatarFrameURL,
		IconURL:        avatarFrame.IconURL,
		Status:         avatarFrame.Status,
	}
	if avatarFrame.ExpireTime != museravatarframemap.ExpireTimeForeverEffective {
		if avatarFrame.ExpireTime < util.TimeNow().Unix() {
			userAvatarFrame.Status = museravatarframemap.StatusExpired
		}

		// 过期时间需要为最后一日的 23:59:59，保证客户端显示的过期日期为最后一日，故此处 -1s
		userAvatarFrame.ExpireTime = avatarFrame.ExpireTime - 1
	}
	return userAvatarFrame
}
