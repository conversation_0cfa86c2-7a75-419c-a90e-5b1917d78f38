package person

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestActionWearAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/x/person/takeoff-avatar-frame"
	param := avatarFrameParams{
		AvatarFrameID: -1,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	_, _, err := ActionWearAvatarFrame(c)
	assert.EqualError(err, "参数错误")

	// 测试头像框过期
	require.NoError(mavatarframe.MAvatarFrame{}.DB().Delete("", "id IN (?)", []int64{testAvatarFrameID, testVipAvatarFrameID}).Error)
	avatarFrames := []mavatarframe.MAvatarFrame{
		{ID: testAvatarFrameID},
		{
			ID:   testVipAvatarFrameID,
			Type: mavatarframe.TypeVip,
		},
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, avatarFrames[0].TableName(), avatarFrames))

	data := &museravatarframemap.MUserAvatarFrameMap{
		UserID:        12,
		AvatarFrameID: testAvatarFrameID,
		Status:        museravatarframemap.StatusTakeoff,
		ExpireTime:    util.TimeNow().Add(-time.Hour).Unix(),
	}
	require.NoError(data.DB().Delete("", "user_id = ?", data.UserID).Error)
	require.NoError(data.DB().Create(data).Error)

	param.AvatarFrameID = testAvatarFrameID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, _, err = ActionWearAvatarFrame(c)
	assert.Equal(actionerrors.ErrAvatarFrameExpired, err)

	// 测试装扮头像框
	require.NoError(data.DB().Where("user_id = ? AND avatar_frame_id = ?", data.UserID, data.AvatarFrameID).
		Update("expire_time", 0).Error)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, message, err := ActionWearAvatarFrame(c)
	require.NoError(err)
	assert.Equal("装扮成功", message)

	var data1 museravatarframemap.MUserAvatarFrameMap
	require.NoError(data1.DB().Where("user_id = ? AND avatar_frame_id = ?",
		data.UserID, data.AvatarFrameID).Take(&data1).Error)
	assert.EqualValues(12, data1.UserID)
	assert.EqualValues(testAvatarFrameID, data1.AvatarFrameID)
	assert.Equal(museravatarframemap.StatusWearing, data1.Status)

	// 测试首次装扮 vip 头像框
	param.AvatarFrameID = testVipAvatarFrameID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	require.NoError(muservip.MUserVip{}.DB().Where("user_id = ?", testUserID).Update("end_time",
		util.TimeNow().Add(time.Second).Unix()).Error)
	_, message, err = ActionWearAvatarFrame(c)
	require.NoError(err)
	assert.Equal("装扮成功", message)

	var data2 museravatarframemap.MUserAvatarFrameMap
	require.NoError(data1.DB().Where("user_id = ? AND avatar_frame_id = ?",
		c.UserID(), param.AvatarFrameID).Take(&data2).Error)
	assert.EqualValues(12, data2.UserID)
	assert.EqualValues(testVipAvatarFrameID, data2.AvatarFrameID)
	assert.Equal(museravatarframemap.StatusWearing, data2.Status)
}
