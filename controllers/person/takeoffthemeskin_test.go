package person

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskinlog"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

func TestActionTakeoffThemeSkin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	uri := "/x/person/takeoff-theme-skin"
	c := handler.NewTestContext(http.MethodPost, uri, true, nil)
	_, _, err := ActionTakeoffThemeSkin(c)
	assert.EqualError(err, actionerrors.ErrParams.Error())

	// 测试停用的主题不存在
	themeSkinID := int64(19999000)
	err = mthemeskin.MThemeSkin{}.DB().Delete("", "id = ?", themeSkinID).Error
	require.NoError(err)
	param := setThemeSkinParams{
		ThemeSkinID: themeSkinID,
	}
	c = handler.NewTestContext(http.MethodPost, uri, true, param)
	_, _, err = ActionTakeoffThemeSkin(c)
	assert.EqualError(err, "主题皮肤不存在")

	// 测试非会员停用会员主题
	userID := int64(169999999)
	err = muservip.MUserVip{}.DB().Delete("", "user_id = ?", userID).Error
	require.NoError(err)
	themeSkinID = 1
	param = setThemeSkinParams{
		ThemeSkinID: themeSkinID,
	}
	c = handler.NewTestContext(http.MethodPost, uri, true, param)
	c.User().ID = userID
	_, _, err = ActionTakeoffThemeSkin(c)
	assert.EqualError(err, "停用失败")

	// 测试用户正常停用正在佩戴的主题皮肤
	now := util.TimeNow().Unix()
	userVip := muservip.MUserVip{
		VipID:     1,
		UserID:    userID,
		StartTime: now - 100,
		EndTime:   now + 10000,
		Type:      muservip.TypePlay,
	}
	err = muservip.MUserVip{}.DB().Create(&userVip).Error
	require.NoError(err)
	err = muserthemeskin.MUserThemeSkin{}.DB().Delete("", "theme_skin_id = ?", themeSkinID).Error
	require.NoError(err)
	userTs := muserthemeskin.MUserThemeSkin{
		UserID:      userID,
		ThemeSkinID: themeSkinID,
		Status:      muserthemeskin.StatusWearing,
	}
	err = muserthemeskin.MUserThemeSkin{}.DB().Create(&userTs).Error
	require.NoError(err)
	err = muserthemeskinlog.MUserThemeSkinLog{}.DB().Delete("", "theme_skin_id = ?", themeSkinID).Error
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, uri, true, param)
	c.User().ID = userID
	data, _, err := ActionTakeoffThemeSkin(c)
	require.NoError(err)
	assert.Nil(data)
	exists, err := servicedb.Exists(muserthemeskin.MUserThemeSkin{}.DB().
		Where("user_id = ? AND theme_skin_id = ? AND status = ? AND expire_time = ?",
			userID, themeSkinID, muserthemeskin.StatusTakeoff, muserthemeskin.ExpireTimeForeverEffective))
	require.NoError(err)
	assert.True(exists)
	exists, err = servicedb.Exists(muserthemeskinlog.MUserThemeSkinLog{}.DB().
		Where("user_id = ? AND theme_skin_id = ? AND end_time <> ?",
			userID, themeSkinID, muserthemeskinlog.EndTimeForeverEffective))
	require.NoError(err)
	assert.True(exists)

	// 测试重复停用主题
	c = handler.NewTestContext(http.MethodPost, uri, true, param)
	c.User().ID = userID
	data, _, err = ActionTakeoffThemeSkin(c)
	require.NoError(err)
	assert.Nil(data)
	var count int64
	err = muserthemeskin.MUserThemeSkin{}.DB().
		Where("user_id = ? AND theme_skin_id = ? AND status = ? AND expire_time = ?", userID, themeSkinID, muserthemeskin.StatusTakeoff, muserthemeskinlog.EndTimeForeverEffective).
		Count(&count).Error
	require.NoError(err)
	assert.EqualValues(1, count)
	err = muserthemeskinlog.MUserThemeSkinLog{}.DB().
		Where("user_id = ? AND theme_skin_id = ? AND end_time <> ?", userID, themeSkinID, muserthemeskinlog.EndTimeForeverEffective).
		Count(&count).Error
	require.NoError(err)
	assert.EqualValues(1, count)
}
