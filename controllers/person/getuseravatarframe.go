package person

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

// 头像框未获得状态
// 该状态值目前仅此接口用到，之后其他地方也需要的话，需要挪到相关 model 中定义
const statusNotOwned = -1

type getAvatarFrameParam struct {
	avatarFrameID int64
	userID        int64
	avatarFrame   *mavatarframe.MAvatarFrame
}

type userInfo struct {
	IsVip int `json:"is_vip"` // 当前登录用户是否为会员。0: 否；1: 是
}

type getAvatarFrameResp struct {
	AvatarFrame avatarFrameInfo `json:"avatar_frame"`
	User        *userInfo       `json:"user,omitempty"` // 未登录时不下发该字段
}

type avatarFrameInfo struct {
	ID        int64  `json:"id"`                   // 头像框 ID
	Name      string `json:"name"`                 // 头像框名称
	Intro     string `json:"intro"`                // 头像框简介
	ImageURL  string `json:"image_url"`            // 头像框图片地址
	Type      int    `json:"type"`                 // 头像框类型。0: 普通挂件；1: 会员挂件
	Status    *int   `json:"status,omitempty"`     // 当前登录用户佩戴状态。-1: 未获得，0: 未佩戴，1: 佩戴中，2: 已过期。未登录时不下发该字段
	DetailURL string `json:"detail_url,omitempty"` // 点击“了解详情”时跳转的地址。未下发时不展示“了解详情”按钮
}

// ActionGetAvatarFrame 获取头像框信息
/**
 * @api {get} /x/person/get-avatar-frame 获取头像框信息
 *
 * @apiVersion 0.1.0
 * @apiName get-avatar-frame
 * @apiGroup /x/person/
 *
 * @apiParam {Number} avatar_frame_id 头像框 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "avatar_frame": { // 头像框信息
 *         "id": 2,
 *         "name": "头像框名称",
 *         "intro": "头像框简介",
 *         "image_url": "https://test.com/test.png",
 *         "type": 0, // 头像框类型。0: 普通挂件；1: 会员挂件
 *         "status": 1, // 当前登录用户佩戴状态。-1: 未获得，0: 未佩戴，1: 佩戴中，2: 已过期。未登录时不下发该字段
 *         "detail_url": "https://www.test.com/mevent/233" // 点击“了解详情”时跳转的地址。未下发时不展示“了解详情”按钮
 *       },
 *       "user": { // 未登录时不下发该字段
 *         "is_vip": 1 // 当前登录用户是否为会员。0: 否；1: 是
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null
 *
 * @apiError (404) {Number} code 200160001
 * @apiError (404) {String} message 头像框不存在
 * @apiError (404) {Object} data null
 */
func ActionGetAvatarFrame(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newGetAvatarFrameParam(c)
	if err != nil {
		return nil, "", err
	}
	resp := getAvatarFrameResp{
		AvatarFrame: avatarFrameInfo{
			ID:       param.avatarFrame.ID,
			Name:     param.avatarFrame.Name,
			Intro:    param.avatarFrame.Intro,
			ImageURL: param.avatarFrame.AvatarFrameURL,
			Type:     param.avatarFrame.Type,
		},
	}
	if param.userID != 0 {
		// 获取用户 vip 信息
		userVipInfo, err := muservip.GetUserVipInfo(param.userID)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		isVipUser := userVipInfo.Status == muservip.VipStatusInEffect
		resp.User = &userInfo{
			IsVip: util.BoolToInt(isVipUser),
		}

		// 获取登录用户对于该头像框的获取、佩戴状态
		resp.AvatarFrame.Status, err = museravatarframemap.GetUserWearStatus(param.userID, param.avatarFrameID)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		isVipFrame := resp.AvatarFrame.Type == mavatarframe.TypeVip
		if isVipFrame && !isVipUser {
			// 当前头像框是 vip 头像框且用户不是 vip 用户时，返回未获得状态
			resp.AvatarFrame.Status = util.NewInt(statusNotOwned)
		} else if resp.AvatarFrame.Status == nil {
			if isVipFrame && isVipUser {
				// 当头像框是 vip 头像框且用户是 vip 时，返回未佩戴状态
				resp.AvatarFrame.Status = util.NewInt(museravatarframemap.StatusTakeoff)
			} else {
				// 否则，返回未获得状态
				resp.AvatarFrame.Status = util.NewInt(statusNotOwned)
			}
		}
	}
	eventID := param.avatarFrame.MoreInfo.EventID
	if eventID != 0 && resp.AvatarFrame.Type == mavatarframe.TypeNormal {
		isEventWithinValidTime, err := mevent.IsEventWithinValidTime(eventID)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		if isEventWithinValidTime {
			resp.AvatarFrame.DetailURL = fmt.Sprintf("%smevent/%d", config.Conf.Params.URL.Main, eventID)
		}
	}
	return resp, "", nil
}

func newGetAvatarFrameParam(c *handler.Context) (*getAvatarFrameParam, error) {
	avatarFrameID, err := c.GetParamInt64("avatar_frame_id")
	if err != nil || avatarFrameID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param := &getAvatarFrameParam{
		avatarFrameID: avatarFrameID,
	}
	// 查询头像框
	param.avatarFrame, err = mavatarframe.FindOne(param.avatarFrameID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.avatarFrame == nil {
		return nil, actionerrors.ErrAvatarFrameNotFound
	}
	param.userID = c.UserID()
	return param, nil
}
