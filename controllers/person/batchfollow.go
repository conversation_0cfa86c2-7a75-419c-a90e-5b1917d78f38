package person

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/person/mattentionuser"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

const (
	followUserLimit      int64 = 100 // 单批次最多关注用户数量
	followUserDailyLimit int64 = 200 // 每个自然日最多批量关注用户数量
)

// batchFollowParam 批量关注用户参数
type batchFollowParam struct {
	UserIDsStr string `json:"user_ids" form:"user_ids"` // 待关注用户 IDsStr

	userID        int64   // 当前用户 ID
	followUserIDs []int64 // 待关注用户 IDs
	dailyLimitKey string  // 用户单自然日关注数量限制 redis key
}

// batchFollowResp 批量关注用户返回值
type batchFollowResp struct {
	FailedUserIDs []int64 `json:"failed_user_ids"` // 未成功关注的用户 IDs
}

// ActionBatchFollow 批量关注用户
/**
 * @api {post} /x/person/batch-follow 批量关注用户
 *
 * @apiVersion 0.1.0
 * @apiName batch-follow
 * @apiGroup x/person
 *
 * @apiPermission user
 *
 * @apiParam {String} user_ids 待关注用户 IDs 字符串，以半角逗号分隔
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 400
 *     {
 *       "code": 201010002,
 *       "message": "参数错误",
 *       "data": null
 *     }
 * @apiSuccessExample {json} Error-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "成功关注 3 位用户！",
 *       "data": {
 *         "failed_user_ids": [39006, 43118] // 本次未成功关注的用户 IDs
 *       }
 *     }
 */
func ActionBatchFollow(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newBatchFollowParam(c)
	if err != nil {
		return nil, "", err
	}
	userID := param.userID
	lock := keys.LockBatchFollowUser1.Format(userID)
	ok, err := service.Redis.SetNX(lock, "1", 10*time.Second).Result()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)

	followUserIDs := param.followUserIDs
	succeedFollowUserIDs, alreadyFollowedUserIDs, err := mattentionuser.BatchFollow(c.UserContext(), userID, followUserIDs)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	followFrom := int(c.Equip().OS)
	util.Go(func() {
		// TODO: 后续考虑改为写入一条批量关注消息
		for _, followUserID := range succeedFollowUserIDs {
			mattentionuser.SendFollowLog(userID, followUserID, followFrom, mattentionuser.FollowTypeFollow)
		}
	})
	successNum := int64(len(succeedFollowUserIDs))
	// 由于接口加了防并发锁且在操作前判断了每日余量，一般情况不会出现 Incr 后超过限制数量的情况
	pipe := service.Redis.TxPipeline()
	pipe.IncrBy(param.dailyLimitKey, successNum)
	pipe.Expire(param.dailyLimitKey, 24*time.Hour)
	_, err = pipe.Exec()
	if err != nil {
		logger.WithField("user_id", userID).Errorf("批量关注用户后增加每日关注数量出错：%v", err)
		// PASS
	}
	msg := fmt.Sprintf("成功关注 %d 位用户！", successNum)
	failedUserIDs := sets.Diff(sets.Diff(followUserIDs, alreadyFollowedUserIDs), succeedFollowUserIDs)
	return batchFollowResp{FailedUserIDs: failedUserIDs}, msg, nil
}

// newBatchFollowParam 构造批量关注用户接口参数
func newBatchFollowParam(c *handler.Context) (*batchFollowParam, error) {
	param := new(batchFollowParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	followUserIDs, err := util.SplitToInt64Array(param.UserIDsStr, ",")
	if err != nil || len(followUserIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	followUserIDs = sets.Uniq(followUserIDs)
	followUserIDsNum := int64(len(followUserIDs))
	if followUserIDsNum > followUserLimit {
		return nil, actionerrors.ErrBadRequest(handler.CodeUserFollowLimit, "已达到单批次关注数量上限~")
	}
	if followUserIDsNum == 0 {
		return nil, actionerrors.ErrParams
	}
	date := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	userID := c.UserID()
	dailyLimitKey := keys.KeyCounterBatchFollowUser2.Format(userID, date)
	dailyLimit, err := service.Redis.Get(dailyLimitKey).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if followUserDailyLimit-dailyLimit < followUserIDsNum {
		// 暂不考虑在还有部分余量时截取部分用户进行关注
		return nil, actionerrors.ErrBadRequest(handler.CodeUserFollowLimit, "已达到每日批量关注数量上限~")
	}
	param.userID = userID
	param.followUserIDs = followUserIDs
	param.dailyLimitKey = dailyLimitKey
	return param, nil
}
