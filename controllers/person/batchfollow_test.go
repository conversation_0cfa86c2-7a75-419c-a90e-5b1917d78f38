package person

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(int64(100), followUserLimit)
	assert.Equal(int64(200), followUserDailyLimit)
}

func clearTestData(userID int64) (string, error) {
	date := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	dailyLimitKey := keys.KeyCounterBatchFollowUser2.Format(userID, date)
	err := service.Redis.Del(dailyLimitKey).Err()
	return dailyLimitKey, err
}

func TestActionBatchFollow(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URIBlockStatusList, func(input interface{}) (interface{}, error) {
		return userapi.BlockStatusListResponse{
			UserBlockList: []int64{20240480, 20240482},
			BlockUserList: []int64{20240481, 20240482},
		}, nil
	})
	defer cancel()

	api := "/x/person/batch-follow"

	// 测试请求失败的情况
	errParam := []int64{70803, 71619}
	c := handler.NewTestContext(http.MethodPost, api, true, errParam)
	dailyLimitKey, err := clearTestData(c.UserID())
	require.NoError(err)
	_, _, err = ActionBatchFollow(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试全部关注成功
	param := &batchFollowParam{UserIDsStr: "18001,18002,18003"}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	data, msg, err := ActionBatchFollow(c)
	require.NoError(err)
	resp, ok := data.(batchFollowResp)
	require.True(ok)
	assert.Equal(batchFollowResp{FailedUserIDs: []int64{}}, resp)
	assert.Equal("成功关注 3 位用户！", msg)
	// 验证正常写入 databus
	pubMsg := service.Databus.AppLogPub.DebugPubMsgs()
	assert.Len(pubMsg, 3)
	// 验证每日关注限制数量加 3
	num, err := service.Redis.Get(dailyLimitKey).Int64()
	require.NoError(err)
	assert.Equal(int64(3), num)

	// 测试全部关注失败
	param = &batchFollowParam{UserIDsStr: "18001,18002,18003,12"}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	data, msg, err = ActionBatchFollow(c)
	require.NoError(err)
	resp, ok = data.(batchFollowResp)
	require.True(ok)
	// 用户 12 为当前登录用户
	assert.Equal(batchFollowResp{FailedUserIDs: []int64{12}}, resp)
	assert.Equal("成功关注 0 位用户！", msg)
}

func TestNewBatchFollowParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	api := "/x/person/batch-follow"

	// 测试参数为空
	var param batchFollowParam
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	dailyLimitKey, err := clearTestData(c.UserID())
	require.NoError(err)
	_, err = newBatchFollowParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数有误
	param.UserIDsStr = "哈哈哈哈哈哈"
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, err = newBatchFollowParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数正常
	param.UserIDsStr = "18001,18002,18003"
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	returnParam, err := newBatchFollowParam(c)
	require.NoError(err)
	assert.Equal([]int64{18001, 18002, 18003}, returnParam.followUserIDs)
	assert.Equal(dailyLimitKey, returnParam.dailyLimitKey)

	// 测试关注用户超出单次限制
	followUserID := int64(98)
	for followUserID > 0 {
		param.UserIDsStr += fmt.Sprintf(",%d", followUserID)
		followUserID--
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, err = newBatchFollowParam(c)
	assert.EqualError(err, "已达到单批次关注数量上限~")

	// 测试关注用户超出每日限制
	err = service.Redis.Set(dailyLimitKey, 191, 5*time.Minute).Err()
	require.NoError(err)
	param.UserIDsStr = "1,2,3,4,5,6,7,8,9,10"
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, err = newBatchFollowParam(c)
	assert.EqualError(err, "已达到每日批量关注数量上限~")
}
