package person

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

type avatarFrameParams struct {
	AvatarFrameID int64 `form:"avatar_frame_id" json:"avatar_frame_id"` // 头像框 ID

	userAvatarFrame *museravatarframemap.MUserAvatarFrameMap // 用户头像框信息
	isVipFrame      bool                                     // 是否为 vip 头像框
}

func lockUserAvatarFrame(userID int64) (func(), error) {
	lockKey := keys.LockUserAvatarFrame1.Format(userID)
	ok, err := service.Redis.SetNX(lockKey, "1", time.Minute).Result()
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, "操作频繁，请稍后再试")
	}
	return func() {
		err := service.Redis.Del(lockKey).Err()
		if err != nil {
			logger.Error(err)
			return
		}
	}, nil
}

func newAvatarFrameParams(c *handler.Context) (*avatarFrameParams, error) {
	param := new(avatarFrameParams)
	err := c.Bind(param)
	if err != nil || param.AvatarFrameID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 查询头像框信息
	avatarFrame, err := mavatarframe.FindOne(param.AvatarFrameID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if avatarFrame == nil {
		return nil, actionerrors.ErrAvatarFrameNotFound
	}
	param.isVipFrame = avatarFrame.Type == mavatarframe.TypeVip
	if param.isVipFrame {
		// 查询用户 vip 信息
		userVipInfo, err := muservip.GetUserVipInfo(c.UserID())
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if userVipInfo.Status != muservip.VipStatusInEffect {
			return nil, actionerrors.ErrAvatarFrameNotOwned
		}
	}
	// 查询用户头像框信息
	param.userAvatarFrame, err = museravatarframemap.FindUserAvatarFrame(c.UserID(), param.AvatarFrameID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.userAvatarFrame == nil && !param.isVipFrame {
		return nil, actionerrors.ErrAvatarFrameNotOwned
	}
	return param, nil
}

// ActionTakeoffAvatarFrame 卸下头像框
/**
 * @api {post} /x/person/takeoff-avatar-frame 卸下头像框
 * @apiDescription 接口保持幂等性，重复卸下依然提示“卸下成功”
 *
 * @apiVersion 0.1.0
 * @apiName takeoff-avatar-frame
 * @apiGroup /x/person/
 *
 * @apiPermission user
 *
 * @apiParam {Number} avatar_frame_id 头像框 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "卸下成功",
 *     "data": nil
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 * @apiError (400) {Object} data null
 *
 * @apiError (404) {Number} code 200160001
 * @apiError (404) {String} info 头像框不存在
 * @apiError (400) {Object} data null
 *
 * @apiError (400) {Number} code 200160002
 * @apiError (400) {String} info 未获得该头像框
 * @apiError (400) {Object} data null
 */
func ActionTakeoffAvatarFrame(c *handler.Context) (handler.ActionResponse, string, error) {
	unlock, err := lockUserAvatarFrame(c.UserID())
	if err != nil {
		return nil, "", err
	}
	defer unlock()

	param, err := newAvatarFrameParams(c)
	if err != nil {
		return nil, "", err
	}
	if param.isVipFrame && param.userAvatarFrame == nil {
		// 用户没有使用过 vip 头像框，直接返回卸下成功信息
		return nil, "卸下成功", nil
	}

	// 卸下头像框
	err = param.userAvatarFrame.SaveUserAvatarFrameStatus(museravatarframemap.StatusTakeoff)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return nil, "卸下成功", nil
}
