package person

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/mthirdpartytask"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestActionTask(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	date := now.Format(util.TimeFormatYMDWithNoSpace)
	key := keys.KeyThirdTaskToken2.Format(mthirdpartytask.SceneWechatOffiaccount, date)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	uri := "/x/person/task"
	token := "abcdabcdabcdabcdabcdabcdabcdabcc"
	// 参数错误
	params := taskParam{
		Gtype:          1,
		ThirdTaskToken: token,
	}
	c := handler.NewTestContext(http.MethodPost, uri, true, params)
	_, _, err = ActionTask(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 任务 token 已经失效
	params.Gtype = gtypeFollowWechatOffiaccount
	c = handler.NewTestContext(http.MethodPost, uri, true, params)
	c.User().ID = int64(1998)
	_, _, err = ActionTask(c)
	assert.EqualError(err, "任务链接已失效，请重新回复公众号获取链接~")

	// 正常完成任务
	c = handler.NewTestContext(http.MethodPost, uri, true, params)
	err = service.Redis.SAdd(key, token).Err()
	require.NoError(err)
	resp, _, err := ActionTask(c)
	require.NoError(err)
	result := resp.(*taskResp)
	assert.Equal(mthirdpartytask.StatusFinished, result.Status)

	// 不能重复完成任务
	c = handler.NewTestContext(http.MethodPost, uri, true, params)
	_, _, err = ActionTask(c)
	assert.EqualError(err, "任务链接已失效，请重新回复公众号获取链接~")

	// 重复完成任务
	err = service.Redis.SAdd(key, token).Err()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, uri, true, params)
	_, _, err = ActionTask(c)
	require.NoError(err)
	assert.Equal(mthirdpartytask.StatusFinished, result.Status)
}
