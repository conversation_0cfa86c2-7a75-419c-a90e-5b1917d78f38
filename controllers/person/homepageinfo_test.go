package person

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
)

func TestHomepageInfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(homepageInfoResp{}, "vip")
	kc.Check(vipInfo{}, "title", "subtitle", "status", "url")
}

func TestActionHomepageInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试 enableVipCenter 为 false 的情况
	config.Conf.Params.Vip.EnableVipCenter = false
	api := "/x/person/homepage-info"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	res, _, err := ActionHomepageInfo(c)
	require.NoError(err)
	assert.Nil(res)

	// 测试 enableVipCenter 为 true 的情况
	config.Conf.Params.Vip.EnableVipCenter = true
	require.NoError(muservip.MUserVip{}.DB().Delete("", "user_id = ?", testUserID).Error)
	res, _, err = ActionHomepageInfo(c)
	require.NoError(err)
	data, ok := res.(homepageInfoResp)
	require.True(ok)
	assert.Equal("开通会员，限时 9.9 元", data.Vip.Title)
	assert.Equal("畅听精品好剧，最多可领 155 钻石", data.Vip.Subtitle)
	assert.Equal(muservip.VipStatusNotYet, data.Vip.Status)
	assert.Equal("https://m.uat.missevan.com/vip", data.Vip.URL)
}

func TestGetVipInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试游客
	res, err := getVipInfo(0)
	require.NoError(err)
	assert.Equal("开通会员，每天领钻石", res.Title)
	assert.Equal("畅听精品好剧，最低 9.9 元/月", res.Subtitle)
	assert.Equal(muservip.VipStatusNotYet, res.Status)
	assert.Equal("https://m.uat.missevan.com/vip", res.URL)

	// 测试用户未开通过正式会员
	require.NoError(muservip.MUserVip{}.DB().Delete("", "user_id = ?", testUserID).Error)
	res, err = getVipInfo(testUserID)
	require.NoError(err)
	assert.Equal("开通会员，限时 9.9 元", res.Title)
	assert.Equal("畅听精品好剧，最多可领 155 钻石", res.Subtitle)
	assert.Equal(muservip.VipStatusNotYet, res.Status)
	assert.Equal("https://m.uat.missevan.com/vip", res.URL)

	// 测试用户会员生效中
	now := goutil.TimeNow()
	m := &muservip.MUserVip{
		UserID:  testUserID,
		Type:    4,
		EndTime: now.Add(time.Minute).Unix(),
	}
	require.NoError(m.DB().Create(m).Error)
	res, err = getVipInfo(testUserID)
	require.NoError(err)
	assert.Equal("会员权益生效中", res.Title)
	assert.Equal(fmt.Sprintf("%s 到期，每日可领 5 钻石", time.Unix(m.EndTime, 0).Format(goutil.TimeFormatYMD)), res.Subtitle)
	assert.Equal(muservip.VipStatusInEffect, res.Status)
	assert.Equal("https://m.uat.missevan.com/vip", res.URL)

	// 测试用户会员已过期
	require.NoError(m.DB().Where("user_id = ?", testUserID).Update("end_time", now.Add(-time.Second).Unix()).Error)
	res, err = getVipInfo(testUserID)
	require.NoError(err)
	assert.Equal("会员权益已过期，续费享众多福利", res.Title)
	assert.Equal("畅听精品好剧，每月最多可领 155 钻石", res.Subtitle)
	assert.Equal(muservip.VipStatusExpired, res.Status)
	assert.Equal("https://m.uat.missevan.com/vip", res.URL)
}
