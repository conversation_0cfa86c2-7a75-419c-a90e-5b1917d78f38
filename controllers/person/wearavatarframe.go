package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
)

// ActionWearAvatarFrame 佩戴头像框
/**
 * @api {post} /x/person/wear-avatar-frame 佩戴头像框
 *
 * @apiVersion 0.1.0
 * @apiName wear-avatar-frame
 * @apiGroup /x/person/
 *
 * @apiPermission user
 *
 * @apiParam {Number} avatar_frame_id 头像框 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "装扮成功",
 *     "data": nil
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 * @apiError (400) {Object} data null
 *
 * @apiError (404) {Number} code 200160001
 * @apiError (404) {String} info 头像框不存在
 * @apiError (400) {Object} data null
 *
 * @apiError (400) {Number} code 200160002
 * @apiError (400) {String} info 未获得该头像框
 * @apiError (400) {Object} data null
 */
func ActionWearAvatarFrame(c *handler.Context) (handler.ActionResponse, string, error) {
	unlock, err := lockUserAvatarFrame(c.UserID())
	if err != nil {
		return nil, "", err
	}
	defer unlock()

	param, err := newAvatarFrameParams(c)
	if err != nil {
		return nil, "", err
	}
	if param.isVipFrame && param.userAvatarFrame == nil {
		// 用户第一次佩戴 vip 头像框，需要插入一条佩戴记录
		param.userAvatarFrame = &museravatarframemap.MUserAvatarFrameMap{
			UserID:        c.UserID(),
			AvatarFrameID: param.AvatarFrameID,
			ExpireTime:    museravatarframemap.ExpireTimeForeverEffective, // 会员头像框不设置过期时间
			Status:        museravatarframemap.StatusWearing,
		}
	}
	if param.userAvatarFrame.ExpireTime != museravatarframemap.ExpireTimeForeverEffective &&
		param.userAvatarFrame.ExpireTime <= util.TimeNow().Unix() {
		return nil, "", actionerrors.ErrAvatarFrameExpired
	}

	// 装扮头像框
	err = param.userAvatarFrame.SaveUserAvatarFrameStatus(museravatarframemap.StatusWearing)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return nil, "装扮成功", nil
}
