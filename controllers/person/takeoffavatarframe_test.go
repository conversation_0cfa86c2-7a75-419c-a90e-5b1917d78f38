package person

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframemap"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	testAvatarFrameID    = 123
	testVipAvatarFrameID = 124
)

func TestNewAvatarFrameParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/x/person/takeoff-avatar-frame"
	param := avatarFrameParams{
		AvatarFrameID: -1,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	_, err := newAvatarFrameParams(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试头像框不存在
	param.AvatarFrameID = testAvatarFrameID
	require.NoError(mavatarframe.MAvatarFrame{}.DB().Delete("", "id IN (?)", []int64{testAvatarFrameID, testVipAvatarFrameID}).Error)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, err = newAvatarFrameParams(c)
	assert.Equal(actionerrors.ErrAvatarFrameNotFound, err)

	// 测试未获得普通头像框
	avatarFrames := []mavatarframe.MAvatarFrame{
		{ID: testAvatarFrameID},
		{
			ID:   testVipAvatarFrameID,
			Type: mavatarframe.TypeVip,
		},
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, avatarFrames[0].TableName(), avatarFrames))
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, err = newAvatarFrameParams(c)
	assert.Equal(actionerrors.ErrAvatarFrameNotOwned, err)

	// 测试获取参数
	data := &museravatarframemap.MUserAvatarFrameMap{
		UserID:        12,
		AvatarFrameID: testAvatarFrameID,
	}
	require.NoError(data.DB().Delete("", "user_id = ? AND avatar_frame_id = ?",
		data.UserID, testAvatarFrameID).Error)
	require.NoError(data.DB().Create(data).Error)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	res, err := newAvatarFrameParams(c)
	require.NoError(err)
	require.NotNil(res)
	assert.NotNil(res.userAvatarFrame)

	// 测试未获得 vip 头像框
	param.AvatarFrameID = testVipAvatarFrameID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	c.User().ID = 233333
	_, err = newAvatarFrameParams(c)
	assert.Equal(actionerrors.ErrAvatarFrameNotOwned, err)
}

func TestActionTakeoffAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/x/person/takeoff-avatar-frame"
	param := avatarFrameParams{
		AvatarFrameID: -2,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	_, _, err := ActionTakeoffAvatarFrame(c)
	assert.EqualError(err, "参数错误")

	// 测试卸下头像框
	avatarFrame := &mavatarframe.MAvatarFrame{
		ID: testAvatarFrameID,
	}
	require.NoError(avatarFrame.DB().Delete("", "id = ?", avatarFrame.ID).Error)
	require.NoError(avatarFrame.DB().Create(avatarFrame).Error)

	data := &museravatarframemap.MUserAvatarFrameMap{
		UserID:        12,
		AvatarFrameID: testAvatarFrameID,
		Status:        museravatarframemap.StatusWearing,
	}
	require.NoError(data.DB().Delete("", "user_id = ? AND avatar_frame_id = ?",
		data.UserID, testAvatarFrameID).Error)
	require.NoError(data.DB().Create(data).Error)

	param.AvatarFrameID = testAvatarFrameID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, message, err := ActionTakeoffAvatarFrame(c)
	require.NoError(err)
	assert.Equal("卸下成功", message)

	var data1 museravatarframemap.MUserAvatarFrameMap
	require.NoError(data1.DB().Where("user_id = ? AND avatar_frame_id = ?",
		data.UserID, data.AvatarFrameID).Take(&data1).Error)
	assert.EqualValues(12, data1.UserID)
	assert.EqualValues(testAvatarFrameID, data1.AvatarFrameID)
	assert.Equal(museravatarframemap.StatusTakeoff, data1.Status)

	// 测试重复卸下头像框
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, message, err = ActionTakeoffAvatarFrame(c)
	require.NoError(err)
	assert.Equal("卸下成功", message)

	var data2 museravatarframemap.MUserAvatarFrameMap
	require.NoError(data2.DB().Where("user_id = ? AND avatar_frame_id = ?",
		data.UserID, data.AvatarFrameID).Take(&data2).Error)
	assert.Equal(data1.Status, data2.Status)
}
