package person

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/mthirdpartytask"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// 任务
const (
	gtypeFollowWechatOffiaccount = 19 // 关注微信公众号任务
)

// taskParam 完成任务接口参数
type taskParam struct {
	Gtype          int    `json:"gtype" form:"gtype"`                       // 任务类型
	ThirdTaskToken string `json:"third_task_token" form:"third_task_token"` // 任务 token，32 位字符串
}

// taskResp 完成任务接口返回数据
type taskResp struct {
	Status int `json:"status" form:"status"` // 任务状态
}

// ActionTask 完成任务
/**
 * @api {post} /x/person/task 完成任务
 *
 * @apiVersion 0.1.0
 * @apiName task
 * @apiGroup /x/person/
 *
 * @apiPermission user
 *
 * @apiParam {number=19} gtype 任务类型。19: 关注公众号任务
 * @apiParam {String} third_task_token 任务 token
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "message": "任务完成",
 *     "data": {
 *       "status": 1 // 当前任务状态，1: 已完成；2: 已完成并已领奖
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} message 参数错误
 * @apiError (400) {Object} data null
 *
 * @apiError (400) {Number} code *********
 * @apiError (400) {String} message 任务链接已失效，请重新回复公众号获取链接~
 * @apiError (400) {Object} data null
 */
func ActionTask(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newTaskParam(c)
	if err != nil {
		return nil, "", err
	}
	now := util.TimeNow()
	date := now.Format(util.TimeFormatYMDWithNoSpace)
	key := keys.KeyThirdTaskToken2.Format(mthirdpartytask.SceneWechatOffiaccount, date)
	exists, err := service.Redis.SIsMember(key, param.ThirdTaskToken).Result()
	if err != nil {
		return nil, "", err
	}
	if !exists {
		return nil, "", actionerrors.ErrBadRequest(handler.CodeUnknownError, "任务链接已失效，请重新回复公众号获取链接~")
	}
	userID := c.UserID()
	taskInfo, err := mthirdpartytask.FindOne(userID, mthirdpartytask.SceneWechatOffiaccount, 0)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if taskInfo != nil && taskInfo.Status != mthirdpartytask.StatusOnGoing {
		return &taskResp{Status: taskInfo.Status}, "任务完成", nil
	}
	err = mthirdpartytask.FinishTask(userID, mthirdpartytask.SceneWechatOffiaccount, param.ThirdTaskToken)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	err = service.Redis.SRem(key, param.ThirdTaskToken).Err()
	if err != nil {
		logger.WithField("user_id", userID).Errorf("从 redis 移除用户第三方任务 token 失败，%v", err)
		// PASS
	}
	return &taskResp{Status: mthirdpartytask.StatusFinished}, "任务完成", nil
}

func newTaskParam(c *handler.Context) (*taskParam, error) {
	param := new(taskParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.Gtype != gtypeFollowWechatOffiaccount || param.ThirdTaskToken == "" || len(param.ThirdTaskToken) != mthirdpartytask.TokenLength {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}
