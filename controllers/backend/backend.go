package backend

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/security"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/controllers/backend/recommended"
)

// HandlerV2 returns the registered handlerV2
func HandlerV2(conf *config.Config) handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "backend/x",
		Middlewares: gin.HandlersChain{
			security.CSRFMiddleware(conf.HTTP.CSRFAllowTopDomains...),
			user.Middleware(),
		},
		SubHandlers: []handler.HandlerV2{
			recommendedHandlerV2(),
		},
	}
}

func recommendedHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "recommended",
		Middlewares: gin.HandlersChain{
			user.IsRole(
				role.OperationSeniorPlatformEdit, role.OperationSuperiorPlatformEdit,
				role.ManagerCommunity, role.OperationCommunityRole,
			),
		},
		Actions: map[string]*handler.ActionV2{
			"home-feed/add":       handler.NewActionV2(handler.POST, recommended.ActionHomeFeedAdd, handler.ActionOption{LoginRequired: true}),
			"home-feed/batch-add": handler.NewActionV2(handler.POST, recommended.ActionHomeFeedBatchAdd, handler.ActionOption{LoginRequired: true}),
			"home-feed/update":    handler.NewActionV2(handler.POST, recommended.ActionHomeFeedUpdate, handler.ActionOption{LoginRequired: true}),
			"home-feed/delete":    handler.NewActionV2(handler.POST, recommended.ActionHomeFeedDelete, handler.ActionOption{LoginRequired: true}),
		},
	}
}
