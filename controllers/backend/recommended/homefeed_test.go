package recommended

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/mrecommendedelements"
	"github.com/MiaoSiLa/missevan-main/models/mrecommendedexposurelevel"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestActionHomeFeedBatchAdd(t *testing.T) {
	// Mock 管理员日志 RPC 调用
	cancel := mrpc.SetMock("go://util/addadminlog", func(input any) (any, error) {
		return map[string]any{"code": 0}, nil
	})
	defer cancel()

	api := "/backend/x/recommended/home-feed/batch-add"

	t.Run("参数错误", func(t *testing.T) {
		assert := assert.New(t)
		c := handler.NewTestContext(http.MethodPost, api, false, nil)
		_, _, err := ActionHomeFeedBatchAdd(c)
		assert.Equal(actionerrors.ErrParams, err)
	})

	t.Run("元素 ID 列表为空", func(t *testing.T) {
		assert := assert.New(t)
		params := map[string]any{
			"element_type":      2,
			"element_ids":       "",
			"exposure_level_id": 1001,
			"start_time":        1640995200,
			"end_time":          1672531200,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, _, err := ActionHomeFeedBatchAdd(c)
		assert.Equal(actionerrors.ErrBadRequest(handler.CodeInvalidParam, "元素 ID 列表不能为空"), err)
	})

	t.Run("无效的元素 ID 格式", func(t *testing.T) {
		assert := assert.New(t)
		params := map[string]any{
			"element_type":      2,
			"element_ids":       "123,abc,456",
			"exposure_level_id": 1001,
			"start_time":        1640995200,
			"end_time":          1672531200,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, _, err := ActionHomeFeedBatchAdd(c)
		assert.Equal(actionerrors.ErrBadRequest(handler.CodeInvalidParam, "无效的元素 ID: abc"), err)
	})

	t.Run("元素类型错误", func(t *testing.T) {
		assert := assert.New(t)
		params := map[string]any{
			"element_type":      999,
			"element_ids":       "123,456",
			"exposure_level_id": 1001,
			"start_time":        1640995200,
			"end_time":          1672531200,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, _, err := ActionHomeFeedBatchAdd(c)
		assert.Equal(actionerrors.ErrBadRequest(handler.CodeInvalidParam, "不支持的元素类型"), err)
	})

	t.Run("时间范围错误", func(t *testing.T) {
		assert := assert.New(t)
		params := map[string]any{
			"element_type":      2,
			"element_ids":       "8",
			"exposure_level_id": 1001,
			"start_time":        1640995200,
			"end_time":          1609459200,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, _, err := ActionHomeFeedBatchAdd(c)
		assert.Equal(actionerrors.ErrBadRequest(handler.CodeInvalidParam, "开始时间需小于结束时间"), err)
	})

	t.Run("批量添加成功", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 创建测试曝光等级数据
		exposureLevel := &mrecommendedexposurelevel.Model{
			CreateTime:   goutil.TimeNow().Unix(),
			ModifiedTime: goutil.TimeNow().Unix(),
			Exposure:     100,
			Level:        "测试批量等级",
			Status:       mrecommendedexposurelevel.StatusEnabled,
			Scene:        mrecommendedexposurelevel.SceneHome,
		}
		err := service.DB.Create(exposureLevel).Error
		require.NoError(err)
		// 清理测试曝光等级数据
		defer func() {
			require.NoError(service.DB.Delete(exposureLevel).Error)
		}()

		params := map[string]any{
			"element_type":      mrecommendedelements.ElementTypeDrama,
			"element_ids":       "7,8", // 使用测试数据中符合条件的剧集 ID（都有 ip_id = 1）
			"exposure_level_id": exposureLevel.ID,
			"start_time":        1640995200,
			"end_time":          1672531200,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, msg, err := ActionHomeFeedBatchAdd(c)
		require.NoError(err)
		assert.Equal("批量创建成功", msg)

		// 验证数据库中是否正确创建了记录
		var createdElements []mrecommendedelements.MRecommendedElement
		err = mrecommendedelements.MRecommendedElement{}.DB().Where(
			"element_type = ? AND element_id IN (?, ?) AND archive = ? AND module_type = ?",
			mrecommendedelements.ElementTypeDrama, 7, 8, mrecommendedelements.ArchiveOnline, mrecommendedelements.ModuleTypeHomeFeed).
			Find(&createdElements).Error
		require.NoError(err)
		require.Len(createdElements, 2)

		// 清理测试数据
		defer func() {
			for _, element := range createdElements {
				require.NoError(element.DB().Delete(&element).Error)
			}
		}()

		for _, element := range createdElements {
			assert.Equal(mrecommendedelements.ModuleTypeHomeFeed, element.ModuleType)
			assert.Equal(mrecommendedelements.ElementTypeDrama, element.ElementType)
			assert.Contains([]int64{7, 8}, element.ElementID)
			assert.Equal(exposureLevel.ID, element.More.ExposureLevelID)
			assert.Equal(mrecommendedelements.ArchiveOnline, element.Archive)
		}
	})
}

func TestParseAndValidateElementIDs(t *testing.T) {
	testCases := []struct {
		name          string
		elementIDsStr string
		expectedIDs   []int64
		wantErr       error
	}{
		{
			name:          "空字符串",
			elementIDsStr: "",
			expectedIDs:   nil,
			wantErr:       actionerrors.ErrBadRequest(handler.CodeInvalidParam, "元素 ID 列表不能为空"),
		},
		{
			name:          "单个 ID",
			elementIDsStr: "123",
			expectedIDs:   []int64{123},
			wantErr:       nil,
		},
		{
			name:          "多个 ID",
			elementIDsStr: "123,456,789",
			expectedIDs:   []int64{123, 456, 789},
			wantErr:       nil,
		},
		{
			name:          "包含空格的 ID",
			elementIDsStr: " 123 , 456 , 789 ",
			expectedIDs:   []int64{123, 456, 789},
			wantErr:       nil,
		},
		{
			name:          "包含无效的 ID",
			elementIDsStr: "123,abc,456",
			expectedIDs:   nil,
			wantErr:       actionerrors.ErrBadRequest(handler.CodeInvalidParam, "无效的元素 ID: abc"),
		},
		{
			name:          "包含负数 ID",
			elementIDsStr: "123,-456,789",
			expectedIDs:   nil,
			wantErr:       actionerrors.ErrBadRequest(handler.CodeInvalidParam, "无效的元素 ID: -456"),
		},
		{
			name:          "包含零 ID",
			elementIDsStr: "123,0,789",
			expectedIDs:   nil,
			wantErr:       actionerrors.ErrBadRequest(handler.CodeInvalidParam, "无效的元素 ID: 0"),
		},
		{
			name:          "忽略空的 ID",
			elementIDsStr: "123,,456,",
			expectedIDs:   []int64{123, 456},
			wantErr:       nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)
			require := require.New(t)
			ids, err := parseAndValidateElementIDs(tc.elementIDsStr)
			if tc.wantErr != nil {
				require.Equal(tc.wantErr, err)
				assert.Nil(ids)
			} else {
				require.NoError(err)
				assert.Equal(tc.expectedIDs, ids)
			}
		})
	}
}

func TestActionHomeFeedUpdate(t *testing.T) {
	// Mock 管理员日志 RPC 调用
	cancel := mrpc.SetMock("go://util/addadminlog", func(input any) (any, error) {
		return map[string]any{"code": 0}, nil
	})
	defer cancel()

	// 准备测试数据
	now := goutil.TimeNow().Unix()

	// 创建测试元素
	element := mrecommendedelements.MRecommendedElement{
		ID:          1002,
		CreateTime:  now,
		UpdateTime:  now,
		ModuleType:  mrecommendedelements.ModuleTypeHomeFeed,
		ElementType: mrecommendedelements.ElementTypeDrama,
		ElementID:   52354,
		StartTime:   0,
		EndTime:     0,
		Archive:     mrecommendedelements.ArchiveOnline,
		More: &mrecommendedelements.MoreDetails{
			ExposureLevelID: 1,
		},
	}
	err := element.DB().Create(&element).Error
	require.NoError(t, err)

	// 测试用例执行后清理测试数据
	defer func() {
		err = element.DB().Delete(&element).Error
		require.NoError(t, err)
	}()

	api := "/backend/x/recommended/home-feed/update"

	t.Run("参数错误", func(t *testing.T) {
		assert := assert.New(t)
		c := handler.NewTestContext(http.MethodPost, api, false, nil)
		_, _, err := ActionHomeFeedUpdate(c)
		assert.Equal(actionerrors.ErrParams, err)
	})

	t.Run("ID 不存在", func(t *testing.T) {
		assert := assert.New(t)
		params := map[string]any{
			"id":                9999,
			"element_type":      5,
			"element_id":        1002,
			"exposure_level_id": 1002,
			"start_time":        1640995200,
			"end_time":          1672531200,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, _, err := ActionHomeFeedUpdate(c)
		assert.Equal(actionerrors.ErrBadRequest(handler.CodeInvalidParam, "未找到指定的干预卡"), err)
	})

	t.Run("成功更新", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		startTime := goutil.TimeNow().Unix()
		endTime := startTime + 86400 // 一天后
		params := map[string]any{
			"id":                1002,
			"element_type":      5,
			"element_id":        1002,
			"exposure_level_id": 1002,
			"start_time":        startTime,
			"end_time":          endTime,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, msg, err := ActionHomeFeedUpdate(c)
		require.NoError(err)
		assert.Equal("更新成功", msg)

		var updatedElement mrecommendedelements.MRecommendedElement
		err = updatedElement.DB().Where("id = ?", 1002).Take(&updatedElement).Error
		require.NoError(err)
		assert.Equal(mrecommendedelements.ElementTypeLive, updatedElement.ElementType)
		assert.Equal(int64(1002), updatedElement.ElementID)
		assert.Equal(startTime, updatedElement.StartTime)
		assert.Equal(endTime, updatedElement.EndTime)
	})
}

func TestActionHomeFeedDelete(t *testing.T) {
	// Mock 管理员日志 RPC 调用
	cancel := mrpc.SetMock("go://util/addadminlog", func(input any) (any, error) {
		return map[string]any{"code": 0}, nil
	})
	defer cancel()

	// 准备测试数据
	now := goutil.TimeNow().Unix()

	// 创建测试元素
	element := mrecommendedelements.MRecommendedElement{
		ID:          1003,
		CreateTime:  now,
		UpdateTime:  now,
		ModuleType:  mrecommendedelements.ModuleTypeHomeFeed,
		ElementType: mrecommendedelements.ElementTypeDrama,
		ElementID:   52354,
		StartTime:   0,
		EndTime:     0,
		Archive:     mrecommendedelements.ArchiveOnline,
		More: &mrecommendedelements.MoreDetails{
			ExposureLevelID: 1,
		},
	}
	err := element.DB().Create(&element).Error
	require.NoError(t, err)

	// 测试用例执行后清理测试数据
	defer func() {
		err = element.DB().Delete(&element).Error
		require.NoError(t, err)
	}()

	api := "/backend/x/recommended/home-feed/delete"

	t.Run("参数错误", func(t *testing.T) {
		assert := assert.New(t)
		c := handler.NewTestContext(http.MethodPost, api, false, nil)
		_, _, err := ActionHomeFeedDelete(c)
		assert.Equal(actionerrors.ErrParams, err)
	})

	t.Run("ID 不存在", func(t *testing.T) {
		assert := assert.New(t)
		params := map[string]any{
			"id": 9999,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, _, err := ActionHomeFeedDelete(c)
		assert.Equal(actionerrors.ErrBadRequest(handler.CodeInvalidParam, "未找到指定的干预卡"), err)
	})

	t.Run("成功删除", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		params := map[string]any{
			"id": 1003,
		}
		c := handler.NewTestContext(http.MethodPost, api, false, params)
		_, msg, err := ActionHomeFeedDelete(c)
		require.NoError(err)
		assert.Equal("删除成功", msg)

		var deletedElement mrecommendedelements.MRecommendedElement
		err = deletedElement.DB().Where("id = ?", 1003).Take(&deletedElement).Error
		require.NoError(err)
		assert.Equal(mrecommendedelements.ArchiveHistory, deletedElement.Archive)
	})
}

func TestValidateTimeRange(t *testing.T) {
	testCases := []struct {
		name      string
		startTime int64
		endTime   int64
		wantErr   error
	}{
		{
			name:      "开始时间大于结束时间",
			startTime: 1640995200,
			endTime:   1609459200,
			wantErr:   actionerrors.ErrBadRequest(handler.CodeInvalidParam, "开始时间需小于结束时间"),
		},
		{
			name:      "开始时间等于结束时间",
			startTime: 1640995200,
			endTime:   1640995200,
			wantErr:   actionerrors.ErrBadRequest(handler.CodeInvalidParam, "开始时间需小于结束时间"),
		},
		{
			name:      "开始时间小于结束时间",
			startTime: 1609459200,
			endTime:   1640995200,
			wantErr:   nil,
		},
		{
			name:      "开始时间为 0",
			startTime: 0,
			endTime:   1640995200,
			wantErr:   actionerrors.ErrBadRequest(handler.CodeInvalidParam, "开始时间不能为空"),
		},
		{
			name:      "结束时间为 0",
			startTime: 1609459200,
			endTime:   0,
			wantErr:   actionerrors.ErrBadRequest(handler.CodeInvalidParam, "结束时间不能为空"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)
			err := validateTimeRange(tc.startTime, tc.endTime)
			assert.Equal(tc.wantErr, err)
		})
	}
}

func TestValidateElementType(t *testing.T) {
	testCases := []struct {
		name        string
		elementType int
		wantErr     error
	}{
		{
			name:        "有效的剧集元素类型",
			elementType: mrecommendedelements.ElementTypeDrama,
			wantErr:     nil,
		},
		{
			name:        "有效的直播元素类型",
			elementType: mrecommendedelements.ElementTypeLive,
			wantErr:     nil,
		},
		{
			name:        "无效的元素类型",
			elementType: 999,
			wantErr:     actionerrors.ErrBadRequest(handler.CodeInvalidParam, "不支持的元素类型"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)
			err := validateElementType(tc.elementType)
			assert.Equal(tc.wantErr, err)
		})
	}
}

func TestCreateHomeFeedElementsTimestamp(t *testing.T) {
	now := time.Date(2023, 12, 1, 12, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	// 创建测试曝光等级数据
	exposureLevel := &mrecommendedexposurelevel.Model{
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		Exposure:     100,
		Level:        "测试时间戳等级",
		Status:       mrecommendedexposurelevel.StatusEnabled,
		Scene:        mrecommendedexposurelevel.SceneHome,
	}
	err := service.DB.Create(exposureLevel).Error
	require.NoError(t, err)
	// 清理测试曝光等级数据
	defer func() {
		require.NoError(t, service.DB.Delete(exposureLevel).Error)
	}()

	params := &HomeFeedBatchAddParams{
		ElementType:     mrecommendedelements.ElementTypeDrama,
		StartTime:       now.Unix(),
		EndTime:         now.Add(time.Hour).Unix(),
		ExposureLevelID: exposureLevel.ID,
		elementIDs:      []int64{7, 8}, // 使用测试数据中的有效剧集 ID
	}

	err = params.createHomeFeedElements()
	require.NoError(t, err)

	// 验证数据库中创建的记录的时间戳
	var createdElements []mrecommendedelements.MRecommendedElement
	err = mrecommendedelements.MRecommendedElement{}.DB().Where(
		"element_type = ? AND element_id IN (?, ?) AND archive = ? AND module_type = ?",
		mrecommendedelements.ElementTypeDrama, 7, 8, mrecommendedelements.ArchiveOnline, mrecommendedelements.ModuleTypeHomeFeed).
		Find(&createdElements).Error
	require.NoError(t, err)
	require.Len(t, createdElements, 2)

	// 清理测试数据
	defer func() {
		for _, element := range createdElements {
			require.NoError(t, element.DB().Delete(&element).Error)
		}
	}()

	// 验证时间戳设置
	expectedTimestamp := now.Unix()
	for i, element := range createdElements {
		assert.Equal(t, expectedTimestamp, element.CreateTime, "Element %d should have mocked CreateTime", i)
		assert.Equal(t, expectedTimestamp, element.UpdateTime, "Element %d should have mocked UpdateTime", i)
		assert.Equal(t, element.CreateTime, element.UpdateTime, "Element %d CreateTime should equal UpdateTime", i)
	}

	// 验证同一批次的元素具有相同的时间戳
	if len(createdElements) > 1 {
		firstTimestamp := createdElements[0].CreateTime
		for i, element := range createdElements {
			assert.Equal(t, firstTimestamp, element.CreateTime, "Element %d should have same CreateTime", i)
			assert.Equal(t, firstTimestamp, element.UpdateTime, "Element %d should have same UpdateTime", i)
		}
	}
}
