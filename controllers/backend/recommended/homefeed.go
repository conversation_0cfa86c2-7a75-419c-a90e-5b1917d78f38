package recommended

import (
	"fmt"
	"slices"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/live"
	"github.com/MiaoSiLa/missevan-main/models/mrecommendedelements"
	"github.com/MiaoSiLa/missevan-main/models/mrecommendedexposurelevel"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

// MaxBatchElementCount 跟业务约定的单次批量操作创建、修改的元素数量上限，避免错误操作导致一次性提交大量脏数据
const MaxBatchElementCount = 1000

// HomeFeedAddParams 添加首页 Feed 流干预卡参数
type HomeFeedAddParams struct {
	ElementType     int   `json:"element_type" form:"element_type"`           // 元素类型，2：剧集，5：直播
	ElementID       int64 `json:"element_id" form:"element_id"`               // 元素 ID，对应剧集 ID 或主播 ID
	ExposureLevelID int64 `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64 `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64 `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionHomeFeedAdd 添加首页 Feed 流干预卡
/**
 * @api {post} /backend/x/recommended/home-feed/add 添加首页 Feed 流干预卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {number=2,5} element_type 元素类型，2：剧集，5：直播
 * @apiParam {Number} element_id 元素 ID
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "添加成功",
 *     "data": null
 *   }
 */
func ActionHomeFeedAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	params, err := newHomeFeedAddParams(c)
	if err != nil {
		return nil, "", err
	}

	element := mrecommendedelements.NewHomeFeedElement(
		params.ElementType,
		params.ElementID,
		params.StartTime,
		params.EndTime,
		params.ExposureLevelID,
	)
	err = element.Create()
	if err != nil {
		err2 := fmt.Errorf("创建首页 Feed 流干预卡失败: %v", err)
		return nil, "", actionerrors.ErrServerInternal(err2, logger.Fields{
			"element_type":      params.ElementType,
			"element_id":        params.ElementID,
			"exposure_level_id": params.ExposureLevelID,
		})
	}

	intro := fmt.Sprintf(
		"添加首页 Feed 流干预卡，元素类型: %d, 元素 ID: %d, 曝光等级 ID: %d",
		params.ElementType, params.ElementID, params.ExposureLevelID,
	)
	logAdminOperation(c, intro)

	return nil, "添加成功", nil
}

func newHomeFeedAddParams(c *handler.Context) (*HomeFeedAddParams, error) {
	var params HomeFeedAddParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = validateExposureLevelExists(params.ExposureLevelID)
	if err != nil {
		return nil, err
	}
	err = validateTimeRange(params.StartTime, params.EndTime)
	if err != nil {
		return nil, err
	}
	_, err = validateElementTypeIDs(params.ElementType, []int64{params.ElementID})
	if err != nil {
		return nil, err
	}

	return &params, nil
}

// HomeFeedBatchAddParams 批量添加首页 Feed 流干预卡参数
type HomeFeedBatchAddParams struct {
	ElementType     int    `json:"element_type" form:"element_type"`           // 元素类型，2：剧集，5：直播
	ElementIDs      string `json:"element_ids" form:"element_ids"`             // 元素 ID 列表，以英文逗号分隔，最多 1000 个
	ExposureLevelID int64  `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64  `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64  `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒

	elementIDs []int64
}

// ActionHomeFeedBatchAdd 批量添加首页 Feed 流干预卡
/**
 * @api {post} /backend/x/recommended/home-feed/batch-add 批量添加首页 Feed 流干预卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {number=2,5} element_type 元素类型，2：剧集，5：直播
 * @apiParam {String} element_ids 元素 ID 列表，以英文逗号分隔，最多 1000 个
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "批量创建成功",
 *     "data": null
 *   }
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 400,
 *     "message": "789: 不支持的剧集分类, 790: 直播间不存在",
 *     "data": null
 *   }
 */
func ActionHomeFeedBatchAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	params, err := newHomeFeedBatchAddParams(c)
	if err != nil {
		return nil, "", err
	}

	err = params.createHomeFeedElements()
	if err != nil {
		return nil, "", err
	}

	intro := fmt.Sprintf(
		"批量添加首页 Feed 流干预卡，元素类型: %d, 元素数量: %d, 曝光等级 ID: %d",
		params.ElementType, len(params.elementIDs), params.ExposureLevelID,
	)
	logAdminOperation(c, intro)

	return nil, "批量创建成功", nil
}

func newHomeFeedBatchAddParams(c *handler.Context) (*HomeFeedBatchAddParams, error) {
	var params HomeFeedBatchAddParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	err = validateTimeRange(params.StartTime, params.EndTime)
	if err != nil {
		return nil, err
	}

	err = validateExposureLevelExists(params.ExposureLevelID)
	if err != nil {
		return nil, err
	}

	err = validateElementType(params.ElementType)
	if err != nil {
		return nil, err
	}

	params.elementIDs, err = parseAndValidateElementIDs(params.ElementIDs)
	if err != nil {
		return nil, err
	}

	return &params, nil
}

func parseAndValidateElementIDs(elementIDsStr string) ([]int64, error) {
	if elementIDsStr == "" {
		return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, "元素 ID 列表不能为空")
	}

	idStrings := strings.Split(elementIDsStr, ",")
	if len(idStrings) > MaxBatchElementCount {
		return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, fmt.Sprintf("元素 ID 数量不能超过 %d 个", MaxBatchElementCount))
	}

	elementIDs := make([]int64, 0, len(idStrings))
	var invalidIDStrings []string

	for _, idStr := range idStrings {
		idStr = strings.TrimSpace(idStr)
		if idStr == "" {
			continue
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil || id <= 0 {
			invalidIDStrings = append(invalidIDStrings, idStr)
			continue
		}
		elementIDs = append(elementIDs, id)
	}

	if len(invalidIDStrings) > 0 {
		return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, "无效的元素 ID: "+strings.Join(invalidIDStrings, ", "))
	}

	if len(elementIDs) == 0 {
		return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, "至少需要一个有效的元素 ID")
	}

	return elementIDs, nil
}

func (p *HomeFeedBatchAddParams) createHomeFeedElements() error {
	invalidElements, err := validateElementTypeIDs(p.ElementType, p.elementIDs)
	if err != nil {
		return err
	}

	if len(invalidElements) > 0 {
		errorMessage := formatValidationErrors(invalidElements)
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, errorMessage)
	}

	now := goutil.TimeNow().Unix()
	validElements := make([]*mrecommendedelements.MRecommendedElement, 0, len(p.elementIDs))
	for _, elementID := range p.elementIDs {
		elem := mrecommendedelements.NewHomeFeedElement(
			p.ElementType,
			elementID,
			p.StartTime,
			p.EndTime,
			p.ExposureLevelID,
		)
		elem.CreateTime = now
		elem.UpdateTime = now
		validElements = append(validElements, elem)
	}

	if len(validElements) > 0 {
		err := mrecommendedelements.BatchInsert(validElements)
		if err != nil {
			return actionerrors.ErrServerInternal(fmt.Errorf("批量插入失败: %v", err), nil)
		}
	}

	return nil
}

// HomeFeedUpdateParams 修改首页 Feed 流干预卡参数
type HomeFeedUpdateParams struct {
	ID              int64 `json:"id" form:"id"`                               // 卡片 ID
	ElementType     int   `json:"element_type" form:"element_type"`           // 元素类型，2：剧集，5：直播
	ElementID       int64 `json:"element_id" form:"element_id"`               // 元素 ID，对应剧集 ID 或主播 ID
	ExposureLevelID int64 `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64 `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64 `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionHomeFeedUpdate 修改首页 Feed 流干预卡
/**
 * @api {post} /backend/x/recommended/home-feed/update 修改首页 Feed 流干预卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} id 卡片 ID
 * @apiParam {number=2,5} element_type 元素类型，2：剧集，5：直播
 * @apiParam {Number} element_id 元素 ID，对应剧集 ID 或主播 ID
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "更新成功",
 *     "data": null
 *   }
 */
func ActionHomeFeedUpdate(c *handler.Context) (handler.ActionResponse, string, error) {
	params, err := newHomeFeedUpdateParams(c)
	if err != nil {
		return nil, "", err
	}

	// 检查记录是否存在
	element, err := mrecommendedelements.FindByID(params.ID, mrecommendedelements.ModuleTypeHomeFeed)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, "", actionerrors.ErrBadRequest(handler.CodeInvalidParam, "未找到指定的干预卡")
	}

	now := goutil.TimeNow().Unix()
	updates := map[string]any{
		"update_time":  now,
		"element_type": params.ElementType,
		"element_id":   params.ElementID,
		"start_time":   params.StartTime,
		"end_time":     params.EndTime,
		"more": mrecommendedelements.MoreDetails{
			ExposureLevelID: params.ExposureLevelID,
		},
	}

	logFields := logger.Fields{
		"id":                params.ID,
		"element_type":      params.ElementType,
		"element_id":        params.ElementID,
		"exposure_level_id": params.ExposureLevelID,
	}

	db := element.DB().
		Where(
			"id = ? AND archive = ? AND module_type = ?",
			params.ID, mrecommendedelements.ArchiveOnline, mrecommendedelements.ModuleTypeHomeFeed,
		).
		Updates(updates)
	if db.Error != nil {
		err2 := fmt.Errorf("修改首页 Feed 流干预卡失败: %v", db.Error)
		return nil, "", actionerrors.ErrServerInternal(err2, logFields)
	}
	if db.RowsAffected == 0 {
		err2 := fmt.Errorf("修改首页 Feed 流干预卡失败")
		return nil, "", actionerrors.ErrServerInternal(err2, logFields)
	}

	intro := fmt.Sprintf(
		"修改首页 Feed 流干预卡，卡片 ID: %d, 元素类型: %d, 元素 ID: %d, 曝光等级 ID: %d",
		params.ID, params.ElementType, params.ElementID, params.ExposureLevelID,
	)
	logAdminOperation(c, intro)

	return nil, "更新成功", nil
}

func newHomeFeedUpdateParams(c *handler.Context) (*HomeFeedUpdateParams, error) {
	var params HomeFeedUpdateParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if params.ID <= 0 {
		return nil, actionerrors.ErrParams
	}
	err = validateTimeRange(params.StartTime, params.EndTime)
	if err != nil {
		return nil, err
	}
	err = validateExposureLevelExists(params.ExposureLevelID)
	if err != nil {
		return nil, err
	}
	invalidElements, err := validateElementTypeIDs(params.ElementType, []int64{params.ElementID})
	if err != nil {
		return nil, err
	}
	if len(invalidElements) > 0 {
		errorMessage := formatValidationErrors(invalidElements)
		return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, errorMessage)
	}

	return &params, nil
}

// HomeFeedDeleteParams 删除首页 Feed 流干预卡参数
type HomeFeedDeleteParams struct {
	ID int64 `json:"id" form:"id"` // 卡片 ID
}

// ActionHomeFeedDelete 删除首页 Feed 流干预卡
/**
 * @api {post} /backend/x/recommended/home-feed/delete 删除首页 Feed 流干预卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} id 卡片 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 */
func ActionHomeFeedDelete(c *handler.Context) (handler.ActionResponse, string, error) {
	params, err := newHomeFeedDeleteParams(c)
	if err != nil {
		return nil, "", err
	}

	element, err := mrecommendedelements.FindByID(params.ID, mrecommendedelements.ModuleTypeHomeFeed)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, "", actionerrors.ErrBadRequest(handler.CodeInvalidParam, "未找到指定的干预卡")
	}

	err = element.Delete()
	if err != nil {
		err2 := fmt.Errorf("删除首页 Feed 流干预卡失败: %v", err)
		return nil, "", actionerrors.ErrServerInternal(err2, logger.Fields{
			"id": params.ID,
		})
	}

	intro := fmt.Sprintf(
		"删除首页 Feed 流干预卡，卡片 ID: %d, 元素类型: %d, 元素 ID: %d",
		params.ID, element.ElementType, element.ElementID,
	)
	logAdminOperation(c, intro)

	return nil, "删除成功", nil
}

func newHomeFeedDeleteParams(c *handler.Context) (*HomeFeedDeleteParams, error) {
	var params HomeFeedDeleteParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if params.ID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return &params, nil
}

func logAdminOperation(c *handler.Context, intro string) {
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageHomeFeedCard, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func validateExposureLevelExists(exposureLevelID int64) error {
	if exposureLevelID <= 0 {
		return actionerrors.ErrParams
	}
	model, err := mrecommendedexposurelevel.FindEnabledByID(exposureLevelID, mrecommendedexposurelevel.SceneHome)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if model == nil {
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, "未找到指定的曝光等级")
	}
	return nil
}

func validateTimeRange(startTime int64, endTime int64) error {
	if startTime <= 0 {
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, "开始时间不能为空")
	}
	if endTime <= 0 {
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, "结束时间不能为空")
	}
	if startTime >= endTime {
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, "开始时间需小于结束时间")
	}
	return nil
}

type elementValidationError struct {
	ID      int64  `json:"id"`
	Message string `json:"message"`
}

func validateElementTypeIDs(elementType int, elementIDs []int64) ([]elementValidationError, error) {
	err := validateElementType(elementType)
	if err != nil {
		return nil, err
	}

	invalidElements := make([]elementValidationError, 0, len(elementIDs))
	for _, elementID := range elementIDs {
		if elementID <= 0 {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      elementID,
				Message: "无效的元素 ID",
			})
		}
	}

	if len(invalidElements) > 0 {
		return invalidElements, nil
	}

	switch elementType {
	case mrecommendedelements.ElementTypeDrama:
		return validateDramaElements(elementIDs)
	case mrecommendedelements.ElementTypeLive:
		return validateLiveElements(elementIDs)
	default:
		return nil, nil
	}
}

var validElementTypes = []int{
	mrecommendedelements.ElementTypeLive,
	mrecommendedelements.ElementTypeDrama,
}

func formatValidationErrors(errors []elementValidationError) string {
	var messages []string
	for _, err := range errors {
		messages = append(messages, fmt.Sprintf("%d: %s", err.ID, err.Message))
	}
	return strings.Join(messages, ", ")
}

func validateElementType(elementType int) error {
	if !slices.Contains(validElementTypes, elementType) {
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, "不支持的元素类型")
	}
	return nil
}

func validateDramaElements(dramaIDs []int64) ([]elementValidationError, error) {
	if len(dramaIDs) == 0 {
		return nil, nil
	}

	validDramaMap, err := dramainfo.MapDramaInfoByIDs(dramaIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	invalidElements := make([]elementValidationError, 0, len(dramaIDs))
	allowedCatalogs := []int64{
		// CabalogIDAudioBook 的子分区
		dramainfo.CatalogIDAudioBookLightNovel,
		dramainfo.CatalogIDAudioBookNetwork,

		// CatalogIDCartoon 的子分区
		dramainfo.CatalogIDCnCartoon,
		dramainfo.CatalogIDJapanAudioComics,

		dramainfo.CatalogIDCnRadioDrama,
	}

	for _, dramaID := range dramaIDs {
		drama, exists := validDramaMap[dramaID]
		if !exists {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      dramaID,
				Message: "剧集 ID 对应剧集不存在",
			})
			continue
		}

		if drama.Checked != dramainfo.CheckedPass {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      dramaID,
				Message: "未通过审核/下架的剧集不能添加到首页 Feed 流",
			})
			continue
		}

		if drama.Refined.IsSet(dramainfo.RefinedRisking) {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      dramaID,
				Message: "擦边剧集不能添加到首页 Feed 流",
			})
			continue
		}

		if drama.IPID == 0 {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      dramaID,
				Message: "非精品版权剧集不能添加到首页 Feed 流",
			})
			continue
		}

		if !slices.Contains(allowedCatalogs, drama.Catalog) {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      dramaID,
				Message: "不支持的剧集分类",
			})
			continue
		}
	}

	return invalidElements, nil
}

func validateLiveElements(creatorIDs []int64) ([]elementValidationError, error) {
	if len(creatorIDs) == 0 {
		return nil, nil
	}

	validLiveMap, err := live.MapLiveRooms(creatorIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	invalidElements := make([]elementValidationError, 0, len(creatorIDs))
	for _, creatorID := range creatorIDs {
		liveRoom, exists := validLiveMap[creatorID]
		if !exists {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      creatorID,
				Message: "主播 ID 对应的直播间不存在",
			})
			continue
		}

		if liveRoom.Status == live.StatusUserDeleted {
			invalidElements = append(invalidElements, elementValidationError{
				ID:      creatorID,
				Message: "主播已注销",
			})
			continue
		}
	}

	return invalidElements, nil
}
