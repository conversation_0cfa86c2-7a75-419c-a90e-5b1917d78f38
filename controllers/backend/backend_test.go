package backend

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)

	conf := new(config.Config)
	h := HandlerV2(conf)
	assert.Equal("backend/x", h.Name)
	assert.Len(h.Middlewares, 2)
	assert.Len(h.SubHandlers, 1)
}

func TestRecommendedHandlerV2(t *testing.T) {
	assert := assert.New(t)

	handler := recommendedHandlerV2()
	assert.Equal(handler.Name, "recommended")
	assert.Len(handler.Middlewares, 1)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(handler.Actions, "home-feed/add", "home-feed/batch-add", "home-feed/update", "home-feed/delete")
}
