package mpersonamoduleelement

import (
	"encoding/json"
	"regexp"
	"strings"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/models/youmightlikemodule"
	"github.com/MiaoSiLa/missevan-main/service"
)

// MPersonaModuleElement model
type MPersonaModuleElement struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间戳，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"` // 更新时间戳，单位：秒
	ModuleID     int64  `gorm:"column:module_id"`     // 模块 ID
	PersonaID    int64  `gorm:"column:persona_id"`    // 用户画像 ID
	ElementType  int    `gorm:"column:element_type"`  // 元素类型
	ElementID    int64  `gorm:"column:element_id"`    // 元素 ID
	Summary      string `gorm:"column:summary"`       // 剧集或音单元素摘要介绍
	Cover        string `gorm:"column:cover"`         // 剧集或音单自定义封面图
	Sort         int64  `gorm:"column:sort"`          // 排序，0 为隐藏元素
}

// sortModuleHidden 隐藏模块的 sort 值
const sortModuleHidden = 0

// 模块下元素类型
const (
	ElementTypeAlbum = iota + 1 // 音单
	ElementTypeDrama            // 剧集
	ElementTypeSound            // 音频
	_
	ElementTypeLive         // 直播
	ElementTypeLiving = 100 // 正在直播
)

// 模块样式
const (
	ModuleStyleDefault      = iota // 竖版（默认）
	ModuleStyleHorizontal          // 横版
	ModuleStyleTopPlayStyle        // 播放量排行样式
	ModuleStyleSlide               // 滑动
)

// BlockTypeCustomModule 模块类型：自定义模块
const BlockTypeCustomModule = 3

// BlockTypeDressUP 模块类型：装扮
const BlockTypeDressUP = 5

const tableName = "m_persona_module_element"

// TableName for current model
func (MPersonaModuleElement) TableName() string {
	return tableName
}

// DB the db instance of current model
func (p MPersonaModuleElement) DB() *gorm.DB {
	return service.DB.Table(p.TableName())
}

// MPersonaModuleInfo 画像下模块信息
type MPersonaModuleInfo struct {
	ModuleID     int64  `json:"module_id"` // 模块 ID
	Sort         int64  `json:"sort"`      // 排序，0 为隐藏元素
	Title        string `json:"title"`     // 剧集或音单元素摘要介绍
	Type         int    `json:"type"`
	ElementStyle int    `json:"element_style"` // 元素样式
	More         []byte `json:"more"`

	MoreInfo youmightlikemodule.More `json:"-"`
}

// ListPersonaModules 获取画像下的指定模块列表
func ListPersonaModules(personaID int64, elementTypeList []int) ([]MPersonaModuleInfo, error) {
	var modules []MPersonaModuleInfo
	err := service.DB.Table(MPersonaModuleElement{}.TableName()+" AS p").
		Select("p.module_id, p.sort, m.title, m.element_type as type, m.element_style, m.more").
		Joins("INNER JOIN "+youmightlikemodule.YouMightLikeModule{}.TableName()+" AS m ON p.module_id = m.id").
		Where("p.persona_id = ? AND p.sort <> ?", personaID, sortModuleHidden).
		Where("m.element_type IN (?)", elementTypeList).
		Order("p.sort ASC").
		Scan(&modules).Error
	if err != nil {
		return nil, err
	}

	re := regexp.MustCompile(`\s?\([^\)]*\)`)
	for i, m := range modules {
		// 去掉半角括号及其内部文字，该内容用于后台备注不应向用户展示
		m.Title = re.ReplaceAllString(m.Title, "")
		modules[i].Title = strings.TrimSpace(m.Title)
		if len(m.More) != 0 {
			err := json.Unmarshal(m.More, &modules[i].MoreInfo)
			if err != nil {
				return nil, err
			}
		}
	}
	return modules, nil
}

// GetLivingModule 获取画像下正在直播模块信息
func GetLivingModule(personaID int64) (*youmightlikemodule.YouMightLikeModule, error) {
	var module youmightlikemodule.YouMightLikeModule
	err := service.DB.Table(youmightlikemodule.YouMightLikeModule{}.TableName()+" AS m").
		Select("m.*").
		Joins("LEFT JOIN "+MPersonaModuleElement{}.TableName()+" AS p ON p.module_id = m.id").
		Where("p.sort <> ? AND p.persona_id = ? AND m.element_type = ?",
			sortModuleHidden, personaID, ElementTypeLiving).
		Take(&module).Error

	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}

	return &module, nil
}
