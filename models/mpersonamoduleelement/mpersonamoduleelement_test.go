package mpersonamoduleelement

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/youmightlikemodule"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MPersonaModuleElement{},
		"id", "create_time", "modified_time", "module_id", "persona_id", "element_type", "element_id",
		"summary", "cover", "sort")
}

func TestMPersonaModuleElement_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_persona_module_element", MPersonaModuleElement{}.TableName())
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, <PERSON><PERSON><PERSON><PERSON><PERSON>lbum)
	assert.EqualValues(2, ElementTypeDrama)
	assert.EqualValues(3, ElementTypeSound)
	assert.EqualValues(5, ElementTypeLive)

	assert.EqualValues(0, ModuleStyleDefault)
	assert.EqualValues(1, ModuleStyleHorizontal)
	assert.EqualValues(2, ModuleStyleTopPlayStyle)
	assert.EqualValues(3, ModuleStyleSlide)
}

func TestListPersonaModules(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试画像下有模块的情况
	personaID := int64(2003)
	list, err := ListPersonaModules(personaID, []int{ElementTypeDrama})
	require.NoError(err)
	expectList := []MPersonaModuleInfo{
		{
			ModuleID:     1001,
			Sort:         1,
			Title:        "会员剧集模块一",
			Type:         ElementTypeDrama,
			ElementStyle: ModuleStyleHorizontal,
			More:         []byte(`{"url":"https://www.test.com/mdrama/123"}`),
			MoreInfo: youmightlikemodule.More{
				URL: "https://www.test.com/mdrama/123",
			},
		},
		{
			ModuleID:     1002,
			Sort:         2,
			Title:        "会员剧集模块二",
			Type:         ElementTypeDrama,
			ElementStyle: ModuleStyleTopPlayStyle,
			More:         []byte(`{"url":"https://www.test.com/mdrama/124"}`),
			MoreInfo: youmightlikemodule.More{
				URL: "https://www.test.com/mdrama/124",
			},
		},
		{
			ModuleID:     1003,
			Sort:         3,
			Title:        "会员剧集模块三",
			Type:         ElementTypeDrama,
			ElementStyle: ModuleStyleSlide,
			More:         []uint8{},
			MoreInfo:     youmightlikemodule.More{},
		},
	}
	assert.Equal(expectList, list)

	// 测试画像下无模块的情况
	personaID = 20
	list, err = ListPersonaModules(personaID, []int{ElementTypeDrama})
	require.NoError(err)
	assert.Empty(list)
}
