package sound

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMSoundConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(-3, CheckedTranscodeFailed)
	assert.Equal(-2, CheckedDubTranscode)
	assert.Equal(-1, CheckedSoundTranscode)
	assert.Equal(0, CheckedUnpass)
	assert.Equal(1, CheckedPass)
	assert.Equal(2, Checked<PERSON><PERSON><PERSON>)
	assert.Equal(3, CheckedDiscontinued)
	assert.Equal(4, CheckedContractExpired)

	assert.Equal(1, RefinedRisking)
	assert.Equal(2, Refined<PERSON><PERSON><PERSON>Hidden)
	assert.Equal(3, Refined<PERSON><PERSON>)

	assert.Equal(10, RefinedBitMaskRiskingOrBlocK)
}

func TestMSoundTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	kc.Check(MSound{}, "id", "catalog_id", "create_time", "last_update_time", "duration", "user_id", "username", "cover_image",
		"animationid", "characterid", "seiyid", "soundstr", "intro", "soundurl", "soundurl_32", "soundurl_64", "soundurl_128",
		"downtimes", "uptimes", "checked", "source", "download", "view_count", "comment_count", "favorite_count", "point", "push",
		"refined", "comments_count", "sub_comments_count", "pay_type", "type")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_sound", MSound{}.TableName())
}

func TestMSound_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试 CoverImage 为空的情况
	sound := &MSound{}
	require.NoError(sound.AfterFind())
	assert.Equal("https://static-test.maoercdn.com/coversmini/nocover.png", sound.FrontCover)

	// 测试 CoverImage 不为空的情况
	sound = &MSound{
		CoverImage: "test/test.png",
	}
	require.NoError(sound.AfterFind())
	assert.Equal("https://static-test.maoercdn.com/coversmini/test/test.png", sound.FrontCover)
}

func TestListSoundInfoByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试测试音频中有擦边球 1
	sounds, err := ListSoundInfoByIDs([]int64{1217692, 1217693})
	require.NoError(err)
	require.Len(sounds, 1)
	assert.EqualValues(1217692, sounds[0].ID)
	assert.EqualValues("测试推荐音频（审核通过）", sounds[0].Soundstr)
	assert.NotEmpty(sounds[0].FrontCover)
	assert.NotEmpty(sounds[0].CoverImage)
	assert.EqualValues(67939, sounds[0].ViewCount)

	// 测试音频中有擦边球 2
	sounds, err = ListSoundInfoByIDs([]int64{1217694})
	require.NoError(err)
	require.Empty(sounds)
}
