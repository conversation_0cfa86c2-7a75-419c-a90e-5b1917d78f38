package msound

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

// MSound model
type MSound struct {
	ID               int64  `gorm:"column:id"`
	CreateTime       int64  `gorm:"column:create_time"`
	LastUpdateTime   int64  `gorm:"column:last_update_time"`
	CatalogID        int    `gorm:"column:catalog_id"`
	Duration         int    `gorm:"column:duration"` // TODO: 该字段表示音频时长（单位：毫秒），字段类型需要改成 int64
	UserID           int64  `gorm:"column:user_id"`
	Username         string `gorm:"column:username"`
	CoverImage       string `gorm:"column:cover_image"`
	AnimationID      int64  `gorm:"column:animationid"`
	CharacterID      int64  `gorm:"column:characterid"`
	SeiyID           int64  `gorm:"column:seiyid"`
	Soundstr         string `gorm:"column:soundstr"`
	Intro            string `gorm:"column:intro"`
	Soundurl         string `gorm:"column:soundurl"`
	Soundurl32       string `gorm:"column:soundurl_32"`
	Soundurl64       string `gorm:"column:soundurl_64"`
	Soundurl128      string `gorm:"column:soundurl_128"`
	Downtimes        int64  `gorm:"column:downtimes"`
	Uptimes          int64  `gorm:"column:uptimes"`
	Checked          int    `gorm:"column:checked"`
	Source           byte   `gorm:"column:source"`
	Download         byte   `gorm:"column:download"`
	ViewCount        int64  `gorm:"column:view_count"`
	CommentCount     int64  `gorm:"column:comment_count"` // 弹幕数
	FavoriteCount    int64  `gorm:"column:favorite_count"`
	Point            int64  `gorm:"column:point"`
	Push             byte   `gorm:"column:push"`
	Refined          byte   `gorm:"column:refined"`
	CommentsCount    int64  `gorm:"column:comments_count"` // 评论数
	SubCommentsCount int64  `gorm:"column:sub_comments_count"`
	PayType          byte   `gorm:"column:pay_type"`
	Type             int    `gorm:"column:type"` // 音频类型

	FrontCover string `gorm:"-" json:"front_cover"`
}

const tableName = "m_sound"

// 音频过审状态
const (
	CheckedTranscodeFailed = iota - 3 // 转码失败
	CheckedDubTranscode               // 配音未转码
	CheckedSoundTranscode             // 未转码
	CheckedUnpass                     // 审核中
	CheckedPass                       // 已过审
	CheckedPolice                     // 报警
	CheckedDiscontinued               // 下架
	CheckedContractExpired            // 合约期满
)

// 字段 refined 状态（按位处理）
const (
	// 普通
	RefinedCommon = 0
	// 加精
	RefinedRefined = 1 << (iota - 1)
	// 擦边球 1
	RefinedNearDanger
	// 无法被搜索到
	RefinedSearchLimit
	// 擦边球 2，在分区、标签及用户主页不可见
	RefinedBlock
)

// RefinedNearDangerOrBlocK 擦边球 1 或 擦边球 2
const RefinedNearDangerOrBlocK = RefinedNearDanger | RefinedBlock

// DB the db instance of MSound model
func (m MSound) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MSound) TableName() string {
	return tableName
}

// ListCheckPassSoundIDs 查询指定音频中过审音频 IDs
func ListCheckPassSoundIDs(soundIDs []int64) ([]int64, error) {
	var checkPassSoundIDs []int64
	err := MSound{}.DB().
		Where("id IN (?) AND checked = ?", soundIDs, CheckedPass).
		Pluck("id", &checkPassSoundIDs).Error
	if err != nil {
		return nil, err
	}
	return checkPassSoundIDs, err
}
