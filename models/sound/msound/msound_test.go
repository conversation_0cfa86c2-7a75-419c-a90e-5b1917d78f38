package msound

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(-3, CheckedTranscodeFailed)
	assert.EqualValues(-2, CheckedDubTranscode)
	assert.EqualValues(-1, CheckedSoundTranscode)
	assert.EqualValues(0, CheckedUnpass)
	assert.EqualValues(1, CheckedPass)
	assert.EqualValues(2, <PERSON><PERSON><PERSON><PERSON><PERSON>)
	assert.EqualValues(3, CheckedDiscontinued)
	assert.EqualValues(4, CheckedContractExpired)

	assert.EqualValues(0, Refined<PERSON>ommon)
	assert.EqualValues(1, RefinedRefined)
	assert.EqualValues(2, Refined<PERSON>ear<PERSON>anger)
	assert.EqualValues(4, RefinedSearchLimit)
	assert.EqualValues(8, Refined<PERSON><PERSON>)
	assert.EqualValues(10, RefinedNearDangerOrBlocK)
}

func TestMsoundTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MSound{}, "id", "create_time", "last_update_time", "catalog_id", "duration", "user_id", "username", "cover_image",
		"animationid", "characterid", "seiyid", "soundstr", "intro", "soundurl", "soundurl_32", "soundurl_64",
		"soundurl_128", "downtimes", "uptimes", "checked", "source", "download", "view_count", "comment_count",
		"favorite_count", "point", "push", "refined", "comments_count", "sub_comments_count", "pay_type",
		"type")
}

func TestMsound_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(tableName, MSound{}.TableName())
}

func TestListCheckPassSoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testSoundIDs := []int64{2, 3, 4}

	// 所有音频不存在或未过审
	checkPassSoundIDs, err := ListCheckPassSoundIDs(testSoundIDs)
	require.NoError(err)
	assert.Empty(checkPassSoundIDs)

	// 部分音频存在
	testSoundIDs = []int64{100001, 100002, 100003, 4}
	checkPassSoundIDs, err = ListCheckPassSoundIDs(testSoundIDs)
	require.NoError(err)
	assert.Equal([]int64{100001, 100002, 100003}, checkPassSoundIDs)
}
