package sound

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

// MTagSoundMap model
type MTagSoundMap struct {
	TagID   int64 `gorm:"column:tag_id" json:"tag_id"`
	SoundID int64 `gorm:"column:sound_id" json:"sound_id"`
}

// DB the db instance of MTagSoundMap model
func (s MTagSoundMap) DB() *gorm.DB {
	return service.DB.Table(s.TableName())
}

// TableName for current model
func (MTagSoundMap) TableName() string {
	return "m_tag_sound_map"
}
