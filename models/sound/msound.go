package sound

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 音频过审状态
const (
	CheckedTranscodeFailed = iota - 3 // 转码失败
	CheckedDubTranscode               // 配音未转码
	CheckedSoundTranscode             // 未转码
	CheckedUnpass                     // 审核中
	CheckedPass                       // 已审核通过
	CheckedPolice                     // 报警
	CheckedDiscontinued               // 下架
	CheckedContractExpired            // 合约期满下架
)

// 付费类型
const (
	PayBySound = 1 // 单音付费
	PayByDrama = 2 // 剧集付费
)

// refined 定义
const (
	// RefinedRisking 擦边球 1（比特位第一位为 1）
	RefinedRisking = iota + 1
	// RefinedSearchHidden 是否为搜索隐藏（比特位第二位为 1）
	RefinedSearchHidden
	// RefinedBlock 擦边球 2，在分区、标签及用户主页不可见（比特位第 3 位为 1）
	RefinedBlock
)

// RefinedBitMaskRiskingOrBlocK 擦边球 1 或擦边球 2
const RefinedBitMaskRiskingOrBlocK = 1<<RefinedRisking | 1<<RefinedBlock

const tableName = "m_sound"

// MSound model
type MSound struct {
	ID               int64        `gorm:"column:id"`
	CatalogID        int          `gorm:"column:catalog_id"`
	CreateTime       int64        `gorm:"column:create_time"`
	LastUpdateTime   int64        `gorm:"column:last_update_time"`
	Duration         int64        `gorm:"column:duration"`
	UserID           int64        `gorm:"column:user_id"`
	Username         string       `gorm:"column:username"`
	CoverImage       string       `gorm:"column:cover_image"`
	AnimationID      int64        `gorm:"column:animationid"`
	CharacterID      int64        `gorm:"column:characterid"`
	SeiyID           int64        `gorm:"column:seiyid"`
	Soundstr         string       `gorm:"column:soundstr"`
	Intro            string       `gorm:"column:intro"`
	Soundurl         string       `gorm:"column:soundurl"`
	Soundurl32       string       `gorm:"column:soundurl_32"`
	Soundurl64       string       `gorm:"column:soundurl_64"`
	Soundurl128      string       `gorm:"column:soundurl_128"`
	Downtimes        int64        `gorm:"column:downtimes"`
	Uptimes          int64        `gorm:"column:uptimes"`
	Checked          int          `gorm:"column:checked"`
	Source           int          `gorm:"column:source"`
	Download         int          `gorm:"column:download"`
	ViewCount        int64        `gorm:"column:view_count"`
	CommentCount     int64        `gorm:"column:comment_count"` // 弹幕数
	FavoriteCount    int64        `gorm:"column:favorite_count"`
	Point            int64        `gorm:"column:point"`
	Push             int          `gorm:"column:push"`
	Refined          util.BitMask `gorm:"column:refined"`
	CommentsCount    int64        `gorm:"column:comments_count"` // 评论数
	SubCommentsCount int64        `gorm:"column:sub_comments_count"`
	PayType          int          `gorm:"column:pay_type"`
	Type             int          `gorm:"column:type"` // 音频类型

	FrontCover string `gorm:"-"`
}

// DB the db instance of MSound model
func (m MSound) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MSound) TableName() string {
	return tableName
}

// AfterFind is a GORM hook for query
func (m *MSound) AfterFind() error {
	if m.CoverImage == "" {
		m.FrontCover = service.Storage.Parse(config.Conf.Params.URL.DefaultCoverURL)
	} else {
		m.FrontCover = service.Storage.Parse(config.Conf.Params.URL.CoverURL + m.CoverImage)
	}
	return nil
}

// ListSoundInfoByIDs 根据音频 IDs 获取音频信息列表
func ListSoundInfoByIDs(soundIDs []int64) ([]*MSound, error) {
	var soundList []*MSound
	err := MSound{}.DB().
		Select("id, soundstr, cover_image, view_count").
		Where("id IN (?) AND checked = ? AND NOT refined & ?", soundIDs, CheckedPass, RefinedBitMaskRiskingOrBlocK).
		Find(&soundList).Error
	if err != nil {
		return nil, err
	}
	return soundList, nil
}
