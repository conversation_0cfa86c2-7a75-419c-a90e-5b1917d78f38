package msoundaddendum

import (
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMSoundAddendumTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MSoundAddendum{}, "id", "create_time", "modified_time", "violation_level", "remark", "first_audit_pass_time")
}

func TestMSoundAddendumConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, ViolationLevelNormal)
	assert.Equal(2, ViolationLevel2)
	assert.Equal(3, ViolationLevel3)
	assert.Equal(4, ViolationLevel4)
	assert.Equal(5, ViolationLevel5)
}

func TestUpdateFirstAuditPassTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testSoundID := int64(233)
	require.NoError(service.DB.Table(MSoundAddendum{}.TableName()).Delete("", "id = ?", testSoundID).Error)

	// 测试新增记录
	require.NoError(servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		return UpdateFirstAuditPassTime(tx, testSoundID, 23333)
	}))
	var m MSoundAddendum
	require.NoError(service.DB.Table(m.TableName()).Where("id = ?", testSoundID).Find(&m).Error)
	assert.NotZero(m.CreateTime)
	assert.NotZero(m.ModifiedTime)
	assert.Equal(ViolationLevelNormal, m.ViolationLevel)
	assert.EqualValues(23333, m.FirstAuditPassTime)

	// 测试更新记录
	require.NoError(UpdateFirstAuditPassTime(nil, testSoundID, 77777))
	var m1 MSoundAddendum
	require.NoError(service.DB.Table(m1.TableName()).Where("id = ?", testSoundID).Find(&m1).Error)
	assert.EqualValues(77777, m1.FirstAuditPassTime)
}
