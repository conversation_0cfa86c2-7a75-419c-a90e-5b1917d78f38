package msoundaddendum

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "m_sound_addendum"

// 音频等级，1：正常音频，2 ~ 5 为敏感度逐级递增的等级
const (
	ViolationLevelNormal = iota + 1
	ViolationLevel2
	ViolationLevel3
	ViolationLevel4
	ViolationLevel5
)

// MSoundAddendum model
type MSoundAddendum struct {
	ID                 int64  `gorm:"column:id;primary_key"`
	CreateTime         int64  `gorm:"column:create_time"`           // 创建时间，单位：秒
	ModifiedTime       int64  `gorm:"column:modified_time"`         // 修改时间，单位：秒
	ViolationLevel     int    `gorm:"column:violation_level"`       // 违规等级
	Remark             string `gorm:"column:remark"`                // 审核备注
	FirstAuditPassTime int64  `gorm:"column:first_audit_pass_time"` // 首次审核通过时间（单位：秒）
}

// TableName for current model
func (MSoundAddendum) TableName() string {
	return tableName
}

// BeforeCreate set create_time and modified_time
func (m *MSoundAddendum) BeforeCreate() (err error) {
	nowStamp := util.TimeNow().Unix()
	m.CreateTime = nowStamp
	m.ModifiedTime = nowStamp
	return nil
}

// GetDB get db
func GetDB(db *gorm.DB) *gorm.DB {
	if db != nil {
		return db
	}
	return service.DB
}

// UpdateFirstAuditPassTime 修改音频初审通过时间
func UpdateFirstAuditPassTime(db *gorm.DB, soundID, firstAuditPassTime int64) error {
	db = GetDB(db)
	var soundAddendum MSoundAddendum
	err := db.Table(soundAddendum.TableName()).Where("id = ?", soundID).Find(&soundAddendum).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return err
	}
	if soundAddendum.ID == 0 {
		soundAddendum.ID = soundID
		soundAddendum.ViolationLevel = ViolationLevelNormal
		soundAddendum.FirstAuditPassTime = firstAuditPassTime
		return db.Table(soundAddendum.TableName()).Create(&soundAddendum).Error
	}
	return db.Table(soundAddendum.TableName()).Where("id = ?", soundID).Updates(map[string]interface{}{
		"modified_time":         util.TimeNow().Unix(),
		"first_audit_pass_time": firstAuditPassTime,
	}).Error
}
