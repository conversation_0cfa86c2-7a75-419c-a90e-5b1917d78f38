package appupdate

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestLatest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	app := AppUpdate{
		ID:         1,
		Intro:      "1.0.0.21",
		Status:     StatusPublished,
		Device:     DeviceWindows,
		UpdateTime: util.TimeNow().Add(-time.Minute).Unix(),
	}
	defer func() {
		err := service.DB.Table(TableName()).Delete(&app).Error
		assert.NoError(err)
	}()
	err := service.DB.Table(TableName()).Create(&app).Error
	require.NoError(err)

	pc, err := Latest(DeviceWindows)
	require.NoError(err)
	require.NotNil(pc)
	assert.Equal(app.Intro, pc.Intro)

	pc, err = Latest(99)
	require.NoError(err)
	assert.Nil(pc)
}
