package appupdate

import (
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

// app 类型
const (
	DeviceAndroid = iota
	DeviceIOS
	DeviceWindows
	DeviceHarmonyOS
)

// app 状态
const (
	// StatusUnpublished 未发布版本
	StatusUnpublished = iota
	// StatusPublished 正式版（已发布）
	StatusPublished
	// StatusBeta  测试版（已发布）
	StatusBeta
)

// AppUpdate app 结构体
type AppUpdate struct {
	ID            int64   `gorm:"column:id;primary_key"`
	Title         string  `gorm:"column:title"`
	Version       string  `gorm:"column:version"` // iOS 使用此列的 Version Code 作为版本
	Intro         string  `gorm:"column:intro"`
	Status        int     `gorm:"column:status"`
	AppURL        string  `gorm:"column:appurl"`
	Device        int     `gorm:"column:device"`
	Size          float64 `gorm:"column:size"`
	UpdateTime    int64   `gorm:"column:update_time"`
	Developer     string  `gorm:"column:developer;default"`
	PrivacyURL    string  `gorm:"column:privacy_url;default"`
	PermissionURL string  `gorm:"column:permission_url;default"`
	AppURL2       string  `gorm:"column:appurl2"`
}

// TableName of m_appupdate
func TableName() string {
	return "m_appupdate"
}

// TableName table name
func (app *AppUpdate) TableName() string {
	return TableName()
}

// Latest 获取最新版 APP
func Latest(device int) (*AppUpdate, error) {
	var app AppUpdate
	err := service.DB.
		Where("device = ? AND status = ?", device, StatusPublished).
		Order("id DESC").First(&app).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}
