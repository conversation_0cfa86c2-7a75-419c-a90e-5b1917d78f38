package youmightlikemodule

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(YouMightLikeModule{},
		"id", "title", "creator_id", "element_type", "element_attr", "weekly_task_target", "skipped", "update_type",
		"create_time", "modified_time", "more", "element_style")
}

func TestYouMightLikeModule_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("you_might_like_module", YouMightLikeModule{}.TableName())
}
