package youmightlikemodule

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "you_might_like_module"

// More 更多设置信息
type More struct {
	URL string `json:"url"` // "更多"跳转链接地址
}

// YouMightLikeModule model
type YouMightLikeModule struct {
	ID               int64  `gorm:"column:id;primary_key"`     // 主键 ID
	Title            string `gorm:"column:title"`              // 模块名称
	CreatorID        int64  `gorm:"column:creator_id"`         // 负责人用户 ID
	ElementType      uint8  `gorm:"column:element_type"`       // 模块类型
	ElementAttr      uint8  `gorm:"column:element_attr"`       // 模块属性
	WeeklyTaskTarget int    `gorm:"column:weekly_task_target"` // 每周更新音频数量
	Skipped          uint   `gorm:"column:skipped"`            // 跳过类型检查
	UpdateType       uint8  `gorm:"column:update_type"`        // 模块更新类型
	CreateTime       int64  `gorm:"column:create_time"`        // 创建时间戳，单位：秒
	ModifiedTime     int64  `gorm:"column:modified_time"`      // 更新时间，单位：秒
	More             []byte `gorm:"column:more"`               // 额外信息，JSON 字符串
	ElementStyle     uint8  `gorm:"column:element_style"`      // 模块样式

	MoreInfo More `gorm:"-"`
}

// AfterFind is a GORM hook for query
func (m *YouMightLikeModule) AfterFind() error {
	if len(m.More) != 0 {
		err := json.Unmarshal(m.More, &m.MoreInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// TableName for current model
func (YouMightLikeModule) TableName() string {
	return tableName
}

// DB the db instance of current model
func (m YouMightLikeModule) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}
