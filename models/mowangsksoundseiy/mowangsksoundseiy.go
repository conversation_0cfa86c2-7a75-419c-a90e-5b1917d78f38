package mowangsksoundseiy

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/models/person/mattentionuser"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "mowangsksoundseiy"

// MowangskSoundSeiy 声优信息表
type MowangskSoundSeiy struct {
	ID            int64  `gorm:"column:id;primary_key"` // 声优 ID
	Name          string `gorm:"column:name"`           // 声优名称
	Icon          string `gorm:"column:icon"`           // 声优图片
	Profile       string `gorm:"column:profile"`        // 声优介绍
	Gender        int    `gorm:"column:gender"`         // 声优性别 1：男；2：女
	Initial       int    `gorm:"column:initial"`        // 首字母 0：其他；1 到 26 代表 A 到 Z
	BirthYear     int    `gorm:"column:birthyear"`      // 出生年份
	BirthMonth    int    `gorm:"column:birthmonth"`     // 出生月份
	Birthday      int    `gorm:"column:birthday"`       // 出生日
	BirthMonthDay int    `gorm:"column:birthmonthday"`  // 出生日期
	BloodType     int    `gorm:"column:bloodtype"`      // 血型 0：A；1：B；2：AB；3：O；4：未知
	Career        int    `gorm:"column:career"`         // 职业 0：日本声优；1：中国声优
	Group         string `gorm:"column:group"`          // 社团
	Weibo         string `gorm:"column:weibo"`          // 微博
	WeiboName     string `gorm:"column:weiboname"`      // 微博名称
	Baike         string `gorm:"column:baike"`          // 百科
	BaikeName     string `gorm:"column:baikename"`      // 百科名称
	MID           int64  `gorm:"column:mid"`            // 用户 ID
	Checked       int    `gorm:"column:checked"`        // 审核状态
	SoundLine1    int64  `gorm:"column:soundline1"`     // 声线 1（对应音频 ID）
	SoundLine2    int64  `gorm:"column:soundline2"`     // 声线 2（对应音频 ID）
	SoundLine3    int64  `gorm:"column:soundline3"`     // 声线 3（对应音频 ID）
	SeiyAlias     string `gorm:"column:seiyalias"`      // 别名
}

// DB the db instance of MowangskSoundSeiy model
func (d MowangskSoundSeiy) DB() *gorm.DB {
	return service.DB.Table(d.TableName())
}

// TableName for MowangskSoundSeiy model
func (d MowangskSoundSeiy) TableName() string {
	return tableName
}

// ListUserCvs 获取声优用户信息
func ListUserCvs(cvIDs []int64) ([]MowangskSoundSeiy, error) {
	var cvs []MowangskSoundSeiy
	err := MowangskSoundSeiy{}.DB().
		Select("id, mid").
		Where("id IN (?) AND mid > 0", cvIDs).Find(&cvs).Error
	if err != nil {
		return nil, err
	}
	return cvs, nil
}

// ListUnfollowUserCvs 获取未关注的声优用户信息
func ListUnfollowUserCvs(userID int64, cvIDs []int64) ([]MowangskSoundSeiy, error) {
	var cvs []MowangskSoundSeiy
	err := service.DB.Table(MowangskSoundSeiy{}.TableName()+" AS t1").
		Select("t1.id, t1.mid").
		Joins(fmt.Sprintf("LEFT JOIN %s AS t2 ON t1.mid = t2.user_passtive AND t2.user_active = %d", mattentionuser.MAttentionUser{}.TableName(), userID)).
		Where("t1.id IN (?) AND t1.mid > 0 AND t2.user_passtive IS NULL", cvIDs).Find(&cvs).Error
	if err != nil {
		return nil, err
	}
	return cvs, nil
}

// ListCvsByIDs 根据 cvIDs 获取声优信息
func ListCvsByIDs(cvIDs []int64) ([]MowangskSoundSeiy, error) {
	if len(cvIDs) <= 0 {
		return nil, nil
	}

	var cvs []MowangskSoundSeiy
	err := MowangskSoundSeiy{}.DB().
		Select("id, name").
		Where("id IN (?)", cvIDs).Find(&cvs).Error
	if err != nil {
		return nil, err
	}
	return cvs, nil
}
