package mowangsksoundseiy

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMowangskSoundSeiyTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MowangskSoundSeiy{},
		"id", "name", "icon", "profile", "gender", "initial", "birthyear", "birthmonth", "birthday",
		"birthmonthday", "bloodtype", "career", "group", "weibo", "weiboname", "baike", "baikename", "mid",
		"checked", "soundline1", "soundline2", "soundline3", "seiyalias")
}

func TestMowangskSoundSeiy_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MowangskSoundSeiy{}.TableName())
}

func TestListUserCvs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试声优不存在
	data, err := ListUserCvs([]int64{99998, 9999})
	require.NoError(err)
	assert.Empty(data)

	// 测试成功返回
	data, err = ListUserCvs([]int64{1, 2})
	require.NoError(err)
	require.NotEmpty(data)
	assert.EqualValues(1, len(data))
	// 断言只返回有M号的声优
	assert.EqualValues(2, data[0].ID)
}

func TestListUnfollowUserCvs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试声优不存在
	data, err := ListUnfollowUserCvs(1, []int64{99998, 9999})
	require.NoError(err)
	assert.Empty(data)

	// 测试成功返回
	data, err = ListUnfollowUserCvs(1, []int64{1, 2, 3})
	require.NoError(err)
	require.NotEmpty(data)
	assert.EqualValues(1, len(data))
	// 断言只返回未关注有M号的声优
	assert.EqualValues(3, data[0].ID)
}

func TestListCvsByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数为空时
	cvs, err := ListCvsByIDs([]int64{})
	require.NoError(err)
	assert.Empty(cvs)

	// 测试正常情况
	cvs, err = ListCvsByIDs([]int64{5, 6})
	require.NoError(err)
	require.Len(cvs, 2)
	assert.EqualValues(5, cvs[0].ID)
	assert.Equal("测试声优 5", cvs[0].Name)
	assert.EqualValues(6, cvs[1].ID)
	assert.Equal("测试声优 6", cvs[1].Name)
}
