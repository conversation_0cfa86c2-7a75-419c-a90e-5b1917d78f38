package mrecommendedelements

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MRecommendedElement{},
		"id", "create_time", "update_time", "client", "module_type", "module_id", "element_type", "element_id",
		"summary", "cover", "url", "sort", "creator_id", "start_time", "end_time", "archive", "more")
}

func TestMRecommendedElement_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_recommended_elements", MRecommendedElement{}.TableName())
}

func TestMRecommendedElement_AfterFind(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name          string
		cover         string
		more          MoreDetails
		expectedCover string
		expectedMore  MoreDetails
	}{
		{
			name:          "空 cover 字段",
			cover:         "",
			more:          MoreDetails{URL: "https://example.com/more"},
			expectedCover: "",
			expectedMore:  MoreDetails{URL: "https://example.com/more"},
		},
		{
			name:          "HTTP 协议地址 cover 字段",
			cover:         "http://example.com/image.jpg",
			more:          MoreDetails{URL: "https://example.com/more"},
			expectedCover: "http://example.com/image.jpg",
			expectedMore:  MoreDetails{URL: "https://example.com/more"},
		},
		{
			name:          "HTTPS 协议地址 cover 字段",
			cover:         "https://example.com/image.jpg",
			more:          MoreDetails{URL: "https://example.com/more"},
			expectedCover: "https://example.com/image.jpg",
			expectedMore:  MoreDetails{URL: "https://example.com/more"},
		},
		{
			name:          "空 more 字段",
			cover:         "https://example.com/test.jpg",
			more:          MoreDetails{},
			expectedCover: "https://example.com/test.jpg",
			expectedMore:  MoreDetails{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			element := &MRecommendedElement{
				Cover: tc.cover,
				More:  &tc.more,
			}

			err := element.AfterFind()
			assert.NoError(err)
			assert.Equal(tc.expectedCover, element.Cover)
			assert.Equal(tc.expectedMore, element.More)
		})
	}
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, ArchiveOnline)
	assert.Equal(1, ArchiveHistory)

	assert.Equal(0, ClientAppAll)
	assert.Equal(1, ClientAppAndrid)
	assert.Equal(2, ClientAppIOS)
	assert.Equal(3, ClientNone)
}

func TestListRecommendElements(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试模块中无推荐元素
	moduleIDs := []int64{1, 2, 3}
	elements, err := ListRecommendElements(moduleIDs)
	require.NoError(err)
	assert.Empty(elements)

	// 测试模块中有推荐元素
	moduleIDs = []int64{1001, 1002}
	elements, err = ListRecommendElements(moduleIDs)
	require.NoError(err)
	assert.Len(elements, 11)
}

func TestCacheDurationCalculator_Calculate(t *testing.T) {
	now := goutil.TimeNow().Round(time.Second)

	type testCase struct {
		name             string
		currentElements  []testElement
		nextElements     []testElement
		expectedDuration time.Duration
	}

	tests := []testCase{
		{
			name:             "NoElementsAndNoNext",
			currentElements:  nil,
			nextElements:     nil,
			expectedDuration: MaxCacheDuration,
		},
		{
			name: "MinEndTimeLessThanNextStartTime",
			currentElements: []testElement{
				{
					moduleID:  1,
					startTime: now,
					endTime:   now.Add(10 * time.Second),
				},
			},
			nextElements: []testElement{
				{
					moduleID:  1,
					startTime: now.Add(20 * time.Second),
					endTime:   now.Add(30 * time.Second),
				},
			},
			expectedDuration: 10 * time.Second,
		},
		{
			name: "MinEndTimeGreaterThanNextStartTime",
			currentElements: []testElement{
				{
					moduleID:  1,
					startTime: now,
					endTime:   now.Add(20 * time.Second),
				},
			},
			nextElements: []testElement{
				{
					moduleID:  1,
					startTime: now.Add(10 * time.Second),
					endTime:   now.Add(30 * time.Second),
				},
			},
			expectedDuration: 10 * time.Second,
		},
		{
			name: "NextStartTimeShouldQueryFromSameModule",
			currentElements: []testElement{
				{
					moduleID:  1,
					startTime: now,
					endTime:   now.Add(20 * time.Second),
				},
			},
			nextElements: []testElement{
				{
					moduleID:  1,
					startTime: now.Add(10 * time.Second),
					endTime:   now.Add(20 * time.Second),
				},
				{
					moduleID:  2,
					startTime: now,
					endTime:   now.Add(20 * time.Second),
				},
			},
			expectedDuration: 10 * time.Second,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			allElements := make([]testElement, 0, len(tt.currentElements)+len(tt.nextElements))
			allElements = append(allElements, tt.currentElements...)
			allElements = append(allElements, tt.nextElements...)
			createTestElements(t, allElements)

			calculator := CacheDurationCalculator{
				Client:     ClientAppAll,
				ModuleType: ModuleTypeExtraBanner,
				ModuleIDs:  []int64{1},
				NowTime:    now,
			}
			duration := calculator.Calculate(goutil.SliceMap(tt.currentElements, func(e testElement) MRecommendedElement {
				return MRecommendedElement{
					ModuleID:  e.moduleID,
					StartTime: e.startTime.Unix(),
					EndTime:   e.endTime.Unix(),
				}
			}))
			assert.Equal(t, tt.expectedDuration, duration)
		})
	}
}

type testElement struct {
	moduleID  int64
	startTime time.Time
	endTime   time.Time
}

func createTestElements(t *testing.T, elements []testElement) {
	ids := make([]int64, 0, len(elements))
	t.Cleanup(func() {
		MRecommendedElement{}.DB().Delete(&MRecommendedElement{}, "id IN (?)", ids)
	})
	for _, e := range elements {
		element := MRecommendedElement{
			Client:     ClientAppAll,
			ModuleType: ModuleTypeExtraBanner,
			ModuleID:   e.moduleID,
			StartTime:  e.startTime.Unix(),
			EndTime:    e.endTime.Unix(),
			Archive:    ArchiveOnline,
		}
		require.NoError(t, element.DB().Create(&element).Error)
		ids = append(ids, element.ID)
	}
}

func TestFindByID(t *testing.T) {
	now := goutil.TimeNow()

	// 创建测试数据
	testElement := MRecommendedElement{
		CreateTime:  now.Unix(),
		UpdateTime:  now.Unix(),
		Client:      ClientAppAll,
		ModuleType:  ModuleTypeLikeDramaAlbum,
		ModuleID:    100,
		ElementType: ElementTypeDrama,
		ElementID:   200,
		Summary:     "测试剧集简介",
		Cover:       "https://example.com/test-cover.jpg",
		URL:         "https://test.example.com",
		Sort:        1,
		CreatorID:   123,
		StartTime:   now.Unix(),
		EndTime:     now.Add(time.Hour).Unix(),
		Archive:     ArchiveOnline,
		More:        &MoreDetails{URL: "https://more.example.com"},
	}

	// 插入测试数据
	err := testElement.DB().Create(&testElement).Error
	require.NoError(t, err)

	// 清理测试数据
	defer func() {
		testElement.DB().Delete(&testElement)
	}()

	t.Run("存在的记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		result, err := FindByID(testElement.ID, ModuleTypeLikeDramaAlbum)
		require.NoError(err)
		require.NotNil(result)

		// 验证主要字段
		assert.Equal(testElement.ID, result.ID)
		assert.Equal(ModuleTypeLikeDramaAlbum, result.ModuleType)
		assert.Equal("测试剧集简介", result.Summary)
		assert.Equal("https://more.example.com", result.More.URL)
		assert.NotEmpty(result.Cover)
	})

	t.Run("不存在的记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		result, err := FindByID(9999, ModuleTypeLikeDramaAlbum)
		require.NoError(err)
		assert.Nil(result)
	})
}

func TestMRecommendedElement_Create(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()

	// 创建测试数据
	moreDetails := MoreDetails{
		URL:             "https://example.com/create-test",
		ExposureLevelID: 200,
	}

	element := MRecommendedElement{
		ModuleType:  ModuleTypeHomeFeed,
		ElementType: ElementTypeDrama,
		ElementID:   int64(54321),
		StartTime:   now.Unix(),
		EndTime:     now.Add(time.Hour).Unix(),
		More:        &moreDetails,
	}

	// 设置其他必要的字段
	element.Summary = "测试创建方法"
	element.Cover = "https://example.com/test-cover.jpg"

	// 调用 Create 方法
	err := element.Create()
	require.NoError(err)

	// 清理测试数据
	defer func() {
		element.DB().Delete(&element)
	}()

	// 验证记录已创建
	assert.NotZero(element.ID) // ID 应该被自动赋值
	var found MRecommendedElement
	err = element.DB().Where("id = ?", element.ID).Take(&found).Error
	require.NoError(err)

	// 验证字段值
	assert.Equal(element.ModuleType, found.ModuleType)
	assert.Equal(element.ElementType, found.ElementType)
	assert.Equal(element.ElementID, found.ElementID)
	assert.Equal(element.Summary, found.Summary)
	assert.Equal(element.Cover, found.Cover)
	assert.Equal(element.StartTime, found.StartTime)
	assert.Equal(element.EndTime, found.EndTime)
	assert.Equal(element.More, found.More)
}

func TestNewHomeFeedElement(t *testing.T) {
	assert := assert.New(t)

	// 测试参数
	elementType := ElementTypeDrama
	elementID := int64(12345)
	startTime := int64(1640995200) // 2022-01-01 00:00:00
	endTime := int64(1641081600)   // 2022-01-02 00:00:00
	exposureLevelID := int64(5)

	// 调用函数
	element := NewHomeFeedElement(elementType, elementID, startTime, endTime, exposureLevelID)

	// 验证字段值
	assert.NotNil(element)
	assert.Equal(ModuleTypeHomeFeed, element.ModuleType)
	assert.Equal(elementType, element.ElementType)
	assert.Equal(elementID, element.ElementID)
	assert.Equal(startTime, element.StartTime)
	assert.Equal(endTime, element.EndTime)
	assert.Equal(exposureLevelID, element.More.ExposureLevelID)
}

func TestMRecommendedElement_Delete(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()

	// 创建测试数据
	element := MRecommendedElement{
		ModuleType:  ModuleTypeHomeFeed,
		ElementType: ElementTypeDrama,
		ElementID:   int64(98765),
		StartTime:   now.Unix(),
		EndTime:     now.Add(time.Hour).Unix(),
		Archive:     ArchiveOnline, // 初始状态为在线
		More: &MoreDetails{
			URL:             "https://example.com/delete-test",
			ExposureLevelID: 300,
		},
	}

	// 清理测试数据
	defer func() {
		element.DB().Delete(&element)
	}()

	// 先创建记录
	err := element.Create()
	require.NoError(err)

	// 验证初始状态为在线
	var beforeDelete MRecommendedElement
	err = element.DB().Where("id = ?", element.ID).Take(&beforeDelete).Error
	require.NoError(err)
	assert.Equal(ArchiveOnline, beforeDelete.Archive)

	// 调用 Delete 方法
	err = element.Delete()
	require.NoError(err)

	// 验证记录已被标记为归档状态
	var afterDelete MRecommendedElement
	err = element.DB().Where("id = ?", element.ID).Take(&afterDelete).Error
	require.NoError(err)

	// 验证 archive 字段已更新为 ArchiveHistory
	assert.Equal(ArchiveHistory, afterDelete.Archive)
}

func TestBatchInsert(t *testing.T) {
	now := goutil.TimeNow()
	elements := []*MRecommendedElement{
		{
			ModuleType:  ModuleTypeHomeFeed,
			ElementType: ElementTypeDrama,
			ElementID:   int64(22222),
			StartTime:   now.Unix(),
			EndTime:     now.Add(time.Hour).Unix(),
			Archive:     ArchiveOnline,
			More: &MoreDetails{
				URL:             "https://example.com/batch-test-2",
				ExposureLevelID: 200,
			},
		},
		{
			ModuleType:  ModuleTypeHomeFeed,
			ElementType: ElementTypeAlbum,
			ElementID:   int64(33333),
			StartTime:   now.Unix(),
			EndTime:     now.Add(2 * time.Hour).Unix(),
			Archive:     ArchiveOnline,
			More: &MoreDetails{
				URL:             "https://example.com/batch-test-3",
				ExposureLevelID: 300,
			},
		},
	}

	err := BatchInsert(elements)
	require.NoError(t, err)

	// 清理测试数据
	defer func() {
		ids := goutil.SliceMap(elements, func(e *MRecommendedElement) int64 { return e.ID })
		err := MRecommendedElement{}.DB().Delete(&MRecommendedElement{}, "id IN (?)", ids).Error
		require.NoError(t, err)
	}()

	// 验证数据库中的记录
	var count int64
	err = MRecommendedElement{}.DB().Where("element_id IN (?)", []int64{22222, 33333}).Count(&count).Error
	require.NoError(t, err)
	assert.EqualValues(t, 2, count)
}
