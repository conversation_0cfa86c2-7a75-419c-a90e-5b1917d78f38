package mrecommendedelements

import (
	"database/sql/driver"
	"encoding/json"
	"slices"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// ElementRecommendCount 推荐剧集模块展示元素数量
const ElementRecommendCount = 6

// 展示范围
const (
	ClientAppAll    = iota // 双端可见
	ClientAppAndrid        // 安卓可见
	ClientAppIOS           // iOS 可见
	ClientNone             // 不可见
)

// 模块类型
const (
	// ModuleTypeLikeDramaAlbum 猜你喜欢剧集或音单模块
	ModuleTypeLikeDramaAlbum = 5
	// ModuleTypeExtraBanner 轮播通栏图模块
	ModuleTypeExtraBanner = 7
	// ModuleTypeHomeFeed 首页 Feed 流
	ModuleTypeHomeFeed = 18
)

// 元素类型
const (
	ElementTypeOthers      = iota // 其它
	ElementTypeAlbum              // 音单
	ElementTypeDrama              // 剧集
	ElementTypeSound              // 单音
	ElementTypeEvent              // 活动
	ElementTypeLive               // 直播
	ElementTypeSearchWords        // 推荐搜索词
)

// 归档情况
const (
	ArchiveOnline  = iota // 显示中
	ArchiveHistory        // 已归档
)

const tableName = "m_recommended_elements"

// MoreDetails 更多设置信息
type MoreDetails struct {
	URL             string `json:"url,omitempty"`               // "更多"跳转链接地址
	ExposureLevelID int64  `json:"exposure_level_id,omitempty"` // 曝光等级 ID
}

// Value 方法将 MoreDetails 转换为 json 字符串，用于存储到数据库中
func (m MoreDetails) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 方法从数据库的 json 字符串中解析出 MoreDetails
func (m *MoreDetails) Scan(input interface{}) error {
	switch p := input.(type) {
	case string:
		// 兼容 sqlite 中已提前存入的数据
		if p == "" {
			return nil
		}
		return json.Unmarshal([]byte(p), m)
	case []uint8:
		if len(p) == 0 {
			return nil
		}
		return json.Unmarshal(input.([]byte), m)
	default:
		return nil
	}
}

// MRecommendedElement model
type MRecommendedElement struct {
	ID          int64        `gorm:"column:id;primary_key"`
	CreateTime  int64        `gorm:"column:create_time"`  // 创建时间戳，单位：秒
	UpdateTime  int64        `gorm:"column:update_time"`  // 更新时间戳，单位：秒
	Client      int          `gorm:"column:client"`       // 平台（0 安卓或 iOS，1 安卓，2 iOS）
	ModuleType  int          `gorm:"column:module_type"`  // 模块类型（1 版头图模块、2 精品必听、活动等四个小图标模块、3 猜你喜欢音模块、4 精品周更模块、5 推荐剧集或音单模块、6 今日推荐音模块）
	ModuleID    int64        `gorm:"column:module_id"`    // 模块 ID
	ElementType int          `gorm:"column:element_type"` // 元素类型（0 其它、1 音单、2 剧集、3 单音、4 活动）
	ElementID   int64        `gorm:"column:element_id"`   // 元素 ID
	Summary     string       `gorm:"column:summary"`      // 简介
	Cover       string       `gorm:"column:cover"`        // 封面
	URL         string       `gorm:"column:url"`          // 原始链接
	Sort        int64        `gorm:"column:sort"`         // 排序值（0、1、2... 越小越靠前，由程序控制）
	CreatorID   int64        `gorm:"column:creator_id"`   // 负责人用户 ID
	StartTime   int64        `gorm:"column:start_time"`   // 自动上线时间戳，单位：秒
	EndTime     int64        `gorm:"column:end_time"`     // 自动下线时间戳，单位：秒
	Archive     int          `gorm:"column:archive"`      // 是否为历史归档：0 为否， 1 为归档（即为被删去的）
	More        *MoreDetails `gorm:"column:more"`         // 更多设置（JSON 类型字符串）
}

// BeforeCreate hook
func (m *MRecommendedElement) BeforeCreate() error {
	now := goutil.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *MRecommendedElement) BeforeSave() error {
	now := goutil.TimeNow().Unix()
	m.UpdateTime = now
	return nil
}

// AfterFind is a GORM hook for query
func (m *MRecommendedElement) AfterFind() error {
	// 处理 cover 字段，如果不是 http 协议地址，则转换为完整的文件 URL
	if m.Cover != "" && !strings.HasPrefix(m.Cover, "http://") && !strings.HasPrefix(m.Cover, "https://") {
		m.Cover = service.Storage.Parse(m.Cover)
	}

	return nil
}

// TableName for current model
func (MRecommendedElement) TableName() string {
	return tableName
}

// DB the db instance of current model
func (m MRecommendedElement) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// ListRecommendElements 获取指定模块中推荐的元素
func ListRecommendElements(moduleIDs []int64) ([]MRecommendedElement, error) {
	var elements []MRecommendedElement
	err := MRecommendedElement{}.DB().
		Select("module_id, element_type, element_id, summary, cover, sort").
		Where("client = ? AND module_type = ? AND archive = ?",
			ClientAppAll, ModuleTypeLikeDramaAlbum, ArchiveOnline).
		Where("module_id IN (?)", moduleIDs).
		Order("sort ASC").
		Find(&elements).Error
	if err != nil {
		return nil, err
	}
	return elements, nil
}

// CacheDurationCalculator 用于计算 RecommendedElements 的缓存时间
type CacheDurationCalculator struct {
	Client     int
	ModuleType int
	ModuleIDs  []int64
	NowTime    time.Time
}

const MaxCacheDuration = 30 * time.Second

// Calculate 计算缓存时间，如果出错则返回 MaxCacheDuration
func (c *CacheDurationCalculator) Calculate(elements []MRecommendedElement) time.Duration {
	if len(elements) == 0 {
		return c.calculateForEmptyCache()
	}
	endTimes := goutil.SliceMap(elements, func(element MRecommendedElement) int64 {
		return element.EndTime
	})
	minEndTime := time.Unix(slices.Min(endTimes), 0)
	nextStartTime, err := c.nextStartTime()
	switch {
	case err != nil:
		logger.Error(err)
		// PASS
	case !nextStartTime.IsZero() && nextStartTime.Before(minEndTime):
		minEndTime = nextStartTime
	}
	return c.calculateFromEndTime(minEndTime)
}

func (c *CacheDurationCalculator) calculateForEmptyCache() time.Duration {
	endTime, err := c.nextStartTime()
	switch {
	case err != nil:
		logger.Error(err)
		return MaxCacheDuration
	case endTime.IsZero():
		return MaxCacheDuration
	default:
		return c.calculateFromEndTime(endTime)
	}
}

func (c *CacheDurationCalculator) calculateFromEndTime(endTime time.Time) time.Duration {
	duration := endTime.Sub(c.NowTime)
	switch {
	case duration <= 0:
		return time.Second
	case duration > MaxCacheDuration:
		return MaxCacheDuration
	default:
		return duration
	}
}

func (c *CacheDurationCalculator) nextStartTime() (time.Time, error) {
	var element MRecommendedElement
	err := MRecommendedElement{}.DB().
		Select("start_time").
		Where(map[string]any{
			"client":      c.Client,
			"module_type": c.ModuleType,
			"archive":     ArchiveOnline,
		}).
		Where("module_id IN (?)", c.ModuleIDs).
		Where("start_time > ?", c.NowTime.Unix()).
		Order("start_time ASC, sort ASC").
		Limit(1).
		Take(&element).Error
	switch {
	case err == gorm.ErrRecordNotFound:
		return time.Time{}, nil
	case err != nil:
		return time.Time{}, err
	default:
		return time.Unix(element.StartTime, 0), nil
	}
}

// FindByID 根据 ID 和模块类型查找推荐元素
func FindByID(id int64, moduleType int) (*MRecommendedElement, error) {
	var element MRecommendedElement
	err := element.DB().Where("id = ? AND module_type = ?", id, moduleType).Take(&element).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &element, nil
}

// Create 创建推荐元素记录
func (m *MRecommendedElement) Create() error {
	err := m.DB().Create(&m).Error
	if err != nil {
		return err
	}
	return nil
}

// BatchInsert 批量创建推荐元素记录
func BatchInsert(elements []*MRecommendedElement) error {
	if len(elements) == 0 {
		return nil
	}

	return servicedb.BatchInsert(MRecommendedElement{}.DB(), MRecommendedElement{}.TableName(), elements)
}

// Delete 删除推荐元素记录
func (m *MRecommendedElement) Delete() error {
	db := m.DB().
		Where("id = ? AND archive = ?", m.ID, ArchiveOnline).
		Updates(map[string]any{"archive": ArchiveHistory})
	if db.Error != nil {
		return db.Error
	}
	if db.RowsAffected == 0 {
		return servicedb.ErrNoRowsAffected
	}
	return nil
}

// NewHomeFeedElement 创建首页 Feed 流元素
func NewHomeFeedElement(elementType int, elementID int64, startTime, endTime int64, exposureLevelID int64) *MRecommendedElement {
	return &MRecommendedElement{
		ModuleType:  ModuleTypeHomeFeed,
		ElementType: elementType,
		ElementID:   elementID,
		StartTime:   startTime,
		EndTime:     endTime,
		More: &MoreDetails{
			ExposureLevelID: exposureLevelID,
		},
	}
}
