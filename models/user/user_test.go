package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Simple{}, "id", "confirm", "username", "iconurl", "userintro", "avatar", "icontype", "boardiconurl")
	kc.Check(MowangskUser{},
		"cip", "uip", "ctime", "utime", "quanxian", "teamid", "teamname", "ban", "ustr", "uint", "uagi",
		"point", "nowsound", "iconid", "iconcolor", "subtitle", "boardiconid", "boardiconcolor", "coverid",
		"coverurl", "isnewmsg", "userintro_audio", "likenum", "fansnum", "follownum", "soundnum", "albumnum", "imagenum",
		"feednum", "soundnumchecked", "imagenumchecked", "mlevel", "coverurl_new")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, UserIconTypeCartoon)
	assert.Equal(1, UserIconTypeNormal)
	assert.Equal(2, UserIconTypeSpecial)
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MowangskUser{}.TableName())
}

func TestFindUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs, err := FindUserIDs([]int64{18001, 18002, 18003, 18004})
	require.NoError(err)
	assert.Equal([]int64{18001, 18002, 18003, 18004}, userIDs)

	userIDs, err = FindUserIDs([]int64{18005})
	require.NoError(err)
	assert.Nil(userIDs)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户存在
	userID := int64(18001)
	user, err := FindOne(userID)
	require.NoError(err)
	assert.NotNil(user)
	assert.Equal("零月", user.UserName)
	assert.Equal("https://static-test.maoercdn.com/profile/201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png", user.IconURL)

	// 测试用户不存在
	userID = 18005
	user, err = FindOne(userID)
	require.NoError(err)
	assert.Nil(user)
}

func TestGetCTimeByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := MowangskUser{}

	// 测试存在的用户
	ctime, err := m.GetCTimeByID(18001)
	require.NoError(err)
	assert.NotZero(ctime)

	ctime, err = m.GetCTimeByID(346286)
	require.NoError(err)
	assert.Equal(int64(1474172405), ctime) // 从 test.sql 中获取的时间戳

	// 测试不存在的用户
	ctime, err = m.GetCTimeByID(18005) // test.sql 中不存在的用户
	assert.Error(err)
	assert.Equal(ErrUserNotFound, err)
	assert.Zero(ctime)
}
