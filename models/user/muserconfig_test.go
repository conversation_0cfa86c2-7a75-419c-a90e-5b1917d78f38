package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testUserID = 1234
const testBuvid = "test_buvid"
const testRegisterTime = 1680000000 // 2023-03-28 20:00:00

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserConfig{},
		"id", "create_time", "modified_time", "user_id", "buvid", "app_config",
	)
}

func TestMUserConfig_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_user_config", MUserConfig{}.TableName())
}

func TestMUserConfig_BeforeSave(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := &MUserConfig{
		AppConfig: AppConfig{
			PersonalizedRecommend: goutil.NewInt(ConfigEnable),
		},
	}
	require.NoError(config.BeforeSave())
	assert.NotZero(config.ModifiedTime)
	assert.NotZero(config.CreateTime)
}

func TestDefaultMessageNotificationConf(t *testing.T) {
	assert := assert.New(t)

	conf := DefaultMessageNotificationConf()
	assert.NotNil(conf)
	assert.Equal(ConfigEnable, *conf.AtMe)
	assert.Equal(ConfigEnable, *conf.Like)
	assert.Equal(ConfigEnable, *conf.Comment)
	assert.Equal(ConfigEnable, *conf.PrivateMessage)
	assert.Equal(ConfigEnable, *conf.Live)
	assert.Equal(ConfigEnable, *conf.InterestRecommend)
}

func TestDefaultAppConfig(t *testing.T) {
	assert := assert.New(t)

	// 测试新用户（注册时间晚于私信推送配置生效时间）
	newUserConfig := DefaultAppConfig(testUserID, messageNotificationPrivateMessageEnableTime+1)
	assert.Equal(ConfigEnable, *newUserConfig.PersonalizedRecommend)
	assert.Equal(ConfigEnable, *newUserConfig.ShowSubscribeDrama)
	assert.Equal(ConfigEnable, *newUserConfig.ShowUserCollect)
	assert.Equal(ConfigEnable, *newUserConfig.MessageNotification.PrivateMessage)

	// 测试老用户（注册时间早于私信推送配置生效时间）
	oldUserConfig := DefaultAppConfig(testUserID, messageNotificationPrivateMessageEnableTime-1)
	assert.Equal(ConfigEnable, *oldUserConfig.PersonalizedRecommend)
	assert.Equal(ConfigEnable, *oldUserConfig.ShowSubscribeDrama)
	assert.Equal(ConfigEnable, *oldUserConfig.ShowUserCollect)
	assert.Equal(ConfigDisable, *oldUserConfig.MessageNotification.PrivateMessage)
}

func TestNewUserConfig(t *testing.T) {
	assert := assert.New(t)

	// 测试已登录用户
	loggedInConfig := NewUserConfig(testUserID, testBuvid, testRegisterTime)
	assert.Equal(int64(testUserID), loggedInConfig.UserID)
	assert.Empty(loggedInConfig.Buvid)
	assert.NotNil(loggedInConfig.AppConfig.PersonalizedRecommend)
	assert.NotNil(loggedInConfig.AppConfig.MessageNotification)

	// 测试未登录用户（使用 buvid）
	notLoggedInConfig := NewUserConfig(0, testBuvid, testRegisterTime)
	assert.Equal(int64(0), notLoggedInConfig.UserID)
	assert.Equal(testBuvid, notLoggedInConfig.Buvid)
	assert.NotNil(notLoggedInConfig.AppConfig.PersonalizedRecommend)
	assert.NotNil(notLoggedInConfig.AppConfig.MessageNotification)
}

func TestMergeDefaultConfig(t *testing.T) {
	assert := assert.New(t)

	// 创建默认配置
	defaultConfig := DefaultAppConfig(testUserID, testRegisterTime)

	// 测试合并空配置
	emptyConfig := &AppConfig{}
	mergeDefaultConfig(emptyConfig, defaultConfig)
	assert.Equal(*defaultConfig.PersonalizedRecommend, *emptyConfig.PersonalizedRecommend)
	assert.Equal(*defaultConfig.ShowSubscribeDrama, *emptyConfig.ShowSubscribeDrama)
	assert.Equal(*defaultConfig.ShowUserCollect, *emptyConfig.ShowUserCollect)
	assert.NotNil(emptyConfig.MessageNotification)

	// 测试部分有值的配置
	disable := ConfigDisable
	partialConfig := &AppConfig{
		PersonalizedRecommend: &disable,
		MessageNotification: &MessageNotificationConf{
			AtMe: &disable,
		},
	}
	mergeDefaultConfig(partialConfig, defaultConfig)
	assert.Equal(disable, *partialConfig.PersonalizedRecommend)                                    // 保持原值
	assert.Equal(*defaultConfig.ShowSubscribeDrama, *partialConfig.ShowSubscribeDrama)             // 使用默认值
	assert.Equal(*defaultConfig.ShowUserCollect, *partialConfig.ShowUserCollect)                   // 使用默认值
	assert.Equal(disable, *partialConfig.MessageNotification.AtMe)                                 // 保持原值
	assert.Equal(*defaultConfig.MessageNotification.Like, *partialConfig.MessageNotification.Like) // 使用默认值
}

func TestGetUserConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理测试数据
	require.NoError(MUserConfig{}.DB().Delete("", "user_id = ? OR buvid = ?", testUserID, testBuvid).Error)

	// 测试获取未存在的已登录用户配置
	config, err := GetUserConfig(testUserID, "", testRegisterTime)
	require.NoError(err)
	assert.Equal(int64(testUserID), config.UserID)
	assert.Empty(config.Buvid)
	assert.NotNil(config.AppConfig.PersonalizedRecommend)

	// 保存用户配置
	require.NoError(config.DB().Create(config).Error)

	// 测试获取已存在的已登录用户配置
	config2, err := GetUserConfig(testUserID, "", testRegisterTime)
	require.NoError(err)
	assert.Equal(int64(testUserID), config2.UserID)
	assert.Equal(config.ID, config2.ID)

	// 测试获取未存在的未登录用户配置
	configBuvid, err := GetUserConfig(0, testBuvid, testRegisterTime)
	require.NoError(err)
	assert.Equal(int64(0), configBuvid.UserID)
	assert.Equal(testBuvid, configBuvid.Buvid)

	// 保存未登录设备配置
	require.NoError(configBuvid.DB().Create(configBuvid).Error)

	// 测试获取已存在的未登录用户配置
	configBuvid2, err := GetUserConfig(0, testBuvid, testRegisterTime)
	require.NoError(err)
	assert.Equal(testBuvid, configBuvid2.Buvid)
	assert.Equal(configBuvid.ID, configBuvid2.ID)

	// 测试既没有用户ID也没有buvid的情况
	configDefault, err := GetUserConfig(0, "", testRegisterTime)
	require.NoError(err)
	assert.Equal(int64(0), configDefault.UserID)
	assert.Empty(configDefault.Buvid)
}
