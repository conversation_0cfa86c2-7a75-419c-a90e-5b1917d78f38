package user

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/jinzhu/gorm"

	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 消息私信默认推送配置生效时间：2023-04-30 00:00:00
const messageNotificationPrivateMessageEnableTime = 1682784000

// 配置项状态：禁用/启用
const (
	ConfigDisable = 0 // 关闭
	ConfigEnable  = 1 // 开启
)

// MessageNotificationConf 消息通知配置
type MessageNotificationConf struct {
	AtMe              *int `json:"at_me,omitempty"`
	Like              *int `json:"like,omitempty"`
	Comment           *int `json:"comment,omitempty"`
	PrivateMessage    *int `json:"private_message,omitempty"`
	Live              *int `json:"live,omitempty"`
	InterestRecommend *int `json:"interest_recommend,omitempty"`
}

// AppConfig APP 配置
type AppConfig struct {
	PersonalizedRecommend *int                     `json:"personalized_recommend,omitempty"`
	ShowSubscribeDrama    *int                     `json:"show_subscribe_drama,omitempty"`
	ShowUserCollect       *int                     `json:"show_user_collect,omitempty"`
	MessageNotification   *MessageNotificationConf `json:"message_notification,omitempty"`
}

// Value 方法将 AppConfig 转换为 json 字符串，用于存储到数据库中
func (a AppConfig) Value() (driver.Value, error) {
	return json.Marshal(a)
}

// Scan 方法从数据库的 json 字符串中解析出 AppConfig
func (a *AppConfig) Scan(input interface{}) error {
	switch p := input.(type) {
	case string:
		if p == "" {
			return nil
		}
		return json.Unmarshal([]byte(p), a)
	case []uint8:
		if len(p) == 0 {
			return nil
		}
		return json.Unmarshal(p, a)
	default:
		return nil
	}
}

// MUserConfig model
type MUserConfig struct {
	ID           int64     `gorm:"column:id;primary_key" json:"-"`
	CreateTime   int64     `gorm:"column:create_time" json:"-"`
	ModifiedTime int64     `gorm:"column:modified_time" json:"-"`
	UserID       int64     `gorm:"column:user_id" json:"user_id"`
	Buvid        string    `gorm:"column:buvid" json:"buvid"`
	AppConfig    AppConfig `gorm:"column:app_config" json:"app_config"`
}

// TableName for current model
func (MUserConfig) TableName() string {
	return "m_user_config"
}

// DB the db instance of MUserConfig model
func (m MUserConfig) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// BeforeSave hook
func (m *MUserConfig) BeforeSave() error {
	now := goutil.TimeNow().Unix()
	m.ModifiedTime = now
	if m.DB().NewRecord(m) {
		m.CreateTime = now
	}
	return nil
}

// DefaultMessageNotificationConf 默认消息通知配置
func DefaultMessageNotificationConf() *MessageNotificationConf {
	enable := ConfigEnable
	return &MessageNotificationConf{
		AtMe:              &enable,
		Like:              &enable,
		Comment:           &enable,
		PrivateMessage:    &enable,
		Live:              &enable,
		InterestRecommend: &enable,
	}
}

// DefaultAppConfig 默认 APP 配置
func DefaultAppConfig(userID int64, registerTime int64) AppConfig {
	messageNotification := DefaultMessageNotificationConf()
	enable := ConfigEnable

	// 根据注册时间判断是否开启私信推送
	if registerTime < messageNotificationPrivateMessageEnableTime {
		disable := ConfigDisable
		messageNotification.PrivateMessage = &disable
	}

	appConfig := AppConfig{
		PersonalizedRecommend: &enable,
		ShowSubscribeDrama:    &enable,
		ShowUserCollect:       &enable,
		MessageNotification:   messageNotification,
	}

	return appConfig
}

// NewUserConfig 创建新的用户配置
func NewUserConfig(userID int64, buvid string, registerTime int64) *MUserConfig {
	config := &MUserConfig{}

	if userID > 0 {
		// 若已登录，则 buvid 存空字符串
		config.UserID = userID
		config.Buvid = ""
	} else {
		// 若未登录，则 user_id 存 0
		config.UserID = 0
		config.Buvid = buvid
	}

	config.AppConfig = DefaultAppConfig(userID, registerTime)

	return config
}

// mergeDefaultConfig 合并默认配置，如果字段为 nil 则使用默认配置值
func mergeDefaultConfig(config *AppConfig, defaultConfig AppConfig) {
	// 如果字段为 nil，则使用默认值
	if config.PersonalizedRecommend == nil {
		config.PersonalizedRecommend = defaultConfig.PersonalizedRecommend
	}
	if config.ShowSubscribeDrama == nil {
		config.ShowSubscribeDrama = defaultConfig.ShowSubscribeDrama
	}
	if config.ShowUserCollect == nil {
		config.ShowUserCollect = defaultConfig.ShowUserCollect
	}

	// 确保 MessageNotification 存在
	if config.MessageNotification == nil {
		config.MessageNotification = defaultConfig.MessageNotification
	} else if defaultConfig.MessageNotification != nil {
		// 确保 MessageNotification 各字段都有值
		if config.MessageNotification.AtMe == nil {
			config.MessageNotification.AtMe = defaultConfig.MessageNotification.AtMe
		}
		if config.MessageNotification.Like == nil {
			config.MessageNotification.Like = defaultConfig.MessageNotification.Like
		}
		if config.MessageNotification.Comment == nil {
			config.MessageNotification.Comment = defaultConfig.MessageNotification.Comment
		}
		if config.MessageNotification.PrivateMessage == nil {
			config.MessageNotification.PrivateMessage = defaultConfig.MessageNotification.PrivateMessage
		}
		if config.MessageNotification.Live == nil {
			config.MessageNotification.Live = defaultConfig.MessageNotification.Live
		}
		if config.MessageNotification.InterestRecommend == nil {
			config.MessageNotification.InterestRecommend = defaultConfig.MessageNotification.InterestRecommend
		}
	}
}

// GetUserConfig 获取用户配置
func GetUserConfig(userID int64, buvid string, registerTime int64) (*MUserConfig, error) {
	if userID <= 0 && buvid == "" {
		// 若未登录且无 buvid，则返回默认用户配置
		return NewUserConfig(userID, buvid, registerTime), nil
	}

	// 登录时查找用户的配置，否则查找设备未登录前的配置
	var condition map[string]interface{}
	if userID > 0 {
		condition = map[string]interface{}{
			"user_id": userID,
			"buvid":   "",
		}
	} else {
		condition = map[string]interface{}{
			"user_id": 0,
			"buvid":   buvid,
		}
	}

	config := &MUserConfig{}
	err := config.DB().Where(condition).Take(config).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			// 记录不存在，创建新配置
			return NewUserConfig(userID, buvid, registerTime), nil
		}
		return nil, err
	}

	// 补充默认配置，确保后续新增的配置项在老记录中存在
	defaultConfig := DefaultAppConfig(userID, registerTime)
	mergeDefaultConfig(&config.AppConfig, defaultConfig)

	return config, nil
}
