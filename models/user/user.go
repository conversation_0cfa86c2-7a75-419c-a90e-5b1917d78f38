package user

import (
	"errors"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "mowangskuser"

// Simple 简单用户信息结构
type Simple struct {
	ID           int64  `gorm:"column:id"`
	Confirm      uint   `gorm:"column:confirm"`
	UserName     string `gorm:"column:username"`
	IconURL      string `gorm:"column:iconurl"`
	UserIntro    string `gorm:"column:userintro"`
	Avatar       string `gorm:"column:avatar"`
	IconType     int64  `gorm:"column:icontype"`
	BoardIconURL string `gorm:"column:boardiconurl"`
}

// MowangskUser model
type MowangskUser struct {
	Simple

	CIP             string `gorm:"column:cip"`
	UIP             string `gorm:"column:uip"`
	CTime           int64  `gorm:"column:ctime"`
	UTime           int64  `gorm:"column:utime"`
	Quanxian        string `gorm:"column:quanxian"`
	TeamID          int64  `gorm:"column:teamid"`
	TeamName        string `gorm:"column:teamname"`
	Ban             int    `gorm:"column:ban"`
	Ustr            int64  `gorm:"column:ustr"`
	Uint            int64  `gorm:"column:uint"`
	Uagi            int64  `gorm:"column:uagi"`
	Point           int64  `gorm:"column:point"`
	NowSound        int64  `gorm:"column:nowsound"`
	IconID          int64  `gorm:"column:iconid"`
	IconColor       string `gorm:"column:iconcolor"`
	Subtitle        string `gorm:"column:subtitle"`
	BoardIconID     int64  `gorm:"column:boardiconid"`
	BoardIconColor  string `gorm:"column:boardiconcolor"`
	CoverID         int64  `gorm:"column:coverid"`
	CoverURL        string `gorm:"column:coverurl"`
	IsNewMsg        int    `gorm:"column:isnewmsg"`
	UserIntroAudio  int64  `gorm:"column:userintro_audio"`
	LikeNum         int64  `gorm:"column:likenum"`
	FansNum         int64  `gorm:"column:fansnum"`
	FollowNum       int64  `gorm:"column:follownum"`
	SoundNum        int64  `gorm:"column:soundnum"`
	AlbumNum        int64  `gorm:"column:albumnum"`
	ImageNum        int64  `gorm:"column:imagenum"`
	FeedNum         int64  `gorm:"column:feednum"`
	SoundNumChecked int64  `gorm:"column:soundnumchecked"`
	ImageNumChecked int64  `gorm:"column:imagenumchecked"`
	MLevel          int    `gorm:"column:mlevel"`
	CoverURLNew     string `gorm:"column:coverurl_new"`

	Authenticated uint `gorm:"-"`
	Bind          bool `gorm:"-"`
}

// TableName for current model
func (MowangskUser) TableName() string {
	return tableName
}

// DB the db instance of MowangskUser model
func (m MowangskUser) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// 用户头像类型
const (
	UserIconTypeCartoon = iota // 二次元头像
	UserIconTypeNormal         // 普通头像
	UserIconTypeSpecial        // 特殊头像
)

// AfterFind is a GORM hook for query
func (s *Simple) AfterFind() error {
	switch s.IconType {
	case UserIconTypeCartoon:
		boardIconURL := s.BoardIconURL
		if boardIconURL == "" {
			boardIconURL = "icon01.png"
		}
		s.IconURL = service.Storage.Parse(params.URL.ProfileURL + boardIconURL)
	case UserIconTypeNormal:
		avatar := s.Avatar
		if avatar == "" {
			avatar = "icon01.png"
		}
		s.IconURL = service.Storage.Parse(params.URL.AvatarURL + avatar)
	}

	return nil
}

// FindUserIDs 查找指定 UserIDs 中存在的用户 IDs
func FindUserIDs(userIDs []int64) ([]int64, error) {
	var checkedUserIDs []int64
	err := MowangskUser{}.DB().
		Where("id IN (?)", userIDs).
		Pluck("id", &checkedUserIDs).Error
	if err != nil {
		return nil, err
	}
	return checkedUserIDs, nil
}

// FindOne 查询用户信息
func FindOne(id int64) (*MowangskUser, error) {
	user := new(MowangskUser)
	err := MowangskUser{}.DB().Where("id = ?", id).Take(user).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return user, nil
}

var (
	// ErrUserNotFound user not found error
	ErrUserNotFound = errors.New("user not found")
)

// GetCTimeByID 获取用户创建时间
func (m MowangskUser) GetCTimeByID(id int64) (int64, error) {
	var ctime int64
	err := m.DB().Select("ctime").Where("id = ?", id).Row().Scan(&ctime)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, ErrUserNotFound
		}
		return 0, err
	}
	return ctime, nil
}
