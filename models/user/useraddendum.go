package user

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 用户性别
const (
	// SexUnknown 未知性别
	SexUnknown = 0
	// SexBoy 男性
	SexBoy = 1
	// SexGirl 女性
	SexGirl = 2
)

// UserAddendum model
type UserAddendum struct {
	ID            int64      `gorm:"column:id;primary_key"`
	Sex           *int       `gorm:"column:sex"`
	Birthday      *time.Time `gorm:"column:birthday"`
	QQ            *string    `gorm:"column:qq"`
	Weibo         *string    `gorm:"column:weibo"`
	Wechat        *string    `gorm:"column:wechat"`
	BiliBili      *string    `gorm:"column:bilibili"`
	Apple         *string    `gorm:"column:apple"`
	MessageConfig *string    `gorm:"column:message_config"`
	IP            string     `gorm:"column:ip"`
	IPDetail      *string    `gorm:"column:ip_detail"`
	BirthdateMMDD *string    `gorm:"column:birthdate_mmdd"`
}

// TableName for current model
func (UserAddendum) TableName() string {
	return "user_addendum"
}

// DB the db instance of UserAddendum model
func (u UserAddendum) DB() *gorm.DB {
	return service.DB.Table(u.TableName())
}

// GetUserSex 获取用户性别
func GetUserSex(userID int64) int {
	var userAddendum UserAddendum
	err := UserAddendum{}.DB().Select("sex").Where("id = ?", userID).Take(&userAddendum).Error
	if err != nil {
		if !servicedb.IsErrNoRows(err) {
			logger.WithField("user_id", userID).Errorf("查询用户性别失败: %v", err)
			// PASS
		}
		return SexUnknown
	}
	sex := userAddendum.Sex
	if sex == nil {
		return SexUnknown
	}
	return *sex
}
