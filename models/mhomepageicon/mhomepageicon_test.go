package mhomepageicon

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MHomepageIcon{}, "id", "create_time", "modified_time", "sort",
		"title", "icon", "dark_icon", "url", "tab_id", "type", "archive", "anchor_name")
}

func TestMHomepageIcon_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MHomepageIcon{}.TableName())
}

func TestListIcons(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有我的页图标
	require.NoError(MHomepageIcon{}.DB().Delete("", "id > 0 AND type = ?", TypeIconHomepage).Error)
	res, err := ListIcons()
	require.NoError(err)
	assert.NotNil(res)
	assert.Len(res, 0)

	// 测试获取我的页图标
	var ms []MHomepageIcon
	for i := 4; i > 0; i-- {
		m := MHomepageIcon{
			Sort:       int64(i),
			Title:      "测试图标名 " + strconv.Itoa(i),
			Icon:       "test://homepage/icon/1.png",
			DarkIcon:   "test://homepage/dark_icon/1.png",
			URL:        "missevan://drama/59723",
			AnchorName: "message",
			Archive:    ArchiveOnline,
			Type:       TypeIconHomepage,
		}
		if i == 4 {
			m.Archive = ArchiveHistory
		}
		if i == 2 {
			m.Type = TypeIconTab
		}
		ms = append(ms, m)
	}
	require.NoError(servicedb.BatchInsert(service.DB, tableName, ms))
	res, err = ListIcons()
	require.NoError(err)
	require.Len(res, 2)
	assert.EqualValues(4, res[0].ID)
	assert.Equal("测试图标名 1", res[0].Title)
	assert.Equal("https://static-test.maoercdn.com/homepage/icon/1.png", res[0].IconURL)
	assert.Equal("https://static-test.maoercdn.com/homepage/dark_icon/1.png", res[0].DarkIconURL)
	assert.Equal("missevan://drama/59723", res[0].URL)
	assert.Equal("message", res[0].AnchorName)
	assert.Equal("message", res[0].Name)
	assert.EqualValues(2, res[1].ID)
	assert.Equal("测试图标名 3", res[1].Title)
	assert.Equal("https://static-test.maoercdn.com/homepage/icon/1.png", res[1].IconURL)
	assert.Equal("https://static-test.maoercdn.com/homepage/dark_icon/1.png", res[1].DarkIconURL)
	assert.Equal("missevan://drama/59723", res[1].URL)
	assert.Equal("message", res[1].AnchorName)
}
