package mhomepageicon

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

// 推荐位状态
const (
	ArchiveOnline  = 0 // 在线的推荐位
	ArchiveHistory = 1 // 历史归档的推荐位
)

// Icon 类型
const (
	TypeIconHomepage = 1 // 我的页图标
	TypeIconTab      = 2 // 首页 Tab 下的图标
)

const tableName = "m_homepage_icon"

// MHomepageIcon 我的页图标
type MHomepageIcon struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Sort         int64  `gorm:"column:sort"`        // 排序
	Title        string `gorm:"column:title"`       // 图标名
	Icon         string `gorm:"column:icon"`        // 简洁白模式的入口图标
	DarkIcon     string `gorm:"column:dark_icon"`   // 黑夜模式的入口图标
	URL          string `gorm:"column:url"`         // 地址
	TabID        int64  `gorm:"column:tab_id"`      // 对应 Tab ID
	Type         int    `gorm:"column:type"`        // 图标类型：1：我的页图标；2：首页 tab 所属图标
	Archive      int    `gorm:"column:archive"`     // 是否为历史归档 0 ：否， 1 ：是（归档）
	AnchorName   string `gorm:"column:anchor_name"` // 图标名称标识（只有提示红点的图标设置此字段）

	IconURL     string `gorm:"-"`
	DarkIconURL string `gorm:"-"`
	Name        string `gorm:"-"`
}

// DB the db instance of MHomepageIcon model
func (m MHomepageIcon) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MHomepageIcon) TableName() string {
	return tableName
}

// AfterFind is a GORM hook for query
func (m *MHomepageIcon) AfterFind() error {
	if m.Icon != "" {
		m.IconURL = service.Storage.Parse(m.Icon)
	}
	if m.DarkIcon != "" {
		m.DarkIconURL = service.Storage.Parse(m.DarkIcon)
	}
	if m.AnchorName != "" {
		// 后台设置了 name 有值，则返回
		// 用于标记 icon 的唯一标识，对应关系可在 m_homepage_icon 表查看
		m.Name = m.AnchorName
	}
	return nil
}

// ListIcons 获取我的页图标
func ListIcons() ([]MHomepageIcon, error) {
	var icons []MHomepageIcon
	err := MHomepageIcon{}.DB().Select("id, title, icon, dark_icon, url, anchor_name").
		Where("archive = ? AND type = ?", ArchiveOnline, TypeIconHomepage).Order("sort ASC").
		Find(&icons).Error
	return icons, err
}
