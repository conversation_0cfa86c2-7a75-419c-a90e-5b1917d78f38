package checkeddramareview

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramataginfo"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestCheckedDramaReviewTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(CheckedDramaReview{},
		"id", "drama_id", "name", "cover", "cover_color", "abstract", "integrity",
		"age", "origin", "author", "birthday", "cv", "ip", "ipname", "type", "newest",
		"organization_id", "user_id", "username", "catalog", "alias", "episodes", "tags")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(CheckedDramaReview{},
		"id", "drama_id", "name", "cover", "cover_color", "abstract", "integrity",
		"age", "origin", "author", "birthday", "cv", "ip", "ipname", "type", "newest",
		"organization_id", "user_id", "username", "catalog", "alias", "episodes", "tags")
}

func TestCheckedDramaReview_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("checked_drama_review", tableName)
	assert.Equal("checked_drama_review", CheckedDramaReview{}.TableName())
}

func TestCheckedDramaReview_UnmarshalEpisodes(t *testing.T) {
	assert := assert.New(t)
	cdr := new(CheckedDramaReview)

	// 测试 cdr.Episodes 为空字符串时
	epi, err := cdr.UnmarshalEpisodes()
	assert.NoError(err)
	assert.Nil(epi)

	// 测试 cdr.Episodes 不为空字符串时
	assert.NoError(cdr.DB().First(cdr, 2).Error)
	epi, err = cdr.UnmarshalEpisodes()
	assert.NoError(err)
	assert.NotNil(epi)
	assert.Len(epi.Delete, 1)
	assert.Len(epi.Update, 2)
}

func TestCheckReviewInfo(t *testing.T) {
	assert := assert.New(t)

	var l CheckedDramaReview
	var r CheckedDramaReview

	// 测试 *string
	assert.False(checkReviewInfo(l.Alias, r.Alias))
	str := "测试 *string"
	r.Alias = &str
	assert.True(checkReviewInfo(l.Alias, r.Alias))
	str1 := "测试 *string"
	l.Alias = &str1
	assert.False(checkReviewInfo(l.Alias, r.Alias))

	// 测试 *int8
	assert.False(checkReviewInfo(l.Integrity, r.Integrity))
	in := int8(11)
	r.Integrity = &in
	assert.True(checkReviewInfo(l.Integrity, r.Integrity))
	in2 := int8(11)
	l.Integrity = &in2
	assert.False(checkReviewInfo(l.Integrity, r.Integrity))

	// 测试 int64
	assert.False(checkReviewInfo(l.UserID, r.UserID))
	r.UserID = int64(11)
	assert.True(checkReviewInfo(l.UserID, r.UserID))
	l.UserID = int64(11)
	assert.False(checkReviewInfo(l.UserID, r.UserID))

	// 测试 int
	assert.False(checkReviewInfo(l.CoverColor, r.CoverColor))
	r.CoverColor = 11
	assert.True(checkReviewInfo(l.CoverColor, r.CoverColor))
	l.CoverColor = 11
	assert.False(checkReviewInfo(l.CoverColor, r.CoverColor))

	// 测试 string
	assert.False(checkReviewInfo(l.UserName, r.UserName))
	r.UserName = "测试 string"
	assert.True(checkReviewInfo(l.UserName, r.UserName))
	l.UserName = "测试 string"
	assert.False(checkReviewInfo(l.UserName, r.UserName))
}

func TestCheckedDramaReview_ReassignDramaInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	name := "测试修改剧集名称 1"
	cover := "20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg"
	author := "测试修改原作者 1"
	abstract := "测试修改剧集简介 1"
	integrity := int8(2)
	age := int8(1)
	origin := int8(2)
	cv := "心外無物"
	ipname := "灰色庭园"
	alias := "测试修改剧集别名 1"
	cdr := CheckedDramaReview{
		Name:           &name,
		Cover:          &cover,
		CoverColor:     1234,
		Abstract:       &abstract,
		Integrity:      &integrity,
		Age:            &age,
		Origin:         &origin,
		Author:         &author,
		Birthday:       int8(1),
		CV:             &cv,
		IP:             int8(1),
		IPName:         &ipname,
		Type:           dramainfo.TypeEnd,
		Newest:         "微小剧",
		OrganizationID: 74,
		UserID:         12,
		UserName:       "十一",
		Catalog:        dramainfo.CatalogIDAudioBook,
		Alias:          &alias,
		Tags:           "6,2",
	}
	edi := dramainfo.RadioDramaDramainfo{}

	err := cdr.ReassignDramaInfo(&edi)
	require.NoError(err)
	// 重新赋值后
	assert.Equal(*edi.Name, *cdr.Name)
	assert.Equal(*edi.Cover, *cdr.Cover)
	assert.Equal(edi.CoverURL, service.Storage.Parse(config.Conf.Params.URL.DramaCoverURL+cover))
	assert.Equal(edi.CoverColor, cdr.CoverColor)
	assert.Equal(*edi.Abstract, *cdr.Abstract)
	assert.Equal(*edi.Integrity, *cdr.Integrity)
	assert.Equal(edi.Age, cdr.Age)
	assert.Equal(edi.Origin, cdr.Origin)
	assert.Equal(*edi.Author, *cdr.Author)
	assert.Equal(edi.Birthday, cdr.Birthday)
	assert.Equal(*edi.CV, *cdr.CV)
	assert.Equal(edi.IP, cdr.IP)
	assert.Equal(edi.IPName, cdr.IPName)
	assert.Equal(edi.Type, cdr.Type)
	assert.Equal(edi.Newest, cdr.Newest)
	assert.Equal(edi.OrganizationID, cdr.OrganizationID)
	assert.Equal(edi.UserID, cdr.UserID)
	assert.Equal(edi.UserName, cdr.UserName)
	assert.Equal(edi.Catalog, cdr.Catalog)
	assert.Equal(*edi.Alias, *cdr.Alias)
	// 断言标签
	require.Len(edi.Tags, 2)
	assert.EqualValues(6, edi.Tags[0].ID)
	assert.EqualValues("轻松", edi.Tags[0].Name)
	assert.EqualValues(radiodramataginfo.TypeContent, edi.Tags[0].Type)
	assert.EqualValues(2, edi.Tags[1].ID)
	assert.EqualValues("都市", edi.Tags[1].Name)
	assert.EqualValues(radiodramataginfo.TypeClassification, edi.Tags[1].Type)
}

func TestCheckedDramaReview_ReassignDramaEpisodesInfo(t *testing.T) {
	assert := assert.New(t)

	cr := &Episode{
		RadioDramaEpisode: dramaepisode.RadioDramaEpisode{
			Name:         "测试新增单集名称 1",
			DramaID:      int64(100),
			SoundID:      int64(20),
			Date:         int64(1639481930),
			Order:        1,
			Type:         1,
			PayType:      1,
			Subtitle:     "测试新增单集副标题 1",
			CreateTime:   int64(1639481930),
			ModifiedTime: int64(1639481930),
		},
		EpisodeID: int64(105),
	}

	up := &Episode{
		RadioDramaEpisode: dramaepisode.RadioDramaEpisode{
			Name:         "测试修改单集名称 2",
			DramaID:      int64(100),
			SoundID:      int64(22),
			Date:         int64(1639481920),
			Type:         2,
			PayType:      2,
			Subtitle:     "单集修改副标题 2",
			CreateTime:   int64(1639481920),
			ModifiedTime: int64(1639481920),
		},
		EpisodeID: int64(103),
	}

	del := &Episode{
		RadioDramaEpisode: dramaepisode.RadioDramaEpisode{
			Name:         "测试删除单集名称 1",
			DramaID:      int64(100),
			SoundID:      int64(23),
			Date:         int64(1639481925),
			Type:         3,
			PayType:      3,
			Subtitle:     "单集删除副标题3",
			CreateTime:   int64(1639481925),
			ModifiedTime: int64(1639481925),
		},
		EpisodeID: int64(104),
	}

	es := Episodes{
		Create: []*Episode{cr},
		Update: []*Episode{up},
		Delete: []*Episode{del},
	}

	noupdate := dramaepisode.RadioDramaEpisode{
		ID:      102,
		Name:    "测试未修改",
		SoundID: 235,
	}

	cdr := CheckedDramaReview{Episodes: tutil.SprintJSON(es)}
	de := []dramaepisode.RadioDramaEpisode{
		noupdate,
		{
			ID:      103,
			Name:    "测试修改前单集名称",
			SoundID: 25,
			Order:   2,
		},
		{ID: 104},
	}

	assert.NoError(cdr.ReassignDramaEpisodesInfo(&de))

	// 重新赋值后
	assert.Len(de, 3)
	// 测试更新
	testUpdate := dramaepisode.RadioDramaEpisode{ID: 103, Order: 2}
	up.setEpisode(&testUpdate, false)
	assert.Equal(testUpdate, de[2])
	// 测试新增
	testCreate := dramaepisode.RadioDramaEpisode{}
	cr.setEpisode(&testCreate, true)
	assert.Equal(testCreate, de[1])
	// 测试未修改
	assert.Equal(noupdate, de[0])
}

func TestSetEpisode(t *testing.T) {
	assert := assert.New(t)

	rde := dramaepisode.RadioDramaEpisode{}

	es := &Episode{
		RadioDramaEpisode: dramaepisode.RadioDramaEpisode{
			Name:     "测试新增单集名称",
			DramaID:  100,
			SoundID:  20,
			Date:     1639481930,
			Order:    2,
			Type:     1,
			PayType:  1,
			Subtitle: "测试新增单集副标题",
		},
		EpisodeID: 104,
	}

	es.setEpisode(&rde, true)

	assert.Equal(rde.Name, es.Name)
	assert.Equal(rde.DramaID, es.DramaID)
	assert.Equal(rde.SoundID, es.SoundID)
	assert.Equal(rde.Date, es.Date)
	assert.Equal(rde.Order, es.Order)
	assert.Equal(rde.Type, es.Type)
	assert.Equal(rde.PayType, es.PayType)
	assert.Equal(rde.Subtitle, es.Subtitle)
}

func TestGetDramaCheckStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集不存在
	res, err := GetDramaCheckStatus([]int64{10000000})
	require.NoError(err)
	require.NotNil(res)
	assert.Len(res, 0)

	// 测试剧集不是再审状态
	res, err = GetDramaCheckStatus([]int64{52355})
	assert.EqualValues(dramainfo.CheckedPass, res[52355].Checked)
	assert.Zero(res[52355].ReviewDramaID)

	// 测试剧集为再审状态
	res, err = GetDramaCheckStatus([]int64{52364})
	assert.EqualValues(dramainfo.CheckedPass, res[52364].Checked)
	assert.NotZero(res[52364].ReviewDramaID)
}
