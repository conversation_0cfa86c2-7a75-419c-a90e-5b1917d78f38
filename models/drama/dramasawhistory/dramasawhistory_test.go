package dramasawhistory

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestDramaSawHistoryTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(DramaSawHistory{},
		"id", "user_id", "drama_id", "episode_id", "episode_name", "create_time", "update_time")
}

func TestDramaSawHistory_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, DramaSawHistory{}.TableName())
}

func TestFindDramaHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集不存在
	testDramaIDs := []int64{99998, 99999}
	testUserID := int64(1)
	data, err := FindDramaHistory(testDramaIDs, testUserID)
	require.NoError(err)
	assert.EqualValues(0, data[99998])
	assert.EqualValues(0, data[99999])

	// 测试剧集存在
	testDramaIDs = []int64{52348, 52349, 52350}
	data, err = FindDramaHistory(testDramaIDs, testUserID)
	require.NoError(err)
	assert.NotEmpty(data)
	require.EqualValues(len(data), len(testDramaIDs))
	assert.EqualValues(1730995200, data[52348])
	assert.EqualValues(1730995200, data[52349])
	assert.EqualValues(0, data[52350])
}

func TestListLastviewed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 所有剧集无收听记录
	testUserID := int64(90)
	testDramaIDs := []int64{2001, 2002, 2003}
	episodeList, err := ListLastviewed(testUserID, testDramaIDs)
	require.NoError(err)
	assert.Empty(episodeList)

	// 正常查询到记录
	testDramaIDs = []int64{101, 102, 103, 105}
	episodeList, err = ListLastviewed(testUserID, testDramaIDs)
	require.NoError(err)
	expectList := []*dramaepisode.RadioDramaEpisode{
		{DramaID: 101, SoundID: 1002},
		{DramaID: 102, SoundID: 2002},
	}
	assert.Equal(expectList, episodeList)
}
