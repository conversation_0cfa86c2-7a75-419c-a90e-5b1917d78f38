package dramasawhistory

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "drama_saw_history"

// DramaSawHistory 用户收听剧集记录表
type DramaSawHistory struct {
	ID          int64  `gorm:"column:id;primary_key"` // ID
	UserID      int64  `gorm:"column:user_id"`        // 用户 ID
	DramaID     int64  `gorm:"column:drama_id"`       // 剧集 ID
	EpisodeID   int64  `gorm:"column:episode_id"`     // 上次观看的单集 ID
	EpisodeName string `gorm:"column:episode_name"`   // 上次观看的单集名称
	CreateTime  int64  `gorm:"column:create_time"`    // 观看记录创建时间
	UpdateTime  int64  `gorm:"column:update_time"`    // 上次观看时间戳，单位：秒
}

// DB the db instance of DramaSawHistory model
func (d DramaSawHistory) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for DramaSawHistory model
func (d DramaSawHistory) TableName() string {
	return tableName
}

// FindDramaHistory 获取指定剧集观看记录
func FindDramaHistory(dramaIDs []int64, userID int64) (map[int64]int64, error) {
	var dramas []DramaSawHistory
	err := DramaSawHistory{}.DB().Select("drama_id, update_time").
		Where("drama_id IN (?) AND user_id = ?", dramaIDs, userID).
		Find(&dramas).Error
	if err != nil {
		return nil, err
	}
	dramaHistoryMap := make(map[int64]int64, len(dramaIDs))
	for _, drama := range dramas {
		dramaHistoryMap[drama.DramaID] = drama.UpdateTime
	}
	for _, dramaID := range dramaIDs {
		if _, ok := dramaHistoryMap[dramaID]; !ok {
			dramaHistoryMap[dramaID] = 0
		}
	}
	return dramaHistoryMap, nil
}

// ListLastviewed 用户在指定剧集中最近收听的单集信息
func ListLastviewed(userID int64, dramaIDs []int64) ([]*dramaepisode.RadioDramaEpisode, error) {
	var episodeIDs []int64
	err := DramaSawHistory{}.DB().
		Where("user_id = ? AND drama_id IN (?)", userID, dramaIDs).
		Pluck("episode_id", &episodeIDs).Error
	if err != nil {
		return nil, err
	}
	if len(episodeIDs) == 0 {
		return nil, nil
	}
	var episodeList []*dramaepisode.RadioDramaEpisode
	err = dramaepisode.RadioDramaEpisode{}.DB().Select("drama_id, sound_id").
		Where("id IN (?)", episodeIDs).
		Find(&episodeList).Error
	if err != nil {
		return nil, err
	}
	return episodeList, nil
}
