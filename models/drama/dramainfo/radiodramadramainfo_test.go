package dramainfo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, PayTypeFree)
	assert.EqualValues(1, PayTypeEpisode)
	assert.EqualValues(2, PayTypeDrama)

	assert.EqualValues(0, PoliceNo)
	assert.EqualValues(1, PoliceYes)
}

func TestRadioDramaDramainfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaDramainfo{},
		"id", "name", "cover", "cover_color", "abstract", "integrity", "age", "origin", "author",
		"birthday", "cv", "ip", "ipname", "type", "newest", "organization_id", "user_id", "username",
		"checked", "create_time", "lastupdate_time", "view_count", "comment_count", "catalog", "alias",
		"pay_type", "push", "refined", "police", "ip_id", "subscription_num", "vip")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RadioDramaDramainfo{},
		"id", "name", "cover", "cover_color", "abstract", "integrity", "age", "origin", "author",
		"birthday", "cv", "ip", "ipname", "type", "newest", "organization_id", "user_id", "username",
		"checked", "create_time", "lastupdate_time", "view_count", "catalog", "catalog_name", "alias",
		"pay_type", "push", "refined", "police", "ip_id", "subscription_num", "integrity_name", "type_name",
		"tags", "price", "purchased", "need_pay", "vip")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_dramainfo", tableName)
	assert.Equal("radio_drama_dramainfo", RadioDramaDramainfo{}.TableName())
}

func TestRadioDramaDramainfo_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var d RadioDramaDramainfo
	err := service.DramaDB.Take(&d).Error
	require.NoError(err)

	assert.NotEmpty(d.CoverURL)
}

func TestCatalogName(t *testing.T) {
	require := require.New(t)

	require.Equal("中文广播剧", catalogName(CatalogIDCnRadioDrama))
	require.Equal("中文有声漫画", catalogName(CatalogIDCnCartoon))
	require.Equal("日文广播剧", catalogName(CatalogIDJapanRadioDrama))
	require.Equal("日文有声漫画", catalogName(CatalogIDJapanAudioComics))
	require.Equal("播客", catalogName(CatalogIDRadio))
	require.Equal("轻小说", catalogName(CatalogIDAudioBookLightNovel))
	require.Equal("网络小说", catalogName(CatalogIDAudioBookNetwork))
	require.Equal("音乐", catalogName(CatalogIDMusic))
	require.Equal("催眠", catalogName(CatalogIDAsmr))
	require.Equal("儿童", catalogName(CatalogIDAudioBookChildren))
	require.Equal("声音恋人", catalogName(CatalogIDSoundLovers))
	require.Empty(catalogName(-1))
}

func TestDramaTypeName(t *testing.T) {
	require := require.New(t)

	require.Equal("全年龄", dramaTypeName(TypeGeneralAudience))
	require.Equal("纯爱", dramaTypeName(TypeBoysLove))
	require.Equal("双女主", dramaTypeName(TypeLesbian))
	require.Equal("言情", dramaTypeName(TypeRomantic))
	require.Equal("未完结", dramaTypeName(TypeSerializing))
	require.Equal("完结", dramaTypeName(TypeEnd))
	require.Equal("全一期", dramaTypeName(TypeOneAndMini))
	require.Equal("未知", dramaTypeName(-1))
}

func TestGetDramaViewCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取不存在剧集的播放量
	res, err := GetDramaViewCount(2333)
	require.NoError(err)
	assert.Zero(res)

	// 测试获取剧集播放量
	dramaID := int64(1)
	err = service.DramaDB.Table(tableName).Where("id = ?", dramaID).
		Update("view_count", 4).Error
	require.NoError(err)
	res, err = GetDramaViewCount(dramaID)
	require.NoError(err)
	assert.EqualValues(4, res)
}

func TestFindDramaInfoByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集不存在
	dramaInfo, err := FindDramaInfoByID(10000)
	require.NoError(err)
	assert.Nil(dramaInfo)

	// 测试剧集存在
	dramaInfo, err = FindDramaInfoByID(7)
	require.NoError(err)
	require.NotNil(dramaInfo)
	assert.EqualValues(7, dramaInfo.ID)
	assert.EqualValues("剧集名称（审核通过）", *dramaInfo.Name)
	assert.EqualValues(CheckedPass, dramaInfo.Checked)
	assert.EqualValues(1, dramaInfo.IPID)
	assert.NotNil(dramaInfo.Cover)
	assert.NotEmpty(dramaInfo.CoverURL)
	assert.EqualValues(12434877, dramaInfo.CoverColor)
	assert.Equal(PayTypeDrama, dramaInfo.PayType)
	assert.EqualValues(0, dramaInfo.Refined)
	assert.EqualValues(89, dramaInfo.Catalog)
}

func TestListDramaInfoByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集不存在
	dramaInfo, err := ListDramaInfoByIDs([]int64{99999})
	require.NoError(err)
	assert.Len(dramaInfo, 0)

	// 测试剧集存在
	dramaInfo, err = ListDramaInfoByIDs([]int64{7})
	require.NoError(err)
	require.Len(dramaInfo, 1)
	assert.EqualValues(7, dramaInfo[0].ID)
	require.NotNil(dramaInfo[0].Name)
	assert.EqualValues("剧集名称（审核通过）", *dramaInfo[0].Name)
	assert.EqualValues(CheckedPass, dramaInfo[0].Checked)
	assert.EqualValues(1, dramaInfo[0].IPID)
	assert.NotNil(dramaInfo[0].Cover)
	assert.NotEmpty(dramaInfo[0].CoverURL)
	assert.EqualValues(12434877, dramaInfo[0].CoverColor)
	assert.Equal(PayTypeDrama, dramaInfo[0].PayType)
	assert.EqualValues(0, dramaInfo[0].Refined)
	assert.EqualValues(4, dramaInfo[0].ViewCount)
	assert.EqualValues(4, dramaInfo[0].CommentCount)
	assert.EqualValues(89, dramaInfo[0].Catalog)
	require.NotNil(dramaInfo[0].Abstract)
	assert.EqualValues("简介", *dramaInfo[0].Abstract)
}

func TestListRankDramaInfoByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集中有擦边球 1 的剧集
	dramas, err := ListRankDramaInfoByIDs([]int64{8, 9})
	require.NoError(err)
	require.Len(dramas, 1)
	assert.EqualValues(8, dramas[0].ID)
	require.NotNil(dramas[0].Name)
	assert.EqualValues("剧集名称（审核通过）", *dramas[0].Name)
	assert.NotNil(dramas[0].Cover)
	assert.NotEmpty(dramas[0].CoverURL)
	assert.EqualValues(12434877, dramas[0].CoverColor)

	// 测试剧集为报警剧集
	dramas, err = ListRankDramaInfoByIDs([]int64{10})
	require.NoError(err)
	require.Empty(dramas)
}

func TestFindDramasViewCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集不存在
	data, err := FindDramasViewCount([]int64{99998, 99999})
	require.NoError(err)
	assert.Empty(data)

	// 测试剧集存在
	data, err = FindDramasViewCount([]int64{52348, 52349})
	require.NoError(err)
	assert.NotEmpty(data)
}

func TestListCheckPassDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testDramaIDs := []int64{100001, 100002, 100003}

	// 所有剧集不存在或未过审
	passDramaIDs, err := ListCheckPassDramaIDs(testDramaIDs)
	require.NoError(err)
	assert.Empty(passDramaIDs)

	// 部分剧集存在
	testDramaIDs = []int64{100001, 100002, 100003, 4}
	passDramaIDs, err = ListCheckPassDramaIDs(testDramaIDs)
	require.NoError(err)
	assert.Equal([]int64{4}, passDramaIDs)
}

func TestIncreaseViewCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dri := new(RadioDramaDramainfo)
	dramaID := int64(1)
	require.NoError(dri.DB().First(dri, dramaID).Error)
	num := dri.ViewCount

	plus := int64(7)
	err := IncreaseViewCount(dri.ID, plus)
	require.NoError(err)
	require.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(num+plus, dri.ViewCount)

	minus := -dri.ViewCount + 1
	err = IncreaseViewCount(dri.ID, minus)
	require.NoError(err)
	require.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(int64(1), dri.ViewCount)

	minus = -dri.ViewCount - 1
	err = IncreaseViewCount(dri.ID, minus)
	require.NoError(err)
	require.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(int64(0), dri.ViewCount)

	minus = -dri.ViewCount - 1
	err = IncreaseViewCount(dri.ID, minus)
	require.NoError(err)
	require.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(int64(0), dri.ViewCount)

	// 测试 panic
	assert.PanicsWithValue("num cannot be equal to 0", func() {
		err = IncreaseViewCount(dri.ID, 0)
	})
}

func TestIsUserPurchased(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rdd := new(RadioDramaDramainfo)

	// 测试剧集 ID 为 0
	pu, err := rdd.IsUserPurchased(12)
	assert.NoError(err)
	assert.False(pu)

	// 测试用户购买过剧集
	require.NoError(rdd.DB().First(rdd, 2).Error)
	pu, err = rdd.IsUserPurchased(12)
	assert.NoError(err)
	assert.True(pu)
}

func TestGetDramaIDBySoundID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试单音没有关联剧集
	dramaID, err := GetDramaIDBySoundID(1998)
	require.NoError(err)
	assert.Zero(dramaID)

	// 测试单音关联剧集 ID，但是剧集未通过审核或合约期下架
	RadioDramaDramainfo{}.DB().Where("id = ?", 300).
		Update("checked", CheckedPending)
	dramaID, err = GetDramaIDBySoundID(1999)
	require.NoError(err)
	assert.Zero(dramaID)

	// 测试单音关联剧集 ID，剧集通过审核
	RadioDramaDramainfo{}.DB().Where("id = ?", 300).
		Update("checked", CheckedPass)
	dramaID, err = GetDramaIDBySoundID(1999)
	require.NoError(err)
	assert.EqualValues(300, dramaID)
}

func TestGetDramaIDsBySoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试音频不在剧集中
	soundIDs := []int64{99999}
	dramaIDMap, err := GetDramaIDsBySoundIDs(soundIDs)
	require.NoError(err)
	assert.Len(dramaIDMap, 0)

	// 测试音频在剧集中
	soundIDs = []int64{99999, 1217701, 1217702}
	dramaIDMap, err = GetDramaIDsBySoundIDs(soundIDs)
	require.NoError(err)
	require.NotNil(dramaIDMap)
	require.Len(dramaIDMap, 2)
	assert.EqualValues(52353, dramaIDMap[1217701])
	assert.EqualValues(52354, dramaIDMap[1217702])
}

func TestFindSameIPRIDDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数为空
	dramaIDs, err := FindSameIPRIDDramaIDs([]int64{})
	require.NoError(err)
	require.Nil(dramaIDs)

	// 测试正常情况
	dramaIDs, err = FindSameIPRIDDramaIDs([]int64{52357, 52359, 52361})
	require.NoError(err)
	require.Len(dramaIDs, 3)
	assert.EqualValues(52359, dramaIDs[0])
	assert.EqualValues(52360, dramaIDs[1])
	assert.EqualValues(52361, dramaIDs[2])
}

func TestMapDramaInfoByIDs(t *testing.T) {
	dramaIDs := []int64{7, 8}
	dramaMap, err := MapDramaInfoByIDs(dramaIDs)
	require.NoError(t, err)
	require.Len(t, dramaMap, 2)

	drama7, exists := dramaMap[7]
	require.True(t, exists)
	assert.Equal(t, int64(7), drama7.ID)

	drama8, exists := dramaMap[8]
	require.True(t, exists)
	assert.Equal(t, int64(8), drama8.ID)
}
