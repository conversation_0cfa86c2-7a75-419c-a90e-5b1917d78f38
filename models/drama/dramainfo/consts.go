package dramainfo

// 剧集审核状态
const (
	CheckedPending         int8 = iota // 未审核
	CheckedPass                        // 审核通过
	CheckedDiscontinued                // 审核未通过（临时下架）
	CheckedPolice                      // 报警（待整合）
	CheckedContractExpired             // 合约到期下架
)

// 完结度
const (
	IntegrityNameSerializing int8 = iota + 1 // 长篇未完结
	IntegrityNameEnd                         // 长篇完结
	IntegrityNameOne                         // 全一期
	IntegrityNameMini                        // 微小剧
)

// 剧集分区 ID
const (
	CatalogIDEntertainment       int64 = 26  // 娱乐
	CatalogIDAudioBook           int64 = 86  // 听书
	CatalogIDAsmr                int64 = 87  // 催眠
	CatalogIDRadio               int64 = 88  // 播客
	CatalogIDCnRadioDrama        int64 = 89  // 中文广播剧
	CatalogIDJapanRadioDrama     int64 = 90  // 日文广播剧
	CatalogIDAudioBookLightNovel int64 = 91  // 轻小说
	CatalogIDAudioBookNetwork    int64 = 93  // 网络小说
	CatalogIDMusic               int64 = 94  // 音乐
	CatalogIDCartoon             int64 = 95  // 有声漫画
	CatalogIDCnCartoon           int64 = 96  // 中文有声漫画
	CatalogIDJapanAudioComics    int64 = 97  // 日文有声漫画
	CatalogIDAudioBookChildren   int64 = 98  // 儿童
	CatalogIDSoundLovers         int64 = 114 // 声音恋人
)

// 剧集类型
const (
	TypeGeneralAudience int32 = iota + 3 // 全年龄
	TypeBoysLove                         // 纯爱
	TypeLesbian                          // 橘气
	TypeRomantic                         // 言情
	TypeSerializing                      // 未完结
	TypeEnd                              // 完结
	TypeOneAndMini                       // 全一期
)

// 剧集的付费情况 0：免费；1：付费剧集未付费；2：付费剧集已付费
const (
	NeedPayFree int = iota
	NeedPayUnpaid
	NeedPayPaid
)

// refined 定义，此字段定义值与音频表（m_sound.refined）不同，注意区分
const (
	// RefinedRisking 是否为擦边球（比特位第一位为 1）
	RefinedRisking = iota + 1
	// RefinedJapanForbidden 是否为日本禁听（比特位第二位为 1）
	RefinedJapanForbidden
	// RefinedInteractive 是否为互动广播剧（比特位第三位为 1）
	RefinedInteractive
	// RefinedLossless 是否为无损音质广播剧（比特位第四位为 1）
	RefinedLossless
	// RefinedSpecial 是否为特殊剧集（比特位第五位为 1）
	RefinedSpecial
	// RefinedSensitive 是否为敏感剧集（比特位第六位为 1）
	RefinedSensitive
	// RefinedNoJapanSale 是否为日本地区禁购剧集（比特位第七位为 1）
	RefinedNoJapanSale
	// RefinedNoLiveRecommend 是否为无推荐直播模块剧集（比特位第八位为 1）
	RefinedNoLiveRecommend
	// RefinedLimitAddDanmaku 是否为限制用户发送弹幕（比特位第九位为 1）
	RefinedLimitAddDanmaku
	// RefinedSearchHidden 是否为搜索隐藏（比特位第十五位为 1）
	RefinedSearchHidden = 15
)

const (
	// RefinedBitMaskRisking 是否为擦边球（比特位第一位为 1）
	RefinedBitMaskRisking = 1 << (RefinedRisking - 1)
)
