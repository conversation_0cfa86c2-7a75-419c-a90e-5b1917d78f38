package dramainfo

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramataginfo"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 剧集的付费类型 0 免费；1 单集付费；2 整剧付费
const (
	PayTypeFree uint32 = iota
	PayTypeEpisode
	PayTypeDrama
)

// TODO: 待整合到 checked 字段中
// 是否是报警剧集 0 否；1 是
const (
	PoliceNo int8 = iota
	PoliceYes
)

// 是否为会员剧 0：否；1：是
const (
	VipNot   = iota
	VipDrama = 1
)
const tableName = "radio_drama_dramainfo"

// RadioDramaDramainfo 剧集信息表
type RadioDramaDramainfo struct {
	ID              int64        `gorm:"column:id" json:"id"`                             // 剧集 ID
	Name            *string      `gorm:"column:name" json:"name"`                         // 剧集名称
	Cover           *string      `gorm:"column:cover" json:"-"`                           // 剧集海报
	CoverURL        string       `gorm:"-" json:"cover"`                                  // 剧集海报的全路径
	CoverColor      int          `gorm:"column:cover_color" json:"cover_color"`           // RGB 颜色值
	Abstract        *string      `gorm:"column:abstract" json:"abstract"`                 // 剧集简介
	Integrity       *int8        `gorm:"column:integrity" json:"integrity"`               // 完结度
	IntegrityName   string       `gorm:"-" json:"integrity_name"`                         // 完结度命名
	Age             *int8        `gorm:"column:age" json:"age"`                           // 年代
	Origin          *int8        `gorm:"column:origin" json:"origin"`                     // 创作类型
	Author          *string      `gorm:"column:author" json:"author"`                     // 原作者
	Birthday        int8         `gorm:"column:birthday" json:"birthday"`                 // 生日剧
	CV              *string      `gorm:"column:cv" json:"cv"`                             // 生日 CV
	IP              int8         `gorm:"column:ip" json:"ip"`                             // 同人剧
	IPName          *string      `gorm:"column:ipname" json:"ipname"`                     // 原作标签
	Type            int32        `gorm:"column:type" json:"type"`                         // 分类类型
	TypeName        string       `gorm:"-" json:"type_name"`                              // 分类类型名称
	Newest          string       `gorm:"column:newest" json:"newest"`                     // 更新至
	OrganizationID  int64        `gorm:"column:organization_id" json:"organization_id"`   // 社团 ID
	UserID          int64        `gorm:"column:user_id" json:"user_id"`                   // 所属用户 ID
	UserName        string       `gorm:"column:username" json:"username"`                 // 所属用户名
	Checked         int8         `gorm:"column:checked" json:"checked"`                   // 审核状态（0 为未审核，1 为审核通过，2 为审核未通过）
	CreateTime      int64        `gorm:"column:create_time" json:"create_time"`           // 发布时间
	LastUpdateTime  int64        `gorm:"column:lastupdate_time" json:"lastupdate_time"`   // 最后编辑时间
	ViewCount       int64        `gorm:"column:view_count" json:"view_count"`             // 查看次数
	CommentCount    int64        `gorm:"column:comment_count" json:"-"`                   // 评论数
	Catalog         int64        `gorm:"column:catalog" json:"catalog"`                   // 剧集分类
	CatalogName     string       `gorm:"-" json:"catalog_name"`                           // 剧集分类名称
	Alias           *string      `gorm:"column:alias" json:"alias"`                       // 别名
	PayType         uint32       `gorm:"column:pay_type" json:"pay_type"`                 // 付费类型（0 免费，1 单音付费，2 剧集付费）
	Push            int8         `gorm:"column:push" json:"push"`                         // 新增单集是否过审推送
	Refined         util.BitMask `gorm:"column:refined" json:"refined"`                   // 属性，比特位第一位为 1 时标识擦边球
	Police          int8         `gorm:"column:police" json:"police"`                     // 是否报警
	IPID            int64        `gorm:"column:ip_id" json:"ip_id"`                       // 剧集所属 ip 的 ID
	SubscriptionNum float64      `gorm:"column:subscription_num" json:"subscription_num"` // 订阅（追剧）人数
	Vip             int          `gorm:"column:vip" json:"vip"`                           // 是否为会员剧（0：否；1：是）

	Tags      []radiodramataginfo.DramaTagInfo `gorm:"-" json:"tags"`
	Price     *int64                           `gorm:"-" json:"price"`
	Purchased *bool                            `gorm:"-" json:"purchased,omitempty"` // 用户是否购买过该剧集，之后应使用 need_pay
	NeedPay   *int                             `gorm:"-" json:"need_pay,omitempty"`  // 用户对剧集的付费状态
}

// DB the db instance of RadioDramaDramainfo model
func (d RadioDramaDramainfo) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaDramainfo model
func (d RadioDramaDramainfo) TableName() string {
	return tableName
}

// AfterFind is a GORM hook for query
func (d *RadioDramaDramainfo) AfterFind() error {
	// TODO: catalog
	if d.Cover != nil && *d.Cover != "" {
		a := config.Conf.Params.URL.DramaCoverURL
		d.CoverURL = service.Storage.Parse(a + *d.Cover)
	} else {
		d.CoverURL = service.Storage.Parse(config.Conf.Params.URL.DefaultCoverURL)
	}
	if d.Integrity != nil {
		d.IntegrityName = integrityName(*d.Integrity)
	}

	// 获取类型名称
	if d.Catalog != 0 {
		d.CatalogName = catalogName(d.Catalog)
	}

	// 获取分类名称
	if d.Type != 0 {
		d.TypeName = typeName(d.Catalog, d.Type, false)
	}

	return nil
}

func integrityName(integrity int8) string {
	switch integrity {
	case IntegrityNameSerializing:
		return "长篇未完结"
	case IntegrityNameEnd:
		return "长篇完结"
	case IntegrityNameOne:
		return "全一期"
	case IntegrityNameMini:
		return "微小剧"
	default:
		return ""
	}
}

func catalogName(catalogID int64) string {
	// TODO: 后续从 catalog 表中获取剧集类型名
	switch catalogID {
	case CatalogIDCnRadioDrama:
		return "中文广播剧"
	case CatalogIDCnCartoon:
		return "中文有声漫画"
	case CatalogIDJapanRadioDrama:
		return "日文广播剧"
	case CatalogIDJapanAudioComics:
		return "日文有声漫画"
	case CatalogIDRadio:
		return "播客"
	case CatalogIDAudioBookLightNovel:
		return "轻小说"
	case CatalogIDAudioBookNetwork:
		return "网络小说"
	case CatalogIDMusic:
		return "音乐"
	case CatalogIDAsmr:
		return "催眠"
	case CatalogIDAudioBookChildren:
		return "儿童"
	case CatalogIDSoundLovers:
		return "声音恋人"
	default:
		return ""
	}
}

func typeName(dramaCatalog int64, dramaType int32, isGeneral bool) string {
	// 若为获取泛称则返回相应的泛称
	// 目前仅中文广播剧或中文有声漫画有泛称
	if isGeneral {
		switch dramaCatalog {
		case CatalogIDCnRadioDrama:
			return "广播剧"
		case CatalogIDCnCartoon:
			return "有声漫画"
		}
	}

	// 88：播客
	if dramaCatalog == CatalogIDRadio {
		return "播客"
	}

	// 94：音乐；26：娱乐；87：催眠；114：声音恋人
	var czj = []int64{CatalogIDMusic, CatalogIDEntertainment, CatalogIDAsmr, CatalogIDSoundLovers}
	if util.HasElem(czj, dramaCatalog) {
		return "专辑"
	}

	// 96：中文有声漫画；89：中文广播剧；97：日文有声漫画；93：网络小说；91：轻小说
	var ca = []int64{
		CatalogIDCnCartoon,
		CatalogIDCnRadioDrama,
		CatalogIDJapanAudioComics,
		CatalogIDAudioBookNetwork,
		CatalogIDAudioBookLightNovel,
	}

	if util.HasElem(ca, dramaCatalog) {
		dt := dramaTypeName(dramaType)
		return dt
	}

	dt := japanDramaTypeName(dramaType)
	return dt
}

func dramaTypeName(dramaType int32) string {
	switch dramaType {
	case TypeGeneralAudience:
		return "全年龄"
	case TypeBoysLove:
		return "纯爱"
	case TypeLesbian:
		return "双女主"
	case TypeRomantic:
		return "言情"
	case TypeSerializing:
		return "未完结"
	case TypeEnd:
		return "完结"
	case TypeOneAndMini:
		return "全一期"
	default:
		return "未知"
	}
}

func japanDramaTypeName(dramaType int32) string {
	switch dramaType {
	case TypeGeneralAudience:
		return "一般"
	case TypeBoysLove:
		return "纯爱"
	case TypeRomantic:
		return "乙女"
	case TypeSerializing:
		return "未完结"
	case TypeEnd:
		return "完结"
	case TypeOneAndMini:
		return "全一期"
	default:
		return "未知"
	}
}

// GetDramaViewCount 根据剧集 ID 获取播放量
func GetDramaViewCount(dramaID int64) (int64, error) {
	var viewCount int64
	err := RadioDramaDramainfo{}.DB().Select("view_count").Where("id = ?", dramaID).Row().Scan(&viewCount)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	return viewCount, nil
}

// FindDramaInfoByID 根据剧集 ID 获取剧集信息
func FindDramaInfoByID(dramaID int64) (*RadioDramaDramainfo, error) {
	dramas, err := ListDramaInfoByIDs([]int64{dramaID})
	if err != nil {
		return nil, err
	}
	if len(dramas) == 0 {
		return nil, nil
	}
	return dramas[0], nil
}

// ListDramaInfoByIDs 根据剧集 ID 列表获取剧集信息
func ListDramaInfoByIDs(dramaIDs []int64) ([]*RadioDramaDramainfo, error) {
	var dramaInfo []*RadioDramaDramainfo
	err := RadioDramaDramainfo{}.DB().
		Select("id, name, checked, ip_id, cover, cover_color, pay_type, refined, view_count, comment_count, catalog, newest, abstract, vip, integrity").
		Where("id IN (?) AND checked IN (?) AND police <> ?",
			dramaIDs, []int8{CheckedPass, CheckedContractExpired}, PoliceYes).
		Find(&dramaInfo).Error
	if err != nil {
		return nil, err
	}

	return dramaInfo, nil
}

// FindDramaPayInfo 获取剧集付费信息
func FindDramaPayInfo(dramaIDs []int64, userID int64) ([]RadioDramaDramainfo, error) {
	var dramas []RadioDramaDramainfo
	err := RadioDramaDramainfo{}.DB().
		Select("id, pay_type, integrity, refined, vip").
		Where("id IN (?) AND checked IN (?)", dramaIDs, []int8{CheckedPass, CheckedContractExpired}).
		Find(&dramas).Error
	if err != nil {
		return nil, err
	}
	// 剧集添加 need_pay 信息
	err = CheckNeedPay(dramas, userID)
	if err != nil {
		return nil, err
	}
	return dramas, nil
}

// CheckNeedPay 给剧集添加用户付费情况
func CheckNeedPay(dramas []RadioDramaDramainfo, userID int64) error {
	dramasLen := len(dramas)
	if dramasLen == 0 {
		return nil
	}
	if userID == 0 {
		for index, drama := range dramas {
			if drama.PayType == PayTypeFree {
				dramas[index].NeedPay = util.NewInt(NeedPayFree)
			} else {
				dramas[index].NeedPay = util.NewInt(NeedPayUnpaid)
			}
		}
		return nil
	}
	// 需要检查付费状态的剧集 ID
	checkPayDramaIDs := make([]int64, 0, dramasLen)
	for index, drama := range dramas {
		if drama.PayType == PayTypeFree {
			dramas[index].NeedPay = util.NewInt(NeedPayFree)
		} else {
			checkPayDramaIDs = append(checkPayDramaIDs, drama.ID)
		}
	}
	// 获取已付费剧集 ID
	if len(checkPayDramaIDs) == 0 {
		return nil
	}
	paidDramaIDs := make([]int64, 0, len(checkPayDramaIDs))
	dramPaidTypes := []int{transaction.TypeSound, transaction.TypeDrama}
	// TODO: 使用 RPC 获取用户已付费的剧集 ID
	err := transaction.TransactionLog{}.DB().Select("gift_id").
		Where("from_id = ? AND status = ? AND gift_id IN (?) AND type IN (?)", userID, transaction.StatusSuccess,
			checkPayDramaIDs, dramPaidTypes).
		Pluck("gift_id", &paidDramaIDs).Error
	if err != nil {
		return err
	}
	paidDramaIDsMap := make(map[int64]bool, len(paidDramaIDs))
	for _, id := range paidDramaIDs {
		paidDramaIDsMap[id] = true
	}
	for index, drama := range dramas {
		if drama.PayType != PayTypeFree {
			if _, ok := paidDramaIDsMap[drama.ID]; ok {
				// 剧集需要付费，用户已付费
				dramas[index].NeedPay = util.NewInt(NeedPayPaid)
			} else {
				dramas[index].NeedPay = util.NewInt(NeedPayUnpaid)
			}
		}
	}
	return nil
}

// ListRankDramaInfoByIDs 根据剧集 ID 列表获取榜单剧集信息
func ListRankDramaInfoByIDs(dramaIDs []int64) ([]*RadioDramaDramainfo, error) {
	var dramas []*RadioDramaDramainfo
	err := RadioDramaDramainfo{}.DB().
		Select("id, name, cover, cover_color, view_count").
		Where("id IN (?) AND checked = ? AND police <> ? AND NOT refined & ?",
			dramaIDs, CheckedPass, PoliceYes, RefinedBitMaskRisking).
		Find(&dramas).Error
	if err != nil {
		return nil, err
	}
	return dramas, nil
}

// FindDramasViewCount 获取剧集播放量
func FindDramasViewCount(dramaIDs []int64) (map[int64]int64, error) {
	var dramasViewCount []RadioDramaDramainfo
	err := RadioDramaDramainfo{}.DB().
		Select("id, view_count").
		Where("id IN (?) AND checked IN (?)", dramaIDs, []int8{CheckedPass, CheckedContractExpired}).
		Find(&dramasViewCount).Error
	if err != nil {
		return nil, err
	}
	dramaIDViewCountMap := make(map[int64]int64, len(dramasViewCount))
	for _, item := range dramasViewCount {
		dramaIDViewCountMap[item.ID] = item.ViewCount
	}
	return dramaIDViewCountMap, nil
}

// ListCheckPassDramaIDs 获取指定剧集中过审剧集 IDs
func ListCheckPassDramaIDs(dramaIDs []int64) ([]int64, error) {
	var passDramaIDs []int64
	err := RadioDramaDramainfo{}.DB().
		Where("id IN (?) AND checked = ?", dramaIDs, CheckedPass).
		Pluck("id", &passDramaIDs).Error
	if err != nil {
		return nil, err
	}
	return passDramaIDs, err
}

// IncreaseViewCount 修改剧集查看次数（num 为负数时最后的差值结果不会为负数）
func IncreaseViewCount(dramaID, num int64, tx ...*gorm.DB) error {
	if num == 0 {
		panic("num cannot be equal to 0")
	}

	var db *gorm.DB
	if len(tx) != 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = RadioDramaDramainfo{}.DB()
	}

	db = db.Table(RadioDramaDramainfo{}.TableName()).Where("id = ?", dramaID)
	// 前台和后台往剧集里面添加单音时更新剧集的更新时间（而修改剧集信息时剧集更新时间不进行修改）
	if num > 0 {
		db = db.Update(map[string]interface{}{
			"view_count": gorm.Expr("view_count + ?", num),
		})
	} else {
		db = db.Update(map[string]interface{}{
			"view_count": servicedb.SubSatExpr("view_count", int(-num)),
		})
	}
	if err := db.Error; err != nil {
		return err
	}
	ra := db.RowsAffected
	if ra == 0 {
		logger.WithField("drama_id", dramaID).Error("剧集查看次数更新失败")
		// PASS
	}

	return nil
}

// IsUserPurchased 用户是否已购买过该剧集
func (d *RadioDramaDramainfo) IsUserPurchased(userID int64) (bool, error) {
	tran := new(transaction.TransactionLog)
	err := tran.DB().
		Where("from_id = ? AND gift_id = ? AND type = ? AND status = ?",
			userID, d.ID, transaction.TypeDrama, transaction.StatusSuccess).
		Take(tran).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// GetDramaIDBySoundID 获取 sound_id 对应的过审或合约期下架剧集 ID
func GetDramaIDBySoundID(soundID int64) (int64, error) {
	soundIDDramaIDMap, err := GetDramaIDsBySoundIDs([]int64{soundID})
	if err != nil {
		return 0, err
	}
	return soundIDDramaIDMap[soundID], nil
}

// GetDramaIDsBySoundIDs 根据音频 IDs 获取剧集 IDs
func GetDramaIDsBySoundIDs(soundIDs []int64) (map[int64]int64, error) {
	var episodes []dramaepisode.RadioDramaEpisode
	err := dramaepisode.RadioDramaEpisode{}.DB().Table(dramaepisode.RadioDramaEpisode{}.TableName()+" AS e").
		Select("e.drama_id, e.sound_id").
		Joins(fmt.Sprintf("LEFT JOIN %s AS d ON e.drama_id = d.id", RadioDramaDramainfo{}.TableName())).
		Where("e.sound_id IN (?) AND d.checked IN (?)",
			soundIDs, []int8{CheckedPass, CheckedContractExpired}).
		Find(&episodes).Error
	if err != nil {
		return nil, err
	}
	soundIDDramaIDMap := make(map[int64]int64, len(episodes))
	if len(episodes) == 0 {
		return soundIDDramaIDMap, nil
	}
	for _, episode := range episodes {
		soundIDDramaIDMap[episode.SoundID] = episode.DramaID
	}
	return soundIDDramaIDMap, nil
}

// FindSameIPRIDDramaIDs 获取跟 dramaIDs 相同 IPR ID 的剧集（包含 dramaIDs）
func FindSameIPRIDDramaIDs(dramaIDs []int64) ([]int64, error) {
	if len(dramaIDs) <= 0 {
		return nil, nil
	}

	var sameIPRIDDramaIDs []int64
	subQuery := RadioDramaDramainfo{}.DB().Select("DISTINCT ip_id").Where("id IN (?) AND ip_id > 0", dramaIDs).SubQuery()
	err := RadioDramaDramainfo{}.DB().
		Where("ip_id IN (?)", subQuery).
		Where("checked IN (?) AND police <> ?", []int8{CheckedPass, CheckedContractExpired}, PoliceYes).
		Pluck("id", &sameIPRIDDramaIDs).Error
	if err != nil {
		return nil, err
	}
	return sameIPRIDDramaIDs, nil
}

// MapDramaInfoByIDs 批量查询剧集信息
func MapDramaInfoByIDs(dramaIDs []int64) (map[int64]*RadioDramaDramainfo, error) {
	if len(dramaIDs) == 0 {
		return nil, nil
	}

	dramas, err := ListDramaInfoByIDs(dramaIDs)
	if err != nil {
		return nil, err
	}

	return util.ToMap(dramas, "ID").(map[int64]*RadioDramaDramainfo), nil
}
