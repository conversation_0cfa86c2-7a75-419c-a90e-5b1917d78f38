package dramareviewers

import (
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/service/dramaapi"
)

// GetDramaIDs 获取用户可以查看收益的剧集 ID
func GetDramaIDs(ctx mrpc.UserContext, userID int64) ([]int64, error) {
	result, err := dramaapi.GetReviewDramas(ctx, userID)
	if err != nil {
		return nil, err
	}
	dramaIDs := make([]int64, 0, len(result))
	for _, item := range result {
		dramaIDs = append(dramaIDs, item.DramaID)
	}

	return sets.Uniq[int64](dramaIDs), nil
}
