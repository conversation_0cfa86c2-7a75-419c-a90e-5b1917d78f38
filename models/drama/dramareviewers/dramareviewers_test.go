package dramareviewers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service/dramaapi"
)

func TestGetDramaIDs(t *testing.T) {
	cleanup := mrpc.SetMock(dramaapi.URIGetReviewDramas, func(input interface{}) (interface{}, error) {
		return &[]dramaapi.GetReviewDramasResp{
			{
				Name:    "剧集 91",
				DramaID: 91,
			},
			{
				Name:    "剧集 92",
				DramaID: 92,
			},
		}, nil
	})
	defer cleanup()

	require := require.New(t)
	assert := assert.New(t)
	dramaIDs, err := GetDramaIDs(mrpc.UserContext{}, 999)
	require.NoError(err)
	require.Len(dramaIDs, 2)
	assert.Equal(int64(91), dramaIDs[0])
	assert.Equal(int64(92), dramaIDs[1])
}
