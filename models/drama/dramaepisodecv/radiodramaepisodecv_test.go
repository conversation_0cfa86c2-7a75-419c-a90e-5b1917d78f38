package dramaepisodecv

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaEpisodeCvTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaEpisodeCv{},
		"id", "drama_id", "episode_id", "cv_id", "character", "main")
}

func TestRadioDramaEpisodeCv_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, RadioDramaEpisodeCv{}.TableName())
}

func TestListDramaMainOrMinorCvIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试声优不存在
	data, err := ListDramaMainOrMinorCvIDs([]int64{99998, 99999}, 1)
	require.NoError(err)
	assert.Empty(data)

	// 测试成功返回
	// 52348 剧集参演 cv 1、2，播放量 90，用户上次观看时间 1730995200
	// 52349 剧集参演 cv 1、3、5，播放量 100，用户上次观看时间 1730995200
	// 52350 剧集参演 cv 4，播放量 100，用户上次观看时间 0
	testDramaIDs := []int64{52348, 52349, 52350}
	data, err = ListDramaMainOrMinorCvIDs(testDramaIDs, 1)
	require.NoError(err)
	assert.NotEmpty(data)
	require.EqualValues(4, len(data))
	// 断言排序：最新收听时间倒序，收听时间一致时按剧集播放量倒序，同一剧集下按声优表 ID 顺序
	assert.EqualValues(1, data[0])
	assert.EqualValues(3, data[1])
	assert.EqualValues(2, data[2])
	assert.EqualValues(4, data[3])
}

func TestListDramaCvIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试声优不存在
	data, err := ListDramaCvIDs([]int64{99999})
	require.NoError(err)
	assert.Empty(data)

	// 测试成功返回
	data, err = ListDramaCvIDs([]int64{52348, 52348})
	require.NoError(err)
	assert.NotEmpty(data)
}

func TestListDramaCvsMain(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数为空时
	cvs, err := ListDramaCvsMain([]int64{})
	require.NoError(err)
	assert.Empty(cvs)

	cvs, err = ListDramaCvsMain([]int64{52355, 52357})
	require.NoError(err)
	require.Len(cvs, 3)
	assert.EqualValues(7, cvs[0].ID)
	assert.EqualValues(52355, cvs[0].DramaID)
	assert.EqualValues(8, cvs[1].ID)
	assert.EqualValues(52355, cvs[1].DramaID)
	assert.EqualValues(12, cvs[2].ID)
	assert.EqualValues(52357, cvs[2].DramaID)
}
