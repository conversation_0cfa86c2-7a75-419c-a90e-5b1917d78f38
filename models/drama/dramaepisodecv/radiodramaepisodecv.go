package dramaepisodecv

import (
	"sort"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramasawhistory"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "radio_drama_episode_cv"

// 参演角色类型
const (
	TypeCharacterMain     = iota + 1 // 主役
	TypeCharacterMinor               // 协役
	TypeCharacterCarefree            // 龙套
)

// RadioDramaEpisodeCv 单集声优参演信息表
type RadioDramaEpisodeCv struct {
	ID        int64  `gorm:"column:id;primary_key"` // ID
	DramaID   int64  `gorm:"column:drama_id"`       // 剧集 ID
	EpisodeID int64  `gorm:"column:episode_id"`     // 单集 ID
	CvID      int64  `gorm:"column:cv_id"`          // 声优 ID
	Character string `gorm:"column:character"`      // 参演角色
	Main      int    `gorm:"column:main"`           // 角色类型 1：主役；2：协役；3：龙套
}

// DB the db instance of RadioDramaEpisodeCv model
func (d RadioDramaEpisodeCv) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaEpisodeCv model
func (d RadioDramaEpisodeCv) TableName() string {
	return tableName
}

// ListDramaMainOrMinorCvIDs 获取剧集中参演的主役、协役声优 IDs
func ListDramaMainOrMinorCvIDs(dramaIDs []int64, userID int64) ([]int64, error) {
	// 获取剧集播放量
	dramaViewCountMap, err := dramainfo.FindDramasViewCount(dramaIDs)
	if err != nil {
		return nil, err
	}
	// 获取不到剧集表中的记录，说明剧集不存在，返回空数组
	var cvIDs []int64
	if len(dramaViewCountMap) == 0 {
		return cvIDs, nil
	}
	cvDramaIDs := make([]int64, 0, len(dramaViewCountMap))
	for dramaID := range dramaViewCountMap {
		cvDramaIDs = append(cvDramaIDs, dramaID)
	}
	var cvs []RadioDramaEpisodeCv
	err = RadioDramaEpisodeCv{}.DB().Select("id, cv_id, drama_id").
		Where("drama_id IN (?) AND main IN (?)", cvDramaIDs, []int64{TypeCharacterMain, TypeCharacterMinor}).
		Find(&cvs).Error
	if err != nil {
		return nil, err
	}
	if len(cvs) == 0 {
		return cvIDs, nil
	}
	// 获取剧集收听记录
	dramaHistoryMap, err := dramasawhistory.FindDramaHistory(cvDramaIDs, userID)
	if err != nil {
		return nil, err
	}
	// 声优 IDs 排序优先级：最新收听时间倒序，收听时间一致时按剧集播放量倒序，同一剧集下按声优表 ID 顺序
	sort.SliceStable(cvs, func(i, j int) bool {
		if cvs[i].DramaID != cvs[j].DramaID {
			if dramaHistoryMap[cvs[i].DramaID] == dramaHistoryMap[cvs[j].DramaID] {
				return dramaViewCountMap[cvs[i].DramaID] > dramaViewCountMap[cvs[j].DramaID]
			}
			return dramaHistoryMap[cvs[i].DramaID] > dramaHistoryMap[cvs[j].DramaID]
		}
		return cvs[i].ID < cvs[j].ID
	})
	for _, item := range cvs {
		cvIDs = append(cvIDs, item.CvID)
	}
	// 去重声优
	cvIDs = sets.Uniq(cvIDs)
	return cvIDs, nil
}

// ListDramaCvIDs 获取剧集中的参演声优 IDs
func ListDramaCvIDs(dramaIDs []int64) ([]int64, error) {
	var cvIDs []int64
	err := RadioDramaEpisodeCv{}.DB().
		Where("drama_id IN (?)", dramaIDs).
		Pluck("DISTINCT cv_id", &cvIDs).Error
	if err != nil {
		return nil, err
	}
	return cvIDs, nil
}

// ListDramaCvsMain 获取剧集 IDs 对应的主役声优信息（根据 ID 升序排序）
func ListDramaCvsMain(dramaIDs []int64) ([]RadioDramaEpisodeCv, error) {
	if len(dramaIDs) <= 0 {
		return nil, nil
	}

	var cvs []RadioDramaEpisodeCv
	err := RadioDramaEpisodeCv{}.DB().
		Where("drama_id IN (?) AND main = ?", dramaIDs, TypeCharacterMain).
		Order("id ASC").Find(&cvs).Error
	if err != nil {
		return nil, err
	}
	return cvs, nil
}
