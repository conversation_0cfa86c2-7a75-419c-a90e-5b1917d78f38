package dramainfoaddendum

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// DramaInfoAddendum 剧集附属信息表
type DramaInfoAddendum struct {
	ID           int64  `gorm:"column:id"`            // 对应剧集表 ID
	Thumbnail    string `gorm:"column:thumbnail"`     // 小图（App 显示）
	ShortIntro   string `gorm:"column:shortintro"`    // 一句话介绍（App 显示）
	UpdatePeriod string `gorm:"column:update_period"` // 剧集更新周期
	Outline      []byte `gorm:"column:outline"`       // 剧集概述（JSON）
	ThumbnailH   string `gorm:"column:thumbnail_h"`   // 宽图（B 站播放页推荐模块显示）
	More         []byte `gorm:"column:more"`          // 额外信息
}

const tableName = "drama_info_addendum"

// TableName for DramaInfoAddendum model
func (DramaInfoAddendum) TableName() string {
	return tableName
}

// DB the db instance of DramaInfoAddendum model
func (d DramaInfoAddendum) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// ListShortIntro 获取指定剧集 IDs 的一句话介绍信息
func ListShortIntro(IDs []int64) ([]DramaInfoAddendum, error) {
	var d []DramaInfoAddendum
	err := DramaInfoAddendum{}.DB().
		Select("id, shortintro").
		Where("id IN (?) AND shortintro <> ''", IDs).
		Scan(&d).Error
	if err != nil {
		return nil, err
	}
	return d, nil
}

// MapDramaInfoAddendum 获取剧集更多信息 map
func MapDramaInfoAddendum(dramaIDs []int64) (map[int64]DramaInfoAddendum, error) {
	if len(dramaIDs) <= 0 {
		return nil, nil
	}

	var moreDramaInfoList []DramaInfoAddendum
	err := DramaInfoAddendum{}.DB().Where("id IN (?)", dramaIDs).Find(&moreDramaInfoList).Error
	if err != nil {
		return nil, err
	}
	moreDramaInfoMap := util.ToMap(moreDramaInfoList, "ID").(map[int64]DramaInfoAddendum)
	return moreDramaInfoMap, nil
}
