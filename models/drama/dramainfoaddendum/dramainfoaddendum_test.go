package dramainfoaddendum

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(DramaInfoAddendum{},
		"id", "thumbnail", "shortintro", "update_period", "outline", "thumbnail_h", "more")
}

func TestListShortIntro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testIDs := []int64{1, 2, 3}
	list, err := ListShortIntro(testIDs)
	require.NoError(err)
	expectList := []DramaInfoAddendum{
		{
			ID:         2,
			ShortIntro: "一句话简介",
		},
	}
	assert.Equal(expectList, list)
}

func TestRadioDramaDramainfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(DramaInfoAddendum{}, "id", "thumbnail", "shortintro", "update_period", "outline", "thumbnail_h", "more")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("drama_info_addendum", DramaInfoAddendum{}.TableName())
}

func TestMapDramaInfoAddendum(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数为空时
	moreDramaInfoMap, err := MapDramaInfoAddendum([]int64{})
	require.NoError(err)
	assert.Nil(moreDramaInfoMap)

	// 测试正常情况
	moreDramaInfoMap, err = MapDramaInfoAddendum([]int64{52355, 52356})
	require.NoError(err)
	require.Len(moreDramaInfoMap, 2)
	require.NotEmpty(moreDramaInfoMap[52355])
	assert.Equal("测试一句话简介 52355", moreDramaInfoMap[52355].ShortIntro)
	require.NotEmpty(moreDramaInfoMap[52356])
	assert.Equal("测试一句话简介 52356", moreDramaInfoMap[52356].ShortIntro)
	require.Empty(moreDramaInfoMap[52357])

	// 测试剧集没有更多信息时
	moreDramaInfoMap, err = MapDramaInfoAddendum([]int64{52357})
	require.NoError(err)
	require.Empty(moreDramaInfoMap)
}
