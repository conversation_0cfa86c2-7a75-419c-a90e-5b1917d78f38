package radiodramatagdramamap

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "radio_drama_tag_drama_map"

// RadioDramaTagDramaMap 剧集和标签关联信息
type RadioDramaTagDramaMap struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间戳，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间戳，单位：秒
	DramaID      int64 `gorm:"column:drama_id"`       // 剧集 ID
	TagID        int64 `gorm:"column:tag_id"`         // 标签 ID
	Sort         int   `gorm:"column:sort"`           // 排序值
}

// DB the db instance of RadioDramaTagDramaMap model
func (d RadioDramaTagDramaMap) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaTagDramaMap model
func (d RadioDramaTagDramaMap) TableName() string {
	return tableName
}
