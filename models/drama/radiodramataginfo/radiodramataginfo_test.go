package radiodramataginfo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, RefinedBitMaskStatusUserInvisible)
	assert.EqualValues(2, RefinedBitMaskStatusUpInvisible)
	assert.EqualValues(1, TypeClassification)
	assert.EqualValues(2, TypeContent)
}

func TestListDramaUploadTags(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := ListDramaUploadTags()
	require.NoError(err)
	assert.NotNil(res)
}

func TestProcessTags(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	tags := []*RadioDramaTagInfo{
		&RadioDramaTagInfo{
			ID:   int64(1),
			Name: "古风",
			Type: TypeClassification,
		},
		&RadioDramaTagInfo{
			ID:   int64(2),
			Name: "甜",
			Type: TypeContent,
		},
		&RadioDramaTagInfo{
			ID:   int64(2),
			Name: "都市",
			Type: TypeClassification,
		},
	}
	data := processTags(tags)
	require.EqualValues(2, len(data))
	assert.Equal("分类标签", data[0].Label)
	assert.Equal(TypeClassification, data[0].Type)
	assert.EqualValues(1, data[0].Items[0].ID)
	assert.EqualValues(2, data[0].Items[1].ID)
	assert.Equal("内容标签", data[1].Label)
	assert.Equal(TypeContent, data[1].Type)
	assert.EqualValues(2, data[1].Items[0].ID)
}

func TestListDramaTagsByDramaID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试标签不存在
	data, err := ListDramaTagsByDramaID(99999)
	require.NoError(err)
	require.NotNil(data)
	assert.Empty(data)

	// 测试有标签
	data, err = ListDramaTagsByDramaID(1)
	require.NoError(err)
	require.NotNil(data)
	require.Len(data, 3)
	// 断言标签顺序
	assert.EqualValues(4, data[0].ID)
	assert.EqualValues("甜", data[0].Name)
	assert.EqualValues(TypeContent, data[0].Type)
	assert.EqualValues(5, data[1].ID)
	assert.EqualValues("虐", data[1].Name)
	assert.EqualValues(TypeContent, data[1].Type)
	assert.EqualValues(1, data[2].ID)
	assert.EqualValues("古风", data[2].Name)
	assert.EqualValues(TypeClassification, data[2].Type)
}

func TestListDramaTagsByIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试标签不存在
	data, err := ListDramaTagsByIDs([]int64{9999, 39008})
	require.NoError(err)
	require.NotNil(data)
	assert.Empty(data)

	// 测试有标签
	data, err = ListDramaTagsByIDs([]int64{4, 5, 1})
	require.NoError(err)
	require.NotNil(data)
	require.Len(data, 3)
	// 断言标签顺序
	assert.EqualValues(4, data[0].ID)
	assert.EqualValues("甜", data[0].Name)
	assert.EqualValues(TypeContent, data[0].Type)
	assert.EqualValues(5, data[1].ID)
	assert.EqualValues("虐", data[1].Name)
	assert.EqualValues(TypeContent, data[1].Type)
	assert.EqualValues(1, data[2].ID)
	assert.EqualValues("古风", data[2].Name)
	assert.EqualValues(TypeClassification, data[2].Type)
}

func TestMapUserVisibleDramaTags(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数为空时
	res, err := MapUserVisibleDramaTags([]int64{})
	require.NoError(err)
	assert.Empty(res)

	// 测试正常情况
	res, err = MapUserVisibleDramaTags([]int64{2, 3})
	require.NoError(err)
	require.Len(res, 2)
	require.NotEmpty(res[2])
	require.Len(res[2], 2)
	assert.EqualValues(5, res[2][0].ID)
	assert.Equal("虐", res[2][0].Name)
	assert.EqualValues(1, res[2][1].ID)
	assert.Equal("古风", res[2][1].Name)

	require.NotEmpty(res[3])
	require.Len(res[3], 3)
	assert.EqualValues(1, res[3][0].ID)
	assert.Equal("古风", res[3][0].Name)
	assert.EqualValues(5, res[3][1].ID)
	assert.Equal("虐", res[3][1].Name)
	assert.EqualValues(2, res[3][2].ID)
	assert.Equal("都市", res[3][2].Name)
}
