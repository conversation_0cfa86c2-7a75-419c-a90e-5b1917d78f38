package radiodramataginfo

import (
	"fmt"
	"sort"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/models/drama/radiodramatagdramamap"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "radio_drama_tag_info"

// 标签 ID
const (
	TagIDOther = 11 // 其他标签
)

// 标签属性（比特位）
const (
	RefinedBitMaskStatusUserInvisible = 1 << 0 // 对用户隐藏
	RefinedBitMaskStatusUpInvisible   = 1 << 1 // 对创作端隐藏
)

// 标签类型
const (
	TypeClassification = iota + 1 // 分类标签
	TypeContent                   // 内容标签
)

// RadioDramaTagInfo 剧集标签信息
type RadioDramaTagInfo struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间戳，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 更新时间戳，单位：秒
	Name         string `gorm:"column:name"`           // 标签名称
	Type         int    `gorm:"column:type"`           // 标签类型 1：分类标签；2：内容标签
	Status       int    `gorm:"column:status"`         // 标签属性 0：可见；比特位第 1 位为 1：对用户隐藏；比特位第 2 位为 1：对创作端隐藏
	Sort         int    `gorm:"column:sort"`           // 排序值
}

// DB the db instance of RadioDramaTagInfo model
func (d RadioDramaTagInfo) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaTagInfo model
func (d RadioDramaTagInfo) TableName() string {
	return tableName
}

// DramaTags 剧集分类标签、内容标签
type DramaTags struct {
	Label string `json:"label"`
	Type  int    `json:"type"`
	Items []tag  `json:"items"`
}

type tag struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// ListDramaUploadTags 获取剧集分类标签、内容标签
func ListDramaUploadTags() ([]DramaTags, error) {
	var dramaTags []*RadioDramaTagInfo
	err := RadioDramaTagInfo{}.DB().
		Select("id, name, type").
		Where("type IN (?) AND NOT (status & ?)", []int64{TypeClassification, TypeContent}, RefinedBitMaskStatusUpInvisible).
		Order("type ASC, sort ASC").
		Find(&dramaTags).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return processTags(dramaTags), nil
}

// processTags 按照类型分类标签
func processTags(tags []*RadioDramaTagInfo) []DramaTags {
	classificationTags := DramaTags{
		Label: "分类标签",
		Type:  TypeClassification,
	}
	contentTags := DramaTags{
		Label: "内容标签",
		Type:  TypeContent,
	}

	for _, v := range tags {
		tagItem := tag{
			ID:   v.ID,
			Name: v.Name,
		}

		switch v.Type {
		case TypeClassification:
			classificationTags.Items = append(classificationTags.Items, tagItem)
		case TypeContent:
			contentTags.Items = append(contentTags.Items, tagItem)
		default:
			panic(fmt.Sprintf("illegal tag type: %d", v.Type))
		}
	}

	var dramaTags []DramaTags
	if len(classificationTags.Items) != 0 {
		dramaTags = append(dramaTags, classificationTags)
	}
	if len(contentTags.Items) != 0 {
		dramaTags = append(dramaTags, contentTags)
	}
	return dramaTags
}

// DramaTagInfo 剧集绑定的标签信息
type DramaTagInfo struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	Type int64  `json:"type"`
}

// ListDramaTagsByDramaID 获取剧集绑定的标签信息
func ListDramaTagsByDramaID(dramaID int64) ([]DramaTagInfo, error) {
	var tags []DramaTagInfo
	err := service.DramaDB.Table(RadioDramaTagInfo{}.TableName()+" AS t1").
		Select("t1.id, t1.name, t1.type").
		Joins("INNER JOIN "+radiodramatagdramamap.RadioDramaTagDramaMap{}.TableName()+" AS t2 ON t2.tag_id = t1.id").
		Where("t2.drama_id = ?", dramaID).
		Order("t2.sort ASC").Find(&tags).Error
	if err != nil {
		return nil, err
	}
	return tags, nil
}

// ListDramaTagsByIDs 根据标签 IDs 获取标签信息
func ListDramaTagsByIDs(ids []int64) ([]DramaTagInfo, error) {
	if len(ids) == 0 {
		return []DramaTagInfo{}, nil
	}
	var tags []DramaTagInfo
	err := RadioDramaTagInfo{}.DB().Select("id, name, type").Where("id IN (?)", ids).
		Find(&tags).Error
	if err != nil {
		return nil, err
	}
	if len(tags) > 0 {
		// 按传进来的 IDs 排序
		idIndexMap := make(map[int64]int, len(ids))
		for index, id := range ids {
			idIndexMap[id] = index
		}
		sort.Slice(tags, func(i, j int) bool {
			return idIndexMap[tags[i].ID] < idIndexMap[tags[j].ID]
		})
	}
	return tags, nil
}

// MapUserVisibleDramaTags 批量获取用户可见的剧集标签 map
func MapUserVisibleDramaTags(dramaIDs []int64, ignoreOtherTag ...bool) (map[int64][]RadioDramaTagInfo, error) {
	if len(dramaIDs) <= 0 {
		return nil, nil
	}

	type dramaIDTagInfo struct {
		RadioDramaTagInfo
		DramaID int64 `gorm:"column:drama_id"`
	}
	var tags []dramaIDTagInfo
	query := service.DramaDB.Table(RadioDramaTagInfo{}.TableName()+" AS t1").
		Select("t1.*, t2.drama_id").
		Joins("INNER JOIN "+radiodramatagdramamap.RadioDramaTagDramaMap{}.TableName()+" AS t2 ON t2.tag_id = t1.id").
		Where("t2.drama_id IN (?) AND NOT status & ?", dramaIDs, RefinedBitMaskStatusUserInvisible)
	if len(ignoreOtherTag) > 0 && ignoreOtherTag[0] {
		query = query.Where("t1.id <> ?", TagIDOther)
	}
	err := query.Order("t2.drama_id ASC, t2.sort ASC").Find(&tags).Error
	if err != nil {
		return nil, err
	}
	dramaIDDramaTagsMap := make(map[int64][]RadioDramaTagInfo, len(tags))
	for _, dramaTag := range tags {
		dramaIDDramaTagsMap[dramaTag.DramaID] = append(dramaIDDramaTagsMap[dramaTag.DramaID],
			RadioDramaTagInfo{
				ID:     dramaTag.ID,
				Name:   dramaTag.Name,
				Type:   dramaTag.Type,
				Status: dramaTag.Status,
			})
	}
	return dramaIDDramaTagsMap, nil
}
