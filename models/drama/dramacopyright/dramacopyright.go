package dramacopyright

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

// 版权管理分发方式
const (
	TypeOther       = iota // 非独播
	TypeExclusive          // 独播
	TypeFirstLaunch        // 首发
)

// RadioDramaDramacopyright 版权信息表
type RadioDramaDramacopyright struct {
	ID                   int64  `gorm:"column:id;primary_key"`
	CreateTime           int64  `gorm:"column:create_time"`           // 创建时间
	ModifiedTime         int64  `gorm:"column:modified_time"`         // 修改时间
	DeleteTime           int64  `gorm:"column:delete_time"`           // 删除时间
	DramaID              int64  `gorm:"column:drama_id"`              // 剧集 ID
	Name                 string `gorm:"column:name"`                  // 剧集名称
	CopyrightAttribution int    `gorm:"column:copyright_attribution"` // 版本归属 0: 非自有版权; 1：自有版权
	CopyrightOwner       int    `gorm:"column:copyright_owner"`       // 出品方 0: 外部出品; 1: 自出品; 2: 联合出品
	Type                 int    `gorm:"column:type"`                  // 分发方式 0: 非独播; 1: 独播; 2: 首发
}

// DB the db instance of RadioDramaDramacopyright model
func (rdd RadioDramaDramacopyright) DB() *gorm.DB {
	return service.DramaDB.Table(rdd.TableName())
}

// TableName for RadioDramaDramacopyright model
func (RadioDramaDramacopyright) TableName() string {
	return "radio_drama_dramacopyright"
}

// FindCopyrightByDramaIDs 根据剧集 IDs 获取版权管理分发方式
func FindCopyrightByDramaIDs(dramaIDs []int64) ([]RadioDramaDramacopyright, error) {
	var copyrights []RadioDramaDramacopyright
	err := RadioDramaDramacopyright{}.DB().Select("drama_id, type").
		Where("drama_id IN (?) AND delete_time = ?", dramaIDs, 0).Find(&copyrights).Error
	if err != nil {
		return nil, err
	}
	return copyrights, nil
}
