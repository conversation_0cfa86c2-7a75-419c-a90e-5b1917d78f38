package dramacopyright

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaDerivative(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaDramacopyright{},
		"id", "create_time", "modified_time", "delete_time", "drama_id", "name", "copyright_attribution",
		"copyright_owner", "type")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_dramacopyright", RadioDramaDramacopyright{}.TableName())
}

func TestFindCopyrightByDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := FindCopyrightByDramaIDs([]int64{1})
	require.NoError(err)
	assert.Len(result, 1)
}
