package radiodramasubscription

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaSubscriptionTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaSubscription{},
		"id", "create_time", "update_time", "user_id", "drama_id", "is_top", "saw_episode", "is_saw", "saw_episode_id")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, RadioDramaSubscription{}.TableName())
}

func TestBatchSubscribe(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(1996)
	// 测试追剧成功：部分剧集状态异常或不存在
	dramaIDs := []int64{1, 2, 3, 4, 5, 5, 3}
	subscribeDramaIDs, err := BatchSubscribe(dramaIDs, testUserID)
	require.NoError(err)
	// 部分剧集追剧失败原因：ID 为 3 的剧集已下架；ID 为 5 的剧集不存在
	assert.Equal([]int64{1, 2, 4}, subscribeDramaIDs)

	// 测试追剧成功：部分剧集已追剧
	dramaIDs = []int64{1, 2, 3, 4, 5, 6, 5, 3}
	subscribeDramaIDs, err = BatchSubscribe(dramaIDs, testUserID)
	require.NoError(err)
	assert.Equal([]int64{6}, subscribeDramaIDs)

	// 测试本次新增追剧数量为 0
	dramaIDs = []int64{1, 2, 3, 4, 5, 6, 5, 3}
	subscribeDramaIDs, err = BatchSubscribe(dramaIDs, testUserID)
	require.NoError(err)
	assert.Nil(subscribeDramaIDs)
}

func TestListUserSubscribeDramaIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试用户没有订阅剧集
	testUserID := int64(99999)
	data, err := ListUserSubscribeDramaIDs(testUserID)
	require.NoError(err)
	assert.Empty(data)

	// 测试用户有订阅剧集
	testUserID = int64(1)
	data, err = ListUserSubscribeDramaIDs(testUserID)
	require.NoError(err)
	assert.NotEmpty(data)
}

func TestIsUserSubscribed(t *testing.T) {
	assert := assert.New(t)

	// 测试用户未订阅
	su, err := IsUserSubscribed(12, 2)
	assert.NoError(err)
	assert.False(su)

	// 测试用户已订阅
	su, err = IsUserSubscribed(100, 10)
	assert.NoError(err)
	assert.True(su)
}
