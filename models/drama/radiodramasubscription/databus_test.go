package radiodramasubscription

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, LogTypeUnsubscribe)
	assert.Equal(1, LogTypeSubscribe)
}

func TestSendSubscribeLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Databus.AppLogPub.ClearDebugPubMsgs()
	testDramaID := int64(19)
	testUserID := int64(2000)

	SendSubscribeLog(testUserID, testDramaID, LogTypeSubscribe)
	pubMsg := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(pubMsg, 1)

	message := <-pubMsg
	assert.Equal(keys.DatabusKeySubscribeDramaDetailLog1.Format(testUserID), message.Key)
	expectBytes, err := json.Marshal(subscribeDramaDetailLog{
		UserID:     testUserID,
		DramaID:    testDramaID,
		Type:       LogTypeSubscribe,
		CreateTime: util.TimeNow().Unix(),
	})
	require.NoError(err)
	actualBytes, err := message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectBytes, actualBytes)
}
