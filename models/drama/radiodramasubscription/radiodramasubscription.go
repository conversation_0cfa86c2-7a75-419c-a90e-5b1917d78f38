package radiodramasubscription

import (
	"fmt"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "radio_drama_subscription"

// RadioDramaSubscription 剧集订阅表
type RadioDramaSubscription struct {
	ID           int64  `gorm:"column:id"`
	CreateTime   int64  `gorm:"column:create_time"`    // 追剧时间（单位：秒）
	UpdateTime   int    `gorm:"column:update_time"`    // 上次观看更新时间（单位：秒）
	UserID       int64  `gorm:"column:user_id"`        // 用户 ID
	DramaID      int64  `gorm:"column:drama_id"`       // 剧集 ID
	IsTop        int    `gorm:"column:is_top"`         // 是否置顶
	SawEpisode   string `gorm:"column:saw_episode"`    // 上次观看的哪一期
	IsSaw        int    `gorm:"column:is_saw"`         // 剧集的更新是否已查看（1 为已查看，0 为未查看）
	SawEpisodeID int64  `gorm:"column:saw_episode_id"` // 上次观看的哪一期对应的 episode ID
}

// DB the db instance of RadioDramaSubscription model
func (rds RadioDramaSubscription) DB() *gorm.DB {
	return service.DramaDB.Table(rds.TableName())
}

// TableName for RadioDramaSubscription model
func (rds RadioDramaSubscription) TableName() string {
	return tableName
}

// BatchSubscribe 批量追剧，返回本次追剧的剧集 IDs
func BatchSubscribe(dramaIDs []int64, userID int64) ([]int64, error) {
	var checkedDramaIDs []int64
	err := dramainfo.RadioDramaDramainfo{}.DB().
		Where("id IN (?) AND checked IN (?)", dramaIDs, []int8{dramainfo.CheckedPass, dramainfo.CheckedContractExpired}).
		Pluck("id", &checkedDramaIDs).Error
	if err != nil {
		return nil, err
	}
	if len(checkedDramaIDs) == 0 {
		// 剧集全部未过审或不存在
		return nil, nil
	}

	var subscribedDramaIDs []int64
	err = RadioDramaSubscription{}.DB().
		Select("drama_id").
		Where("drama_id IN (?) AND user_id = ?", checkedDramaIDs, userID).
		Pluck("drama_id", &subscribedDramaIDs).Error
	if err != nil {
		return nil, err
	}
	needSubscribedDramaIDs := sets.Diff(checkedDramaIDs, subscribedDramaIDs)
	if len(needSubscribedDramaIDs) == 0 {
		// 用户已追全部剧集
		return nil, nil
	}

	// 批量追剧
	needSubscribedDramas := make([]RadioDramaSubscription, len(needSubscribedDramaIDs))
	timeNow := util.TimeNow().Unix()
	for k, dramaID := range needSubscribedDramaIDs {
		needSubscribedDramas[k] = RadioDramaSubscription{
			UserID:     userID,
			DramaID:    dramaID,
			CreateTime: timeNow,
		}
	}
	err = servicedb.BatchInsert(RadioDramaSubscription{}.DB(), RadioDramaSubscription{}.TableName(), needSubscribedDramas)
	if err != nil {
		return nil, err
	}
	return needSubscribedDramaIDs, nil
}

// ListUserSubscribeDramaIDs 获取用户订阅剧集 IDs
func ListUserSubscribeDramaIDs(userID int64) ([]int64, error) {
	var dramaIDs []int64
	err := service.DramaDB.Table(dramainfo.RadioDramaDramainfo{}.TableName()+" AS t1").
		Joins(fmt.Sprintf("INNER JOIN %s AS t2 ON t1.id = t2.drama_id", RadioDramaSubscription{}.TableName())).
		Where("t2.user_id = ? AND t1.checked IN (?)", userID, []int8{dramainfo.CheckedPass, dramainfo.CheckedContractExpired}).
		Order("t1.view_count DESC").
		// LIMIT 1000 避免出现性能问题
		Limit(1000).
		Pluck("t2.drama_id", &dramaIDs).Error
	if err != nil {
		return nil, err
	}
	return dramaIDs, err
}

// IsUserSubscribed 用户是否已订阅剧集
func IsUserSubscribed(userID, dramaID int64) (bool, error) {
	rds := new(RadioDramaSubscription)
	err := rds.DB().Where("user_id = ? AND drama_id = ?", userID, dramaID).Take(rds).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
