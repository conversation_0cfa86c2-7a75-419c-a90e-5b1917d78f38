package radiodramasubscription

import (
	"context"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// 追剧动作类型
const (
	LogTypeUnsubscribe = iota // 取消追剧
	LogTypeSubscribe          // 追剧
)

// subscribeDramaDetailLog 追剧 databus 日志信息
type subscribeDramaDetailLog struct {
	DramaID    int64 `json:"drama_id"` // 成功追剧的剧集 ID
	UserID     int64 `json:"user_id"`
	Type       int   `json:"type"`        // 类型。0：取消追剧；1：追剧
	CreateTime int64 `json:"create_time"` // 创建时间。单位：秒
}

// SendSubscribeLog 生产追剧数据到 databus
func SendSubscribeLog(userID int64, dramaID int64, actionType int) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	key := keys.DatabusKeySubscribeDramaDetailLog1.Format(userID)
	log := &subscribeDramaDetailLog{
		UserID:     userID,
		DramaID:    dramaID,
		Type:       actionType,
		CreateTime: util.TimeNow().Unix(),
	}
	err := service.Databus.AppLogPub.Send(ctx, key, log)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
