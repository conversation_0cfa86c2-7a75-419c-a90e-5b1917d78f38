package dramaipr

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaIPRTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaIPR{}, "id", "name", "user_id", "seasons", "create_time", "modified_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("radio_drama_ip", RadioDramaIPR{}.TableName())
}

func TestFindIPRNameByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	iprName, err := FindIPRNameByID(1)
	require.NoError(err)
	assert.Equal("test_ipr1", iprName)

	iprName, err = FindIPRNameByID(10000)
	require.NoError(err)
	assert.Equal("", iprName)
}
