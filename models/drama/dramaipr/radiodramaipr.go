package dramaipr

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

// RadioDramaIPR 剧集 IPR 信息表
type RadioDramaIPR struct {
	ID           int64  `gorm:"column:id"`
	Name         string `gorm:"column:name"`    // IPR 名
	Seasons      string `gorm:"column:seasons"` // 季度信息 (格式为 JSON)
	UserID       int64  `gorm:"column:user_id"` // 创建者 ID
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
}

// DB the db instance of RadioDramaIPR model
func (d RadioDramaIPR) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaIPR model
func (RadioDramaIPR) TableName() string {
	return "radio_drama_ip"
}

// FindIPRNameByID 根据 ID 查询剧集 IPR 名称
func FindIPRNameByID(id int64) (string, error) {
	var name string
	err := RadioDramaIPR{}.DB().Select("name").
		Where("id = ?", id).Row().Scan(&name)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return "", err
	}
	return name, nil
}
