package dramaepisode

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaEpisodeTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaEpisode{},
		"id", "name", "drama_id", "sound_id", "date", "order", "type", "pay_type", "create_time", "modified_time", "subtitle")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, RadioDramaEpisode{}.TableName())
}

func TestListDramaFirstSoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 所有记录不存在
	testDramaIDs := []int64{2001, 2002, 2003}
	episodeList, err := ListDramaFirstSoundIDs(testDramaIDs)
	require.NoError(err)
	assert.Empty(episodeList)

	// 正常查询到记录
	testDramaIDs = []int64{101, 102, 103, 105}
	episodeList, err = ListDramaFirstSoundIDs(testDramaIDs)
	require.NoError(err)
	expectList := []*RadioDramaEpisode{
		// 返回 order 最小的一条记录
		{DramaID: 101, SoundID: 1003},
		{DramaID: 102, SoundID: 2002},
		// order 值相同时只返回一条记录
		{DramaID: 103, SoundID: 3001},
	}
	assert.Equal(expectList, episodeList)
}
