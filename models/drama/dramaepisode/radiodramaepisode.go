package dramaepisode

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "radio_drama_episode"

// 单集所属类型
const (
	TypeDrama     = iota // 正剧
	TypeInterview        // 访谈
	TypeMusic            // 音乐
	TypeMore             // 更多
)

// RadioDramaEpisode 单集信息表
type RadioDramaEpisode struct {
	ID           int64  `gorm:"column:id;primary_key" json:"id"`           // 单集 ID
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`     // 创建时间
	ModifiedTime int64  `gorm:"column:modified_time" json:"modified_time"` // 更新时间
	Name         string `gorm:"column:name" json:"name"`                   // 单集名称
	DramaID      int64  `gorm:"column:drama_id" json:"drama_id"`           // 剧集 ID
	SoundID      int64  `gorm:"column:sound_id" json:"sound_id"`           // 音频 ID
	Date         int64  `gorm:"column:date" json:"date"`                   // 发表日期
	Order        int64  `gorm:"column:order" json:"order"`                 // 序号
	Type         int    `gorm:"column:type" json:"type"`                   // 类型
	PayType      int    `gorm:"column:pay_type" json:"pay_type"`           // 付费类型
	Subtitle     string `gorm:"column:subtitle" json:"subtitle"`           // 单集副标题
}

// DB the db instance of RadioDramaEpisode model
func (d RadioDramaEpisode) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaEpisode model
func (d RadioDramaEpisode) TableName() string {
	return tableName
}

// ListDramaFirstSoundIDs 指定剧集中第一个单集对应音频信息
func ListDramaFirstSoundIDs(dramaIDs []int64) ([]*RadioDramaEpisode, error) {
	var episodeList []*RadioDramaEpisode
	subQuery := RadioDramaEpisode{}.DB().Select("drama_id, sound_id, ROW_NUMBER() OVER (PARTITION BY drama_id ORDER BY `order` ASC, id ASC) AS row_num").
		Where("drama_id IN (?)", dramaIDs).SubQuery()
	sql := `SELECT drama_id, sound_id FROM (?) ranked WHERE row_num = 1`
	err := service.DramaDB.Raw(sql, subQuery).Scan(&episodeList).Error
	if err != nil {
		return nil, err
	}
	return episodeList, nil
}
