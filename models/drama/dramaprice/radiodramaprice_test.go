package dramaprice

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaPriceTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaPrice{},
		"drama_id", "price", "rate", "user_id", "name", "type", "rewardable", "vip_discount")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(VipDiscountInfo{}, "rate", "price")

	kc.CheckOmitEmpty(VipDiscountInfo{}, "price")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("radio_drama_price", RadioDramaPrice{}.TableName())
}

func TestFindPriceByDramaID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试有记录时
	priceInfo, err := FindPriceByDramaID(7)
	require.NoError(err)
	require.NotNil(priceInfo)
	assert.EqualValues(7, priceInfo.DramaID)
	assert.EqualValues(200, priceInfo.Price)
	assert.EqualValues(80, priceInfo.VipDiscount)

	// 测试记录不存在时
	priceInfo, err = FindPriceByDramaID(1000)
	require.NoError(err)
	assert.Nil(priceInfo)
}

func TestRadioDramaPrice_HasVipDiscount(t *testing.T) {
	assert := assert.New(t)

	// 测试无折扣的情况
	price := RadioDramaPrice{VipDiscount: vipDramaNoDiscount, Price: 100}
	assert.False(price.HasVipDiscount())

	// 测试有效折扣的情况
	price = RadioDramaPrice{VipDiscount: 20, Price: 100}
	assert.True(price.HasVipDiscount())

	// 测试最大折扣的情况
	price = RadioDramaPrice{VipDiscount: vipDramaMaxDiscount, Price: 100}
	assert.True(price.HasVipDiscount())

	// 测试超出最大折扣的情况
	price = RadioDramaPrice{VipDiscount: 100, Price: 100}
	assert.False(price.HasVipDiscount())
}

func TestRadioDramaPrice_GetDramaVipPrice(t *testing.T) {
	assert := assert.New(t)

	// 测试无折扣的情况
	price := RadioDramaPrice{VipDiscount: vipDramaNoDiscount, Price: 100}
	assert.EqualValues(0, price.GetDramaVipPrice())

	// 测试有折扣的情况
	price = RadioDramaPrice{VipDiscount: 10, Price: 29}
	assert.EqualValues(2, price.GetDramaVipPrice())

	// 测试保底 1 钻
	price = RadioDramaPrice{VipDiscount: 10, Price: 9}
	assert.EqualValues(1, price.GetDramaVipPrice())

	// 测试最大折扣的情况
	price = RadioDramaPrice{VipDiscount: vipDramaMaxDiscount, Price: 10}
	assert.EqualValues(9, price.GetDramaVipPrice())
}

func TestRadioDramaPrice_GetDramaVipRate(t *testing.T) {
	assert := assert.New(t)

	// 测试无折扣的情况
	price := RadioDramaPrice{VipDiscount: 0, Price: 100}
	assert.EqualValues(0, price.GetDramaVipRate())

	// 测试有折扣的情况
	price = RadioDramaPrice{VipDiscount: 20, Price: 100}
	assert.EqualValues(0.2, price.GetDramaVipRate())

	// 测试最大折扣的情况
	price = RadioDramaPrice{VipDiscount: vipDramaMaxDiscount, Price: 100}
	assert.EqualValues(0.99, price.GetDramaVipRate())
}

func TestRadioDramaPrice_GetDramaVipDiscountInfoFromUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无折扣的情况
	testDramaID := int64(564564)
	price := RadioDramaPrice{DramaID: testDramaID, VipDiscount: 0, Price: 100}
	result, err := price.GetDramaVipDiscountInfoFromUserID(dramainfo.PayTypeDrama, 0)
	require.NoError(err)
	require.Nil(result)

	// 测试有折扣，整剧付费的情况
	price = RadioDramaPrice{DramaID: testDramaID, VipDiscount: 20, Price: 100}
	result, err = price.GetDramaVipDiscountInfoFromUserID(dramainfo.PayTypeDrama, 0)
	require.NoError(err)
	require.NotNil(result)
	assert.EqualValues(0.2, result.Rate)
	assert.EqualValues(20, result.Price)

	// 测试有折扣，但不是整剧付费的情况
	price = RadioDramaPrice{DramaID: testDramaID, VipDiscount: 20, Price: 100}
	result, err = price.GetDramaVipDiscountInfoFromUserID(dramainfo.PayTypeEpisode, 0)
	require.NoError(err)
	require.NotNil(result)
	assert.EqualValues(0.2, result.Rate)
	assert.EqualValues(0, result.Price)

	// 测试剧集有会员折扣，但是用户已经购买过的情况
	testUserID := int64(2345454)
	require.NoError(transaction.TransactionLog{}.DB().Create(&transaction.TransactionLog{
		Type:   transaction.TypeDrama,
		Status: transaction.StatusSuccess,
		FromID: testUserID,
		GiftID: testDramaID,
	}).Error)
	price = RadioDramaPrice{DramaID: testDramaID, VipDiscount: 20, Price: 100}
	result, err = price.GetDramaVipDiscountInfoFromUserID(dramainfo.PayTypeDrama, testUserID)
	require.NoError(err)
	require.Nil(result)

	// 测试剧集有会员折扣，但是用户未购买过的情况
	price = RadioDramaPrice{DramaID: testDramaID, VipDiscount: 20, Price: 100}
	result, err = price.GetDramaVipDiscountInfoFromUserID(dramainfo.PayTypeDrama, 999999999)
	require.NoError(err)
	require.NotNil(result)
	assert.EqualValues(0.2, result.Rate)
	assert.EqualValues(20, result.Price)
}
