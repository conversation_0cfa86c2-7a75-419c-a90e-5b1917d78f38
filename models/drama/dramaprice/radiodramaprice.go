package dramaprice

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/models/transaction"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	vipDramaNoDiscount  = 0  // 会员剧集无折扣
	vipDramaMaxDiscount = 99 // 会员剧集折扣字段最大值（99 折）
)

// RadioDramaPrice 剧集价格表
type RadioDramaPrice struct {
	DramaID     int64   `gorm:"column:drama_id"`     // 剧集 ID
	Price       int64   `gorm:"column:price"`        // 价格
	Rate        float32 `gorm:"column:rate"`         // 分成比例
	UserID      int64   `gorm:"column:user_id"`      // 用户 ID
	Name        string  `gorm:"column:name"`         // 剧集名称
	Type        uint32  `gorm:"column:type"`         // 类型（同剧集 RadioDramaDramainfo 的 pay_type）
	Rewardable  int     `gorm:"column:rewardable"`   // 是否可进行打赏（0 为否，1 为是）
	VipDiscount int     `gorm:"column:vip_discount"` // 会员打折百分比值，如存入 80 表示折扣力度为原价 80%，为 0 时表示不打折。折扣后若出现小数点，则小数点后金额舍去。注意，单集付费剧需将要购买的单集价格加起来以后再打折
}

// VipDiscountInfo 会员折扣信息
type VipDiscountInfo struct {
	Rate  float32 `json:"rate"`            // 会员折扣值，e.g. 0.8
	Price int64   `json:"price,omitempty"` // 整剧付费剧集的会员折扣价格，仅在整剧付费时下发。单位：钻
}

// DB the db instance of RadioDramaPrice model
func (rdp RadioDramaPrice) DB() *gorm.DB {
	return service.DramaDB.Table(rdp.TableName())
}

// TableName for RadioDramaPrice model
func (RadioDramaPrice) TableName() string {
	return "radio_drama_price"
}

// FindPriceByDramaID 根据剧集 ID 查询剧集价格
func FindPriceByDramaID(dramaID int64) (*RadioDramaPrice, error) {
	var price RadioDramaPrice
	err := RadioDramaPrice{}.DB().
		Where("drama_id = ?", dramaID).
		Take(&price).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &price, nil
}

// HasVipDiscount 判断是否有会员折扣
func (p RadioDramaPrice) HasVipDiscount() bool {
	return p.VipDiscount > vipDramaNoDiscount && p.VipDiscount <= vipDramaMaxDiscount
}

// GetDramaVipPrice 获取整剧付费剧集的会员折扣价格（返回 0 表示没有会员折扣价）
func (p RadioDramaPrice) GetDramaVipPrice() int64 {
	if p.HasVipDiscount() {
		vipPrice := int64(float32(p.Price) * p.GetDramaVipRate())
		// 保底 1 钻
		if vipPrice <= 0 {
			return 1
		}
		return vipPrice
	}
	return 0
}

// GetDramaVipRate 获取会员折扣率
func (p RadioDramaPrice) GetDramaVipRate() float32 {
	if p.HasVipDiscount() {
		return float32(p.VipDiscount) / 100
	}
	return 0
}

// GetDramaVipDiscountInfoFromUserID 获取剧集对用户的会员折扣信息
func (p RadioDramaPrice) GetDramaVipDiscountInfoFromUserID(dramaPayType uint32, userID int64) (*VipDiscountInfo, error) {
	if !p.HasVipDiscount() {
		return nil, nil
	}
	// 对于整剧付费剧集，若传入的用户 ID 有值，需要判断该用户是否购入过该剧集，若购入过，则不再对该剧享受会员折扣
	if dramaPayType == dramainfo.PayTypeDrama && userID > 0 {
		bought, err := servicedb.Exists(transaction.TransactionLog{}.DB().
			Where("type = ? AND status = ? AND from_id = ? AND gift_id = ?",
				transaction.TypeDrama, transaction.StatusSuccess, userID, p.DramaID))
		if err != nil {
			return nil, err
		}
		if bought {
			// 若购入过，则不再对该剧享受会员折扣
			return nil, nil
		}
	}

	vipDiscountInfo := VipDiscountInfo{
		Rate: p.GetDramaVipRate(),
	}
	if dramaPayType == dramainfo.PayTypeDrama {
		// 整剧付费的剧集下发会员折扣价格
		vipDiscountInfo.Price = p.GetDramaVipPrice()
	}
	return &vipDiscountInfo, nil
}
