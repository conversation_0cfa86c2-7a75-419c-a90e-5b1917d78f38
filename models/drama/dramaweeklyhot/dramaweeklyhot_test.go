package dramaweeklyhot

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaWeeklyHot{},
		"id", "create_time", "modified_time", "drama_ids", "date")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_weekly_hot", RadioDramaWeeklyHot{}.TableName())
}

func TestFindWeeklyHotDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Date(2022, 10, 9, 6, 6, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	// 测试无缓存
	now := util.TimeNow().Add(-2 * time.Minute)
	date := util.BeginningOfWeek(now).Format(util.TimeFormatYMDWithNoSpace)
	key := keys.KeyDramaCornerMarkViewRanks1.Format(date)
	require.NoError(service.LRURedis.Del(key).Err())
	val := service.LRURedis.Exists(key).Val()
	assert.Zero(val)
	dramaIDs := FindWeeklyHotDramaIDs()
	assert.Equal([]int64{1, 2}, dramaIDs)

	// 测试有缓存
	val = service.LRURedis.Exists(key).Val()
	assert.NotZero(val)
	dramaIDs = FindWeeklyHotDramaIDs()
	assert.Equal([]int64{1, 2}, dramaIDs)

	// 模拟 key 过期生成新的缓存
	err := service.LRURedis.Del(key).Err()
	assert.NoError(err)
	dramaIDs = FindWeeklyHotDramaIDs()
	assert.Equal([]int64{1, 2}, dramaIDs)
}
