package dramaweeklyhot

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// RadioDramaWeeklyHot 每周热播剧
type RadioDramaWeeklyHot struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间
	DramaIDs     string `gorm:"column:drama_ids"`     // 剧集 IDs，上周热播剧集按热度从高到低，用半角逗号分隔，最多有 40 个剧集 ID
	Date         string `gorm:"column:date"`          // 每周周一日期，举例：2006-01-02
}

// DB the db instance of RadioDramaWeeklyHot model
func (rdw RadioDramaWeeklyHot) DB() *gorm.DB {
	return service.DramaDB.Table(rdw.TableName())
}

// TableName for RadioDramaWeeklyHot model
func (RadioDramaWeeklyHot) TableName() string {
	return "radio_drama_weekly_hot"
}

// FindWeeklyHotDramaIDs 根据每周热播剧 IDs
func FindWeeklyHotDramaIDs() []int64 {
	// 当前时间减去 2 分钟，避免每周一零点数据未生成，获取不到热播剧集数据
	now := util.TimeNow().Add(-2 * time.Minute)
	date := util.BeginningOfWeek(now).Format(util.TimeFormatYMDWithNoSpace)

	emptyResult := make([]int64, 0)
	key := keys.KeyDramaCornerMarkViewRanks1.Format(date)
	resStr, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Errorf("从 redis 获取上周热播剧 IDs 失败: %v", err)
		// PASS
		return emptyResult
	}
	if err == nil {
		dramaIDs, err := util.SplitToInt64Array(resStr, ",")
		if err != nil {
			logger.Error(err)
			// PASS
			return emptyResult
		}
		return dramaIDs
	}

	var dramaIDsStr string
	date = util.BeginningOfWeek(now).Format(util.TimeFormatYMD)
	err = RadioDramaWeeklyHot{}.DB().Select("drama_ids").
		Where("date = ?", date).Row().Scan(&dramaIDsStr)
	if err != nil {
		logger.Errorf("从数据库获取上周热播剧 IDs 失败: %v", err)
		// PASS
		return emptyResult
	}
	err = service.LRURedis.Set(key, dramaIDsStr, 5*time.Minute).Err()
	if err != nil {
		logger.Errorf("往 redis 设置上周热播剧 IDs 失败: %v", err)
		// PASS
	}
	dramaIDs, err := util.SplitToInt64Array(dramaIDsStr, ",")
	if err != nil {
		logger.Error(err)
		// PASS
		return emptyResult
	}
	return dramaIDs
}
