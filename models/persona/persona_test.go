package persona

import (
	"encoding/json"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

const testUserID = 1234

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Persona{}, "id", "equip_id", "persona", "user_id", "create_time", "modified_time", "buvid", "points")
}

func TestPersona_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("persona", Persona{}.TableName())
}

func TestPersona_getFemalePersonaID(t *testing.T) {
	assert := assert.New(t)

	// 测试无画像分
	persona := &Persona{}
	assert.EqualValues(TypeGirl, persona.getFemalePersonaID())

	// 测试腐女画像得分大于 0 且大于等于乙女画像分
	persona.PointMap = map[int64]int64{
		TypeFujoshi: 10,
		TypeOtome:   9,
	}
	assert.EqualValues(TypeGirl, persona.getFemalePersonaID())

	// 测试乙女画像分大于等于腐女画像分且乙女画像分大于指定分数分
	persona.PointMap[TypeFujoshi] = 8
	assert.EqualValues(TypeOtome, persona.getFemalePersonaID())

	// 测试乙女画像分大于腐女画像分且乙女画像分小于指定分数分
	persona.PointMap[TypeFujoshi] = 3
	persona.PointMap[TypeOtome] = 4
	assert.EqualValues(TypeGirl, persona.getFemalePersonaID())
}

func TestPersona_BeforeCreate(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	p := new(Persona)
	require.NoError(p.BeforeCreate(nil))
	assert.NotEqual(0, p.CreateTime)
	assert.NotEqual(0, p.ModifiedTime)
}

func TestPersona_BeforeSave(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	p := new(Persona)
	require.NoError(p.BeforeSave(nil))
	assert.NotEqual(0, p.CreateTime)
	assert.NotEqual(0, p.ModifiedTime)
}

func TestPersona_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &Persona{}
	require.NoError(p.AfterFind())
	require.NotNil(p.PointMap)
	assert.Empty(p.PointMap)

	p = &Persona{
		Points: "{\"3\":2333,\"7\":3222}",
	}
	require.NoError(p.AfterFind())
	require.NotNil(p.PointMap)
	assert.Len(p.PointMap, 2)
	point, ok := p.PointMap[3]
	require.True(ok)
	assert.EqualValues(2333, point)
}

func TestPersona_IsFemale(t *testing.T) {
	assert := assert.New(t)

	// 测试男性画像
	assert.False(IsFemale(TypeBoy))
	assert.False(IsFemale(TypeManualBoy))

	// 测试女性画像
	assert.True(IsFemale(TypeGirl))
	assert.True(IsFemale(TypeOtome))
}

func TestPersona_GetPersonaPointsFromRedis(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	field := strconv.FormatInt(testUserID, 10)
	keyOtome := keys.KeyUserPersonaPoints1.Format(TypeOtome)
	require.NoError(service.Redis.HDel(keyOtome, field).Err())
	keyFujoshi := keys.KeyUserPersonaPoints1.Format(TypeFujoshi)
	require.NoError(service.Redis.HDel(keyFujoshi, field).Err())

	// 测试从 redis 中获取画像分
	require.NoError(service.Redis.HSet(keyOtome, field, 9).Err())
	require.NoError(service.Redis.HSet(keyFujoshi, field, 8).Err())
	userPersona := &Persona{
		Persona: TypeBoy,
		UserID:  util.NewInt64(testUserID),
	}
	require.NoError(userPersona.GetPersonaPointsFromRedis(keys.KeyUserPersonaPoints1, field))
	assert.EqualValues(userPersona.PointMap[TypeFujoshi], 8)
	assert.EqualValues(userPersona.PointMap[TypeOtome], 9)

	// 测试 redis 中没有数据
	testEquipID := "test_equip_id"
	equipPersona := &Persona{
		Persona: TypeBoy,
		EquipID: &testEquipID,
	}
	require.NoError(equipPersona.GetPersonaPointsFromRedis(keys.KeyEquipPersonaPoints1, testEquipID))
	point, exists := equipPersona.PointMap[TypeFujoshi]
	assert.True(exists)
	assert.Zero(point)
	point, exists = equipPersona.PointMap[TypeOtome]
	assert.True(exists)
	assert.Zero(point)
}

func TestGetUserPersona(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	field := strconv.FormatInt(testUserID, 10)
	keyOtome := keys.KeyUserPersonaPoints1.Format(TypeOtome)
	require.NoError(service.Redis.HDel(keyOtome, field).Err())
	keyFujoshi := keys.KeyUserPersonaPoints1.Format(TypeFujoshi)
	require.NoError(service.Redis.HDel(keyFujoshi, field).Err())

	// 测试没有用户画像
	require.NoError(Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	res, err := GetUserPersona(testUserID)
	require.NoError(err)
	assert.Nil(res)

	// 测试获取用户画像
	require.NoError(service.Redis.HSet(keyOtome, field, 9).Err())
	require.NoError(service.Redis.HSet(keyFujoshi, field, 8).Err())
	userPersona := &Persona{
		Persona: TypeGirl,
		UserID:  util.NewInt64(testUserID),
	}
	encodePointMap, err := json.Marshal(map[int64]int64{
		TypeOtome: 10,
	})
	require.NoError(err)
	userPersona.Points = string(encodePointMap)
	require.NoError(userPersona.DB().Create(userPersona).Error)
	res, err = GetUserPersona(testUserID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(TypeGirl, res.Persona)
	assert.EqualValues(8, res.PointMap[TypeFujoshi])
	assert.EqualValues(10, res.PointMap[TypeOtome])
}

func TestGetEquipPersona(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有设备画像
	testEquipID := "test_equip_id"
	require.NoError(Persona{}.DB().Delete("", "equip_id = ?", testEquipID).Error)
	res, err := GetEquipPersona(testEquipID)
	require.NoError(err)
	assert.Nil(res)

	// 测试获取设备画像
	equipPersona := &Persona{
		Persona: TypeGirl,
		EquipID: &testEquipID,
	}
	require.NoError(equipPersona.DB().Create(equipPersona).Error)
	res, err = GetEquipPersona(testEquipID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(TypeGirl, res.Persona)
	point, exists := res.PointMap[TypeFujoshi]
	assert.True(exists)
	assert.Zero(point)
	point, exists = res.PointMap[TypeOtome]
	assert.True(exists)
	assert.Zero(point)
}

func TestPersona_UpdatePersona(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 更新设备画像
	testEquipID := "test_equip_id"
	equipPersona := &Persona{
		Persona: TypeBoy | 0xf000,
		EquipID: &testEquipID,
		PointMap: map[int64]int64{
			TypeOtome:   11,
			TypeFujoshi: 8,
		},
	}
	require.NoError(Persona{}.DB().Delete("", "equip_id = ?", testEquipID).Error)
	require.NoError(equipPersona.DB().Create(equipPersona).Error)
	// 更新画像猜你喜欢音推荐策略
	err := equipPersona.UpdatePersona(TypeGirl|MaskStrategyLikeSounds, true)
	require.NoError(err)
	assert.EqualValues(TypeOtome|MaskStrategyLikeSounds, equipPersona.Persona)

	// 更新用户画像
	userPersona := &Persona{
		Persona: TypeBoy | MaskStrategyLikeSounds,
		UserID:  util.NewInt64(testUserID),
		PointMap: map[int64]int64{
			TypeOtome:   11,
			TypeFujoshi: 8,
		},
	}
	require.NoError(Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	require.NoError(userPersona.DB().Create(userPersona).Error)
	// 不更新画像猜你喜欢音推荐策略
	err = userPersona.UpdatePersona(TypeGirl|0xf000, false)
	require.NoError(err)
	assert.EqualValues(TypeOtome, userPersona.Persona&MaskPersonaModule)
	assert.EqualValues(MaskStrategyLikeSounds, userPersona.Persona&MaskStrategyLikeSounds)
}

func TestPersona_CreatePersona(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试创建设备画像
	testEquipID := "test_equip_id"
	equipIDPersona := &Persona{
		Persona: TypeGirl | MaskStrategyLikeSounds,
		EquipID: &testEquipID,
		PointMap: map[int64]int64{
			TypeOtome: 6,
		},
	}
	require.NoError(Persona{}.DB().Delete("", "equip_id = ?", testEquipID).Error)
	require.NoError(equipIDPersona.CreatePersona())
	assert.EqualValues(TypeOtome|MaskStrategyLikeSounds, equipIDPersona.Persona)
	var equipPersona1 Persona
	require.NoError(equipPersona1.DB().Where("equip_id = ?", testEquipID).Take(&equipPersona1).Error)
	assert.EqualValues(equipIDPersona.Persona, equipPersona1.Persona)

	// 测试创建用户画像
	userPersona := &Persona{
		Persona: TypeGirl | MaskStrategyLikeSounds,
		UserID:  util.NewInt64(testUserID),
		PointMap: map[int64]int64{
			TypeFujoshi: 8,
			TypeOtome:   10,
		},
	}
	require.NoError(Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	require.NoError(userPersona.CreatePersona())
	assert.EqualValues(TypeOtome|MaskStrategyLikeSounds, userPersona.Persona)
	var userPersona1 Persona
	require.NoError(userPersona1.DB().Where("user_id = ?", testUserID).Take(&userPersona1).Error)
	assert.EqualValues(userPersona.Persona, userPersona1.Persona)
}

func TestPersona_AddPersonaPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试增加用户腐女画像分
	userPersona := &Persona{
		Persona: TypeGirl | MaskStrategyLikeSounds,
		UserID:  util.NewInt64(testUserID),
		PointMap: map[int64]int64{
			TypeFujoshi: 8,
			TypeOtome:   10,
		},
	}
	require.NoError(Persona{}.DB().Delete("", "user_id = ?", testUserID).Error)
	require.NoError(userPersona.DB().Create(userPersona).Error)
	require.NoError(userPersona.AddPersonaPoint(TypeFujoshi, 1))
	var userPersona1 Persona
	require.NoError(userPersona1.DB().Where("user_id = ?", testUserID).Take(&userPersona1).Error)
	assert.EqualValues(9, userPersona1.PointMap[TypeFujoshi])
	assert.EqualValues(10, userPersona1.PointMap[TypeOtome])
	assert.EqualValues(TypeOtome, userPersona1.Persona&MaskPersonaModule)
	assert.EqualValues(MaskStrategyLikeSounds, userPersona1.Persona&MaskStrategyLikeSounds)

	// 测试画像分为负数
	require.NoError(userPersona.AddPersonaPoint(TypeOtome, -100))
	var userPersona2 Persona
	require.NoError(userPersona2.DB().Where("user_id = ?", testUserID).Take(&userPersona2).Error)
	assert.EqualValues(9, userPersona2.PointMap[TypeFujoshi])
	assert.EqualValues(0, userPersona2.PointMap[TypeOtome])
	assert.EqualValues(TypeGirl, userPersona2.Persona&MaskPersonaModule)
	assert.EqualValues(MaskStrategyLikeSounds, userPersona2.Persona&MaskStrategyLikeSounds)
}

func TestIsValidatePersona(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsValidatePersona(TypeBoy))

	assert.False(IsValidatePersona(111))
}
