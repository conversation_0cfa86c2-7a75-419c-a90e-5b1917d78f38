package persona

import (
	"encoding/json"
	"slices"
	"strconv"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/user"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// 用户画像（用于 App 及 Web 首页显示）
// NOTICE: 女性画像 ID 需要为奇数
const (
	TypeGeneral    = 1  // 大众
	TypeBoy        = 2  // 普通男
	TypeGirl       = 3  // 普通女
	TypeFujoshi    = 7  // 腐女
	TypeOtome      = 9  // 乙女
	TypeManualGirl = 11 // 人工维护女性画像
	TypeManualBoy  = 12 // 人工维护男性画像

	TypeTheatre   = 2000 // 盲盒剧场
	TypeNewUser   = 2001 // 新人 Tab
	TypeDiscovery = 2002 // 搜索页榜单
	TypeVip       = 2003 // Vip 专享
)

// OtomeMinPoint 为乙女画像时的最低分
const OtomeMinPoint = 5

// 字段 persona 第 1~8 位存模块画像，第 9~16 位存猜你喜欢音推荐策略
const (
	MaskPersonaModule      = 0x00ff
	MaskStrategyLikeSounds = 0xff00
)

// UserPersonas 所有的画像
var UserPersonas = []int64{TypeGeneral, TypeBoy, TypeGirl, TypeFujoshi, TypeOtome}

// PersonaPointTypes 画像分类型列表
var PersonaPointTypes = []int64{TypeFujoshi, TypeOtome}

// Persona model
type Persona struct {
	ID           int64   `gorm:"column:id;primary_key"` // 主键
	EquipID      *string `gorm:"column:equip_id"`       // 设备号。设备号存在时 user_id 必须为空
	Persona      int64   `gorm:"column:persona"`        // 画像信息
	UserID       *int64  `gorm:"column:user_id"`        // 用户 ID。用户 ID 存在时 equip_id 必须为空
	CreateTime   int64   `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime int64   `gorm:"column:modified_time"`  // 更新时间。单位：秒
	BUVID        string  `gorm:"column:buvid"`          // buvid
	Points       string  `gorm:"column:points"`         // 画像分列表

	PointMap map[int64]int64 `gorm:"-"`
	// WORKAROUND: 目前画像分还没有迁移到 MySQL 数据库中，待数据全部迁移后删掉该字段
	needUpdatePoints bool
}

// TableName for current model
func (Persona) TableName() string {
	return "persona"
}

// DB the db instance of MPersona model
func (p Persona) DB() *gorm.DB {
	return service.DB.Table(p.TableName())
}

// BeforeCreate hook
func (p *Persona) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	p.CreateTime = now
	return nil
}

// BeforeSave hook
func (p *Persona) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	p.ModifiedTime = now
	return nil
}

// AfterFind is a GORM hook for query
func (p *Persona) AfterFind() error {
	if p.Points != "" {
		err := json.Unmarshal([]byte(p.Points), &p.PointMap)
		if err != nil {
			return err
		}
	} else {
		p.PointMap = make(map[int64]int64, len(PersonaPointTypes))
	}
	return nil
}

// IsFemale 是否为女性画像
func IsFemale(personaID int64) bool {
	// 奇数为女性，偶数为男性
	return personaID&1 == 1
}

// getFemalePersonaID 获取女性画像 ID
func (p *Persona) getFemalePersonaID() int64 {
	var personaID int64
	if p.PointMap[TypeFujoshi] > 0 &&
		p.PointMap[TypeFujoshi] >= p.PointMap[TypeOtome] {
		// 腐女画像得分大于 0 且大于等于乙女画像得分时返回腐女画像
		// TEMP: 腐女用户也返回普通女性画像
		personaID = TypeGirl
	} else if p.PointMap[TypeOtome] >= OtomeMinPoint {
		// 乙女画像得分大于等于腐女画像得分且大于指定分数分时返回乙女画像
		personaID = TypeOtome
	} else {
		// 若画像都无分数，返回普通女性画像作为默认画像
		personaID = TypeGirl
	}
	return personaID
}

// GetUserPersona 获取用户画像
func GetUserPersona(userID int64) (*Persona, error) {
	userPersona := new(Persona)
	err := userPersona.DB().Where("user_id = ?", userID).Take(userPersona).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	err = userPersona.GetPersonaPointsFromRedis(keys.KeyUserPersonaPoints1, strconv.FormatInt(userID, 10))
	if err != nil {
		return nil, err
	}
	return userPersona, nil
}

// GetEquipPersona 获取设备画像
func GetEquipPersona(equipID string) (*Persona, error) {
	equipPersona := new(Persona)
	err := equipPersona.DB().Where("equip_id = ?", equipID).Take(equipPersona).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	err = equipPersona.GetPersonaPointsFromRedis(keys.KeyEquipPersonaPoints1, equipID)
	if err != nil {
		return nil, err
	}
	return equipPersona, nil
}

// GetPersonaPointsFromRedis 从 redis 中获取画像分
func (p *Persona) GetPersonaPointsFromRedis(keyFormat cache.KeyFormat, field string) error {
	if p.PointMap == nil {
		p.PointMap = make(map[int64]int64, len(PersonaPointTypes))
	}

	// WORKAROUND: 目前画像分还没有迁移到 MySQL 数据库中，待数据全部迁移后删掉查询 redis 相关操作
	notExistPersonaIDs := make([]int64, 0, len(PersonaPointTypes))
	for _, personaID := range PersonaPointTypes {
		if _, exists := p.PointMap[personaID]; !exists {
			// 数据库中没有画像分数信息则从 redis 中查询
			notExistPersonaIDs = append(notExistPersonaIDs, personaID)
		}
	}
	if len(notExistPersonaIDs) == 0 {
		return nil
	}

	hGetMap := make(map[int64]*redis.StringCmd, len(notExistPersonaIDs))
	pipe := service.Redis.Pipeline()
	for _, personaID := range notExistPersonaIDs {
		hGetMap[personaID] = pipe.HGet(keyFormat.Format(personaID), field)
	}
	_, err := pipe.Exec()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return err
	}

	for personaID, hGet := range hGetMap {
		point, err := hGet.Int64()
		if err != nil && !serviceredis.IsRedisNil(err) {
			return err
		}
		if serviceredis.IsRedisNil(err) {
			// 若 redis 中也没有数据，该画像分初始化为 0
			p.PointMap[personaID] = 0
		} else {
			p.PointMap[personaID] = point
		}
	}
	p.needUpdatePoints = true
	return nil
}

// UpdatePersona 设置画像
func (p *Persona) UpdatePersona(persona int64, setStrategy bool) error {
	personaID := persona & MaskPersonaModule
	if IsFemale(persona) {
		// 若为女性画像，则画像需要区分乙女腐女
		personaID = p.getFemalePersonaID()
	}
	var newPersona int64
	if setStrategy {
		newPersona = (persona & MaskStrategyLikeSounds) | personaID
	} else {
		newPersona = (p.Persona & MaskStrategyLikeSounds) | personaID
	}
	updates := map[string]interface{}{}
	if newPersona != p.Persona {
		// 若当前通过画像分获取的画像与原画像不同，则更新画像
		updates["persona"] = newPersona
	}

	if p.needUpdatePoints {
		// 若存存在缺失的画像分，则更新画像分
		encodePointMap, err := json.Marshal(p.PointMap)
		if err != nil {
			return err
		}
		updates["points"] = string(encodePointMap)
	}
	if len(updates) > 0 {
		now := util.TimeNow().Unix()
		updates["modified_time"] = now
		db := p.DB().Where("id = ? AND modified_time = ?",
			p.ID, p.ModifiedTime).Updates(updates)
		if db.Error != nil {
			return db.Error
		}
		if db.RowsAffected == 0 {
			// FIXME: 并发请求时，容易触发乐观锁导致更新失败，暂时忽略该错误
			logger.WithField("id", p.ID).Warnf("乐观锁更新画像失败 error: %v", servicedb.ErrNoRowsAffected)
			return nil
		}
		p.ModifiedTime = now
		p.Persona = newPersona
	}
	return nil
}

// CreatePersona 新建画像
func (p *Persona) CreatePersona() error {
	if IsFemale(p.Persona) {
		p.Persona = (p.Persona & MaskStrategyLikeSounds) | p.getFemalePersonaID()
	}
	if len(p.PointMap) > 0 {
		encodePointMap, err := json.Marshal(p.PointMap)
		if err != nil {
			return err
		}
		p.Points = string(encodePointMap)
	}
	return p.DB().Create(p).Error
}

// AddPersonaPoint 增加画像分
func (p *Persona) AddPersonaPoint(personaID, point int64) error {
	p.PointMap[personaID] += point
	if personaPoint := p.PointMap[personaID]; personaPoint < 0 {
		p.PointMap[personaID] = 0
	}
	encodePointMap, err := json.Marshal(p.PointMap)
	if err != nil {
		return err
	}
	now := util.TimeNow().Unix()
	updates := map[string]interface{}{
		"modified_time": now,
		"points":        string(encodePointMap),
	}
	if IsFemale(p.Persona) {
		// 若为女性画像，则画像需要区分乙女腐女
		newPersona := (p.Persona & MaskStrategyLikeSounds) | p.getFemalePersonaID()
		if newPersona != p.Persona {
			// 若当前通过画像分获取的画像与原画像不同，则更新画像
			updates["persona"] = newPersona
		}
		p.Persona = newPersona
	}

	db := p.DB().Where("id = ? AND modified_time = ?",
		p.ID, p.ModifiedTime).Updates(updates)
	if db.Error != nil {
		return db.Error
	}
	if db.RowsAffected == 0 {
		// FIXME: 并发请求时，容易触发乐观锁导致更新失败，暂时忽略该错误
		logger.WithField("id", p.ID).Warnf("乐观锁更新画像分失败 error: %v", servicedb.ErrNoRowsAffected)
		return nil
	}
	p.ModifiedTime = now
	return nil
}

// IsValidatePersona 验证用户画像是否已定义
func IsValidatePersona(personaID int64) bool {
	return slices.Contains(UserPersonas, personaID)
}

// PersonaInfo 用户画像信息
type PersonaInfo struct {
	Persona    int64 `json:"persona"`                // 画像信息
	Sex        int   `json:"sex"`                    // 用户性别，0 未知，1 表示男，2 表示女
	IsNewUser  *bool `json:"is_new_user,omitempty"`  // 是否为新建用户画像
	IsNewEquip *bool `json:"is_new_equip,omitempty"` // 是否为新建设备画像
}

// GetPersonaInfo 获取用户画像
func GetPersonaInfo(equipID string, buvid string, userID int64) (*PersonaInfo, error) {
	var isNewEquip *bool
	var personaInfo *Persona
	var userSex = user.SexUnknown
	var equipPersona *Persona

	// 根据 equipID 处理设备画像
	if equipID != "" {
		isNewEquip = new(bool)
		var err error
		equipPersona, err = GetEquipPersona(equipID)
		if err != nil {
			return nil, err
		}
		if equipPersona == nil {
			// 新建设备画像
			equipPersona = &Persona{
				Persona: TypeGirl,
				EquipID: &equipID,
				BUVID:   buvid,
			}
			// WORKAROUND: 目前画像分还没有迁移到 MySQL 中，还需要在 redis 中查一次，待数据全部迁移后删除该逻辑
			if err = equipPersona.GetPersonaPointsFromRedis(keys.KeyEquipPersonaPoints1, equipID); err != nil {
				return nil, err
			}
			if err = equipPersona.CreatePersona(); err != nil {
				return nil, err
			}
			*isNewEquip = true
		}
		personaInfo = equipPersona
	}

	var isNewUser *bool
	if userID > 0 {
		isNewUser = new(bool)
		userPersona, err := GetUserPersona(userID)
		if err != nil {
			return nil, err
		}
		if userPersona == nil {
			// 新建用户画像
			userPersona = &Persona{
				Persona: TypeGirl,
				UserID:  util.NewInt64(userID),
			}
			// WORKAROUND: 目前画像分还没有迁移到 MySQL 中，还需要在 redis 中查一次，待数据全部迁移后删除该逻辑
			if err = userPersona.GetPersonaPointsFromRedis(keys.KeyUserPersonaPoints1, strconv.FormatInt(userID, 10)); err != nil {
				return nil, err
			}
			if equipPersona != nil {
				userPersona.Persona = equipPersona.Persona
				for personaID, point := range equipPersona.PointMap {
					// 同步设备画像分至用户画像分
					userPersona.PointMap[personaID] += point
				}
			}
			if err = userPersona.CreatePersona(); err != nil {
				return nil, err
			}
			*isNewUser = true
		}
		personaInfo = userPersona
		userSex = user.GetUserSex(userID)
	}

	// 若无任何画像数据，则返回默认女性画像
	if personaInfo == nil {
		return &PersonaInfo{Persona: TypeGirl, Sex: userSex, IsNewUser: isNewUser, IsNewEquip: isNewEquip}, nil
	}

	if (isNewEquip != nil && *isNewEquip) || (isNewUser != nil && *isNewUser) {
		// 新建画像时不需要根据画像分区分画像，直接返回
		return &PersonaInfo{
			Persona:    personaInfo.Persona,
			IsNewUser:  isNewUser,
			IsNewEquip: isNewEquip,
			Sex:        userSex,
		}, nil
	}

	if IsFemale(personaInfo.Persona) {
		// 若为女性画像，则画像需要通过画像分区分乙女腐女，同时将最新的画像更新到数据库
		err := personaInfo.UpdatePersona(personaInfo.Persona, false)
		if err != nil {
			logger.WithFields(logger.Fields{
				"id":       personaInfo.ID,
				"user_id":  userID,
				"equip_id": equipID,
			}).Errorf("更新画像失败 error: %v", err)
			// PASS
		}
	}

	return &PersonaInfo{
		Persona:    personaInfo.Persona,
		IsNewUser:  isNewUser,
		IsNewEquip: isNewEquip,
		Sex:        userSex,
	}, nil
}
