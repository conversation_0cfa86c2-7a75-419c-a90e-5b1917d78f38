package mpointfeed

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	keys2 "github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMPointFeedTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MPointFeed{},
		"id", "sound_id", "user_id", "create_time", "num", "catalog_id")
}

func TestMPointFeed_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.EqualValues(tableName, MPointFeed{}.TableName())
}

func TestMPointFeed_BeforeCreate(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	pf := new(MPointFeed)
	require.NoError(pf.BeforeCreate(nil))
	assert.EqualValues(now.Unix(), pf.CreateTime)
}

func TestMPointFeed_Create(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	pf := MPointFeed{
		SoundID:   1,
		UserID:    1,
		Num:       1,
		CatalogID: 1,
	}
	err := pf.Create()
	require.NoError(err)
	query := service.DB.Table(pf.TableName()).
		Where("sound_id = ? AND user_id = ? AND num = ? AND catalog_id = ?", 1, 1, 1, 1)
	exists, err := servicedb.Exists(query)
	require.NoError(err)
	assert.True(exists)
}

func TestSendDataBusLog(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(1)
	testSoundID := int64(1)
	testPointNum := 1
	SendDataBusLog(testUserID, testSoundID, testPointNum, user.TypeOriginApp, DataBusLogTypeSoundTsReward)
	msgs := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	key := keys2.DatabusKeyUserPointDetailLog1.Format(testUserID)
	require.EqualValues(key, m.Key)
	var message user.PointDetailLog
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(testUserID, message.UserID)
	assert.EqualValues(testPointNum, message.Num)
	assert.EqualValues(user.TypeOriginApp, message.Origin)
	assert.EqualValues(DataBusLogTypeSoundTsReward, message.Type)
	assert.EqualValues(fmt.Sprintf(`{"sound_id":%d}`, testSoundID), string(message.More))
}
