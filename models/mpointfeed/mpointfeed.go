package mpointfeed

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	keys2 "github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// DataBusLogTypeSoundTsReward 通过投稿的音频被投食获得鱼干场景
const DataBusLogTypeSoundTsReward = 13

const tableName = "m_point_feed"

// MPointFeed model
type MPointFeed struct {
	ID         int64 `gorm:"column:id" json:"id"`                   // 主键 ID
	SoundID    int64 `gorm:"column:sound_id" json:"sound_id"`       // 音频 ID
	UserID     int64 `gorm:"column:user_id" json:"user_id"`         // 用户 ID
	CreateTime int64 `gorm:"column:create_time" json:"create_time"` // 创建时间时间戳，单位：秒
	Num        int   `gorm:"column:num" json:"num"`                 // 投食的小鱼干数量
	CatalogID  int64 `gorm:"column:catalog_id" json:"catalog_id"`   // 音频分类 ID
}

// DB the db instance of MPointFeed model
func (pf MPointFeed) DB() *gorm.DB {
	return service.DB.Table(pf.TableName())
}

// TableName for MPointFeed model
func (pf MPointFeed) TableName() string {
	return tableName
}

// BeforeCreate hook
func (pf *MPointFeed) BeforeCreate(scope *gorm.Scope) (err error) {
	pf.CreateTime = util.TimeNow().Unix()
	return nil
}

// NewMPointFeed new MPointFeed
func NewMPointFeed(soundID, userID, catalogID int64, num int) *MPointFeed {
	return &MPointFeed{
		SoundID:   soundID,
		UserID:    userID,
		Num:       num,
		CatalogID: catalogID,
	}
}

// Create 创建投食音频记录
func (pf *MPointFeed) Create() error {
	err := pf.DB().Create(&pf).Error
	if err != nil {
		return err
	}
	return nil
}

// SendDataBusLog 推送小鱼干 databus 日志
func SendDataBusLog(userID, soundID int64, pointNum, origin, logType int) {
	p := user.PointDetailLog{
		CreateTime: util.TimeNow().Unix(),
		UserID:     userID,
		Num:        pointNum,
		Origin:     origin,
		Type:       logType,
		More:       json.RawMessage(fmt.Sprintf(`{"sound_id":%d}`, soundID)),
	}
	key := keys2.DatabusKeyUserPointDetailLog1.Format(p.UserID)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err := service.Databus.AppLogPub.Send(ctx, key, p)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
