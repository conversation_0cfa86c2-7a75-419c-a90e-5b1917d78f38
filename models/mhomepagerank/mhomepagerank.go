package mhomepagerank

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 榜单类型
const (
	TypeRankDramaNew        = 1 // 新品榜
	TypeRankDramaPopularity = 2 // 人气榜
	TypeRankDramaReward     = 3 // 打赏榜
	TypeRankDramaFree       = 4 // 免费榜
	TypeRankDramaRomantic   = 5 // 言情榜
	TypeRankSoundLover      = 6 // 声音恋人榜
	TypeRankLive            = 7 // 直播榜
	TypeRankHotSearch       = 8 // 热搜榜
)

// 榜单子类型
const (
	SubTypeRankDay   = 1 // 日榜
	SubTypeRankWeek  = 2 // 周榜
	SubTypeRankMonth = 3 // 月榜
)

// 榜单数据类型
const (
	ElementTypeAlbum     = 1 // 音单
	ElementTypeDrama     = 2 // 剧集
	ElementTypeSound     = 3 // 音频
	ElementTypeLive      = 5 // 直播
	ElementTypeHotSearch = 6 // 热搜
)

// 搜索页榜单需要查询的数量或展示的数量
const (
	// RankItemLengthDiscoveryShow10 搜索页榜单需要展示的数量
	RankItemLengthDiscoveryShow10 = 10
	// RankItemLengthDiscoverySelect20 搜索页榜单需要查询的数量（每种榜单在搜索页展示 10 个，但查询时会过滤不符合要求的剧集或音频，因此多查询 10 个）
	RankItemLengthDiscoverySelect20 = 20
)

// TypeRankList 榜单类型列表
var TypeRankList = []int{
	TypeRankDramaNew,
	TypeRankDramaPopularity,
	TypeRankDramaReward,
	TypeRankDramaFree,
	TypeRankDramaRomantic,
	TypeRankSoundLover,
}

// SubTypeRankList 榜单子类型列表
var SubTypeRankList = []int{
	SubTypeRankDay,
	SubTypeRankWeek,
	SubTypeRankMonth,
}

// MHomepageRank 榜单表
type MHomepageRank struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间，单位：秒
	Bizdate      string `gorm:"column:bizdate"`       // 业务日期（格式：2023-03-01）
	Type         int    `gorm:"column:type"`          // 榜单类型（1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜）
	SubType      int    `gorm:"column:sub_type"`      // 榜单子类型（1: 日榜；2: 周榜；3: 月榜）
	Data         []byte `gorm:"column:data"`          // 榜单数据（对应榜单排序后的元素 ID【音频 ID 或剧集 ID】，e.g. [8012,4002,705,888]）
	// TODO: 增加 ElementType 说明榜单数据类型

	DataInfo []int64 `gorm:"-"`
}

// TableName for current model
func (MHomepageRank) TableName() string {
	return "m_homepage_rank"
}

// DB the db instance of MHomepageRank model
func (h MHomepageRank) DB() *gorm.DB {
	return service.MainDB.Table(h.TableName())
}

// AfterFind is a GORM hook for query
func (h *MHomepageRank) AfterFind() error {
	if len(h.Data) > 0 {
		err := json.Unmarshal(h.Data, &h.DataInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// FindRank 获取榜单数据
func FindRank(rankType, subRankType int, bizdate string) (*MHomepageRank, error) {
	var rankInfo MHomepageRank
	query := rankInfo.DB().Select("DATE(bizdate) AS bizdate, data").
		Where("type = ? AND sub_type = ?", rankType, subRankType)
	if bizdate == "" {
		query = query.Order("bizdate DESC")
	} else {
		query = query.Where("bizdate = ?", bizdate)
	}
	err := query.Take(&rankInfo).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &rankInfo, nil
}

// GetRankElementType 获取榜单数据类型
// TODO: 后续将在数据源表中新增字段说明榜单数据类型，将该方法删除，使用表中字段
func GetRankElementType(rankType int) int {
	var elementType int
	switch rankType {
	case TypeRankHotSearch:
		elementType = ElementTypeHotSearch
	case TypeRankDramaNew, TypeRankDramaPopularity, TypeRankDramaFree, TypeRankDramaRomantic, TypeRankDramaReward:
		elementType = ElementTypeDrama
	case TypeRankLive:
		elementType = ElementTypeLive
	case TypeRankSoundLover:
		elementType = ElementTypeSound
	default:
		logger.Errorf("未知榜单元素类型，榜单类型：%d", rankType)
		// PASS
	}
	return elementType
}

// GetDiscoveryRanks 获取发现页榜单数据
func GetDiscoveryRanks() (map[int]*MHomepageRank, error) {
	// TODO: 后续可以用窗口函数优化查询
	var rankInfo []*MHomepageRank
	// 只查询 7 天内的数据
	now := util.TimeNow()
	startDay, endDay := now.Add(-7*24*time.Hour).Format(util.TimeFormatYMD), now.Format(util.TimeFormatYMD)
	subQuery := MHomepageRank{}.DB().Select("type, sub_type, MAX(bizdate) AS max_bizdate").
		Where("data IS NOT NULL").
		Where("bizdate > ? AND bizdate <= ?", startDay, endDay).
		Where("(type IN (?) AND sub_type = ?) OR (type IN (?) AND sub_type = ?)",
			[]int{TypeRankDramaNew, TypeRankDramaFree, TypeRankDramaRomantic}, SubTypeRankDay,
			[]int{TypeRankDramaPopularity, TypeRankSoundLover}, SubTypeRankWeek).
		Group("type, sub_type")

	err := service.MainDB.Table(MHomepageRank{}.TableName()+" AS t").
		Select("DATE(t.bizdate) AS bizdate, t.type, t.sub_type, t.data").
		Joins("INNER JOIN ? AS t1 ON t.type = t1.type AND t.sub_type = t1.sub_type AND t.bizdate = t1.max_bizdate",
			subQuery.SubQuery()).
		Find(&rankInfo).Error
	if err != nil {
		return nil, err
	}

	return util.ToMap(rankInfo, "Type").(map[int]*MHomepageRank), nil
}
