package mhomepagerank

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MHomepageRank{}, "id", "create_time", "modified_time", "type", "sub_type", "data", "bizdate")
}

func TestMHomepageRank_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_homepage_rank", MHomepageRank{}.TableName())
}

func TestGetRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有榜单数据
	require.NoError(MHomepageRank{}.DB().Delete("", "bizdate = ?", "2024-08-08").Error)
	res, err := FindRank(TypeRankDramaPopularity, SubTypeRankWeek, "2024-08-08")
	require.NoError(err)
	assert.Nil(res)

	// 测试获取榜单数据
	rankInfo := MHomepageRank{
		Type:    TypeRankDramaPopularity,
		SubType: SubTypeRankWeek,
		Data:    []byte("[123,233]"),
		Bizdate: "2024-08-08",
	}
	require.NoError(rankInfo.DB().Create(&rankInfo).Error)
	res, err = FindRank(TypeRankDramaPopularity, SubTypeRankWeek, "2024-08-08")
	require.NoError(err)
	assert.NotNil(res)
	assert.Equal([]int64{123, 233}, res.DataInfo)
	assert.Equal("2024-08-08", res.Bizdate)

	bizdate := util.TimeNow().Add(24 * time.Hour).Format(util.TimeFormatYMD)
	// 测试获取榜单数据时没有业务日期
	rankInfo = MHomepageRank{
		Type:    TypeRankDramaPopularity,
		SubType: SubTypeRankWeek,
		Data:    []byte("[1233,2334]"),
		Bizdate: bizdate,
	}
	require.NoError(rankInfo.DB().Create(&rankInfo).Error)
	res, err = FindRank(TypeRankDramaPopularity, SubTypeRankWeek, "")
	require.NoError(err)
	assert.NotNil(res)
	assert.Equal([]int64{1233, 2334}, res.DataInfo)
	assert.Equal(bizdate, res.Bizdate)
}

func TestGetRankElementType(t *testing.T) {
	assert := assert.New(t)

	// 测试新品榜
	assert.Equal(ElementTypeDrama, GetRankElementType(TypeRankDramaNew))

	// 测试人气榜
	assert.Equal(ElementTypeDrama, GetRankElementType(TypeRankDramaPopularity))

	// 测试打赏榜
	assert.Equal(ElementTypeDrama, GetRankElementType(TypeRankDramaReward))

	// 测试免费榜
	assert.Equal(ElementTypeDrama, GetRankElementType(TypeRankDramaFree))

	// 测试言情榜
	assert.Equal(ElementTypeDrama, GetRankElementType(TypeRankDramaRomantic))

	// 测试声音恋人榜
	assert.Equal(ElementTypeSound, GetRankElementType(TypeRankSoundLover))

	// 测试直播榜
	assert.Equal(ElementTypeLive, GetRankElementType(TypeRankLive))

	// 测试热搜榜
	assert.Equal(ElementTypeHotSearch, GetRankElementType(TypeRankHotSearch))

	// 测试未知榜单
	assert.Zero(GetRankElementType(2333))
}

func TestGetDiscoveryRanks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bizdate1 := util.TimeNow().Format(util.TimeFormatYMD)
	bizdate2 := util.TimeNow().Add(-24 * time.Hour).Format(util.TimeFormatYMD)
	ranks, err := GetDiscoveryRanks()
	require.NoError(err)
	require.NotNil(ranks)
	require.Len(ranks, 5)
	require.NotNil(ranks[TypeRankDramaNew])
	assert.Equal(SubTypeRankDay, ranks[TypeRankDramaNew].SubType)
	assert.Equal(bizdate1, ranks[TypeRankDramaNew].Bizdate)

	require.NotNil(ranks[TypeRankDramaPopularity])
	assert.Equal(SubTypeRankWeek, ranks[TypeRankDramaPopularity].SubType)
	assert.Equal(bizdate2, ranks[TypeRankDramaPopularity].Bizdate)

	require.NotNil(ranks[TypeRankDramaFree])
	assert.Equal(SubTypeRankDay, ranks[TypeRankDramaFree].SubType)
	assert.Equal(bizdate1, ranks[TypeRankDramaFree].Bizdate)

	require.NotNil(ranks[TypeRankDramaRomantic])
	assert.Equal(SubTypeRankDay, ranks[TypeRankDramaRomantic].SubType)
	assert.Equal(bizdate1, ranks[TypeRankDramaRomantic].Bizdate)

	require.NotNil(ranks[TypeRankSoundLover])
	assert.Equal(SubTypeRankWeek, ranks[TypeRankSoundLover].SubType)
	assert.Equal(bizdate1, ranks[TypeRankSoundLover].Bizdate)
}
