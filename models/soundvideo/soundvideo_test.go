package soundvideo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMSoundVideoTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	kc.Check(MSoundVideo{},
		"id", "sid", "video_url", "create_time", "modified_time", "videourl_360",
		"videourl_480", "videourl_720", "videourl_1080", "attr", "source", "checked", "more")
}

func TestMSoundVideo_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("sound_video", MSoundVideo{}.TableName())
}

func TestListHasVideoSoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	soundIDs, err := ListHasVideoSoundIDs([]int64{1217695, 1})
	require.NoError(err)
	require.Len(soundIDs, 1)
	assert.EqualValues(1217695, soundIDs[0])

	soundIDs, err = ListHasVideoSoundIDs([]int64{})
	require.NoError(err)
	assert.Empty(soundIDs)
}
