package soundvideo

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/sound"
	"github.com/MiaoSiLa/missevan-main/service"
)

// MSoundVideo model
type MSoundVideo struct {
	ID           int64        `gorm:"column:id"`            // 主键 ID
	SID          int64        `gorm:"column:sid"`           // 音频 ID
	VideoURL     string       `gorm:"column:video_url"`     // 视频 URL 地址
	CreateTime   int64        `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64        `gorm:"column:modified_time"` // 修改时间，单位：秒
	VideoURL360  string       `gorm:"column:videourl_360"`  // 360P 视频地址
	VideoURL480  string       `gorm:"column:videourl_480"`  // 480P 视频地址
	VideoURL720  string       `gorm:"column:videourl_720"`  // 720P 视频地址
	VideoURL1080 string       `gorm:"column:videourl_1080"` // 1080P 视频地址
	Attr         util.BitMask `gorm:"column:attr"`          // 视频属性，比特位第一位为 1 时表示优先播放
	Source       int          `gorm:"column:source"`        // 视频来源，1：后台绑定；2：配音秀
	Checked      int          `gorm:"column:checked"`       // 视频审核状态，-3：转码失败；-1：待转码；0：待审核；1：审核通过
	More         []byte       `gorm:"column:more"`          // 视频额外信息，格式为 json 字符串，size 字段存放不同视频大小，单位 Bytes
}

// DB the db instance of MSoundVideo model
func (m MSoundVideo) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MSoundVideo) TableName() string {
	return "sound_video"
}

// ListHasVideoSoundIDs 获取给定音频 ID 中已绑定视频并审核通过的音频 ID 列表
func ListHasVideoSoundIDs(soundIDs []int64) ([]int64, error) {
	if len(soundIDs) == 0 {
		return []int64{}, nil
	}
	var ids []int64
	err := MSoundVideo{}.DB().Select("sid").
		Where("sid IN (?) AND checked = ?", soundIDs, sound.CheckedPass).
		Pluck("sid", &ids).Error
	if err != nil {
		return nil, err
	}
	return ids, nil
}
