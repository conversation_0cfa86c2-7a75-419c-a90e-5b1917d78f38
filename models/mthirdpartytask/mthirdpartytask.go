package mthirdpartytask

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 场景
const (
	SceneBaidu             = iota + 1 // 百度
	SceneCTRIP                        // 携程
	SceneDianPing                     // 大众点评
	SceneBaiduMap                     // 百度地图
	SceneYouku                        // 优酷视频
	SceneQQBrowser                    // QQ 浏览器
	SceneWeibo                        // 微博
	SceneQuark                        // 夸克
	SceneWechatOffiaccount            // 关注微信公众号
)

// token 字符串指定长度
const TokenLength = 32

const (
	StatusOnGoing  = iota // 未完成
	StatusFinished        // 已完成未领奖
	StatusRewarded        // 已完成已领奖
)

// MThirdPartyTask 三方导流用户任务详情
type MThirdPartyTask struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间戳，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 更新时间戳，单位：秒
	TaskTime     int64  `gorm:"column:task_time"`      // 任务发起时间戳，单位：秒
	UserID       int64  `gorm:"column:user_id"`        // 任务用户 ID
	Token        string `gorm:"column:token"`          // 任务 token
	Scene        int    `gorm:"column:scene"`          // 第三方场景
	Status       int    `gorm:"column:status"`         // 任务状态
}

// TableName for current model
func (MThirdPartyTask) TableName() string {
	return "m_third_party_task"
}

// DB the db instance of MThirdPartyTask model
func (m MThirdPartyTask) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// BeforeCreate automatically sets columns create_time and modified_time
func (m *MThirdPartyTask) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	m.CreateTime = now
	m.ModifiedTime = now
	m.TaskTime = now

	return nil
}

// BeforeUpdate automatically sets column modified_time
func (m *MThirdPartyTask) BeforeUpdate() (err error) {
	now := goutil.TimeNow().Unix()
	m.ModifiedTime = now

	return nil
}

// FindOne 检查是否完成过任务，关注公众号之类只能完成一次的任务传入 taskTime 为 0，表示不筛选任务创建时间
func FindOne(userID int64, scene, taskTime int) (*MThirdPartyTask, error) {
	m := new(MThirdPartyTask)
	query := MThirdPartyTask{}.DB().
		Where("user_id = ? AND scene = ?",
			userID, scene)
	if taskTime > 0 {
		query = query.Where("taskTime = ?", taskTime)
	}
	err := query.Take(m).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return m, nil
}

// FinishTask 完成任务，返回是否完成成功
func FinishTask(userID int64, scene int, token string) error {
	m := MThirdPartyTask{
		UserID: userID,
		Scene:  scene,
		Status: StatusFinished,
		Token:  token,
	}
	err := m.DB().Create(&m).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			return nil
		}
		return err
	}
	return nil
}
