package mthirdpartytask

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, StatusOn<PERSON>oing)
	assert.EqualValues(1, StatusFinished)
	assert.EqualValues(2, StatusRewarded)

	assert.EqualValues(1, SceneBaidu)
	assert.EqualValues(2, SceneCTRIP)
	assert.EqualValues(3, SceneDianPing)
	assert.EqualValues(4, SceneBaiduMap)
	assert.EqualValues(5, <PERSON><PERSON><PERSON><PERSON>)
	assert.EqualValues(6, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
	assert.EqualValues(7, SceneWeibo)
	assert.EqualValues(8, SceneQuark)
	assert.EqualValues(9, SceneWechatOffiaccount)
}

func TestFinishTask(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试正常完成任务
	userID := int64(1997)

	m, err := FindOne(userID, SceneWechatOffiaccount, 0)
	require.NoError(err)
	assert.Nil(m)

	token := "abcdabcdabcdabcdabcdabcdabcdabcb"
	err = FinishTask(userID, SceneWechatOffiaccount, token)
	require.NoError(err)

	m, err = FindOne(userID, SceneWechatOffiaccount, 0)
	require.NoError(err)
	assert.NotNil(m)
	assert.Equal(StatusFinished, m.Status)
}
