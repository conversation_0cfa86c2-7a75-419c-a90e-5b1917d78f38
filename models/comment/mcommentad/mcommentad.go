package mcommentad

import (
	"fmt"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/comment/mcommentadelement"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "m_comment_ad"

// 元素类型
const (
	// ElementTypeDrama 剧集
	ElementTypeDrama int = 2
	// ElementTypeSound 音频
	ElementTypeSound int = 3
)

// MCommentAd 评论区小黄条广告信息
type MCommentAd struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 修改时间。单位：秒
	Title        string `gorm:"column:title"`          // 广告标题
	AppURL       string `gorm:"column:app_url"`        // App 广告跳转链接
	WebURL       string `gorm:"column:web_url"`        // Web 广告跳转链接
	StartTime    int64  `gorm:"column:start_time"`     // 上线时间。单位：秒
	EndTime      int64  `gorm:"column:end_time"`       // 下线时间。单位：秒
}

// AdInfo 小黄条广告信息，关联元素信息通过 m_comment_ad_element 表查询
type AdInfo struct {
	ID          int64  `gorm:"column:id"`           // 评论区小黄条广告 ID
	Title       string `gorm:"column:title"`        // 广告标题
	AppURL      string `gorm:"column:app_url"`      // App 广告跳转链接
	WebURL      string `gorm:"column:web_url"`      // Web 广告跳转链接
	ElementType int    `gorm:"column:element_type"` // 元素类型。2: 剧集
	ElementID   int64  `gorm:"column:element_id"`   // 元素 ID
}

// DB the db instance of mCommentAd model
func (s MCommentAd) DB() *gorm.DB {
	return service.MainDB.Table(s.TableName())
}

// TableName for current model
func (MCommentAd) TableName() string {
	return tableName
}

// FindCommentAd 查找元素对应的（生效中的）小黄条广告信息
func FindCommentAd(elementType int, elementID int64) (*AdInfo, error) {
	var adInfo AdInfo
	nowUnix := util.TimeNow().Unix()
	err := service.MainDB.Table(MCommentAd{}.TableName()+" AS t1").
		Select("t1.id, t1.title, t1.app_url, t1.web_url, t2.element_type, t2.element_id").
		Joins(fmt.Sprintf("INNER JOIN %s AS t2 ON t1.id = t2.comment_ad_id", mcommentadelement.MCommentAdElement{}.TableName())).
		Where("t2.element_type = ? AND t2.element_id = ? AND t1.start_time <= ? AND t1.end_time > ?",
			elementType, elementID, nowUnix, nowUnix).
		Take(&adInfo).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &adInfo, nil
}
