package mcommentad

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MCommentAd{},
		"id", "create_time", "modified_time", "title", "app_url", "web_url", "start_time", "end_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MCommentAd{}.TableName())
}

func TestFindCommentAd(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试正常获取小黄条广告信息
	ad, err := FindCommentAd(ElementTypeDrama, 3000)
	require.NoError(err)
	assert.NotNil(ad)
	exceptAd := &AdInfo{
		ID:          1001,
		Title:       "测试小黄条",
		AppURL:      "missevan://test/3",
		WebURL:      "https://static.com",
		ElementType: ElementTypeDrama,
		ElementID:   3000,
	}
	assert.Equal(exceptAd, ad)

	// 测试无广告的情况
	ad, err = FindCommentAd(ElementTypeDrama, 100)
	require.NoError(err)
	assert.Nil(ad)

	// 测试广告未开始生效的情况
	ad, err = FindCommentAd(ElementTypeDrama, 3001)
	require.NoError(err)
	assert.Nil(ad)
}
