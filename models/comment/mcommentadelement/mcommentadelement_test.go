package mcommentadelement

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MCommentAdElement{},
		"id", "create_time", "modified_time", "element_type", "element_id", "comment_ad_id")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MCommentAdElement{}.TableName())
}
