package mcommentadelement

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "m_comment_ad_element"

// MCommentAdElement 评论区小黄条广告关联信息
type MCommentAdElement struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 修改时间。单位：秒
	ElementType  int   `gorm:"column:element_type"`   // 元素类型。2: 剧集
	ElementID    int64 `gorm:"column:element_id"`     // 元素 ID
	CommentAdID  int64 `gorm:"column:comment_ad_id"`  // 评论区小黄条广告 ID
}

// DB the db instance of mCommentAdElement model
func (s MCommentAdElement) DB() *gorm.DB {
	return service.MainDB.Table(s.TableName())
}

// TableName for current model
func (MCommentAdElement) TableName() string {
	return tableName
}
