package vipsubscriptionsignagreement

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, StatusPending)
	assert.EqualValues(2, StatusValid)
	assert.EqualValues(3, StatusTerminated)
}

func TestSubscriptionSignAgreementTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(VipSubscriptionSignAgreement{}, "id", "create_time", "modified_time", "user_id", "pay_type", "vip_id", "status", "start_time", "expire_time", "more")
}

func TestSubscriptionSignAgreement_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("vip_subscription_sign_agreement", VipSubscriptionSignAgreement{}.TableName())
}

func TestGetNextDeductVipUserByTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取一个用户
	now := util.TimeNow()
	timeNow := util.BeginningOfDay(now).Unix()
	endTime := util.NextDayTime(now).Unix()
	list, err := GetNextDeductVipUserByTime(timeNow, endTime, 1, 10)
	require.NoError(err)
	assert.Len(list, 1)

	// 测试获取不到用户
	now = util.TimeNow().Add(240 * time.Hour)
	timeNow = util.BeginningOfDay(now).Unix()
	endTime = util.NextDayTime(now).Unix()
	list, err = GetNextDeductVipUserByTime(timeNow, endTime, 1, 10)
	require.NoError(err)
	assert.Len(list, 0)
}
