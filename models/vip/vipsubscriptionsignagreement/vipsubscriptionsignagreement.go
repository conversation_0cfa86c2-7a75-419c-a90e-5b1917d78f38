package vipsubscriptionsignagreement

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/models/vip/vipfeedeductedrecord"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 协议签约状态（1 待签约，2 签约生效中，3 已解约）
const (
	StatusPending = iota + 1
	StatusValid
	StatusTerminated
)

// VipSubscriptionSignAgreement 用户订阅会员周期扣款签约协议
type VipSubscriptionSignAgreement struct {
	ID           int64  `gorm:"column:id"`            // 主键
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间（秒级时间戳）
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间（秒级时间戳）
	UserID       int64  `gorm:"column:user_id"`       // 用户 ID
	PayType      int    `gorm:"column:pay_type"`      // 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play
	VipID        int64  `gorm:"column:vip_id"`        // 会员套餐价目 ID
	Status       int    `gorm:"column:status"`        // 协议签约状态（1 待签约，2 签约生效中，3 已解约）
	StartTime    int64  `gorm:"column:start_time"`    // 协议生效时间（秒级时间戳）
	ExpireTime   int64  `gorm:"column:expire_time"`   // 协议失效时间（秒级时间戳），包含此时刻
	More         []byte `gorm:"column:more"`          // 更多详情 (JSON)
}

// DB the db instance of VipSubscriptionSignAgreement model
func (vm VipSubscriptionSignAgreement) DB() *gorm.DB {
	return service.PayDB.Table(vm.TableName())
}

// TableName for VipSubscriptionSignAgreement model
func (VipSubscriptionSignAgreement) TableName() string {
	return "vip_subscription_sign_agreement"
}

// GetNextDeductVipUserByTime 分页获取指定时间段下次续费的用户 ID
func GetNextDeductVipUserByTime(startTime, endTime int64, page, pageSize int) ([]int64, error) {
	var userIDs []int64
	err := service.PayDB.Table(VipSubscriptionSignAgreement{}.TableName()+" AS t1").
		Joins(fmt.Sprintf("INNER JOIN %s AS t2 ON t1.id = t2.sign_agreement_id", vipfeedeductedrecord.VipFeeDeductedRecord{}.TableName())).
		Where("t1.status = ? AND t2.next_deduct_time >= ? AND t2.next_deduct_time < ?", StatusValid, startTime, endTime).
		Order("t1.id ASC").
		Limit(pageSize).Offset(pageSize*(page-1)).Pluck("t1.user_id", &userIDs).Error
	if err != nil {
		return nil, err
	}
	return userIDs, nil
}
