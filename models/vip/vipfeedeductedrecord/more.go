package vipfeedeductedrecord

import (
	"database/sql/driver"
	"encoding/json"
)

// MoreDetails 其他信息
type MoreDetails struct {
	TradeNo              string `json:"trade_no,omitempty"`                // 商户订单号
	TransactionID        string `json:"transaction_id,omitempty"`          // 支付平台交易 ID
	WechatOpenid         string `json:"wechat_openid,omitempty"`           // 微信 openid
	IsFirstTopupDiscount bool   `json:"is_first_topup_discount,omitempty"` // 是否是首开折扣价
	IsCycleDeduction     bool   `json:"is_cycle_deduction,omitempty"`      // 是否是周期扣费的记录
	RetryTimes           int    `json:"retry_times,omitempty"`             // 重试扣费的次数
}

// Value 方法将 MoreDetails 转换为 json 字符串，用于存储到数据库中。
func (m MoreDetails) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 方法从数据库的 json 字符串中解析出 MoreDetails。
func (m *MoreDetails) Scan(input interface{}) error {
	switch p := input.(type) {
	case string:
		// 兼容 sqlite 中已提前存入的数据
		if p == "" {
			return nil
		}
		return json.Unmarshal([]byte(p), m)
	case []uint8:
		if len(p) == 0 {
			return nil
		}
		return json.Unmarshal(input.([]byte), m)
	default:
		return nil
	}
}
