package vipfeedeductedrecord

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, StatusPending)
	assert.EqualValues(2, StatusSuccess)
	assert.EqualValues(3, StatusFailed)
	assert.EqualValues(4, StatusDuplicatePay)

	assert.EqualValues(1, PayTypeIOS)
	assert.EqualValues(2, PayTypeWechat)
	assert.EqualValues(3, PayTypeAlipay)
	assert.EqualValues(4, PayTypeGooglePlay)
}

func TestVipFeeDeductedRecordTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(VipFeeDeductedRecord{}, "id", "create_time", "modified_time", "user_id", "vip_id", "sign_agreement_id", "pay_type", "price", "status", "next_deduct_time", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MoreDetails{}, "trade_no", "transaction_id", "wechat_openid", "is_first_topup_discount", "is_cycle_deduction", "retry_times")
}

func TestVipFeeDeductedRecord_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("vip_fee_deducted_record", VipFeeDeductedRecord{}.TableName())
}

func TestHasSubscribePromotional(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试购买过
	purchased, err := HasSubscribePromotional(int64(3457181))
	require.NoError(err)
	assert.True(purchased)

	// 测试未购买过
	purchased, err = HasSubscribePromotional(int64(12))
	require.NoError(err)
	assert.False(purchased)
}
