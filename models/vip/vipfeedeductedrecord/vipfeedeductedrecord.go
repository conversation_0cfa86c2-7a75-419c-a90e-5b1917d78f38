package vipfeedeductedrecord

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 扣款状态（1 待扣款，2 扣款成功，3 扣款失败，4 重复支付）
const (
	StatusPending = iota + 1
	StatusSuccess
	StatusFailed
	StatusDuplicatePay
)

// 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play（与 vip_subscription_sign_agreement 中的同名常量一致）
const (
	PayTypeIOS = iota + 1
	PayTypeWechat
	PayTypeAlipay
	PayTypeGooglePlay
)

// VipFeeDeductedRecord 会员扣费记录
type VipFeeDeductedRecord struct {
	ID              int64       `gorm:"column:id"`                // 主键
	CreateTime      int64       `gorm:"column:create_time"`       // 创建时间（秒级时间戳）
	ModifiedTime    int64       `gorm:"column:modified_time"`     // 修改时间（秒级时间戳）
	UserID          int64       `gorm:"column:user_id"`           // 用户 ID
	VipID           int64       `gorm:"column:vip_id"`            // 会员价目 ID
	SignAgreementID int64       `gorm:"column:sign_agreement_id"` // 签约协议 ID（0 为单次付费）
	PayType         int         `gorm:"column:pay_type"`          // 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play
	Price           int         `gorm:"column:price"`             // 本次扣款金额（单位：分）
	Status          int         `gorm:"column:status"`            // 本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败，4 重复支付
	NextDeductTime  int64       `gorm:"column:next_deduct_time"`  // 下次扣款时间（秒级时间戳，0 为单次付费）
	More            MoreDetails `gorm:"column:more"`              // 更多详情 (JSON)
}

// DB the db instance of VipFeeDeductedRecord model
func (v VipFeeDeductedRecord) DB() *gorm.DB {
	return service.PayDB.Table(v.TableName())
}

// TableName for VipFeeDeductedRecord model
func (VipFeeDeductedRecord) TableName() string {
	return "vip_fee_deducted_record"
}

// HasSubscribePromotional 是否享受过连续包月首次优惠
func HasSubscribePromotional(userID int64, payType ...int64) (bool, error) {
	query := VipFeeDeductedRecord{}.DB().
		Where("user_id = ? AND status = ? AND JSON_EXTRACT(more, '$.is_first_topup_discount') IS TRUE", userID, StatusSuccess)
	if len(payType) > 0 {
		query = query.Where("pay_type = ?", payType[0])
	}
	return servicedb.Exists(query)
}
