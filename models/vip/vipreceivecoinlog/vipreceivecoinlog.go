package vipreceivecoinlog

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// VipReceiveCoinLog 记录会员领取钻石的日志
type VipReceiveCoinLog struct {
	ID           int64  `gorm:"column:id"`            // 主键
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间（秒级时间戳）
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间（秒级时间戳）
	UserID       int64  `gorm:"column:user_id"`       // 用户 ID
	VipID        int64  `gorm:"column:vip_id"`        // 领取时的会员 ID
	CoinNum      int64  `gorm:"column:coin_num"`      // 领取的钻石数量
	ReceiveTime  int64  `gorm:"column:receive_time"`  // 领取时间（秒级时间戳）
	More         []byte `gorm:"column:more"`          // 更多详情 (JSON)
}

// DB the db instance of VipReceiveCoinLog model
func (m VipReceiveCoinLog) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for VipReceiveCoinLog model
func (VipReceiveCoinLog) TableName() string {
	return "vip_receive_coin_log"
}

// IsReceivedToday 判断用户在今天内是否领取过钻石
func IsReceivedToday(userID int64) (bool, error) {
	todayStart := util.BeginningOfDay(util.TimeNow()).Unix()
	query := VipReceiveCoinLog{}.DB().Where("user_id = ? AND receive_time >= ?", userID, todayStart)
	return servicedb.Exists(query)
}
