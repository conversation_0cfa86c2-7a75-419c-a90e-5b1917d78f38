package vipreceivecoinlog

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestVipReceiveCoinLogTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(VipReceiveCoinLog{}, "id", "create_time", "modified_time", "user_id", "vip_id", "coin_num", "receive_time", "more")
}

func TestVipReceiveCoinLogTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("vip_receive_coin_log", VipReceiveCoinLog{}.TableName())
}

func TestIsReceivedToday(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userNotReceived := int64(1001)
	userReceived := int64(1002)

	// 测试未领取过的用户
	received, err := IsReceivedToday(userNotReceived)
	require.NoError(err)
	assert.False(received)

	// 测试今天领取过的用户
	received, err = IsReceivedToday(userReceived)
	require.NoError(err)
	assert.True(received)
}
