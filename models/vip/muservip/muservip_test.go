package muservip

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserVip{},
		"id", "create_time", "modified_time", "vip_id", "user_id", "type", "start_time", "end_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MUserVip{}.TableName())
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, VipStatusNotYet)
	assert.EqualValues(1, VipStatusInEffect)
	assert.EqualValues(2, VipStatusTrial)
	assert.EqualValues(3, VipStatusExpired)
}

func TestGetUserVipInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户无购买记录
	testUserID := int64(1)
	vip, err := GetUserVipInfo(testUserID)
	require.NoError(err)
	expectUserVip := &UserVipInfo{
		Status:  VipStatusNotYet,
		EndTime: 0,
	}
	assert.EqualValues(expectUserVip, vip)

	// 测试当前在会员有效期内
	testUserID = 11
	vip, err = GetUserVipInfo(testUserID)
	require.NoError(err)
	expectUserVip = &UserVipInfo{
		Status:  VipStatusInEffect,
		EndTime: 1999999998,
	}
	assert.EqualValues(expectUserVip, vip)

	// 测试会员已过期
	testUserID = 10
	vip, err = GetUserVipInfo(testUserID)
	require.NoError(err)
	expectUserVip = &UserVipInfo{
		Status:  VipStatusExpired,
		EndTime: 1728964593,
	}
	assert.EqualValues(expectUserVip, vip)
}

func TestListVipUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户列表为空
	result, err := ListVipUserIDs([]int64{})
	require.NoError(err)
	assert.Len(result, 0)

	// 测试 vip 用户列表为空
	result, err = ListVipUserIDs([]int64{123456})
	require.NoError(err)
	assert.Len(result, 0)

	// 测试获取 vip 用户列表
	result, err = ListVipUserIDs([]int64{11, 123456})
	require.NoError(err)
	require.Len(result, 1)
	assert.EqualValues(11, result[0])
}

func TestIsVip(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试当前非会员
	testUserID := int64(10)
	vip, err := IsVip(testUserID)
	require.NoError(err)
	assert.False(vip)

	// 测试当前为会员
	testUserID = 11
	vip, err = IsVip(testUserID)
	require.NoError(err)
	assert.True(vip)
}

func TestGetExpiredVipUserIDsOnDay(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取过期会员用户
	vipExpirationTime := util.TimeNow().Add(time.Hour * -24)
	userIDs, err := GetExpiredVipUserIDsOnDay(vipExpirationTime, 1, 10)
	require.NoError(err)
	require.Len(userIDs, 2)
	assert.Equal(userIDs[0], int64(3222))

	// 测试获取不到过期会员用户
	userIDs, err = GetExpiredVipUserIDsOnDay(vipExpirationTime.Add(time.Hour*-999999), 1, 1)
	require.NoError(err)
	assert.Len(userIDs, 0)
}
