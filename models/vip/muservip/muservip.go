package muservip

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "m_user_vip"

// 会员类型：点播会员
const TypePlay = 4

// 会员开通状态
const (
	VipStatusNotYet   = iota // 未开通过正式会员
	VipStatusInEffect        // 正式会员
	VipStatusTrial           // 体验会员
	VipStatusExpired         // 正式会员已过期
)

// UserVipInfo 基本会员信息
type UserVipInfo struct {
	Status  int
	EndTime int64 // 会员真实过期时间点（一般为过期日的 23:59:59 时间戳），单位：秒
}

// MUserVip model
type MUserVip struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间。单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 修改时间。单位：秒
	VipID        int64 `gorm:"column:vip_id"`
	UserID       int64 `gorm:"column:user_id"`
	Type         int   `gorm:"column:type"`       // vip 类型。4：点播会员，其他类型待定
	StartTime    int64 `gorm:"column:start_time"` // 开始时间。单位：秒
	EndTime      int64 `gorm:"column:end_time"`   // 过期时间，存的值为结束日时间的下一秒（即过期日第二天零点）。单位：秒
}

// DB the db instance of MUserVip model
func (m MUserVip) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MUserVip) TableName() string {
	return tableName
}

// GetUserVipInfo 获取基本会员信息
func GetUserVipInfo(userID int64) (*UserVipInfo, error) {
	var endTime int64
	vip := &UserVipInfo{
		Status: VipStatusNotYet,
	}
	err := MUserVip{}.DB().Select("end_time").
		Where("user_id = ? AND type = ?", userID, TypePlay).
		Order("end_time DESC").Limit(1).Row().Scan(&endTime)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return vip, nil
		}
		return nil, err
	}
	// 过期时间保存的是结束日时间的下一秒，所以这里减一秒
	vip.EndTime = endTime - 1
	if endTime < util.TimeNow().Unix() {
		vip.Status = VipStatusExpired
	} else {
		vip.Status = VipStatusInEffect
	}
	return vip, nil
}

// ListVipUserIDs 获取 vip 用户 IDs
func ListVipUserIDs(userIDs []int64) ([]int64, error) {
	if len(userIDs) == 0 {
		return []int64{}, nil
	}

	var vipUserIDs []int64
	now := util.TimeNow().Unix()
	err := MUserVip{}.DB().Where("user_id IN (?) AND start_time <= ? AND end_time > ? AND type = ?",
		userIDs, now, now, TypePlay).Pluck("user_id", &vipUserIDs).Error
	if err != nil {
		return nil, err
	}
	return vipUserIDs, nil
}

// IsVip 用户当前是否为会员
func IsVip(userID int64) (bool, error) {
	now := util.TimeNow().Unix()
	return servicedb.Exists(MUserVip{}.DB().
		Where("user_id = ? AND type = ? AND start_time <= ? AND end_time > ?", userID, TypePlay, now, now))
}

// GetExpiredVipUserIDsOnDay 根据指定时间获取在该时间当天过期的 VIP 用户 ID 列表
func GetExpiredVipUserIDsOnDay(time time.Time, page, pageSize int) ([]int64, error) {
	var userIDs []int64
	startTime := util.BeginningOfDay(time).Unix()
	endTime := util.NextDayTime(time).Unix()
	err := service.DB.Table(MUserVip{}.TableName()+" AS t1").
		Joins(fmt.Sprintf(`
			LEFT JOIN %s AS t2 ON t1.user_id = t2.user_id
			AND t2.type = ?
			AND t2.start_time >= ?
		 `, MUserVip{}.TableName()), TypePlay, endTime).
		Where("t1.type = ? AND t1.end_time > ? AND t1.end_time <= ? AND t2.id IS NULL", TypePlay, startTime, endTime).
		Limit(pageSize).
		Offset(pageSize*(page-1)).
		Pluck("t1.user_id", &userIDs).Error
	if err != nil {
		return nil, err
	}
	return userIDs, nil
}
