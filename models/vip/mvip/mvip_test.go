package mvip

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, PlatformIOS)
	assert.EqualValues(2, PlatformAndroid)
	assert.EqualValues(3, PlatformGooglePlay)

	assert.EqualValues(1, DeductFeeScheduleOnce)
	assert.EqualValues(2, DeductFeeScheduleContinuousMonthly)
	assert.EqualValues(3, DeductFeeScheduleContinuousQuarterly)
}

func TestMVipTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MVip{}, "id", "create_time", "modified_time", "delete_time", "deduct_fee_schedule", "sort", "price", "platform", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MoreDetails{}, "title", "first_title", "first_subscribe_discount_price", "original_price", "first_original_price", "description", "first_description", "active", "corner_mark_text", "period", "wechat_plan_id")
}

func TestMVip_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_vip", MVip{}.TableName())
}

func TestListVipMenu(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试 iOS
	res, err := ListVipMenu(PlatformIOS)
	require.NoError(err)
	require.NotEmpty(res)
	assert.Len(res, 5)

	// 测试没有价目信息时
	res, err = ListVipMenu(5)
	require.NoError(err)
	require.Empty(res)
}

func TestGetPromotionalSubscribePrice(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("9.9", GetPromotionalSubscribePrice())
}
