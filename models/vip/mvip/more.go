package mvip

import (
	"database/sql/driver"
	"encoding/json"
)

// MoreDetails 其他信息
type MoreDetails struct {
	Title                       string `json:"title"`                          // 标题
	FirstTitle                  string `json:"first_title"`                    // 首次开通的标题
	FirstSubscribeDiscountPrice int    `json:"first_subscribe_discount_price"` // 首次开通的优惠价格（单位：分）
	OriginalPrice               int    `json:"original_price"`                 // 划线价格（单位：分）
	FirstOriginalPrice          int    `json:"first_original_price"`           // 首次开通的划线价格（单位：分）
	Description                 string `json:"description"`                    // 说明文案
	FirstDescription            string `json:"first_description"`              // 首次开通的说明文案
	Active                      int    `json:"active"`                         // 是否默认选中。1：是
	CornerMarkText              string `json:"corner_mark_text"`               // 角标文案
	Period                      int    `json:"period"`                         // 开通后的周期时间（单位：秒）
	WechatPlanID                int    `json:"wechat_plan_id"`                 // 微信协议代扣模板 ID
}

// Value 方法将 MoreDetails 转换为 json 字符串，用于存储到数据库中。
func (m MoreDetails) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 方法从数据库的 json 字符串中解析出 MoreDetails。
func (m *MoreDetails) Scan(input interface{}) error {
	switch p := input.(type) {
	case string:
		// 兼容 sqlite 中已提前存入的数据
		if p == "" {
			return nil
		}
		return json.Unmarshal([]byte(p), m)
	case []uint8:
		if len(p) == 0 {
			return nil
		}
		return json.Unmarshal(input.([]byte), m)
	default:
		return nil
	}
}
