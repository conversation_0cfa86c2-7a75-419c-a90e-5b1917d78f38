package mvip

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 平台（1 iOS、2 Android（非 Google 渠道）、3 Google Play）
const (
	PlatformIOS = iota + 1
	PlatformAndroid
	PlatformGooglePlay
)

// 付费周期（1 单次付费、2 连续包月、3 连续包季）
const (
	DeductFeeScheduleOnce = iota + 1
	DeductFeeScheduleContinuousMonthly
	DeductFeeScheduleContinuousQuarterly
)

// MVip 会员价目
type MVip struct {
	ID                int64       `gorm:"column:id"`                  // 主键 ID
	CreateTime        int64       `gorm:"column:create_time"`         // 创建时间（秒级时间戳）
	ModifiedTime      int64       `gorm:"column:modified_time"`       // 修改时间（秒级时间戳）
	DeleteTime        int64       `gorm:"column:delete_time"`         // 删除时间（秒级时间戳）
	DeductFeeSchedule int         `gorm:"column:deduct_fee_schedule"` // 付费周期（1 单次付费、2 连续包月、3 连续包季）
	Sort              int         `gorm:"column:sort"`                // 展示顺序
	Price             int         `gorm:"column:price"`               // 价格（单位：分）
	Platform          int         `gorm:"column:platform"`            // 平台（1 iOS、2 Android（非 Google 渠道）、3 Google Play）
	More              MoreDetails `gorm:"column:more"`                // 更多详情
}

// DB the db instance of MVip model
func (mv MVip) DB() *gorm.DB {
	return service.DB.Table(mv.TableName())
}

// TableName for MVip model
func (MVip) TableName() string {
	return "m_vip"
}

// ListVipMenu 获取平台价目列表
func ListVipMenu(platform int) ([]*MVip, error) {
	var vipMenus []*MVip
	err := MVip{}.DB().
		Where("platform = ? AND delete_time = 0", platform).
		Order("sort ASC").
		Find(&vipMenus).Error
	if err != nil {
		return nil, err
	}
	return vipMenus, nil
}

// GetPromotionalSubscribePrice 获取会员促销价（仅用作显示，单位：元）
func GetPromotionalSubscribePrice() string {
	// NOTICE: 目前不同平台优惠价都是一样的，先简单从配置返回，后续优惠价不同或调整频繁，可改为从数据库取
	return config.Conf.Params.Vip.PromotionalPrice
}
