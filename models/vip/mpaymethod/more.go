package mpaymethod

import (
	"database/sql/driver"
	"encoding/json"
)

// MoreDetails 其他信息
type MoreDetails struct {
	Active int `json:"active"` // 是否默认选中。1：是
}

// Value 方法将 MoreDetails 转换为 json 字符串，用于存储到数据库中。
func (m MoreDetails) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 方法从数据库的 json 字符串中解析出 MoreDetails。
func (m *MoreDetails) Scan(input interface{}) error {
	switch p := input.(type) {
	case string:
		// 兼容 sqlite 中已提前存入的数据
		if p == "" {
			return nil
		}
		return json.Unmarshal([]byte(p), m)
	case []uint8:
		if len(p) == 0 {
			return nil
		}
		return json.Unmarshal(input.([]byte), m)
	default:
		return nil
	}
}
