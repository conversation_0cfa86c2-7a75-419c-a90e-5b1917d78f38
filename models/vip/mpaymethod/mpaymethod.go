package mpaymethod

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// 支付场景（1: 开通 vip；2: 充值钻石）
const (
	sceneSubscribeVip = iota + 1
	sceneTopUpDiamond
)

// MPayMethod 支付方式
// TODO: 充值钻石场景支付方式待整合到 m_pay_method 表中
type MPayMethod struct {
	ID           int64       `gorm:"column:id" json:"id"`           // 主键
	CreateTime   int64       `gorm:"column:create_time" json:"-"`   // 创建时间，单位：秒
	ModifiedTime int64       `gorm:"column:modified_time" json:"-"` // 修改时间，单位：秒
	Scene        int         `gorm:"column:scene" json:"-"`         // 支付场景（1: 开通 vip；2: 充值钻石）
	Title        string      `gorm:"column:title" json:"title"`     // 支付方式名称
	Icon         string      `gorm:"column:icon" json:"icon"`       // 支付图标协议地址
	Type         string      `gorm:"column:type" json:"type"`       // 支付方式类型（wechatpay: 微信支付；alipay: 支付宝支付）
	Sort         int         `gorm:"column:sort" json:"sort"`       // 展示顺序
	More         MoreDetails `gorm:"column:more" json:"more"`       // 更多详情 (JSON)
}

// DB the db instance of MPayMethod model
func (m MPayMethod) DB() *gorm.DB {
	return service.PayDB.Table(m.TableName())
}

// TableName for MPayMethod model
func (MPayMethod) TableName() string {
	return "m_pay_method"
}

// ListVipPayMethod 获取 vip 支付方式
func ListVipPayMethod() ([]*MPayMethod, error) {
	key := keys.KeyVipPaymentMethod0.Format()
	payMethodStr, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}

	var list []*MPayMethod
	if payMethodStr != "" {
		err = json.Unmarshal([]byte(payMethodStr), &list)
		if err != nil {
			logger.Error(err)
			// PASS
			return nil, nil
		}
	} else {
		err = MPayMethod{}.DB().
			Select("id, title, icon, type, sort, more").
			Where("scene = ?", sceneSubscribeVip).
			Order("sort ASC").Find(&list).Error
		if err != nil {
			return nil, err
		}
		if len(list) <= 0 {
			logger.Error("会员支付方式数据不存在")
			// PASS
			return nil, nil
		}
		payMethodBytes, err := json.Marshal(list)
		if err != nil {
			logger.Error(err)
			// PASS
			return nil, nil
		}
		err = service.LRURedis.Set(key, payMethodBytes, 5*time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	return list, nil
}
