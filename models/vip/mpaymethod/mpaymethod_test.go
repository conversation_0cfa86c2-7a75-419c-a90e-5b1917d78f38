package mpaymethod

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, sceneSubscribeVip)
	assert.EqualValues(2, sceneTopUpDiamond)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MPayMethod{}, "id", "create_time", "modified_time", "scene", "title", "icon", "type", "sort", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MPayMethod{}, "id", "title", "icon", "type", "sort", "more")
	kc.Check(MoreDetails{}, "active")
}

func TestMPayMethod_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_pay_method", MPayMethod{}.TableName())
}

func TestListVipPayMethod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除缓存数据
	key := keys.KeyVipPaymentMethod0.Format()
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试没有缓存时
	list, err := ListVipPayMethod()
	require.NoError(err)
	require.NotEmpty(list)
	require.Len(list, 2)
	assert.Equal("微信支付", list[0].Title)
	assert.Equal("wechatpay", list[0].Type)
	assert.Equal("test://wechatpay.png", list[0].Icon)
	assert.Equal(1, list[0].Sort)
	assert.Equal(1, list[0].More.Active)

	assert.Equal("支付宝支付", list[1].Title)
	assert.Equal("alipay", list[1].Type)
	assert.Equal("test://alipay.png", list[1].Icon)
	assert.Equal(2, list[1].Sort)
	assert.Equal(0, list[1].More.Active)

	// 验证生成缓存数据
	payMethodStr, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	require.NoError(json.Unmarshal([]byte(payMethodStr), &list))
	require.NotEmpty(list)
	require.Len(list, 2)
	assert.Equal("微信支付", list[0].Title)
	assert.Equal("wechatpay", list[0].Type)
	assert.Equal("test://wechatpay.png", list[0].Icon)
	assert.Equal(1, list[0].Sort)
	assert.Equal(1, list[0].More.Active)

	assert.Equal("支付宝支付", list[1].Title)
	assert.Equal("alipay", list[1].Type)
	assert.Equal("test://alipay.png", list[1].Icon)
	assert.Equal(2, list[1].Sort)
	assert.Equal(0, list[1].More.Active)

	// 测试有缓存时
	list, err = ListVipPayMethod()
	require.NoError(err)
	require.NotEmpty(list)
	require.Len(list, 2)
	assert.Equal("微信支付", list[0].Title)
	assert.Equal("wechatpay", list[0].Type)
	assert.Equal("test://wechatpay.png", list[0].Icon)
	assert.Equal(1, list[0].Sort)
	assert.Equal(1, list[0].More.Active)

	assert.Equal("支付宝支付", list[1].Title)
	assert.Equal("alipay", list[1].Type)
	assert.Equal("test://alipay.png", list[1].Icon)
	assert.Equal(2, list[1].Sort)
	assert.Equal(0, list[1].More.Active)
}
