package vipdramarevenueflow

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

// TableName 会员剧集收益流水汇总表
const TableName = "vip_drama_revenue_flow"

// 属性
const (
	AttrNormalPartner   = iota // 普通合作方（流水为分成前）
	AttrNormalJingjiang        // 晋江（流水为分成后）
)

// VipDramaRevenueFlow 会员剧集收益流水
/*
CREATE TABLE IF NOT EXISTS `vip_drama_revenue_flow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）',
  `modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）',
  `user_id` bigint NOT NULL COMMENT '合作方M号',
  `delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间（秒级时间戳，0 为数据可用，非删除状态）',
  `drama_id` bigint NOT NULL COMMENT '剧集 ID',
  `drama_name` varchar(60) NOT NULL COMMENT '剧集名称',
  `vip_listen_flow` bigint NOT NULL COMMENT '会员听剧流水（单位：分）',
  `start_time` bigint NOT NULL COMMENT '起始时间',
  `end_time` bigint NOT NULL COMMENT '结束时间（不含此刻）',
  `attr` int NOT NULL DEFAULT 0 COMMENT '属性（1 为晋江流水，分成后；0 为非晋江流水，分成前）',
  `more` json NULL COMMENT '更多详情',
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='会员剧集收益流水汇总';
*/
type VipDramaRevenueFlow struct {
	ID           int64 `gorm:"-" json:"-"`
	CreateTime   int64 `gorm:"-" json:"-"`
	ModifiedTime int64 `gorm:"-" json:"-"`
	DeleteTime   int64 `gorm:"-" json:"-"`

	UserID    int64  `gorm:"column:user_id" json:"-"`
	DramaID   int64  `gorm:"column:drama_id" json:"-"`
	DramaName string `gorm:"column:drama_name" json:"-"`

	VipListenFlow int64 `gorm:"column:vip_listen_flow" json:"-"`

	StartTime int64 `gorm:"column:start_time" json:"-"`
	EndTime   int64 `gorm:"column:end_time" json:"-"`

	Attr int         `gorm:"-" json:"-"`
	More MoreDetails `gorm:"-" json:"-"`
}

// MoreDetails 其他信息
type MoreDetails struct{}

// Value 方法将 MoreDetails 转换为 json 字符串，用于存储到数据库中。
func (m MoreDetails) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 方法从数据库的 json 字符串中解析出 MoreDetails。
func (m *MoreDetails) Scan(input interface{}) error {
	switch p := input.(type) {
	case string:
		// 兼容 sqlite 中已提前存入的数据
		if p == "" {
			return nil
		}
		return json.Unmarshal([]byte(p), m)
	case []uint8:
		if len(p) == 0 {
			return nil
		}
		return json.Unmarshal(input.([]byte), m)
	default:
		return nil
	}
}

// DB the db instance of VipDramaRevenueFlow model
func (v VipDramaRevenueFlow) DB() *gorm.DB {
	return service.PayDB.Table(v.TableName())
}

// TableName for VipDramaRevenueFlow model
func (v VipDramaRevenueFlow) TableName() string {
	return TableName
}
