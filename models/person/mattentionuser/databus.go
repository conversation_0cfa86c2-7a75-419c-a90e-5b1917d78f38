package mattentionuser

import (
	"context"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// 关注类型
const (
	FollowTypeFollow = iota
	FollowTypeUnfollow
)

// 关注来源
const (
	// FollowFromDefault 默认（其他）
	FollowFromDefault = iota
	// FollowFromWeb web
	FollowFromWeb
	// FollowFromApp app
	FollowFromApp
	// FollowFromLive live
	FollowFromLive
	// FollowFromGameDownload game download
	FollowFromGameDownload
)

// followLog 关注用户 databus 日志信息
type followLog struct {
	UserID       int64 `json:"user_id"`        // 当前用户 ID
	FollowUserID int64 `json:"follow_user_id"` // 被关注用户 ID
	FollowFrom   int   `json:"follow_from"`    // 关注来源
	FollowType   int   `json:"follow_type"`    // 类型。0：关注；1：取关
	CreateTime   int64 `json:"create_time"`    // 创建时间。单位：秒
}

// SendFollowLog 生产关注数据到 databus
func SendFollowLog(userID int64, followUserID int64, followFrom int, followType int) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	key := keys.DatabusKeyUserFollowLog1.Format(userID)
	log := &followLog{
		UserID:       userID,
		FollowUserID: followUserID,
		FollowFrom:   followFrom,
		FollowType:   followType,
		CreateTime:   util.TimeNow().Unix(),
	}
	err := service.Databus.AppLogPub.Send(ctx, key, log)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
