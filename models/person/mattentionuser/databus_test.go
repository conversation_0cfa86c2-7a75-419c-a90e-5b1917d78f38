package mattentionuser

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, FollowTypeFollow)
	assert.Equal(1, FollowTypeUnfollow)

	assert.Equal(0, FollowFromDefault)
	assert.Equal(1, FollowFromWeb)
	assert.Equal(2, FollowFromApp)
	assert.Equal(3, FollowFromLive)
	assert.Equal(4, FollowFromGameDownload)
}

func TestSendFollowLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Databus.AppLogPub.ClearDebugPubMsgs()
	testUserID := int64(2000)
	testFollowUserID := int64(3000)

	SendFollowLog(testUserID, testFollowUserID, FollowFromWeb, FollowTypeFollow)
	pubMsg := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(pubMsg, 1)

	message := <-pubMsg
	assert.Equal(keys.DatabusKeyUserFollowLog1.Format(testUserID), message.Key)
	expectBytes, err := json.Marshal(followLog{
		UserID:       testUserID,
		FollowUserID: testFollowUserID,
		FollowFrom:   FollowFromWeb,
		FollowType:   FollowTypeFollow,
		CreateTime:   util.TimeNow().Unix(),
	})
	require.NoError(err)
	actualBytes, err := message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectBytes, actualBytes)
}
