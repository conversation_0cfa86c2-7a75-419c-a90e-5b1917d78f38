package mattentionuser

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	"github.com/MiaoSiLa/missevan-main/models/user"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

const tableName = "m_attention_user"

// MAttentionUser model
type MAttentionUser struct {
	ID           int64 `gorm:"column:id;primary_key"`
	UserActive   int64 `gorm:"column:user_active"`
	UserPasstive int64 `gorm:"column:user_passtive"`
	Time         int64 `gorm:"column:time"` // 创建时间戳。单位：秒
}

// DB the db instance of MAttentionUser model
func (m MAttentionUser) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MAttentionUser) TableName() string {
	return tableName
}

// findNeedAndFollowedUserIDs 获取指定 IDs 中用户待关注及已关注的用户 IDs
func findNeedAndFollowedUserIDs(c mrpc.UserContext, userID int64, followUserIDs []int64) (filteredFollowUserIDs, alreadyFollowedUserIDs []int64, err error) {
	filteredFollowUserIDs = sets.Diff(followUserIDs, []int64{userID})
	if len(filteredFollowUserIDs) == 0 {
		return []int64{}, []int64{}, nil
	}
	filteredFollowUserIDs, err = user.FindUserIDs(filteredFollowUserIDs)
	if err != nil {
		return nil, nil, err
	}
	if len(filteredFollowUserIDs) == 0 {
		return []int64{}, []int64{}, nil
	}
	blockUserList, userBlockList, err := userapi.BlockStatusList(c, userID, filteredFollowUserIDs)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id": userID,
			"rpc":     userapi.URIBlockStatusList,
		}).Errorf("访问 rpc 接口出错，error: %v", err)
		return nil, nil, err
	}
	blackUserIDs := append(blockUserList, userBlockList...)
	if len(blackUserIDs) > 0 {
		blackUserIDs = sets.Uniq(blackUserIDs)
		filteredFollowUserIDs = sets.Diff(filteredFollowUserIDs, blackUserIDs)
		if len(filteredFollowUserIDs) == 0 {
			return []int64{}, []int64{}, nil
		}
	}
	err = MAttentionUser{}.DB().
		Where("user_active = ? AND user_passtive IN (?)", userID, filteredFollowUserIDs).
		Pluck("user_passtive", &alreadyFollowedUserIDs).Error
	if err != nil {
		return nil, nil, err
	}
	return sets.Diff(filteredFollowUserIDs, alreadyFollowedUserIDs), alreadyFollowedUserIDs, nil
}

// BatchFollow 批量关注用户
func BatchFollow(c mrpc.UserContext, userID int64, followUserIDs []int64) (succeedFollowUserIDs, alreadyFollowedUserIDs []int64, err error) {
	followUserIDs, alreadyFollowedUserIDs, err = findNeedAndFollowedUserIDs(c, userID, followUserIDs)
	if err != nil {
		return nil, nil, err
	}
	followUserNum := len(followUserIDs)
	if followUserNum == 0 {
		return []int64{}, alreadyFollowedUserIDs, nil
	}
	followList := make([]MAttentionUser, 0, followUserNum)
	timeNow := util.TimeNow().Unix()
	for _, v := range followUserIDs {
		followInfo := MAttentionUser{
			UserActive:   userID,
			UserPasstive: v,
			Time:         timeNow,
		}
		followList = append(followList, followInfo)
	}
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 暂不考虑重复索引问题
		err := servicedb.BatchInsert(tx, MAttentionUser{}.TableName(), followList)
		if err != nil {
			return err
		}
		err = tx.Table(user.MowangskUser{}.TableName()).Where("id IN (?)", followUserIDs).
			Update("fansnum", gorm.Expr("fansnum + ?", 1)).Error
		if err != nil {
			return err
		}
		return tx.Table(user.MowangskUser{}.TableName()).Where("id = ?", userID).
			Update("follownum", gorm.Expr("follownum + ?", followUserNum)).Error
	})
	if err != nil {
		return nil, nil, err
	}
	return followUserIDs, alreadyFollowedUserIDs, nil
}
