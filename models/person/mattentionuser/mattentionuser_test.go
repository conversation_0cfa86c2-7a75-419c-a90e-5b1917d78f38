package mattentionuser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/user"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/userapi"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MAttentionUser{},
		"id", "user_active", "user_passtive", "time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MAttentionUser{}.TableName())
}

func TestFindNeedAndFollowedUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIBlockStatusList, func(input interface{}) (interface{}, error) {
		return userapi.BlockStatusListResponse{
			UserBlockList: []int64{20240480, 20240482},
			BlockUserList: []int64{20240481, 20240482},
		}, nil
	})
	defer cancel()

	// 测试部分用户不存在或已关注
	testUserID := int64(190)
	testUserIDs := []int64{18001, 18002, 18003, 18004, 18005, 18006}
	var c mrpc.UserContext
	filteredFollowUserIDs, alreadyFollowedUserIDs, err := findNeedAndFollowedUserIDs(c, testUserID, testUserIDs)
	require.NoError(err)
	// 用户 18005 不存在，用户 18006 已关注
	assert.Equal([]int64{18001, 18002, 18003, 18004}, filteredFollowUserIDs)
	assert.Equal([]int64{18006}, alreadyFollowedUserIDs)

	// 测试全部用户不存在
	testUserIDs = []int64{18005}
	filteredFollowUserIDs, alreadyFollowedUserIDs, err = findNeedAndFollowedUserIDs(c, testUserID, testUserIDs)
	require.NoError(err)
	assert.Empty(filteredFollowUserIDs)
	assert.Empty(alreadyFollowedUserIDs)

	// 测试全部用户已关注
	testUserIDs = []int64{18006}
	filteredFollowUserIDs, alreadyFollowedUserIDs, err = findNeedAndFollowedUserIDs(c, testUserID, testUserIDs)
	require.NoError(err)
	assert.Empty(filteredFollowUserIDs)
	assert.Equal([]int64{18006}, alreadyFollowedUserIDs)
}

func TestBatchFollow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIBlockStatusList, func(input interface{}) (interface{}, error) {
		return userapi.BlockStatusListResponse{
			UserBlockList: []int64{20240480, 20240482},
			BlockUserList: []int64{20240481, 20240482},
		}, nil
	})
	defer cancel()

	// 测试全部关注成功
	testUserID := int64(12)
	testUserIDs := []int64{18001, 18002, 18003}
	var c mrpc.UserContext
	successFollowUserIDs, alreadyFollowedUserIDs, err := BatchFollow(c, testUserID, testUserIDs)
	require.NoError(err)
	assert.Equal([]int64{18001, 18002, 18003}, successFollowUserIDs)
	assert.Empty(alreadyFollowedUserIDs)
	// 验证被关注者粉丝数增加
	var fansnumList []int64
	require.NoError(user.MowangskUser{}.DB().
		Where("id IN (?)", testUserIDs).
		Pluck("fansnum", &fansnumList).Error)
	assert.ElementsMatch([]int64{80, 80, 80}, fansnumList)
	// 验证关注者关注数增加
	var follownum int64
	require.NoError(user.MowangskUser{}.DB().
		Select("follownum").
		Where("id = ?", testUserID).Row().Scan(&follownum))
	assert.Equal(int64(233), follownum)

	// 测试全部用户已被关注过
	testUserIDs = []int64{18001, 18002, 18003}
	successFollowUserIDs, alreadyFollowedUserIDs, err = BatchFollow(c, testUserID, testUserIDs)
	require.NoError(err)
	assert.Empty(successFollowUserIDs)
	assert.Equal([]int64{18001, 18002, 18003}, alreadyFollowedUserIDs)

	// 测试部分用户已被关注过
	testUserIDs = []int64{18001, 18002, 18003, 18004, testUserID}
	successFollowUserIDs, alreadyFollowedUserIDs, err = BatchFollow(c, testUserID, testUserIDs)
	require.NoError(err)
	assert.Equal([]int64{18004}, successFollowUserIDs)
	assert.Equal([]int64{18001, 18002, 18003}, alreadyFollowedUserIDs)
}
