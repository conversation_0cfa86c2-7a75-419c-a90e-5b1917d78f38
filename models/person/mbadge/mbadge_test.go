package mbadge

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

const testBadgeID = 233

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MBadge{}, "id", "create_time", "modified_time", "element_type",
		"element_id", "title", "intro", "cover", "expire_duration", "more", "icon")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(More{}, "special_no_list")
}

func TestMBadge_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_badge", MBadge{}.TableName())
}

func TestMBadge_AfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	b := MBadge{
		Cover: "test://test/202309/27/test.png",
		More:  "{\"special_no_list\":[1,2,3,4]}",
		Icon:  "test://test/202309/27/icon/test.png",
	}
	require.NoError(b.AfterFind())
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", b.CoverURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", b.IconURL)
	assert.Len(b.MoreInfo.SpecialNOList, 4)
}

func TestFindBadgeByID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试没有称号
	require.NoError(MBadge{}.DB().Delete("", "id = ?", testBadgeID).Error)
	res, err := FindBadgeByID(testBadgeID)
	require.NoError(err)
	assert.Nil(res)

	// 测试获取称号
	badgeInfo := &MBadge{
		ID:    233,
		Title: "测试称号名",
		Intro: "测试称号简介",
		Cover: "test://test/202309/27/test.png",
		Icon:  "test://test/202309/27/icon/test.png",
	}
	require.NoError(badgeInfo.DB().Create(badgeInfo).Error)
	res, err = FindBadgeByID(testBadgeID)
	require.NoError(err)
	assert.EqualValues(testBadgeID, res.ID)
	assert.Equal(badgeInfo.Title, res.Title)
	assert.Equal(badgeInfo.Intro, res.Intro)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", res.CoverURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", res.IconURL)
}
