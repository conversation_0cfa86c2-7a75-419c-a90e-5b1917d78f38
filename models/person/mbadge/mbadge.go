package mbadge

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

// EndTimeForeverEffective 称号 end_time 永久生效值
const EndTimeForeverEffective int64 = 0

// 称号所属类型
const (
	// TypeVirtualIdol 虚拟偶像
	TypeVirtualIdol = 1
)

// MBadge model
type MBadge struct {
	ID             int64  `gorm:"column:id;primary_key"`  // 主键
	CreateTime     int64  `gorm:"column:create_time"`     // 创建时间戳。单位：秒
	ModifiedTime   int64  `gorm:"column:modified_time"`   // 最后更新时间戳。单位：秒
	ElementType    int    `gorm:"column:element_type"`    // 称号所属类型。1：虚拟偶像
	ElementID      int64  `gorm:"column:element_id"`      // 元素 ID
	Title          string `gorm:"column:title"`           // 称号名
	Intro          string `gorm:"column:intro"`           // 称号简介
	Cover          string `gorm:"column:cover"`           // 称号卡片背景图片
	ExpireDuration int64  `gorm:"column:expire_duration"` // 称号获得后过期时长，为 0 时表示不会过期。单位：秒
	More           string `gorm:"column:more"`            // 其他信息
	Icon           string `gorm:"column:icon"`            // 称号 icon

	CoverURL string `gorm:"-"`
	IconURL  string `gorm:"-"`
	MoreInfo More   `gorm:"-"`
}

// More 其他信息
type More struct {
	SpecialNOList []int64 `json:"special_no_list"` // 特殊称号编号，预留给内部人员，json 格式 e.g. [2,3]
}

// TableName for current model
func (MBadge) TableName() string {
	return "m_badge"
}

// DB the db instance of MBadge model
func (m MBadge) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// AfterFind is a GORM hook for query
func (m *MBadge) AfterFind() error {
	if m.Cover != "" {
		m.CoverURL = service.Storage.Parse(m.Cover)
	}
	if m.Icon != "" {
		m.IconURL = service.Storage.Parse(m.Icon)
	}
	if m.More != "" {
		if err := json.Unmarshal([]byte(m.More), &m.MoreInfo); err != nil {
			return err
		}
	}
	return nil
}

// FindBadgeByID 根据 ID 查询称号信息
func FindBadgeByID(id int64) (*MBadge, error) {
	badge := new(MBadge)
	err := badge.DB().Where("id = ?", id).Take(badge).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return badge, nil
}
