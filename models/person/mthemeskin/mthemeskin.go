package mthemeskin

import (
	"encoding/json"
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskin"
	"github.com/MiaoSiLa/missevan-main/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 是否是会员专享皮肤
const (
	VipNot   = iota // 否
	VipLimit        // 是
)

// ExpireTimeForeverEffective 用户主题皮肤 expire_time 永久生效值
const ExpireTimeForeverEffective int64 = 0

// MoreInfo more 中扩展信息
type MoreInfo struct {
	Cover               string   `json:"cover"`                  // 封面图，目前用于列表页
	ListBgStartColor    string   `json:"list_bg_start_color"`    // 列表页背景色渐变起始颜色
	ListBgEndColor      string   `json:"list_bg_end_color"`      // 列表页背景色渐变结束颜色
	PreviewBgStartColor string   `json:"preview_bg_start_color"` // 详情页背景色渐变起始颜色
	PreviewBgEndColor   string   `json:"preview_bg_end_color"`   // 详情页背景色渐变结束颜色
	PreviewCovers       []string `json:"preview_covers"`         // 详情页预览图列表

	CoverURL         string   `json:"cover_url"`
	PreviewCoverUrls []string `json:"preview_cover_urls"`
}

// MThemeSkin model
type MThemeSkin struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间戳，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 更新时间戳，单位：秒
	Title        string `gorm:"column:title"`          // 名称
	Intro        string `gorm:"column:intro"`          // 简介
	Vip          int8   `gorm:"column:vip"`            // 是否会员专享。0：否；1：是
	PayType      int8   `gorm:"column:pay_type"`       // 付费类型。0：非付费使用；1：付费使用
	Package      string `gorm:"column:package"`        // 皮肤资源压缩包协议地址
	More         []byte `gorm:"column:more"`           // 更多信息，JSON 格式
	Sort         int64  `gorm:"column:sort"`           // 排序，值越小越靠前展示

	MoreInfo   MoreInfo `gorm:"-"`
	PackageURL string   `gorm:"-"`
}

// ThemeSkinElement 会员皮肤元素
type ThemeSkinElement struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	Intro        string `json:"intro"`
	ImageURL     string `json:"image_url"`
	BgStartColor string `json:"bg_start_color"`
	BgEndColor   string `json:"bg_end_color"`
}

// UserThemeSkinInfo 用户主题皮肤信息
type UserThemeSkinInfo struct {
	ID         int64  `json:"id"`
	PackageURL string `json:"package_url"`

	Package string `json:"-"`
	Vip     int    `json:"-"`
}

// AfterFind is a GORM hook for query
func (m *MThemeSkin) AfterFind() error {
	if len(m.More) != 0 {
		err := json.Unmarshal(m.More, &m.MoreInfo)
		if err != nil {
			return err
		}
		if m.MoreInfo.Cover != "" {
			m.MoreInfo.CoverURL = service.Storage.Parse(m.MoreInfo.Cover)
		}
		if len(m.MoreInfo.PreviewCovers) != 0 {
			for _, v := range m.MoreInfo.PreviewCovers {
				m.MoreInfo.PreviewCoverUrls = append(m.MoreInfo.PreviewCoverUrls, service.Storage.Parse(v))
			}
		}
	}
	if m.Package != "" {
		m.PackageURL = service.Storage.Parse(m.Package)
	}
	return nil
}

// DB the db instance of current model
func (m MThemeSkin) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MThemeSkin) TableName() string {
	return "m_theme_skin"
}

// ListVipThemeSkin 获取会员皮肤列表
func ListVipThemeSkin() ([]ThemeSkinElement, error) {
	var skins []*MThemeSkin
	err := MThemeSkin{}.DB().
		Select("id, title, intro, more").
		Where("vip = ?", VipLimit).
		Order("sort ASC").
		Find(&skins).Error
	if err != nil {
		return nil, err
	}
	skinsLen := len(skins)
	if skinsLen == 0 {
		return []ThemeSkinElement{}, nil
	}
	skinElements := make([]ThemeSkinElement, 0, skinsLen)
	for _, skin := range skins {
		skinElement := ThemeSkinElement{
			ID:           skin.ID,
			Name:         skin.Title,
			Intro:        skin.Intro,
			ImageURL:     skin.MoreInfo.CoverURL,
			BgStartColor: skin.MoreInfo.ListBgStartColor,
			BgEndColor:   skin.MoreInfo.ListBgEndColor,
		}
		skinElements = append(skinElements, skinElement)
	}
	return skinElements, nil
}

// GetUserThemeSkin 查找用户生效中的主题皮肤信息
// TODO: 暂未考虑购买皮肤的情况
func GetUserThemeSkin(userID int64) (*UserThemeSkinInfo, error) {
	var info UserThemeSkinInfo
	err := service.DB.Table(MThemeSkin{}.TableName()+" AS t1").
		Select("t1.id, t1.vip, t1.package").
		Joins(fmt.Sprintf("INNER JOIN %s AS t2 ON t1.id = t2.theme_skin_id", muserthemeskin.MUserThemeSkin{}.TableName())).
		Where("t2.user_id = ? AND t2.status = ? AND(t2.expire_time = ? OR t2.expire_time >= ?)",
			userID, muserthemeskin.StatusWearing, muserthemeskin.ExpireTimeForeverEffective, util.TimeNow().Unix()).
		Take(&info).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	if info.Vip == VipLimit {
		isVip, err := muservip.IsVip(userID)
		if err != nil {
			return nil, err
		}
		if !isVip {
			return nil, nil
		}
	}
	if info.Package == "" {
		logger.WithField("id", info.ID).Error("主题皮肤资源为空")
		// PASS
		return nil, nil
	}
	info.PackageURL = service.Storage.Parse(info.Package)
	return &info, nil
}

// FindOne 获取一条主题皮肤信息
func FindOne(id int64) (*MThemeSkin, error) {
	info := new(MThemeSkin)
	err := MThemeSkin{}.DB().Where("id = ?", id).Take(&info).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return info, nil
}

// GetUserVipThemeSkinID 查找用户装扮中的会员主题皮肤 ID
func GetUserVipThemeSkinID(userID int64) (int64, error) {
	var id int64
	err := service.DB.Table(MThemeSkin{}.TableName()+" AS t1").
		Select("t1.id").
		Joins(fmt.Sprintf("INNER JOIN %s AS t2 ON t1.id = t2.theme_skin_id", muserthemeskin.MUserThemeSkin{}.TableName())).
		Where("t1.vip = ? AND t2.user_id = ? AND t2.status = ? AND(t2.expire_time = ? OR t2.expire_time >= ?)",
			VipLimit, userID, muserthemeskin.StatusWearing, muserthemeskin.ExpireTimeForeverEffective, util.TimeNow().Unix()).
		Row().Scan(&id)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, nil
		}
		return 0, err
	}
	return id, nil
}
