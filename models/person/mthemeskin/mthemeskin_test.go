package mthemeskin

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MThemeSkin{}, "id", "create_time", "modified_time", "title",
		"intro", "vip", "pay_type", "package", "more", "sort")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, VipNot)
	assert.EqualValues(1, VipLimit)
}

func TestListVipThemeSkin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ListVipThemeSkin()
	require.NoError(err)
	// 当前有 4 条会员主题皮肤信息，为了避免后续有新的测试数据影响测试结果，此处不断言全部会员主题皮肤信息
	assert.GreaterOrEqual(len(list), 4)
	expectThemeSkin := ThemeSkinElement{
		ID:           1,
		Name:         "椰子的假日",
		Intro:        "和小猫一起，享受假日！",
		ImageURL:     "https://static-test.maoercdn.com/themeskin/202412/25/5e1646155d2497829dbe9541b55bdc8d170940.jpg",
		BgStartColor: "#F8A623",
		BgEndColor:   "#F8A629",
	}
	assert.Equal(expectThemeSkin, list[0])
}

func TestGetUserThemeSkin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户当前无皮肤（会员过期皮肤失效）
	userID := int64(10)
	info, err := GetUserThemeSkin(userID)
	require.NoError(err)
	assert.Nil(info)

	// 测试用户当前有皮肤（非会员皮肤）
	userID = 9
	info, err = GetUserThemeSkin(userID)
	require.NoError(err)
	expectInfo := &UserThemeSkinInfo{
		ID:         5,
		Vip:        VipNot,
		Package:    "test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f944.zip",
		PackageURL: "https://static-test.maoercdn.com/themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f944.zip",
	}
	assert.Equal(expectInfo, info)

	// 测试用户当前有皮肤（会员皮肤）
	userID = 11
	info, err = GetUserThemeSkin(userID)
	require.NoError(err)
	expectInfo = &UserThemeSkinInfo{
		ID:         1,
		Vip:        VipLimit,
		Package:    "test://themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.zip",
		PackageURL: "https://static-test.maoercdn.com/themeskin/202412/25/se1646155d2497829dbe9541b55bdc8d17f941.zip",
	}
	assert.Equal(expectInfo, info)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试正常获取到主题皮肤
	id := int64(1)
	info, err := FindOne(id)
	require.NoError(err)
	assert.NotNil(info)
	assert.EqualValues(1, info.ID)

	// 测试主题皮肤不存在
	id = 999999999
	info, err = FindOne(id)
	require.NoError(err)
	assert.Nil(info)
}

func TestGetUserVipThemeSkinID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户未装扮会员主题皮肤
	id, err := GetUserVipThemeSkinID(1)
	require.NoError(err)
	assert.Zero(id)

	// 测试用户正在装扮会员主题皮肤
	id, err = GetUserVipThemeSkinID(10)
	require.NoError(err)
	assert.NotZero(id)
}
