package museravatarframemap

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	testUserID           = 233
	testAvatarFrameID    = 123
	testAvatarFrameID2   = 124
	testVipAvatarFrameID = 125
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserAvatarFrameMap{}, "id", "create_time", "modified_time", "user_id",
		"avatar_frame_id", "expire_time", "status")
	kc.Check(UserAvatarFrame{}, "user_id", "avatar_frame_id", "name", "intro", "frame",
		"expire_time", "status", "icon", "more", "type")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, ExpireTypeDay)
	assert.EqualValues(1, ExpireTypeTime)

	assert.EqualValues(0, StatusTakeoff)
	assert.EqualValues(1, StatusWearing)
	assert.EqualValues(2, StatusExpired)
}

func TestMUserAvatarFrameMap_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_user_avatar_frame_map", MUserAvatarFrameMap{}.TableName())
}

func TestMUserAvatarFrameMap_BeforeCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := MUserAvatarFrameMap{}
	require.NoError(m.BeforeCreate(nil))
	assert.NotZero(m.CreateTime)
}

func TestMUserAvatarFrameMap_BeforeSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := MUserAvatarFrameMap{}
	require.NoError(m.BeforeSave(nil))
	assert.NotZero(m.ModifiedTime)
}

func TestUserAvatarFrame_AfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	u := UserAvatarFrame{
		Frame: "test://test/202309/27/test.png",
		Icon:  "test://test/202309/27/icon/test.png",
	}
	require.NoError(u.AfterFind())
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", u.AvatarFrameURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", u.IconURL)
	assert.Empty(u.MoreInfo.ShowDramaIDs)

	// 测试 More 有值时
	more := "{\"show_drama_ids\": [23333]}"
	u.More = []byte(more)
	require.NoError(u.AfterFind())
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", u.AvatarFrameURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", u.IconURL)
	require.NotEmpty(u.MoreInfo.ShowDramaIDs)
	assert.Equal([]int64{23333}, u.MoreInfo.ShowDramaIDs)
}

func TestFindUserAvatarFrame(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试没有查询到头像框
	require.NoError(MUserAvatarFrameMap{}.DB().
		Delete("", "user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).Error)
	res, err := FindUserAvatarFrame(testUserID, testAvatarFrameID)
	require.NoError(err)
	assert.Nil(res)

	// 测试查询到头像框
	data := &MUserAvatarFrameMap{
		UserID:        testUserID,
		AvatarFrameID: testAvatarFrameID,
	}
	require.NoError(data.DB().Create(data).Error)
	res, err = FindUserAvatarFrame(testUserID, testAvatarFrameID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(testUserID, res.UserID)
	assert.EqualValues(testAvatarFrameID, res.AvatarFrameID)
}

func TestMUserAvatarFrameMap_SaveUserAvatarFrameStatus(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	require.NoError(MUserAvatarFrameMap{}.DB().Delete("", "user_id = ? AND avatar_frame_id IN (?)",
		testUserID, []int64{testAvatarFrameID, testAvatarFrameID2}).Error)
	userAvatarFrame1 := &MUserAvatarFrameMap{
		UserID:        testUserID,
		AvatarFrameID: testAvatarFrameID,
		Status:        StatusTakeoff,
	}
	require.NoError(userAvatarFrame1.DB().Create(userAvatarFrame1).Error)
	userAvatarFrame2 := &MUserAvatarFrameMap{
		UserID:        testUserID,
		AvatarFrameID: testAvatarFrameID2,
		Status:        StatusWearing,
		ExpireTime:    util.TimeNow().Add(time.Hour).Unix(),
	}
	require.NoError(userAvatarFrame2.DB().Create(userAvatarFrame2).Error)

	// 测试用户佩戴头像框
	require.NoError(userAvatarFrame1.SaveUserAvatarFrameStatus(StatusWearing))
	var data1 MUserAvatarFrameMap
	require.NoError(data1.DB().Where("user_id = ? AND avatar_frame_id = ?",
		userAvatarFrame1.UserID, userAvatarFrame1.AvatarFrameID).Find(&data1).Error)
	assert.EqualValues(testUserID, data1.UserID)
	assert.EqualValues(testAvatarFrameID, data1.AvatarFrameID)
	assert.Equal(StatusWearing, data1.Status)

	var data2 MUserAvatarFrameMap
	require.NoError(data2.DB().Where("user_id = ? AND avatar_frame_id = ?",
		userAvatarFrame2.UserID, userAvatarFrame2.AvatarFrameID).Find(&data2).Error)
	assert.EqualValues(testUserID, data2.UserID)
	assert.EqualValues(userAvatarFrame2.AvatarFrameID, data2.AvatarFrameID)
	assert.Equal(StatusTakeoff, data2.Status)

	// 测试用户卸下头像框
	require.NoError(data1.SaveUserAvatarFrameStatus(StatusTakeoff))
	var data3 MUserAvatarFrameMap
	require.NoError(data3.DB().Where("user_id = ? AND avatar_frame_id = ?",
		data1.UserID, data1.AvatarFrameID).Find(&data3).Error)
	assert.EqualValues(testUserID, data3.UserID)
	assert.EqualValues(testAvatarFrameID, data3.AvatarFrameID)
	assert.Equal(StatusTakeoff, data3.Status)

	// 测试修改状态和原状态一致
	require.NoError(data3.SaveUserAvatarFrameStatus(StatusTakeoff))
	var data4 MUserAvatarFrameMap
	require.NoError(data4.DB().Where("user_id = ? AND avatar_frame_id = ?",
		data1.UserID, data1.AvatarFrameID).Find(&data4).Error)
	assert.Equal(StatusTakeoff, data4.Status)

	// 测试用户首次佩戴 vip 头像框
	userAvatarFrame3 := &MUserAvatarFrameMap{
		UserID:        testUserID,
		AvatarFrameID: testVipAvatarFrameID,
		Status:        StatusWearing,
		ExpireTime:    ExpireTimeForeverEffective,
	}
	require.NoError(userAvatarFrame3.SaveUserAvatarFrameStatus(StatusWearing))
	var data5 MUserAvatarFrameMap
	require.NoError(data5.DB().Where("user_id = ? AND avatar_frame_id = ?",
		userAvatarFrame3.UserID, userAvatarFrame3.AvatarFrameID).Find(&data5).Error)
	assert.EqualValues(testUserID, data5.UserID)
	assert.EqualValues(testVipAvatarFrameID, data5.AvatarFrameID)
	assert.Equal(StatusWearing, data5.Status)
	assert.Zero(data5.ExpireTime)
}
func TestIsWearingAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1223211)
	)

	m := MUserAvatarFrameMap{
		UserID:        testUserID,
		AvatarFrameID: 1010101,
		ExpireTime:    util.TimeNow().Add(time.Hour).Unix(),
		Status:        StatusWearing,
	}
	err := m.DB().Delete(MUserAvatarFrameMap{}, "user_id = ?", testUserID).Error
	require.NoError(err)
	err = m.DB().Create(&m).Error
	require.NoError(err)

	ok, err := isWearingAvatarFrame(m.DB(), testUserID)
	require.NoError(err)
	assert.True(ok)

	err = m.DB().Where("user_id = ?", testUserID).
		Update("expire_time", util.TimeNow().Add(-time.Hour).Unix()).Error
	require.NoError(err)
	ok, err = isWearingAvatarFrame(m.DB(), testUserID)
	require.NoError(err)
	assert.False(ok)

	ok, err = isWearingAvatarFrame(m.DB(), 99009000999)
	require.NoError(err)
	assert.False(ok)
}

func TestSendAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试头像框发放为永久有效的情况
	testAvatarFrameID := int64(233)
	testUserID := int64(123456)
	require.NoError(MUserAvatarFrameMap{}.DB().
		Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).Delete("").Error)
	avatarFrame := mavatarframe.MAvatarFrame{
		ID:             testAvatarFrameID,
		ExpireDuration: 0,
	}
	isNew, userAvatarFrame, err := SendAvatarFrame(&avatarFrame, testUserID, util.NewInt(StatusWearing), nil, ExpireTypeDay, 0)
	require.NoError(err)
	assert.Zero(userAvatarFrame.ExpireTime)
	assert.True(isNew)
	// 验证数据
	m := new(MUserAvatarFrameMap)
	require.NoError(m.DB().Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).
		Take(m).Error)
	assert.Equal(StatusWearing, m.Status)
	assert.Zero(m.ExpireTime)

	// 测试头像框发放为非永久有效的情况
	require.NoError(MUserAvatarFrameMap{}.DB().
		Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).Delete("").Error)
	avatarFrame.ExpireDuration = 86400
	util.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	})
	defer util.SetTimeNow(nil)
	isNew, userAvatarFrame, err = SendAvatarFrame(&avatarFrame, testUserID, util.NewInt(StatusWearing), nil, ExpireTypeDay, 0)
	require.NoError(err)
	nextDayTime := util.NextDayTime(util.TimeNow()).Unix()
	assert.EqualValues(nextDayTime, userAvatarFrame.ExpireTime)
	assert.True(isNew)
	// 验证数据
	m = new(MUserAvatarFrameMap)
	require.NoError(m.DB().Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).
		Take(m).Error)
	assert.Equal(StatusWearing, m.Status)
	assert.EqualValues(nextDayTime, m.ExpireTime)

	// 测试发放其他头像框（永久有效，立即佩戴），之前已佩戴头像变为未佩戴
	avatarFrame2 := mavatarframe.MAvatarFrame{
		ID:             234,
		ExpireDuration: 0,
	}
	isNew, userAvatarFrame, err = SendAvatarFrame(&avatarFrame2, testUserID, util.NewInt(StatusWearing), nil, ExpireTypeDay, 0)
	require.NoError(err)
	assert.Zero(userAvatarFrame.ExpireTime)
	assert.True(isNew)
	// 验证数据
	newAvatarFrame := new(MUserAvatarFrameMap)
	require.NoError(m.DB().Where("user_id = ? AND avatar_frame_id = ?", testUserID, avatarFrame2.ID).
		Take(newAvatarFrame).Error)
	assert.Equal(StatusWearing, newAvatarFrame.Status)
	// 获得的头像框永久有效
	assert.EqualValues(ExpireTimeForeverEffective, newAvatarFrame.ExpireTime)
	oldAvatarFrame := new(MUserAvatarFrameMap)
	require.NoError(m.DB().Where("user_id = ? AND avatar_frame_id = ?", testUserID, avatarFrame.ID).
		Take(oldAvatarFrame).Error)
	// 旧的佩戴中头像框变为未佩戴
	assert.Equal(StatusTakeoff, oldAvatarFrame.Status)

	// 测试重复发放
	isNew, userAvatarFrame, err = SendAvatarFrame(&avatarFrame, testUserID, util.NewInt(StatusTakeoff), nil, ExpireTypeDay, 86400)
	require.NoError(err)
	assert.EqualValues(nextDayTime+86400, userAvatarFrame.ExpireTime)
	assert.False(isNew)
	// 验证数据
	m = new(MUserAvatarFrameMap)
	require.NoError(m.DB().Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).
		Take(m).Error)
	assert.Equal(StatusTakeoff, m.Status)
	assert.EqualValues(nextDayTime+86400, m.ExpireTime)

	uaf := MUserAvatarFrameMap{
		UserID:        1223211,
		AvatarFrameID: avatarFrame.ID,
		ExpireTime:    util.TimeNow().Add(time.Hour).Unix(),
		Status:        StatusTakeoff,
	}
	err = m.DB().Delete(MUserAvatarFrameMap{}, "user_id = ?", uaf.UserID).Error
	require.NoError(err)
	err = m.DB().Create(&uaf).Error
	require.NoError(err)
	_, userAvatarFrame, err = SendAvatarFrame(&avatarFrame, uaf.UserID, nil, util.NewInt(1), ExpireTypeDay, 0)
	require.NoError(err)
	assert.Equal(StatusWearing, userAvatarFrame.Status)

	// 测试已有佩戴的头像框，不佩戴该头像框
	err = m.DB().Delete(MUserAvatarFrameMap{}, "user_id = ?", uaf.UserID).Error
	require.NoError(err)
	uaf.Status = StatusWearing
	err = m.DB().Create(&uaf).Error
	require.NoError(err)
	af := &mavatarframe.MAvatarFrame{ID: 11111}
	_, userAvatarFrame, err = SendAvatarFrame(af, uaf.UserID, nil, util.NewInt(0), ExpireTypeDay, 0)
	require.NoError(err)
	assert.Equal(StatusTakeoff, userAvatarFrame.Status)

	// 测试已有该头像框，没有佩戴，自动佩戴该头像框
	err = m.DB().Delete(MUserAvatarFrameMap{}, "user_id = ?", uaf.UserID).Error
	require.NoError(err)
	uaf.Status = StatusTakeoff
	err = m.DB().Create(&uaf).Error
	require.NoError(err)
	_, userAvatarFrame, err = SendAvatarFrame(&avatarFrame, uaf.UserID, nil, util.NewInt(1), ExpireTypeDay, 0)
	require.NoError(err)
	assert.Equal(StatusWearing, userAvatarFrame.Status)
}

func TestNewExpireTime(t *testing.T) {
	assert := assert.New(t)

	// 测试获得新头像框，头像框过期时间为 0
	cancel := util.SetTimeNow(func() time.Time {
		return time.Now()
	})
	defer cancel()
	m := MUserAvatarFrameMap{}
	avatarFrame := &mavatarframe.MAvatarFrame{
		EndTime:        0,
		ExpireDuration: 0,
	}
	assert.EqualValues(ExpireTimeForeverEffective, m.newExpireTime(avatarFrame, ExpireTypeDay, 0))

	// 测试获得新头像框，头像框过期时间不为 0
	now := util.TimeNow()
	m = MUserAvatarFrameMap{}
	oneDayDuration := int64(86400)
	avatarFrame = &mavatarframe.MAvatarFrame{
		EndTime:        9999999999,
		ExpireDuration: oneDayDuration,
	}
	nextDayTime := util.NextDayTime(now).Unix()
	assert.EqualValues(nextDayTime, m.newExpireTime(avatarFrame, ExpireTypeDay, 0))

	// 测试获得新头像框，指定过期时长
	avatarFrame = &mavatarframe.MAvatarFrame{
		EndTime:        9999999999,
		ExpireDuration: 1,
	}
	assert.EqualValues(nextDayTime, m.newExpireTime(avatarFrame, ExpireTypeDay, oneDayDuration))

	// 测试获得新头像框，指定过期时长，不按自然日过期
	assert.EqualValues(now.Add(time.Duration(oneDayDuration)*time.Second).Unix(), m.newExpireTime(avatarFrame, ExpireTypeTime, oneDayDuration))

	// 测试获得已有头像框，头像框过期时间为永不过期
	m = MUserAvatarFrameMap{
		ID:         233,
		ExpireTime: ExpireTimeForeverEffective,
	}
	assert.EqualValues(ExpireTimeForeverEffective, m.newExpireTime(avatarFrame, ExpireTypeDay, oneDayDuration))

	// 测试获得已有头像框，之前的佩戴已过期
	m = MUserAvatarFrameMap{
		ID:         233,
		ExpireTime: 1,
	}
	assert.EqualValues(nextDayTime, m.newExpireTime(avatarFrame, ExpireTypeDay, oneDayDuration))

	// 测试获得已有头像框，之前的佩戴未过期
	m = MUserAvatarFrameMap{
		ID:         233,
		ExpireTime: nextDayTime,
	}
	assert.EqualValues(util.BeginningOfDay(now.Add(time.Duration(oneDayDuration*2)*time.Second)).Unix(), m.newExpireTime(avatarFrame, ExpireTypeDay, oneDayDuration))
}

func TestListWearingByUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户 IDs 为空时
	avatarFrameList, err := ListWearingByUserIDs([]int64{}, false)
	require.NoError(err)
	require.Empty(avatarFrameList)

	// 测试有用户 ID 但对应用户都没有佩戴的头像框时
	avatarFrameList, err = ListWearingByUserIDs([]int64{10001, 1002}, false)
	require.NoError(err)
	require.Empty(avatarFrameList)

	// 测试有用户 ID 且有用户佩戴的头像框时
	avatarFrameList, err = ListWearingByUserIDs([]int64{1, 2, 3, 3222}, false)
	require.NoError(err)
	require.Len(avatarFrameList, 3)
	assert.EqualValues(4, avatarFrameList[0].AvatarFrameID)
	assert.EqualValues(1, avatarFrameList[0].UserID)
	assert.Equal("测试头像框 4", avatarFrameList[0].Name)
	assert.Equal(service.Storage.Parse("test://test_4.webp"), avatarFrameList[0].AvatarFrameURL)
	assert.Equal(mavatarframe.TypeNormal, avatarFrameList[0].Type)
	assert.EqualValues(5, avatarFrameList[1].AvatarFrameID)
	assert.EqualValues(2, avatarFrameList[1].UserID)
	assert.Equal("测试头像框 5", avatarFrameList[1].Name)
	assert.Equal(service.Storage.Parse("test://test_5.webp"), avatarFrameList[1].AvatarFrameURL)
	assert.Equal(ExpireTimeForeverEffective, avatarFrameList[1].ExpireTime)
	assert.Equal(StatusWearing, avatarFrameList[1].Status)
	assert.Equal(mavatarframe.TypeNormal, avatarFrameList[1].Type)
	assert.EqualValues(6, avatarFrameList[2].AvatarFrameID)
	assert.EqualValues(3222, avatarFrameList[2].UserID)
	assert.Equal("会员头像框 1", avatarFrameList[2].Name)
	assert.Equal(service.Storage.Parse("test://test_1.webp"), avatarFrameList[2].AvatarFrameURL)
	assert.Equal(ExpireTimeForeverEffective, avatarFrameList[2].ExpireTime)
	assert.Equal(StatusWearing, avatarFrameList[2].Status)
	assert.Equal(mavatarframe.TypeVip, avatarFrameList[2].Type)

	// 测试有用户 ID 且仅获取用户佩戴的普通头像框时
	avatarFrameList, err = ListWearingByUserIDs([]int64{1, 2, 3, 3222}, true)
	require.NoError(err)
	require.Len(avatarFrameList, 2)
	assert.EqualValues(4, avatarFrameList[0].AvatarFrameID)
	assert.EqualValues(1, avatarFrameList[0].UserID)
	assert.Equal("测试头像框 4", avatarFrameList[0].Name)
	assert.Equal(service.Storage.Parse("test://test_4.webp"), avatarFrameList[0].AvatarFrameURL)
	assert.Equal(mavatarframe.TypeNormal, avatarFrameList[0].Type)
	assert.EqualValues(5, avatarFrameList[1].AvatarFrameID)
	assert.EqualValues(2, avatarFrameList[1].UserID)
	assert.Equal("测试头像框 5", avatarFrameList[1].Name)
	assert.Equal(service.Storage.Parse("test://test_5.webp"), avatarFrameList[1].AvatarFrameURL)
	assert.Equal(ExpireTimeForeverEffective, avatarFrameList[1].ExpireTime)
	assert.Equal(StatusWearing, avatarFrameList[1].Status)
	assert.Equal(mavatarframe.TypeNormal, avatarFrameList[1].Type)
}

func TestUserAvatarFrameLister_ListUserNormalAvatarFrame(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试用户没有头像框
	require.NoError(MUserAvatarFrameMap{}.DB().Delete("", "user_id = ?", testUserID).Error)
	param := UserAvatarFrameLister{
		Page:     1,
		PageSize: 3,
		UserID:   testUserID,
	}
	res, hasMore, err := param.ListUserNormalAvatarFrame()
	require.NoError(err)
	assert.False(hasMore)
	assert.Empty(res)

	// 测试获取用户普通头像框
	testAvatarFrameIDs := []int64{233, 234, 235, 236, 237, 238}
	avatarFrames := make([]mavatarframe.MAvatarFrame, 0, len(testAvatarFrameIDs))
	userAvatarFrames := make([]MUserAvatarFrameMap, 0, len(testAvatarFrameIDs))
	for i, testAvatarFrameID := range testAvatarFrameIDs {
		avatarFrameType := mavatarframe.TypeNormal
		if testAvatarFrameID == 238 {
			avatarFrameType = mavatarframe.TypeVip
		}
		avatarFrames = append(avatarFrames, mavatarframe.MAvatarFrame{
			ID:    testAvatarFrameID,
			Name:  "测试头像框 " + strconv.Itoa(i+1),
			Intro: "测试头像框简介",
			Frame: "test://test/202309/27/test.png",
			Type:  avatarFrameType,
		})

		userAvatarFrame := MUserAvatarFrameMap{
			CreateTime:    util.TimeNow().Add(time.Hour * time.Duration(i+1)).Unix(),
			AvatarFrameID: testAvatarFrameID,
			UserID:        testUserID,
			ExpireTime:    util.TimeNow().Add(-time.Hour).Unix(),
		}
		if testAvatarFrameID == 233 {
			userAvatarFrame.ExpireTime = util.TimeNow().Add(time.Hour).Unix()
		}
		if testAvatarFrameID == 235 {
			userAvatarFrame.ExpireTime = ExpireTimeForeverEffective
		}
		userAvatarFrames = append(userAvatarFrames, userAvatarFrame)
	}
	require.NoError(avatarFrames[0].DB().Delete("", "id IN (?)", testAvatarFrameIDs).Error)
	require.NoError(servicedb.BatchInsert(service.MainDB, avatarFrames[0].TableName(), avatarFrames))
	require.NoError(userAvatarFrames[0].DB().Delete("",
		"user_id = ? AND avatar_frame_id IN (?)", testUserID, testAvatarFrameIDs).Error)
	require.NoError(servicedb.BatchInsert(service.MainDB, userAvatarFrames[0].TableName(), userAvatarFrames))

	res, hasMore, err = param.ListUserNormalAvatarFrame()
	require.NoError(err)
	assert.True(hasMore)
	assert.Len(res, 3)
	assert.EqualValues(235, res[0].AvatarFrameID)
	assert.EqualValues(233, res[1].AvatarFrameID)
	assert.EqualValues(237, res[2].AvatarFrameID)

	// 测试获取最后一页数据
	param.Page = 2
	res, hasMore, err = param.ListUserNormalAvatarFrame()
	require.NoError(err)
	assert.False(hasMore)
	assert.Len(res, 2)
	assert.EqualValues(236, res[0].AvatarFrameID)
	assert.EqualValues(234, res[1].AvatarFrameID)
}

func TestGetUserWearStatus(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试用户没有头像框
	testAvatarFrameID := int64(456465)
	require.NoError(MUserAvatarFrameMap{}.DB().Delete("", "user_id = ?", testUserID).Error)
	res, err := GetUserWearStatus(testUserID, testAvatarFrameID)
	require.NoError(err)
	assert.Nil(res)

	// 测试用户未佩戴头像框且未过期
	userAvatarFrame := MUserAvatarFrameMap{
		AvatarFrameID: testAvatarFrameID,
		UserID:        testUserID,
		Status:        StatusTakeoff,
		ExpireTime:    util.TimeNow().Add(time.Hour).Unix(),
	}
	require.NoError(userAvatarFrame.DB().Create(&userAvatarFrame).Error)
	res, err = GetUserWearStatus(testUserID, testAvatarFrameID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(StatusTakeoff, *res)

	// 测试用户佩戴了头像框且未过期
	require.NoError(userAvatarFrame.DB().
		Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).
		Update("status", StatusWearing).Error)
	res, err = GetUserWearStatus(testUserID, testAvatarFrameID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(StatusWearing, *res)

	// 测试用户佩戴的头像框已过期
	require.NoError(userAvatarFrame.DB().
		Where("user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).
		Update("expire_time", util.TimeNow().Add(-time.Hour).Unix()).Error)
	res, err = GetUserWearStatus(testUserID, testAvatarFrameID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(StatusExpired, *res)
}

func TestBatchTakeoffByIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试更新头像框佩戴状态
	ids := []int64{1, 2, 3}
	var userAvatarFrameRelation []MUserAvatarFrameMap
	err := MUserAvatarFrameMap{}.DB().Where("id IN (?) AND status = ?", ids, StatusTakeoff).Find(&userAvatarFrameRelation).Error
	require.NoError(err)
	assert.Len(userAvatarFrameRelation, 0)
	err = BatchTakeoffByIDs(ids)
	require.NoError(err)
	err = MUserAvatarFrameMap{}.DB().Where("id IN (?) AND status = ?", ids, StatusTakeoff).Find(&userAvatarFrameRelation).Error
	require.NoError(err)
	assert.Len(userAvatarFrameRelation, len(ids))
}

func TestGetOnWearVipAvatarFrames(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试目标用户获取头像框佩戴信息
	userIDS := []int64{3223}
	list, err := GetOnWearVipAvatarFrames(userIDS)
	require.NoError(err)
	assert.Len(list, 1)

	// 测试多个用户只有目标用户能获取头像框佩戴信息
	userIDS = []int64{1, 2, 3, 3223}
	list, err = GetOnWearVipAvatarFrames(userIDS)
	require.NoError(err)
	require.Len(list, 1)
	assert.Equal(userIDS[3], list[0].UserID)
}

func TestGetUserVipAvatarFrameID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户未佩戴会员头像挂件
	id, err := GetUserVipAvatarFrameID(1)
	require.NoError(err)
	assert.Zero(id)

	// 测试用户正在佩戴会员头像挂件
	id, err = GetUserVipAvatarFrameID(3223)
	require.NoError(err)
	assert.NotZero(id)
}
