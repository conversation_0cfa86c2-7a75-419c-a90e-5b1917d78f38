package museravatarframemap

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mavatarframe"
	"github.com/MiaoSiLa/missevan-main/models/person/museravatarframelog"
	"github.com/MiaoSiLa/missevan-main/service"
)

// ExpireTimeForeverEffective 用户头像框 expire_time 永久生效值
const ExpireTimeForeverEffective int64 = 0

// 头像框佩戴过期方式
const (
	ExpireTypeDay  = iota // 按自然日过期（按过期当日 23:59:59 过期）
	ExpireTypeTime        // 按过期时间点过期
)

// status 定义
const (
	// StatusTakeoff 未佩戴
	StatusTakeoff = iota
	// StatusWearing 佩戴中
	StatusWearing
	// StatusExpired 已过期
	StatusExpired
)

// MUserAvatarFrameMap model
type MUserAvatarFrameMap struct {
	ID            int64 `gorm:"column:id;primary_key"`  // 主键
	CreateTime    int64 `gorm:"column:create_time"`     // 创建时间。单位：秒
	ModifiedTime  int64 `gorm:"column:modified_time"`   // 修改时间。单位：秒
	UserID        int64 `gorm:"column:user_id"`         // 用户 ID
	AvatarFrameID int64 `gorm:"column:avatar_frame_id"` // 头像框 ID，m_avatar_frame 表主键
	ExpireTime    int64 `gorm:"column:expire_time"`     // 头像框过期时间，为 0 时表示永久有效。单位：秒
	Status        int   `gorm:"column:status"`          // 使用状态。0: 未佩戴；1: 佩戴中
}

// DB the db instance of MUserAvatarFrameMap model
func (m MUserAvatarFrameMap) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MUserAvatarFrameMap) TableName() string {
	return "m_user_avatar_frame_map"
}

// BeforeCreate hook
func (m *MUserAvatarFrameMap) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *MUserAvatarFrameMap) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// FindUserAvatarFrame 根据头像框 ID 查询用户头像框
func FindUserAvatarFrame(userID, avatarFrameID int64) (*MUserAvatarFrameMap, error) {
	return findAvatarFrame(nil, userID, avatarFrameID)
}

// findAvatarFrame 查询用户头像框
func findAvatarFrame(db *gorm.DB, userID, avatarFrameID int64) (*MUserAvatarFrameMap, error) {
	userAvatarFrame := new(MUserAvatarFrameMap)
	if db == nil {
		db = userAvatarFrame.DB()
	}
	err := db.Where("user_id = ? AND avatar_frame_id = ?", userID, avatarFrameID).
		Take(userAvatarFrame).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return userAvatarFrame, nil
}

// SaveUserAvatarFrameStatus 更新用户头像框佩戴状态
func (m *MUserAvatarFrameMap) SaveUserAvatarFrameStatus(status int) error {
	if m.ID > 0 && m.Status == status {
		return nil
	}

	return servicedb.Tx(service.MainDB, func(tx *gorm.DB) error {
		now := util.TimeNow().Unix()
		if status == StatusWearing {
			// 将用户佩戴中的头像框卸下
			err := takeoffOtherAvatarFrame(tx, m.UserID)
			if err != nil {
				return err
			}
		}

		if m.ID == 0 {
			// 若用户首次使用 vip 头像框，新建记录
			err := tx.Table(MUserAvatarFrameMap{}.TableName()).Create(m).Error
			if err != nil && !servicedb.IsUniqueError(err) {
				return err
			}
			// TODO: 将头像框相关表与佩戴头像框记录表放到同一个库中，使用事务新增佩戴记录
			return museravatarframelog.AddLog(m.UserID, m.AvatarFrameID, status)
		}
		// 更新用户头像框佩戴状态
		updates := map[string]interface{}{
			"modified_time": now,
			"status":        status,
		}
		if err := tx.Table(MUserAvatarFrameMap{}.TableName()).Where("user_id = ? AND avatar_frame_id = ? AND status = ?",
			m.UserID, m.AvatarFrameID, m.Status).Updates(updates).Error; err != nil {
			return err
		}
		return museravatarframelog.AddLog(m.UserID, m.AvatarFrameID, status)
	})
}

// takeoffOtherAvatarFrame 在佩戴头像框时，卸下其他正在佩戴的头像框
func takeoffOtherAvatarFrame(db *gorm.DB, userID int64) error {
	var m MUserAvatarFrameMap
	err := m.DB().Where("user_id = ? AND status = ?", userID, StatusWearing).Take(&m).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil
		}
		return err
	}

	dbRes := db.Table(MUserAvatarFrameMap{}.TableName()).
		Where("user_id = ? AND avatar_frame_id = ?", m.UserID, m.AvatarFrameID).
		Updates(map[string]interface{}{
			"modified_time": util.TimeNow().Unix(),
			"status":        StatusTakeoff,
		})
	if dbRes.Error != nil {
		return dbRes.Error
	}
	if dbRes.RowsAffected > 0 {
		return museravatarframelog.AddLog(m.UserID, m.AvatarFrameID, StatusTakeoff)
	}
	return nil
}

// isWearingAvatarFrame 当前是否存在佩戴中的头像框
func isWearingAvatarFrame(db *gorm.DB, userID int64) (bool, error) {
	return servicedb.Exists(db.Table(MUserAvatarFrameMap{}.TableName()).
		Where("user_id = ? AND status = ?", userID, StatusWearing).
		Where("expire_time > ? OR expire_time = ?", util.TimeNow().Unix(), ExpireTimeForeverEffective))
}

// SendAvatarFrame 给用户发放头像框，第一个返回值为是否发放成功（重复发放不视作成功）
func SendAvatarFrame(avatarFrame *mavatarframe.MAvatarFrame, userID int64, status, autoWear *int, expireType int, expireDuration int64) (bool, *MUserAvatarFrameMap, error) {
	// 查询是否已获得过头像框
	var m MUserAvatarFrameMap
	err := m.DB().
		Where("user_id = ? AND avatar_frame_id = ?", userID, avatarFrame.ID).
		Take(&m).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return false, nil, err
	}
	// 获取到过期时间点
	expireTime := m.newExpireTime(avatarFrame, expireType, expireDuration)
	now := util.TimeNow()
	if m.ID != 0 {
		// 过期（已下线）头像框不可重复获取
		if avatarFrame.EndTime != ExpireTimeForeverEffective && avatarFrame.EndTime < util.TimeNow().Unix() {
			return false, &m, nil
		}
		// 需要更新的字段
		updates := map[string]interface{}{
			"expire_time":   expireTime,
			"modified_time": now.Unix(),
		}
		err = servicedb.Tx(m.DB(), func(tx *gorm.DB) error {
			if autoWear != nil && util.IntToBool(*autoWear) && m.Status != StatusWearing {
				// 如果需要自动佩戴，并且当前没有佩戴头像框，则将该未过期的头像框设置为佩戴状态
				isWearing, err := isWearingAvatarFrame(tx, userID)
				if err != nil {
					return err
				}
				// 无佩戴中的头像框时，佩戴该头像框
				if !isWearing {
					updates["status"] = StatusWearing
				}
			}
			db := tx.Table(m.TableName()).
				Where("id = ? AND modified_time = ?", m.ID, m.ModifiedTime).
				Updates(updates)
			if err = db.Error; err != nil {
				logger.Error(err)
				// PASS
			} else if db.RowsAffected > 0 {
				m.Status = StatusWearing
				m.ExpireTime = expireTime
				m.ModifiedTime = now.Unix()
			}
			return nil
		})
		if err != nil {
			return false, nil, err
		}
		return false, &m, nil
	}
	m = MUserAvatarFrameMap{
		UserID:        userID,
		AvatarFrameID: avatarFrame.ID,
		ExpireTime:    expireTime,
	}
	db := m.DB()
	err = servicedb.Tx(db, func(tx *gorm.DB) error {
		switch {
		case autoWear != nil && util.IntToBool(*autoWear):
			// 若当前没有佩戴头像框则自动佩戴，若当前佩戴了头像框则不佩戴
			isWearing, err := isWearingAvatarFrame(tx, userID)
			if err != nil || isWearing {
				if err != nil {
					logger.WithFields(logger.Fields{
						"user_id":         userID,
						"avatar_frame_id": avatarFrame.ID,
					}).Error(err)
					// PASS: 查询出错时记录日志，当作不自动佩戴处理
				}
				m.Status = StatusTakeoff
			} else {
				m.Status = StatusWearing
			}
		case status != nil && *status == StatusWearing:
			// 若发放状态为立即佩戴，需要将正在佩戴中的头像框调整为未佩戴
			err = takeoffOtherAvatarFrame(tx, userID)
			if err != nil {
				return err
			}
			m.Status = StatusWearing
		default:
			m.Status = StatusTakeoff
		}
		// 创建发放记录
		return tx.Create(&m).Error
	})
	if err != nil {
		if servicedb.IsUniqueError(err) {
			// 若为唯一索引错误，直接返回该头像框数据（保证接口幂等）
			var userAvatarFrame *MUserAvatarFrameMap
			// 使用 tx 保证从主库进行查询
			err = servicedb.Tx(db, func(tx *gorm.DB) error {
				userAvatarFrame, err = findAvatarFrame(tx, userID, avatarFrame.ID)
				return err
			})
			if err != nil {
				return false, nil, err
			}
			return false, userAvatarFrame, nil
		}
		return false, nil, err
	}
	return true, &m, nil
}

// newExpireTime 获取头像框新的佩戴过期时间点
func (m MUserAvatarFrameMap) newExpireTime(avatarFrame *mavatarframe.MAvatarFrame, expireType int, expireDuration int64) int64 {
	// 计算过期时间的开始时间点
	startTime := util.TimeNow()
	if m.ID != 0 {
		if m.ExpireTime == ExpireTimeForeverEffective {
			return ExpireTimeForeverEffective
		}
		if m.ExpireTime > startTime.Unix() {
			// 若当前获得的头像框未过期，则使用当前头像框的过期时间作为开始时间点
			startTime = time.Unix(m.ExpireTime, 0)
		}
	}
	expireTime := avatarFrame.EndTime
	if expireDuration == 0 {
		// 未指定过期时长时，使用头像框默认过期时间
		expireDuration = avatarFrame.ExpireDuration
	}
	if expireDuration != 0 {
		if expireType == ExpireTypeDay {
			expireTime = util.BeginningOfDay(startTime.Add(time.Duration(expireDuration) * time.Second)).Unix()
		} else {
			expireTime = startTime.Add(time.Duration(expireDuration) * time.Second).Unix()
		}
	}
	return expireTime
}

// UserAvatarFrame 用户头像框信息
type UserAvatarFrame struct {
	UserID        int64  `gorm:"column:user_id"`
	AvatarFrameID int64  `gorm:"column:avatar_frame_id"`
	Name          string `gorm:"column:name"`
	Intro         string `gorm:"column:intro"`
	Frame         string `gorm:"column:frame"`
	Icon          string `gorm:"column:icon"`
	ExpireTime    int64  `gorm:"column:expire_time"`
	Status        int    `gorm:"column:status"`
	More          []byte `gorm:"column:more"`
	Type          int    `gorm:"column:type"`

	AvatarFrameURL string                `gorm:"-"`
	IconURL        string                `gorm:"-"`
	MoreInfo       mavatarframe.MoreInfo `gorm:"-"`
}

// AfterFind is a GORM hook for query
func (u *UserAvatarFrame) AfterFind() error {
	if u.Frame != "" {
		u.AvatarFrameURL = service.Storage.Parse(u.Frame)
	}
	if u.Icon != "" {
		u.IconURL = service.Storage.Parse(u.Icon)
	}
	if u.Icon != "" {
		u.IconURL = service.Storage.Parse(u.Icon)
	}
	var err error
	u.MoreInfo, err = mavatarframe.UnmarshalMore(u.More)
	if err != nil {
		return err
	}
	return nil
}

var selectFields = "a.avatar_frame_id, a.expire_time, a.status, b.name, b.intro, b.frame, b.icon"

// ListWearingByUserIDs 根据用户 IDs 获取用户佩戴中的头像框
func ListWearingByUserIDs(userIDs []int64, onlyFindNormalAvatarFrame bool) ([]*UserAvatarFrame, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	now := util.TimeNow().Unix()
	var mUserAvatarFrames []*UserAvatarFrame
	db := service.MainDB.Table(MUserAvatarFrameMap{}.TableName()+" AS a").
		Select("a.user_id, b.more, b.type, "+selectFields).
		Joins(fmt.Sprintf("INNER JOIN %s AS b ON a.avatar_frame_id = b.id", mavatarframe.MAvatarFrame{}.TableName())).
		Where("a.user_id IN (?) AND a.status = ? AND (a.expire_time > ? OR a.expire_time = ?)",
			util.Uniq(userIDs), StatusWearing, now, ExpireTimeForeverEffective)
	if onlyFindNormalAvatarFrame {
		db = db.Where("b.type = ?", mavatarframe.TypeNormal)
	}
	err := db.Find(&mUserAvatarFrames).Error
	if err != nil {
		return nil, err
	}

	return mUserAvatarFrames, nil
}

// UserAvatarFrameLister 获取用户头像框列表的参数
type UserAvatarFrameLister struct {
	Page     int64
	PageSize int64
	UserID   int64
}

// ListUserNormalAvatarFrame 获取用户普通头像框列表
func (ul *UserAvatarFrameLister) ListUserNormalAvatarFrame() ([]UserAvatarFrame, bool, error) {
	db := service.MainDB.Table(MUserAvatarFrameMap{}.TableName()+" AS a").
		Joins(fmt.Sprintf("INNER JOIN %s AS b ON a.avatar_frame_id = b.id", mavatarframe.MAvatarFrame{}.TableName())).
		Where("a.user_id = ? AND b.type = ?", ul.UserID, mavatarframe.TypeNormal)

	var avatarFrames []UserAvatarFrame
	// 排序规则为首先展示用户生效中的头像框，其次展示未生效的头像框，最后按照用户获得头像框的时间倒序排列
	err := db.Select(selectFields).
		Order(fmt.Sprintf("CASE WHEN a.expire_time = %d OR a.expire_time > %d THEN 1 ELSE 2 END ASC, a.create_time DESC",
			ExpireTimeForeverEffective, util.TimeNow().Unix())).Offset(ul.PageSize * (ul.Page - 1)).Limit(ul.PageSize + 1).
		Find(&avatarFrames).Error
	if err != nil {
		return nil, false, err
	}
	var hasMore bool
	if len(avatarFrames) > int(ul.PageSize) {
		hasMore = true
		avatarFrames = avatarFrames[:ul.PageSize]
	}
	return avatarFrames, hasMore, nil
}

// GetUserWearStatus 获取用户头像框佩戴状态。未获得头像框时返回 nil
func GetUserWearStatus(userID, avatarFrameID int64) (*int, error) {
	var userAvatarFrame MUserAvatarFrameMap
	err := MUserAvatarFrameMap{}.DB().
		Select("expire_time, status").
		Where("user_id = ? AND avatar_frame_id = ?", userID, avatarFrameID).
		Take(&userAvatarFrame).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	if userAvatarFrame.ExpireTime != ExpireTimeForeverEffective && userAvatarFrame.ExpireTime < util.TimeNow().Unix() {
		return util.NewInt(StatusExpired), nil
	}
	return &userAvatarFrame.Status, nil
}

// BatchTakeoffByIDs 根据 ID 批量更新会员头像框的佩戴状态
func BatchTakeoffByIDs(ids []int64) error {
	now := util.TimeNow().Unix()
	return MUserAvatarFrameMap{}.DB().
		Where("id IN (?)", ids).
		Updates(map[string]interface{}{"modified_time": now, "status": StatusTakeoff}).Error
}

// GetOnWearVipAvatarFrames 获取正在佩戴会员头像框的用户佩戴信息
func GetOnWearVipAvatarFrames(userIDs []int64) ([]*MUserAvatarFrameMap, error) {
	var mUserAvatarFrameMaps []*MUserAvatarFrameMap
	err := service.MainDB.Table(MUserAvatarFrameMap{}.TableName()+" AS t1").
		Select("t1.*").
		Joins(fmt.Sprintf("INNER JOIN %s AS t2 ON t1.avatar_frame_id = t2.id", mavatarframe.MAvatarFrame{}.TableName())).
		Where("t1.user_id IN (?) AND t1.status = ? AND t2.type = ?", userIDs, StatusWearing, mavatarframe.TypeVip).
		Find(&mUserAvatarFrameMaps).Error
	if err != nil {
		return nil, err
	}
	return mUserAvatarFrameMaps, nil
}

// GetUserVipAvatarFrameID 查找用户佩戴中的会员头像挂件 ID
func GetUserVipAvatarFrameID(userID int64) (int64, error) {
	var id int64
	err := service.MainDB.Table(mavatarframe.MAvatarFrame{}.TableName()+" AS t1").
		Select("t1.id").
		Joins(fmt.Sprintf("INNER JOIN %s AS t2 ON t1.id = t2.avatar_frame_id", MUserAvatarFrameMap{}.TableName())).
		Where("t1.type = ? AND t2.user_id = ? AND t2.status = ? AND(t2.expire_time = ? OR t2.expire_time >= ?)",
			mavatarframe.TypeVip, userID, StatusWearing, ExpireTimeForeverEffective, util.TimeNow().Unix()).
		Row().Scan(&id)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, nil
		}
		return 0, err
	}
	return id, nil
}
