package muserthemeskin

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskinlog"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserThemeSkin{}, "id", "create_time", "modified_time", "user_id",
		"theme_skin_id", "expire_time", "status")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(-1, StatusNotOwned)
	assert.EqualValues(0, StatusTakeoff)
	assert.EqualValues(1, StatusWearing)
}

func TestIsWear(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 正在装扮
	userID, themeSkinID := int64(11), int64(1)
	wear, err := IsWear(userID, themeSkinID)
	require.NoError(err)
	assert.True(wear)

	// 未装扮
	themeSkinID = 2
	wear, err = IsWear(userID, themeSkinID)
	require.NoError(err)
	assert.False(wear)
}

func clearLog(t *testing.T, userID int64) {
	require := require.New(t)

	err := muserthemeskinlog.MUserThemeSkinLog{}.DB().Delete("", "user_id = ?", userID).Error
	require.NoError(err)
}

func assertExistsLog(t *testing.T, userID, themeSkinID, now int64, isWear bool) {
	assert := assert.New(t)
	require := require.New(t)
	var log muserthemeskinlog.MUserThemeSkinLog
	query := log.DB().Where("user_id = ? AND theme_skin_id = ?", userID, themeSkinID)
	if isWear {
		query.Where("start_time = ? AND end_time = ?", now, 0)
	} else {
		query.Where("start_time = ? AND end_time = ?", 0, now)
	}
	exists, err := servicedb.Exists(query)
	require.NoError(err)
	assert.True(exists)
}

func TestMUserThemeSkin_saveStatusAndAddLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试卸下未装扮的主题
	userID, themeSkinID := int64(199), int64(1000)
	err := MUserThemeSkin{}.DB().Delete("", "user_id = ? AND theme_skin_id = ?", userID, themeSkinID).Error
	require.NoError(err)
	now := util.TimeNow().Unix()
	m := MUserThemeSkin{
		CreateTime:   now,
		ModifiedTime: now,
		UserID:       userID,
		ThemeSkinID:  themeSkinID,
		ExpireTime:   ExpireTimeForeverEffective,
		Status:       StatusWearing,
	}
	err = m.saveStatusAndAddLog(m.DB(), StatusTakeoff, false)
	require.NoError(err)
	// 断言未写入记录
	exists, err := servicedb.Exists(m.DB().Where("user_id = ? AND theme_skin_id = ? AND status = ? AND expire_time = ?", userID, themeSkinID, StatusTakeoff, ExpireTimeForeverEffective))
	require.NoError(err)
	assert.False(exists)

	// 测试装扮主题
	err = m.saveStatusAndAddLog(m.DB(), StatusWearing, true)
	require.NoError(err)
	assertExists(t, userID, themeSkinID, StatusWearing)

	// 测试停用主题
	err = m.saveStatusAndAddLog(m.DB(), StatusTakeoff, false)
	require.NoError(err)
	// 断言旧装扮的状态被更改
	assertExists(t, userID, themeSkinID, StatusTakeoff)
}

func assertExists(t *testing.T, userID, themeSkinID int64, status int) {
	assert := assert.New(t)
	require := require.New(t)

	var m MUserThemeSkin
	exists, err := servicedb.Exists(m.DB().Where("user_id = ? AND theme_skin_id = ? AND status = ? AND expire_time = ?", userID, themeSkinID, status, ExpireTimeForeverEffective))
	require.NoError(err)
	assert.True(exists)
}

func TestSetThemeSkin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	nowTime := util.TimeNow()
	util.SetTimeNow(func() time.Time {
		return nowTime
	})
	defer util.SetTimeNow(nil)

	// 测试卸下未装扮的主题不会报错
	userID, themeSkinID := int64(199), int64(1000)
	err := MUserThemeSkin{}.DB().Delete("", "user_id = ? AND theme_skin_id = ?", userID, themeSkinID).Error
	require.NoError(err)
	clearLog(t, userID)

	now := nowTime.Unix()
	m := MUserThemeSkin{
		CreateTime:   now,
		ModifiedTime: now,
		UserID:       userID,
		ThemeSkinID:  themeSkinID,
		ExpireTime:   ExpireTimeForeverEffective,
		Status:       StatusWearing,
	}
	err = m.SetThemeSkin(StatusTakeoff)
	require.NoError(err)
	exists, err := servicedb.Exists(m.DB().Where("user_id = ? AND theme_skin_id = ? AND status = ?", userID, themeSkinID, StatusWearing))
	require.NoError(err)
	assert.False(exists)

	// 测试装扮主题
	clearLog(t, userID)
	err = m.SetThemeSkin(StatusWearing)
	require.NoError(err)
	assertExists(t, userID, themeSkinID, StatusWearing)
	// 断言写入装扮记录表
	assertExistsLog(t, userID, themeSkinID, now, true)

	// 测试装扮新的主题（自动卸下旧的主题）
	newThemeSkinID := int64(888)
	m.ID = 0
	m.ThemeSkinID = newThemeSkinID
	err = m.SetThemeSkin(StatusWearing)
	require.NoError(err)
	// 断言旧的装扮记录被更改
	assertExists(t, userID, themeSkinID, StatusTakeoff)
	// 断言写入新的装扮数据
	assertExists(t, userID, newThemeSkinID, StatusWearing)
	// 断言写入旧主题的停用记录
	assertExistsLog(t, userID, themeSkinID, now, false)
	// 断言写入新的装扮记录
	assertExistsLog(t, userID, newThemeSkinID, now, true)

	// 测试成功卸下新的主题
	err = m.SetThemeSkin(StatusTakeoff)
	require.NoError(err)
	// 断言新主题的状态被更改
	assertExists(t, userID, newThemeSkinID, StatusTakeoff)
	// 断言写入新主题的停用记录
	assertExistsLog(t, userID, newThemeSkinID, now, false)
}
