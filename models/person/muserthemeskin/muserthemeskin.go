package muserthemeskin

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/muserthemeskinlog"
	"github.com/MiaoSiLa/missevan-main/service"
)

// ExpireTimeForeverEffective 用户主题皮肤 expire_time 永久生效值
const ExpireTimeForeverEffective int64 = 0

// 主题皮肤装扮状态
// TODO：目前未区分从未获取过装扮和装扮已过期的情况
const (
	StatusNotOwned = iota - 1 // 无权限。数据库中不会直接存放该值，仅用于用户当前未拥有时返回

	StatusTakeoff // 未装扮
	StatusWearing // 装扮中
)

// MUserThemeSkin model
type MUserThemeSkin struct {
	ID           int64 `gorm:"column:id"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	UserID       int64 `gorm:"column:user_id"`
	ThemeSkinID  int64 `gorm:"column:theme_skin_id"`
	ExpireTime   int64 `gorm:"column:expire_time"` // 失效时间，包含此时刻
	Status       int   `gorm:"column:status"`
}

// DB the db instance of current model
func (m MUserThemeSkin) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MUserThemeSkin) TableName() string {
	return "m_user_theme_skin"
}

// BeforeCreate hook
func (m *MUserThemeSkin) BeforeCreate() (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *MUserThemeSkin) BeforeSave() (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// IsWear 用户是否正在使用指定主题皮肤
func IsWear(userID, themeSkinID int64) (bool, error) {
	return servicedb.Exists(MUserThemeSkin{}.DB().
		Where("user_id = ? AND theme_skin_id = ? AND status = ? AND(expire_time = ? OR expire_time >= ?)",
			userID, themeSkinID, StatusWearing, ExpireTimeForeverEffective, util.TimeNow().Unix()))
}

// FindUserThemeSkin 查询用户装扮指定主题的记录
func FindUserThemeSkin(userID, themeSkinID int64) (*MUserThemeSkin, error) {
	userThemeSkin := new(MUserThemeSkin)
	err := userThemeSkin.DB().Where("user_id = ? AND theme_skin_id = ?", userID, themeSkinID).
		Take(userThemeSkin).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return userThemeSkin, nil
}

// findUserWearThemeSkin 查询用户装扮中的主题信息
func findUserWearThemeSkin(db *gorm.DB, userID int64) (*MUserThemeSkin, error) {
	themeSkin := new(MUserThemeSkin)
	err := db.Table(MUserThemeSkin{}.TableName()).
		Where("user_id = ? AND status = ? AND (expire_time = ? OR expire_time >= ?)",
			userID, StatusWearing, ExpireTimeForeverEffective, util.TimeNow().Unix()).Take(&themeSkin).Error
	if err != nil && servicedb.IsErrNoRows(err) {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return themeSkin, nil
}

// saveStatusAndAddLog 保存佩戴状态并增加佩戴记录
func (m *MUserThemeSkin) saveStatusAndAddLog(db *gorm.DB, statusNew int, isWear bool) error {
	if m.ID == 0 && isWear {
		m.Status = statusNew
		err := db.Create(&m).Error
		if err != nil {
			return err
		}
	} else {
		err := db.Table(MUserThemeSkin{}.TableName()).Where("id = ? AND status <> ?", m.ID, statusNew).
			Update("status", statusNew).Error
		if err != nil {
			return err
		}
	}
	err := muserthemeskinlog.AddLog(db, m.UserID, m.ThemeSkinID, isWear)
	if err != nil {
		return err
	}
	return nil
}

// SetThemeSkin 装扮/卸下主题皮肤
func (m *MUserThemeSkin) SetThemeSkin(statusNew int) error {
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		isWear := statusNew == StatusWearing
		if isWear {
			oldThemeSkin, err := findUserWearThemeSkin(tx, m.UserID)
			if err != nil {
				return err
			}
			if oldThemeSkin != nil && oldThemeSkin.ID != m.ID {
				err := oldThemeSkin.saveStatusAndAddLog(tx, StatusTakeoff, false)
				if err != nil {
					return err
				}
			}
		}
		err := m.saveStatusAndAddLog(tx, statusNew, isWear)
		if err != nil {
			return err
		}
		return nil
	})
}
