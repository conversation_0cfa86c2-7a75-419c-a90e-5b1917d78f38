package usercertification

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

// UserCertification 用户认证信息
type UserCertification struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 最后修改时间，单位：秒
	UserID       int64  `gorm:"column:user_id"`        // 用户 ID
	Subtitle     string `gorm:"column:subtitle"`       // 用户认证头衔
}

// TableName for current model
func (UserCertification) TableName() string {
	return "user_certification"
}

// DB the db instance of UserCertification model
func (m UserCertification) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// FindByUserID 根据用户 ID 查找用户认证信息
func FindByUserID(userID int64) (*UserCertification, error) {
	var uc UserCertification
	err := uc.DB().Where("user_id = ?", userID).Take(&uc).Error
	if servicedb.IsErrNoRows(err) {
		return nil, nil
	}
	return &uc, err
}
