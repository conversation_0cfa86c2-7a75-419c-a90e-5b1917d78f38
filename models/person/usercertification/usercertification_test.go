package usercertification

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestFindByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uc, err := FindByUserID(-999)
	require.NoError(err)
	assert.Nil(uc)

	uc, err = FindByUserID(12)
	require.NoError(err)
	require.NotNil(uc)
	assert.NotEmpty(uc.Subtitle)
}
