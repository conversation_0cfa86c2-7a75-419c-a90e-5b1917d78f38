package mappearance

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

// 外观套装是否归档
const (
	ArchiveOnline  = iota // 未归档
	ArchiveHistory        // 已归档
)

// 是否是会员专享外观套装
const (
	VipNot   = iota // 否
	VipLimit        // 是
)

// MAppearance model
type MAppearance struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间戳，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 更新时间戳，单位：秒
	Name         string `gorm:"column:name"`           // 名称
	Intro        string `gorm:"column:intro"`          // 简介
	Vip          int8   `gorm:"column:vip"`            // 是否会员免费 0：否；1：是
	PayType      int8   `gorm:"column:pay_type"`       // 付费类型 0：非付费使用；1：付费使用
	Price        int64  `gorm:"column:price"`          // 价格，单位：分
	Archive      int8   `gorm:"column:archive"`        // 归档状态 0：未归档；1：已归档
	Appearance   []byte `gorm:"column:appearance"`     // 套装信息，JSON 格式
	More         []byte `gorm:"column:more"`           // 更多信息，JSON 格式

	AppearanceInfo AppearanceInfo `gorm:"-"`
	MoreInfo       MoreInfo       `gorm:"-"`
}

// AppearanceInfo appearance 套装信息（主题皮肤，头像挂件等）
type AppearanceInfo struct {
	SkinIDs        []int64 `json:"skin_ids"`         // 主题皮肤 ID 列表
	AvatarFrameIDs []int64 `json:"avatar_frame_ids"` // 头像挂件 ID 列表
}

// MoreInfo more 中扩展信息
type MoreInfo struct {
	ListCoverURL string    `json:"list_cover_url"` // 列表页封面图
	BgCoverURL   string    `json:"bg_cover_url"`   // 套装详情页背景图
	BgStartColor string    `json:"bg_start_color"` // 遮罩渐变起始颜色
	BgEndColor   string    `json:"bg_end_color"`   // 遮罩渐变结束颜色
	Previews     []Preview `json:"previews"`       // 套装详情页预览图列表
}

// Preview 预览列表信息
type Preview struct {
	ID       int64  `json:"id"`        // 单品 ID（例：主题皮肤 ID、头像挂件 ID）
	Type     int8   `json:"type"`      // 单品类型 1：主题皮肤；2：头像挂件
	Title    string `json:"title"`     // 预览标题（例：首页、我的页）
	CoverURL string `json:"cover_url"` // 预览图
	URL      string `json:"url"`       // 跳转链接
}

// DB the db instance of current model
func (m MAppearance) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MAppearance) TableName() string {
	return "m_appearance"
}

// AfterFind is a GORM hook for query
func (m *MAppearance) AfterFind() error {
	if len(m.More) != 0 {
		err := json.Unmarshal(m.More, &m.MoreInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// AppearanceElement 套装元素
type AppearanceElement struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`           // 套装名称
	Intro        string `json:"intro"`          // 套装简介
	ImageURL     string `json:"image_url"`      // 套装封面图
	BgStartColor string `json:"bg_start_color"` // 遮罩渐变起始颜色
	BgEndColor   string `json:"bg_end_color"`   // 遮罩渐变结束颜色
	IsVip        int    `json:"is_vip"`         // 是否会员免费
}

// ListVipAppearanceElements 获取会员外观套装列表
func ListVipAppearanceElements() ([]AppearanceElement, error) {
	var appearances []*MAppearance
	err := MAppearance{}.DB().
		Select("id, name, intro, vip, more").
		Where("archive = ? AND vip = ?", ArchiveOnline, VipLimit).
		Find(&appearances).Error
	if err != nil {
		return nil, err
	}
	appearancesLen := len(appearances)
	if appearancesLen == 0 {
		return []AppearanceElement{}, nil
	}
	elements := make([]AppearanceElement, 0, appearancesLen)
	for _, appearance := range appearances {
		var listCoverURL string
		if appearance.MoreInfo.ListCoverURL != "" {
			listCoverURL = service.Storage.Parse(appearance.MoreInfo.ListCoverURL)
		}
		skinElement := AppearanceElement{
			ID:           appearance.ID,
			Name:         appearance.Name,
			Intro:        appearance.Intro,
			ImageURL:     listCoverURL,
			BgStartColor: appearance.MoreInfo.BgStartColor,
			BgEndColor:   appearance.MoreInfo.BgEndColor,
			IsVip:        int(appearance.Vip),
		}
		elements = append(elements, skinElement)
	}
	return elements, nil
}
