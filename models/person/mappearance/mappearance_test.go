package mappearance

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MAppearance{}, "id", "create_time", "modified_time", "name",
		"intro", "vip", "pay_type", "price", "archive", "appearance", "more")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, VipNot)
	assert.EqualValues(1, VipLimit)

	assert.EqualValues(0, ArchiveOnline)
	assert.EqualValues(1, ArchiveHistory)
}

func TestMAppearance_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	more, err := json.Marshal(MoreInfo{
		ListCoverURL: "test://image/test.png",
		BgStartColor: "#F8A623",
		BgEndColor:   "#F8A626",
	})
	require.NoError(err)
	a := MAppearance{
		More: more,
	}
	err = a.AfterFind()
	require.NoError(err)
	assert.Equal("test://image/test.png", a.MoreInfo.ListCoverURL)
	assert.Equal("#F8A623", a.MoreInfo.BgStartColor)
	assert.Equal("#F8A626", a.MoreInfo.BgEndColor)
}

func TestListVipAppearanceElements(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	elements, err := ListVipAppearanceElements()
	require.NoError(err)
	assert.NotEmpty(elements)
	assert.Len(elements, 1)
	assert.EqualValues(1, elements[0].ID)
	assert.EqualValues("测试外观套装", elements[0].Name)
	assert.EqualValues("测试", elements[0].Intro)
	assert.EqualValues("https://static-test.maoercdn.com/image/test.png", elements[0].ImageURL)
	assert.EqualValues("#F8A623", elements[0].BgStartColor)
	assert.EqualValues("#F8A626", elements[0].BgEndColor)
	assert.EqualValues(1, elements[0].IsVip)
}
