package mavatarframe

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

const testAvatarFrameID = 233

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MAvatarFrame{}, "id", "create_time", "modified_time", "name",
		"intro", "frame", "start_time", "end_time", "expire_duration", "icon", "type", "more", "sort")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, TypeNormal)
	assert.EqualValues(1, TypeVip)
}

func TestMAvatarFrame_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_avatar_frame", MAvatarFrame{}.TableName())
}

func TestMAvatarFrame_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无头像框和 icon 地址的情况
	m := MAvatarFrame{}
	require.NoError(m.AfterFind())
	assert.Equal("", m.AvatarFrameURL)
	assert.Equal("", m.IconURL)

	// 测试有头像框地址的情况
	m.Frame = "test://test.webp"
	m.Icon = "test://icon/test.webp"
	require.NoError(m.AfterFind())
	assert.Contains(m.AvatarFrameURL, "https://")
	assert.Contains(m.IconURL, "https://")
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试查找不存在的头像框
	m, err := FindOne(99999999)
	require.NoError(err)
	assert.Nil(m)

	// 测试查找存在的头像框
	avatarFrame := &MAvatarFrame{
		ID: testAvatarFrameID,
	}
	require.NoError(avatarFrame.DB().Delete("", "id = ?", testAvatarFrameID).Error)
	require.NoError(avatarFrame.DB().Create(avatarFrame).Error)
	m, err = FindOne(testAvatarFrameID)
	require.NoError(err)
	require.NotNil(m)
	assert.EqualValues(testAvatarFrameID, m.ID)
}

func TestFindValidOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试查找不存在的头像框
	m, err := FindValidOne(99999999)
	require.NoError(err)
	assert.Nil(m)

	// 测试查找未失效的头像框（end_time 为 100）
	util.SetTimeNow(func() time.Time {
		return time.Unix(101, 0)
	})
	defer util.SetTimeNow(nil)
	m, err = FindValidOne(2)
	require.NoError(err)
	assert.Nil(m)

	// 测试查找未失效的头像框（end_time 为 100）
	util.SetTimeNow(func() time.Time {
		return time.Unix(99, 0)
	})
	m, err = FindValidOne(2)
	require.NoError(err)
	assert.NotNil(m)
	assert.EqualValues(2, m.ID)

	// 测试查找无过期时间的头像框（end_time 为 0）
	m, err = FindValidOne(1)
	require.NoError(err)
	assert.NotNil(m)
	assert.EqualValues(1, m.ID)
}

func TestListVipAvatarFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ListVipAvatarFrame()
	require.NoError(err)
	// 当前有 3 条会员头像框信息，为了避免后续有新的测试数据影响测试结果，此处不断言全部会员头像框信息
	assert.GreaterOrEqual(len(list), 3)
	expectAvatarFrame := AvatarFrameElement{
		ID:             6,
		Name:           "会员头像框 1",
		Intro:          "简介 1",
		AvatarFrameURL: "https://static-test.maoercdn.com/test_1.webp",
	}
	assert.Equal(expectAvatarFrame, list[0])
}
