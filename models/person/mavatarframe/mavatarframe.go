package mavatarframe

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// EndTimeForeverEffective 头像框 end_time 永久生效值
const EndTimeForeverEffective int64 = 0

// 挂件类型
const (
	TypeNormal = iota // 普通挂件
	TypeVip           // vip 专享
)

// MAvatarFrame model
type MAvatarFrame struct {
	ID             int64  `gorm:"column:id;primary_key"`  // 主键
	CreateTime     int64  `gorm:"column:create_time"`     // 创建时间。单位：秒
	ModifiedTime   int64  `gorm:"column:modified_time"`   // 修改时间。单位：秒
	Name           string `gorm:"column:name"`            // 头像框名称
	Intro          string `gorm:"column:intro"`           // 头像框简介，支持 HTML
	Frame          string `gorm:"column:frame"`           // 头像框地址
	StartTime      int64  `gorm:"column:start_time"`      // 头像框可用开始时间。单位：秒
	EndTime        int64  `gorm:"column:end_time"`        // 头像框可用截止时间，为 0 时表示永久有效。单位：秒
	ExpireDuration int64  `gorm:"column:expire_duration"` // 头像框获得后过期时长，为 0 时表示不会过期。单位：秒
	Icon           string `gorm:"column:icon"`            // 头像框 icon 地址
	Type           int    `gorm:"column:type"`            // 挂件类型。0：普通挂件；1：vip 挂件
	More           []byte `gorm:"column:more"`
	Sort           int64  `gorm:"column:sort"` // 排序，值越小越靠前展示

	AvatarFrameURL string   `gorm:"-"`
	IconURL        string   `gorm:"-"`
	MoreInfo       MoreInfo `gorm:"-"`
}

// MoreInfo 头像框更多信息
// show_drama_ids 字段中保存该头像框可被展示的剧集播放页的剧集 IDs。e.g. {"show_drama_ids": [1, 2]}
// event_id 头像框发放所关联活动 ID
type MoreInfo struct {
	ShowDramaIDs []int64 `json:"show_drama_ids,omitempty"`
	EventID      int64   `json:"event_id,omitempty"`
}

// AvatarFrameElement 头像挂件元素
type AvatarFrameElement struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	Intro          string `json:"intro"`
	AvatarFrameURL string `json:"avatar_frame_url"`
}

// DB the db instance of MAvatarFrame model
func (m MAvatarFrame) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MAvatarFrame) TableName() string {
	return "m_avatar_frame"
}

// AfterFind is a GORM hook for query
func (m *MAvatarFrame) AfterFind() error {
	if m.Frame != "" {
		m.AvatarFrameURL = service.Storage.Parse(m.Frame)
	}
	if m.Icon != "" {
		m.IconURL = service.Storage.Parse(m.Icon)
	}
	var err error
	m.MoreInfo, err = UnmarshalMore(m.More)
	if err != nil {
		return err
	}
	return nil
}

// UnmarshalMore 处理 more 字段中包含的头像框更多信息，返回 More
func UnmarshalMore(moreBytes []byte) (MoreInfo, error) {
	var moreInfo MoreInfo
	if len(moreBytes) != 0 {
		err := json.Unmarshal(moreBytes, &moreInfo)
		if err != nil {
			return moreInfo, err
		}
	}
	return moreInfo, nil
}

// FindOne 通过 ID 查找头像框
func FindOne(id int64) (*MAvatarFrame, error) {
	avatarFrame := new(MAvatarFrame)
	err := avatarFrame.DB().Where("id = ?", id).Take(avatarFrame).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return avatarFrame, nil
}

// FindValidOne 通过 id 查找可用的头像框
func FindValidOne(id int64) (*MAvatarFrame, error) {
	avatarFrame := new(MAvatarFrame)
	now := util.TimeNow().Unix()
	err := avatarFrame.DB().
		Where("id = ? AND (start_time <= ? AND (end_time > ? OR end_time = ?))", id, now, now, EndTimeForeverEffective).
		Take(avatarFrame).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return avatarFrame, nil
}

// ListVipAvatarFrame 获取会员头像挂件列表
func ListVipAvatarFrame() ([]AvatarFrameElement, error) {
	var avatarFrames []*MAvatarFrame
	now := util.TimeNow().Unix()
	err := MAvatarFrame{}.DB().
		Select("id, name, intro, frame").
		Where("type = ? AND start_time <= ? AND (end_time > ? OR end_time = ?)", TypeVip, now, now, EndTimeForeverEffective).
		Order("sort ASC").
		Find(&avatarFrames).Error
	if err != nil {
		return nil, err
	}
	avatarFramaLen := len(avatarFrames)
	if avatarFramaLen == 0 {
		return []AvatarFrameElement{}, nil
	}
	avatarFrameElement := make([]AvatarFrameElement, 0, avatarFramaLen)
	for _, avatarFrame := range avatarFrames {
		element := AvatarFrameElement{
			ID:             avatarFrame.ID,
			Name:           avatarFrame.Name,
			Intro:          avatarFrame.Intro,
			AvatarFrameURL: avatarFrame.AvatarFrameURL,
		}
		avatarFrameElement = append(avatarFrameElement, element)
	}
	return avatarFrameElement, nil
}
