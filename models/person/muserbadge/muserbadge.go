package muserbadge

import (
	"fmt"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mbadge"
	"github.com/MiaoSiLa/missevan-main/service"
)

// ExpireTimeForeverEffective 用户称号 expire_time 永久生效值
const ExpireTimeForeverEffective int64 = 0

// status 定义
const (
	// StatusTakeoff 未佩戴
	StatusTakeoff = iota
	// StatusWearing 佩戴中
	StatusWearing
	// StatusExpired 已过期
	StatusExpired
)

// MUserBadge model
type MUserBadge struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间戳。单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 最后更新时间戳。单位：秒
	UserID       int64 `gorm:"column:user_id"`        // 用户 ID
	BadgeID      int64 `gorm:"column:badge_id"`       // 称号 ID
	ExpireTime   int64 `gorm:"column:expire_time"`    // 称号过期时间，为 0 时表示永久有效。单位：秒
	Status       int   `gorm:"column:status"`         // 使用状态。0: 未佩戴；1: 佩戴中
	NO           int64 `gorm:"column:no"`             // 用户称号编号，不同称号的编号是分开递增计数的

	NOStr string `gorm:"-"`
}

// TableName for current model
func (MUserBadge) TableName() string {
	return "m_user_badge"
}

// DB the db instance of MUserBadge model
func (m MUserBadge) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// BeforeCreate hook
func (m *MUserBadge) BeforeCreate() (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *MUserBadge) BeforeSave() (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// AfterFind is a GORM hook for query
func (m *MUserBadge) AfterFind() error {
	if m.NO > 0 {
		m.NOStr = noToString(m.NO)
	}
	return nil
}

// GetNextNO 获取新的称号编号
func GetNextNO(tx *gorm.DB, badgeID int64, specialNOList []int64) (int64, error) {
	var curNO int64
	var db *gorm.DB
	if tx != nil {
		db = tx
	} else {
		db = MUserBadge{}.DB()
	}
	query := db.Select("IFNULL(MAX(no), 0)").Where("badge_id = ?", badgeID)
	if len(specialNOList) > 0 {
		query = query.Where("no NOT IN (?)", specialNOList)
	}
	err := query.Row().Scan(&curNO)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	nextNO := curNO + 1
	for util.HasElem(specialNOList, nextNO) {
		nextNO++
	}
	return nextNO, nil
}

// CreateUserBadge 新建用户称号
func (m *MUserBadge) CreateUserBadge() error {
	createErr := m.DB().Create(m).Error
	if createErr != nil {
		if servicedb.IsUniqueError(createErr) {
			// 若为唯一索引错误，查询是否用户称号已存在（保证接口幂等）
			var userBadge MUserBadge
			// 使用 tx 保证从主库进行查询
			if err := servicedb.Tx(userBadge.DB(), func(tx *gorm.DB) error {
				return tx.Table(userBadge.TableName()).Select("no").
					Where("user_id = ? AND badge_id = ?", m.UserID, m.BadgeID).Take(&userBadge).Error
			}); err != nil {
				if servicedb.IsErrNoRows(err) {
					return createErr
				}
				return err
			}
			m.NO = userBadge.NO
			m.NOStr = userBadge.NOStr
			return nil
		}
		return createErr
	}
	m.NOStr = noToString(m.NO)
	return nil
}

// noToString 称号编号字符串格式
func noToString(no int64) string {
	return fmt.Sprintf("%06d", no)
}

// UserBadge 用户称号，用于查询用户称号列表详情
type UserBadge struct {
	ID         int64  `gorm:"column:id"`
	Title      string `gorm:"column:title"`
	Intro      string `gorm:"column:intro"`
	NO         int64  `gorm:"column:no"`
	Cover      string `gorm:"column:cover"`
	Icon       string `gorm:"column:icon"`
	CreateTime int64  `gorm:"column:create_time"`

	CoverURL string `gorm:"-"`
	IconURL  string `gorm:"-"`
	NOStr    string `gorm:"-"`
}

// AfterFind is a GORM hook for query
func (u *UserBadge) AfterFind() error {
	if u.Cover != "" {
		u.CoverURL = service.Storage.Parse(u.Cover)
	}
	if u.Icon != "" {
		u.IconURL = service.Storage.Parse(u.Icon)
	}
	if u.NO > 0 {
		u.NOStr = noToString(u.NO)
	}
	return nil
}

// ListUserBadge 获取用户称号
func ListUserBadge(elementType int, elementID, userID int64) ([]UserBadge, error) {
	var userBadges []UserBadge
	err := service.MainDB.Table(mbadge.MBadge{}.TableName()+" AS b").
		Select("b.id, b.title, b.intro, b.cover, b.icon, u.no, u.create_time").
		Joins(fmt.Sprintf("INNER JOIN %s AS u ON b.id = u.badge_id", MUserBadge{}.TableName())).
		Where("b.element_type = ? AND b.element_id = ? AND u.user_id = ?", elementType, elementID, userID).
		Order("u.create_time DESC").
		Find(&userBadges).Error
	if err != nil {
		return nil, err
	}
	return userBadges, nil
}
