package muserbadge

import (
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/person/mbadge"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	testUserID1   = 12
	testUserID2   = 13
	testBadgeID   = 233
	testElementID = 233
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, StatusTakeoff)
	assert.EqualValues(1, StatusWearing)
	assert.EqualValues(2, StatusExpired)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserBadge{}, "id", "create_time", "modified_time", "user_id", "badge_id", "expire_time", "status", "no")
	kc.Check(UserBadge{}, "id", "title", "intro", "no", "cover", "icon", "create_time")
}

func TestMUserBadge_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_user_badge", MUserBadge{}.TableName())
}

func TestMUserBadge_BeforeCreate(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	m := new(MUserBadge)
	require.NoError(m.BeforeCreate())
	assert.NotEqual(0, m.CreateTime)
	assert.NotEqual(0, m.ModifiedTime)
}

func TestMUserBadge_BeforeSave(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	u := new(MUserBadge)
	require.NoError(u.BeforeSave())
	assert.NotEqual(0, u.CreateTime)
	assert.NotEqual(0, u.ModifiedTime)
}

func TestMUserBadge_AfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	m := MUserBadge{}
	require.NoError(m.AfterFind())
	assert.Empty(m.NOStr)

	m = MUserBadge{
		NO: 12345,
	}
	require.NoError(m.AfterFind())
	assert.Equal("012345", m.NOStr)

	m.NO = 123456
	require.NoError(m.AfterFind())
	assert.Equal("123456", m.NOStr)

	m.NO = 1234567
	require.NoError(m.AfterFind())
	assert.Equal("1234567", m.NOStr)
}

func TestMUserBadge_CreateUserBadge(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试新建用户称号
	require.NoError(MUserBadge{}.DB().Delete("", "badge_id = ?", testBadgeID).Error)
	userBadge := MUserBadge{
		UserID:  testUserID1,
		BadgeID: testBadgeID,
		NO:      1,
	}
	require.NoError(userBadge.CreateUserBadge())
	assert.NotZero(userBadge.ID)
	assert.NotEmpty(userBadge.NO)

	// 测试触发用户和称号的唯一索引的情况
	userBadge1 := MUserBadge{
		UserID:  testUserID1,
		BadgeID: testBadgeID,
	}
	require.NoError(userBadge1.CreateUserBadge())
	assert.Equal(userBadge.NOStr, userBadge1.NOStr)

	// 测试触发称号和称号编号的唯一索引
	userBadge2 := MUserBadge{
		UserID:  testUserID2,
		BadgeID: testBadgeID,
		NO:      userBadge.NO,
	}
	assert.True(servicedb.IsUniqueError(userBadge2.CreateUserBadge()))
}

func TestGetNextNO(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试称号没有发放记录
	require.NoError(MUserBadge{}.DB().Delete("", "badge_id = ?", testBadgeID).Error)
	res, err := GetNextNO(nil, testBadgeID, []int64{5})
	require.NoError(err)
	assert.EqualValues(1, res)

	// 测试获取新的称号编号
	var userBadges []MUserBadge
	for i := 1; i <= 3; i++ {
		userBadges = append(userBadges, MUserBadge{
			UserID:  int64(i),
			BadgeID: testBadgeID,
			NO:      int64(i),
		})
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, MUserBadge{}.TableName(), userBadges))
	res, err = GetNextNO(nil, testBadgeID, []int64{5})
	require.NoError(err)
	assert.EqualValues(4, res)

	userBadge := MUserBadge{
		UserID:  4,
		BadgeID: testBadgeID,
		NO:      4,
	}
	require.NoError(userBadge.DB().Create(&userBadge).Error)
	res, err = GetNextNO(nil, testBadgeID, []int64{5})
	require.NoError(err)
	assert.EqualValues(6, res)

	// 测试无特殊称号编号情况
	userBadge = MUserBadge{
		UserID:  5,
		BadgeID: testBadgeID,
		NO:      6,
	}
	require.NoError(userBadge.DB().Create(&userBadge).Error)
	require.NoError(servicedb.Tx(MUserBadge{}.DB(), func(tx *gorm.DB) error {
		res, err = GetNextNO(tx, testBadgeID, []int64{})
		require.NoError(err)
		assert.EqualValues(7, res)
		return nil
	}))
}

func TestListUserBadge(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	require.NoError(mbadge.MBadge{}.DB().Delete("", "element_type = ? AND element_id = ?",
		mbadge.TypeVirtualIdol, testElementID).Error)
	var badgeInfos []mbadge.MBadge
	for i := 1; i <= 2; i++ {
		badgeInfos = append(badgeInfos, mbadge.MBadge{
			ID:          int64(i),
			ElementType: mbadge.TypeVirtualIdol,
			ElementID:   testElementID,
			Title:       "测试称号名",
			Intro:       "测试称号简介",
			Cover:       "test://test/202309/27/test.png",
			Icon:        "test://test/202309/27/icon/test.png",
		})
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, mbadge.MBadge{}.TableName(), badgeInfos))
	require.NoError(MUserBadge{}.DB().Delete("", "user_id = ?", testUserID1).Error)

	// 测试用户没有称号
	res, err := ListUserBadge(mbadge.TypeVirtualIdol, testElementID, testUserID1)
	require.NoError(err)
	require.NotNil(res)
	assert.Empty(res)

	// 测试获取用户称号
	var userBadges []MUserBadge
	for i := 1; i <= 2; i++ {
		userBadges = append(userBadges, MUserBadge{
			UserID:     testUserID1,
			BadgeID:    int64(i),
			NO:         int64(i),
			CreateTime: util.TimeNow().Add(time.Duration(i) * time.Minute).Unix(),
		})
	}
	require.NoError(servicedb.BatchInsert(service.MainDB, MUserBadge{}.TableName(), userBadges))
	res, err = ListUserBadge(mbadge.TypeVirtualIdol, testElementID, testUserID1)
	require.NoError(err)
	require.Len(res, 2)
	assert.EqualValues(2, res[0].ID)
	assert.Equal("测试称号名", res[0].Title)
	assert.Equal("测试称号简介", res[0].Intro)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/test.png", res[0].CoverURL)
	assert.Equal("https://static-test.maoercdn.com/test/202309/27/icon/test.png", res[0].IconURL)
	assert.Equal("000002", res[0].NOStr)
	assert.EqualValues(1, res[1].ID)
	assert.NotEmpty(res[1].Title)
	assert.NotEmpty(res[1].Intro)
	assert.NotEmpty(res[1].CoverURL)
	assert.Equal("000001", res[1].NOStr)
}
