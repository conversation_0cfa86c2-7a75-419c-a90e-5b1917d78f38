package museravatarframelog

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

const (
	testUserID        = 233
	testAvatarFrameID = 123
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestBatchCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试批量插入 2 条记录
	timestamp := util.TimeNow().Unix()
	var userAvatarFrameRelations []MUserAvatarFrameLog = []MUserAvatarFrameLog{
		MUserAvatarFrameLog{
			UserID:        100861,
			AvatarFrameID: 100101,
			StartTime:     timestamp,
			EndTime:       timestamp,
			CreateTime:    timestamp,
			ModifiedTime:  timestamp,
		},
		MUserAvatarFrameLog{
			UserID:        100862,
			AvatarFrameID: 100102,
			StartTime:     timestamp,
			EndTime:       timestamp,
			CreateTime:    timestamp,
			ModifiedTime:  timestamp,
		},
	}
	require.NoError(MUserAvatarFrameLog{}.DB().Delete("", "user_id IN(100861,100862)").Error)
	err := BatchCreate(userAvatarFrameRelations)
	require.NoError(err)
	// 验证 user_id 和 avatar_frame_id 为 100861 和 100101 时数据插入成功
	exists, err := servicedb.Exists(MUserAvatarFrameLog{}.DB().Where("user_id = ? AND avatar_frame_id = ?", 100861, 100101))
	require.NoError(err)
	assert.True(exists)
	// 验证 user_id 和 avatar_frame_id 为 100862 和 100102 时数据插入成功
	exists, err = servicedb.Exists(MUserAvatarFrameLog{}.DB().Where("user_id = ? AND avatar_frame_id = ?", 100862, 100102))
	require.NoError(err)
	assert.True(exists)
}
func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserAvatarFrameLog{}, "id", "create_time", "modified_time", "user_id",
		"avatar_frame_id", "start_time", "end_time")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, StatusTakeoff)
	assert.EqualValues(1, StatusWearing)
}

func TestMUserAvatarFrameLog_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_user_avatar_frame_log", MUserAvatarFrameLog{}.TableName())
}

func TestGetUserFirstWearTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户未佩戴过该头像挂件
	startTime, err := GetUserFirstWearTime(1, 1)
	require.NoError(err)
	assert.Zero(startTime)

	// 测试用户佩戴过该头像挂件
	startTime, err = GetUserFirstWearTime(10, 1)
	require.NoError(err)
	assert.NotZero(startTime)
}

func TestMUserAvatarFrameLog_BeforeCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := MUserAvatarFrameLog{}
	require.NoError(m.BeforeCreate(nil))
	assert.NotZero(m.CreateTime)
}

func TestMUserAvatarFrameLog_BeforeSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := MUserAvatarFrameLog{}
	require.NoError(m.BeforeSave(nil))
	assert.NotZero(m.ModifiedTime)
}

func TestAddLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试新增头像框佩戴记录
	require.NoError(MUserAvatarFrameLog{}.DB().
		Delete("", "user_id = ? AND avatar_frame_id = ?", testUserID, testAvatarFrameID).Error)
	require.NoError(AddLog(testUserID, testAvatarFrameID, StatusWearing))
	var m MUserAvatarFrameLog
	require.NoError(MUserAvatarFrameLog{}.DB().Where("user_id = ? AND avatar_frame_id = ?",
		testUserID, testAvatarFrameID).Take(&m).Error)
	assert.EqualValues(testUserID, m.UserID)
	assert.EqualValues(testAvatarFrameID, m.AvatarFrameID)
	assert.NotZero(m.StartTime)
	assert.Zero(m.EndTime)

	// 测试新增头像框卸下记录
	require.NoError(AddLog(testUserID, testAvatarFrameID, StatusTakeoff))
	var m1 MUserAvatarFrameLog
	require.NoError(MUserAvatarFrameLog{}.DB().Where("user_id = ? AND avatar_frame_id = ?",
		testUserID, testAvatarFrameID).Order("id DESC").Take(&m1).Error)
	assert.EqualValues(testUserID, m1.UserID)
	assert.EqualValues(testAvatarFrameID, m1.AvatarFrameID)
	assert.Zero(m1.StartTime)
	assert.NotZero(m1.EndTime)
}
