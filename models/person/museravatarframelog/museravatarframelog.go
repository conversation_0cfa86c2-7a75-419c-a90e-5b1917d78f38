package museravatarframelog

import (
	"fmt"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// status 与 m_user_avatar_frame_map 表 status 保持一致
const (
	// StatusTakeoff 未佩戴
	StatusTakeoff = iota
	// StatusWearing 佩戴中
	StatusWearing
)

// MUserAvatarFrameLog model
type MUserAvatarFrameLog struct {
	ID            int64 `gorm:"column:id;primary_key"`  // 主键
	CreateTime    int64 `gorm:"column:create_time"`     // 创建时间。单位：秒
	ModifiedTime  int64 `gorm:"column:modified_time"`   // 修改时间。单位：秒
	UserID        int64 `gorm:"column:user_id"`         // 用户 ID
	AvatarFrameID int64 `gorm:"column:avatar_frame_id"` // 头像框 ID，m_avatar_frame 表主键
	StartTime     int64 `gorm:"column:start_time"`      // 开始佩戴时间。单位：秒
	EndTime       int64 `gorm:"column:end_time"`        // 结束佩戴时间，佩戴时值为 0。单位：秒
}

// DB the db instance of MUserAvatarFrameLog model
func (m MUserAvatarFrameLog) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MUserAvatarFrameLog) TableName() string {
	return "m_user_avatar_frame_log"
}

// BatchCreate 批量插入用户头像挂件佩戴日志表
func BatchCreate(mUserAvatarFrameLogs []MUserAvatarFrameLog) error {
	return servicedb.BatchInsert(MUserAvatarFrameLog{}.DB(), MUserAvatarFrameLog{}.TableName(), mUserAvatarFrameLogs)
}

// BeforeCreate hook
func (m *MUserAvatarFrameLog) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *MUserAvatarFrameLog) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// GetUserFirstWearTime GetUserFirstWearTime 获取用户首次佩戴指定头像挂件的时间戳，单位：秒
func GetUserFirstWearTime(userID, avatarFrameID int64) (int64, error) {
	var startTime int64
	err := service.DB.Table(MUserAvatarFrameLog{}.TableName()).Select("start_time").
		Where("user_id = ? AND avatar_frame_id = ? AND end_time = 0", userID, avatarFrameID).
		Order("start_time").Limit(1).Row().Scan(&startTime)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, nil
		}
		return 0, err
	}
	return startTime, err
}

// AddLog 创建用户头像框佩戴记录
func AddLog(userID, avatarFrameID int64, status int) error {
	log := MUserAvatarFrameLog{
		UserID:        userID,
		AvatarFrameID: avatarFrameID,
	}
	now := util.TimeNow().Unix()
	switch status {
	case StatusWearing:
		log.StartTime = now
		log.EndTime = 0
	case StatusTakeoff:
		log.StartTime = 0
		log.EndTime = now
	default:
		panic(fmt.Sprintf("错误的佩戴状态: %d", status))
	}
	return log.DB().Create(&log).Error
}
