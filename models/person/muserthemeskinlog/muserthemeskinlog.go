package muserthemeskinlog

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// EndTimeForeverEffective 主题 end_time 永久生效值
const EndTimeForeverEffective int64 = 0

// MUserThemeSkinLog model
type MUserThemeSkinLog struct {
	ID           int64 `gorm:"column:id"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	UserID       int64 `gorm:"column:user_id"`
	ThemeSkinID  int64 `gorm:"column:theme_skin_id"`
	StartTime    int64 `gorm:"column:start_time"`
	EndTime      int64 `gorm:"column:end_time"`
}

// DB the db instance of current model
func (m MUserThemeSkinLog) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MUserThemeSkinLog) TableName() string {
	return "m_user_theme_skin_log"
}

// BeforeCreate hook
func (m *MUserThemeSkinLog) BeforeCreate() (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *MUserThemeSkinLog) BeforeSave() (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// AddLog 写入记录
func AddLog(db *gorm.DB, userID, themeSkinID int64, isWear bool) error {
	log := MUserThemeSkinLog{
		UserID:      userID,
		ThemeSkinID: themeSkinID,
	}
	now := util.TimeNow().Unix()
	if isWear {
		log.StartTime = now
	} else {
		log.EndTime = now
	}
	return db.Table(log.TableName()).Create(&log).Error
}

// GetUserFirstWearTime GetUserFirstWearTime 获取用户首次装扮指定主题皮肤的时间戳，单位：秒
func GetUserFirstWearTime(userID, themeSkinID int64) (int64, error) {
	var startTime int64
	err := service.DB.Table(MUserThemeSkinLog{}.TableName()).Select("start_time").
		Where("user_id = ? AND theme_skin_id = ? AND end_time = 0", userID, themeSkinID).
		Order("start_time").Limit(1).Row().Scan(&startTime)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, nil
		}
		return 0, err
	}
	return startTime, err
}
