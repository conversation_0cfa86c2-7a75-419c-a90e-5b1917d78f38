package muserthemeskinlog

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserThemeSkinLog{}, "id", "create_time", "modified_time", "user_id",
		"theme_skin_id", "start_time", "end_time")
}

func clearLog(t *testing.T, userID int64) {
	require := require.New(t)

	err := MUserThemeSkinLog{}.DB().Delete("", "user_id = ?", userID).Error
	require.NoError(err)
}

func assertExistsLog(t *testing.T, userID, themeSkinID, now int64, isWear bool) {
	assert := assert.New(t)
	require := require.New(t)
	var log MUserThemeSkinLog
	query := log.DB().Where("user_id = ? AND theme_skin_id = ?", userID, themeSkinID)
	if isWear {
		query.Where("start_time = ? AND end_time = ?", now, 0)
	} else {
		query.Where("start_time = ? AND end_time = ?", 0, now)
	}
	exists, err := servicedb.Exists(query)
	require.NoError(err)
	assert.True(exists)
}

func TestAddLog(t *testing.T) {
	require := require.New(t)

	// 测试停用不存在的主题
	userID, themeSkinID := int64(199), int64(1000)
	clearLog(t, userID)
	now := util.TimeNow().Unix()
	log := MUserThemeSkinLog{
		CreateTime:   now,
		ModifiedTime: now,
		UserID:       userID,
		ThemeSkinID:  themeSkinID,
		StartTime:    now,
		EndTime:      EndTimeForeverEffective,
	}
	err := AddLog(log.DB(), userID, themeSkinID, false)
	require.NoError(err)
	// 停用主题时不判断主题是否存在，所以可以写入成功
	assertExistsLog(t, userID, themeSkinID, now, false)

	// 测试成功装扮主题
	clearLog(t, userID)
	err = AddLog(log.DB(), userID, themeSkinID, true)
	require.NoError(err)
	assertExistsLog(t, userID, themeSkinID, now, true)

	// 测试成功停用主题
	clearLog(t, userID)
	err = AddLog(log.DB(), userID, themeSkinID, false)
	require.NoError(err)
	// 断言写入新主题的停用记录
	assertExistsLog(t, userID, themeSkinID, now, false)
}

func TestGetUserFirstWearTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户未装扮过该主题皮肤
	startTime, err := GetUserFirstWearTime(1, 1)
	require.NoError(err)
	assert.Zero(startTime)

	// 测试用户装扮过该会员主题皮肤
	startTime, err = GetUserFirstWearTime(10, 1)
	require.NoError(err)
	assert.NotZero(startTime)
}
