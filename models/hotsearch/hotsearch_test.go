package hotsearch

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	assert.PanicsWithValue("Limit param error: -1", func() {
		_ = List(-1)
	})

	// 测试没有热搜词
	key := keys.KeyHotSearchKeyword0.Format()
	require.NoError(service.Redis.Del(key).Err())
	defer service.Redis.Del(key)
	result := List(2)
	assert.Equal([]*Item{}, result)

	// 设置缓存
	jsonData := `[{"checked":0,"key":"天官赐福","level":1,"url":"missevan://search"},{"checked":0,"key":"吞海","level":0,"url":"missevan://search"}]`
	require.NoError(service.Redis.Set(key, jsonData, time.Minute).Err())
	// 测试返回 2 条数据
	result = List(2)
	require.Len(result, 2)
	assert.Equal("天官赐福", result[0].Name)
	assert.Equal("missevan://search", result[0].URL)
	assert.Empty(result[0].OpenURL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", result[0].IconURL)
	assert.Equal("吞海", result[1].Name)
	assert.Equal("missevan://search", result[1].URL)
	assert.Empty(result[1].OpenURL)
	assert.Equal("", result[1].IconURL)

	// 测试返回 1 条数据
	result = List(1)
	require.Len(result, 1)
	assert.Equal("天官赐福", result[0].Name)
	assert.Equal("missevan://search", result[0].URL)
	assert.Empty(result[0].OpenURL)
	assert.Equal("https://static-test.maoercdn.com/image/icon.png", result[0].IconURL)
}
