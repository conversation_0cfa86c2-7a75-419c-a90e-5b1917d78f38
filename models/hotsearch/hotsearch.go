package hotsearch

import (
	"encoding/json"
	"fmt"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

// levelSupper 超热搜
const levelSupper = 1

// cacheItem 从 redis 获取的热搜词
type cacheItem struct {
	Key   string `json:"key"`
	Level int    `json:"level"`
	URL   string `json:"url"`
}

// Item 热搜词数据
type Item struct {
	Name    string `json:"name"`
	OpenURL string `json:"open_url"` // 根据版本号处理后的可跳转链接
	IconURL string `json:"icon_url,omitempty"`
	URL     string `json:"url,omitempty"` // 原始跳转链接
}

// List 获取热搜词列表
func List(limit int) []*Item {
	if limit <= 0 {
		panic(fmt.Sprintf("Limit param error: %d", limit))
	}
	hotSearchWordKey := keys.KeyHotSearchKeyword0.Format()
	hotSearchWords, err := service.Redis.Get(hotSearchWordKey).Result()
	if err != nil {
		// redis 中没有数据或 redis 数据错误都记录错误日志
		logger.Errorf("从 redis 获取热搜词失败: %v", err)
		// PASS
		return []*Item{}
	}

	var keywords []cacheItem
	err = json.Unmarshal([]byte(hotSearchWords), &keywords)
	if err != nil {
		logger.Error(err)
		// PASS
		return []*Item{}
	}

	items := make([]*Item, 0, len(keywords))
	for _, keyword := range keywords {
		item := &Item{
			Name: keyword.Key,
			URL:  keyword.URL,
		}
		if keyword.Level == levelSupper {
			item.IconURL = service.Storage.Parse(config.Conf.Params.Search.HotSearchWordHotIconURL)
		}
		items = append(items, item)
	}

	itemsLen := len(items)
	if itemsLen > limit {
		items = items[:limit]
	} else if itemsLen < limit {
		logger.Errorf("热搜词数量不足 %d，当前数量为 %d", limit, itemsLen)
		// PASS
	}
	return items
}
