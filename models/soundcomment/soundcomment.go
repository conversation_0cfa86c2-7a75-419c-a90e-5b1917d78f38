package soundcomment

// 被评论的元素类型
const (
	TypeSound       = 1
	TypeAlbum       = 2
	TypeNews        = 3
	TypeTag         = 4
	TypeTopic       = 6
	TypeEvent       = 7
	TypeVoiceCard   = 8
	TypeOmikujiCard = 9
)

// LabelOfType 评论类型对应的字符串
var LabelOfType = map[int]string{
	TypeSound:       "音频",
	TypeAlbum:       "音单",
	TypeNews:        "新闻",
	TypeTag:         "频道",
	TypeTopic:       "专题",
	TypeEvent:       "活动",
	TypeVoiceCard:   "语音包",
	TypeOmikujiCard: "求签包",
}
