package mpersonarank

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

// MPersonaRank 榜单表
type MPersonaRank struct {
	ID           int64  `gorm:"column:id"`            // 主键
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间，单位：秒
	PersonaID    int64  `gorm:"column:persona_id"`    // 画像 ID
	RankType     int    `gorm:"column:rank_type"`     // 榜单类型
	Sort         int64  `gorm:"column:sort"`          // 榜单排序
	Name         string `gorm:"column:name"`          // 榜单名称
	Active       int    `gorm:"column:active"`        // 是否默认选中，0: 否；1: 是
}

// TableName for current model
func (MPersonaRank) TableName() string {
	return "m_persona_rank"
}

// DB the db instance of MPersonaRank model
func (m MPersonaRank) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// ListByPersonaID 根据画像 ID 获取榜单数据
func ListByPersonaID(personaID int64) ([]*MPersonaRank, error) {
	var personaRankList []*MPersonaRank
	err := MPersonaRank{}.DB().Select("id, rank_type, sort, name, active").
		Where("persona_id = ?", personaID).
		Order("sort ASC").
		Find(&personaRankList).Error
	if err != nil {
		return nil, err
	}

	return personaRankList, nil
}
