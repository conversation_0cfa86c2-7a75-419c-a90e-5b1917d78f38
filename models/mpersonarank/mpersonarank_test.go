package mpersonarank

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/models/mhomepagerank"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMPersonaRankTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	kc.Check(MPersonaRank{}, "id", "create_time", "modified_time", "persona_id", "rank_type", "sort", "name", "active")
}

func TestMPersonaRank_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_persona_rank", MPersonaRank{}.TableName())
}

func TestListByPersonaID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ListByPersonaID(persona.TypeDiscovery)
	require.NoError(err)
	require.Len(list, 7)
	assert.Equal(mhomepagerank.TypeRankHotSearch, list[0].RankType)
	assert.Equal("热门搜索", list[0].Name)
	assert.Equal(mhomepagerank.TypeRankDramaNew, list[1].RankType)
	assert.Equal("新品榜", list[1].Name)
	assert.Equal(mhomepagerank.TypeRankDramaPopularity, list[2].RankType)
	assert.Equal("人气榜", list[2].Name)
	assert.Equal(mhomepagerank.TypeRankDramaFree, list[3].RankType)
	assert.Equal("免费榜", list[3].Name)
	assert.Equal(mhomepagerank.TypeRankDramaRomantic, list[4].RankType)
	assert.Equal("言情榜", list[4].Name)
	assert.Equal(mhomepagerank.TypeRankLive, list[5].RankType)
	assert.Equal("直播榜", list[5].Name)
	assert.Equal(mhomepagerank.TypeRankSoundLover, list[6].RankType)
	assert.Equal("声音恋人榜", list[6].Name)
}
