package live

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func setupTestLive(t *testing.T, live *Live) {
	cleanup := func() {
		service.DB.Where("user_id = ?", live.UserID).Delete(&Live{})
	}
	cleanup()

	err := service.DB.Create(live).Error
	require.NoError(t, err)
	t.Cleanup(cleanup)
}

func TestFindLiveByCreatorID(t *testing.T) {
	testLive := &Live{
		RoomID:     1001,
		CatalogID:  123,
		Title:      "测试直播间",
		Intro:      "这是一个测试直播间",
		Cover:      "https://example.com/cover.jpg",
		Status:     StatusOpen,
		ContractID: 1,
		UserID:     9999,
	}
	setupTestLive(t, testLive)

	t.Run("查询存在的主播直播间", func(t *testing.T) {
		live, err := FindLiveByCreatorID(testLive.UserID)
		require.NoError(t, err)
		require.NotNil(t, live)
		assert.Equal(t, testLive.UserID, live.UserID)
		assert.Equal(t, testLive.Title, live.Title)
		assert.Equal(t, testLive.RoomID, live.RoomID)
	})

	t.Run("查询不存在的主播直播间", func(t *testing.T) {
		nonExistentID := int64(8888)
		live, err := FindLiveByCreatorID(nonExistentID)
		require.NoError(t, err)
		assert.Nil(t, live)
	})
}

func TestListLiveRooms(t *testing.T) {
	testLives := []*Live{
		{
			RoomID:     2001,
			CatalogID:  123,
			Title:      "直播间1",
			Intro:      "这是一个直播间1",
			Cover:      "https://example.com/cover.jpg",
			Status:     StatusOpen,
			ContractID: 1,
			UserID:     10001,
		},
		{
			RoomID:     2002,
			CatalogID:  123,
			Title:      "直播间2",
			Intro:      "这是一个直播间2",
			Cover:      "https://example.com/cover.jpg",
			Status:     StatusOpen,
			ContractID: 1,
			UserID:     10002,
		},
		{
			RoomID:     2003,
			CatalogID:  125,
			Title:      "直播间3",
			Intro:      "这是一个直播间3",
			Cover:      "https://example.com/cover.jpg",
			Status:     StatusClose,
			ContractID: 1,
			UserID:     10003,
		},
	}
	for _, live := range testLives {
		setupTestLive(t, live)
	}

	t.Run("空切片参数", func(t *testing.T) {
		lives, err := ListLiveRooms([]int64{})
		require.NoError(t, err)
		assert.Nil(t, lives)
	})

	t.Run("查询单个存在的直播间", func(t *testing.T) {
		lives, err := ListLiveRooms([]int64{testLives[0].UserID})
		require.NoError(t, err)
		require.Len(t, lives, 1)
		assert.Equal(t, testLives[0].UserID, lives[0].UserID)
		assert.Equal(t, testLives[0].Title, lives[0].Title)
	})

	t.Run("查询多个存在的直播间", func(t *testing.T) {
		creatorIDs := []int64{testLives[0].UserID, testLives[1].UserID, testLives[2].UserID}
		lives, err := ListLiveRooms(creatorIDs)
		require.NoError(t, err)
		require.Len(t, lives, 3)

		userIDs := make([]int64, len(lives))
		for i, live := range lives {
			userIDs[i] = live.UserID
		}
		assert.ElementsMatch(t, creatorIDs, userIDs)
	})

	t.Run("查询不存在的直播间", func(t *testing.T) {
		nonExistentIDs := []int64{99999, 88888}
		lives, err := ListLiveRooms(nonExistentIDs)
		require.NoError(t, err)
		assert.Empty(t, lives)
	})

	t.Run("混合查询存在和不存在的直播间", func(t *testing.T) {
		mixedIDs := []int64{testLives[0].UserID, 99999, testLives[1].UserID}
		lives, err := ListLiveRooms(mixedIDs)
		require.NoError(t, err)
		require.Len(t, lives, 2)
	})
}

func TestMapLiveRooms(t *testing.T) {
	testLive := &Live{
		RoomID:     3001,
		CatalogID:  123,
		Title:      "直播间",
		Intro:      "这是一个直播间",
		Cover:      "https://example.com/cover.jpg",
		Status:     StatusOpen,
		ContractID: 1,
		UserID:     20001,
	}
	setupTestLive(t, testLive)

	liveMap, err := MapLiveRooms([]int64{testLive.UserID})
	require.NoError(t, err)
	require.Len(t, liveMap, 1)

	live, exists := liveMap[testLive.UserID]
	require.True(t, exists)
	assert.Equal(t, testLive.Title, live.Title)
}
