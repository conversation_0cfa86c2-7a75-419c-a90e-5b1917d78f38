package live

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 状态常量
const (
	StatusUserDeleted = iota - 1
	StatusClose
	StatusOpen
)

// Live 直播用户关联表
type Live struct {
	ID            int64  `gorm:"column:id;primary_key" json:"id"`
	RoomID        int64  `gorm:"column:room_id" json:"room_id"`
	CatalogID     int64  `gorm:"column:catalog_id" json:"catalog_id"`
	Title         string `gorm:"column:title" json:"title"`
	Intro         string `gorm:"column:intro" json:"intro"`
	Cover         string `gorm:"column:cover" json:"cover"`
	Status        int    `gorm:"column:status" json:"status"`
	ContractID    int64  `gorm:"column:contract_id;default:1" json:"contract_id"`
	LiveStartTime int64  `gorm:"column:live_start_time" json:"live_start_time"`
	CreateTime    int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime  int64  `gorm:"column:modified_time" json:"-"`
	UserID        int64  `gorm:"column:user_id" json:"user_id"`
	Score         *int64 `gorm:"column:score;default" json:"-"`
	Username      string `gorm:"column:username" json:"-"`
}

// TableName 表名
func (Live) TableName() string {
	return "live"
}

// DB the db instance of current model
func (m Live) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// FindLiveByCreatorID 根据主播 ID 查询房间信息
func FindLiveByCreatorID(creatorID int64) (*Live, error) {
	var live Live
	err := service.DB.Take(&live, "user_id = ?", creatorID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &live, nil
}

// ListLiveRooms 批量查询直播间信息
func ListLiveRooms(creatorIDs []int64) ([]*Live, error) {
	if len(creatorIDs) == 0 {
		return nil, nil
	}
	var liveRooms []*Live
	err := Live{}.DB().Where("user_id IN (?)", creatorIDs).Find(&liveRooms).Error
	if err != nil {
		return nil, err
	}
	return liveRooms, nil
}

// MapLiveRooms 批量查询直播间信息
func MapLiveRooms(creatorIDs []int64) (map[int64]*Live, error) {
	if len(creatorIDs) == 0 {
		return nil, nil
	}

	liveRooms, err := ListLiveRooms(creatorIDs)
	if err != nil {
		return nil, err
	}

	return goutil.ToMap(liveRooms, "UserID").(map[int64]*Live), nil
}
