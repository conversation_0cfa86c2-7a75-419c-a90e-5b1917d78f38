package statisticdramaprofit

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestStatisticDramaProfit_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("statistic_drama_profit", StatisticDramaProfit{}.TableName())
}

func TestGetSortedStatisticDramaProfitList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	list, err := GetSortedStatisticDramaProfitList([]int64{29, 34})
	require.NoError(err)
	require.Len(list, 2)
	assert.Equal(int64(34), list[0].ID)
	assert.Equal(int64(29), list[1].ID)
}
