package statisticdramaprofit

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// StatisticDramaProfit model
type StatisticDramaProfit struct {
	ID     int64         `gorm:"column:id;primary_key"`
	Name   string        `gorm:"column:name"`
	Profit util.Float2DP `gorm:"column:profit"`
}

// DB the db instance of StatisticDramaProfit model
func (s StatisticDramaProfit) DB() *gorm.DB {
	return service.DB.Table(s.TableName())
}

// TableName for current model
func (StatisticDramaProfit) TableName() string {
	return "statistic_drama_profit"
}

// GetSortedStatisticDramaProfitList 按总收益倒序获取剧集收益列表
func GetSortedStatisticDramaProfitList(dramaIDs []int64) ([]StatisticDramaProfit, error) {
	var list []StatisticDramaProfit
	err := StatisticDramaProfit{}.DB().
		Select("id, profit").
		Where("id IN (?)", dramaIDs).
		Order("profit DESC").Find(&list).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, err
}
