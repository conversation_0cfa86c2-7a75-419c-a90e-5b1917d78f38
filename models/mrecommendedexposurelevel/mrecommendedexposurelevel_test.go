package mrecommendedexposurelevel

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestFindEnabledByID(t *testing.T) {
	// 清理可能存在的测试数据
	service.DB.Exec("DELETE FROM " + TableName() + " WHERE id = 1001")

	// 创建测试数据
	now := goutil.TimeNow().Unix()
	model := Model{
		ID:           1001,
		CreateTime:   now,
		ModifiedTime: now,
		Exposure:     100,
		Level:        "测试等级 S",
		Status:       StatusEnabled,
		Scene:        SceneHome,
	}

	// 插入测试数据
	err := service.DB.Table(TableName()).Create(&model).Error
	require.NoError(t, err)

	defer func() {
		// 清理测试数据
		service.DB.Exec("DELETE FROM " + TableName() + " WHERE id = 1001")
	}()

	t.Run("存在且启用的记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		result, err := FindEnabledByID(1001, SceneHome)
		require.NoError(err)
		require.NotNil(result)
		assert.Equal(int64(1001), result.ID)
		assert.Equal("测试等级 S", result.Level)
		assert.Equal(int64(100), result.Exposure)
		assert.Equal(SceneHome, result.Scene)
		assert.Equal(StatusEnabled, result.Status)
	})

	t.Run("不存在的记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		result, err := FindEnabledByID(9999, SceneHome)
		require.NoError(err)
		assert.Nil(result)
	})

	t.Run("存在但场景不匹配的记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		result, err := FindEnabledByID(1001, SceneLive)
		require.NoError(err)
		assert.Nil(result)
	})

	t.Run("存在但已禁用的记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 更新记录状态为禁用
		err := service.DB.Table(TableName()).
			Where("id = ?", 1001).
			Update("status", StatusDisabled).Error
		require.NoError(err)

		result, err := FindEnabledByID(1001, SceneHome)
		require.NoError(err)
		assert.Nil(result)

		// 将状态恢复为启用，避免影响其他测试
		err = service.DB.Table(TableName()).
			Where("id = ?", 1001).
			Update("status", StatusEnabled).Error
		require.NoError(err)
	})
}
