package personalrecommendblocks

import (
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermarkstyle"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramainfoaddendum"
	"github.com/MiaoSiLa/missevan-main/models/mpersonamoduleelement"
	"github.com/MiaoSiLa/missevan-main/models/mrecommendedelements"
	"github.com/MiaoSiLa/missevan-main/models/youmightlikemodule"
)

// ElementDrama 推荐剧集模块下的剧集元素
type ElementDrama struct {
	ID         int64                            `json:"id"`
	Name       string                           `json:"name"`
	Abstract   string                           `json:"abstract"`
	CoverColor int                              `json:"cover_color"`
	Integrity  int                              `json:"integrity"`
	Newest     string                           `json:"newest"`
	PayType    int                              `json:"pay_type"`
	ViewCount  int64                            `json:"view_count"`
	NeedPay    int                              `json:"need_pay"`
	Sort       int64                            `json:"sort"`
	ModuleID   int64                            `json:"module_id"`
	FrontCover string                           `json:"front_cover"`
	CornerMark *dramacornermarkstyle.CornerMark `json:"corner_mark,omitempty"`
}

// RecommendModuleDrama 推荐剧集模块信息
type RecommendModuleDrama struct {
	BlockType int                      `json:"block_type"`
	ModuleID  int64                    `json:"module_id"`
	Title     string                   `json:"title"`
	Type      int                      `json:"type"`
	Style     int                      `json:"style"`
	More      *youmightlikemodule.More `json:"more,omitempty"`
	Elements  []ElementDrama           `json:"elements"`
}

// RecommendDramaModules 获取会员中心下剧集模块信息
func RecommendDramaModules(personaID, userID int64) ([]RecommendModuleDrama, error) {
	// 获取会员画像下所有剧集模块
	modules, err := mpersonamoduleelement.ListPersonaModules(personaID, []int{mpersonamoduleelement.ElementTypeDrama})
	if err != nil {
		return nil, err
	}
	modulesLen := len(modules)
	if modulesLen == 0 {
		return nil, nil
	}
	moduleIDs := make([]int64, 0, modulesLen)
	for _, module := range modules {
		moduleIDs = append(moduleIDs, module.ModuleID)
	}
	// 获取所有剧集模块下推荐元素
	elements, err := mrecommendedelements.ListRecommendElements(moduleIDs)
	if err != nil {
		return nil, err
	}
	elementsLen := len(elements)
	if elementsLen == 0 {
		logger.WithField("persona_id", personaID).Error("推荐剧集模块可推荐剧集元素为空，请补充")
		return nil, nil
	}
	dramaIDs := make([]int64, 0, len(elements))
	for _, element := range elements {
		dramaIDs = append(dramaIDs, element.ElementID)
	}
	// 获取所有过审剧集
	dramas, err := dramainfo.ListDramaInfoByIDs(dramaIDs)
	if err != nil {
		return nil, err
	}
	shortIntros, err := dramainfoaddendum.ListShortIntro(dramaIDs)
	if err != nil {
		return nil, err
	}
	shortIntroMap := goutil.ToMap(shortIntros, "ID").(map[int64]dramainfoaddendum.DramaInfoAddendum)
	dramasLen := len(dramas)
	if dramasLen == 0 {
		logger.WithField("persona_id", personaID).Error("推荐剧集模块可推荐过审剧集为空，请补充")
		return nil, nil
	}
	dramasInfoList := make([]dramainfo.RadioDramaDramainfo, 0, dramasLen)
	for _, drama := range dramas {
		dramasInfoList = append(dramasInfoList, *drama)
	}
	// 获取剧集角标信息，该方法同时会对 dramasInfoList 中剧集加上 need_pay 信息
	cornerMarkMap, err := dramacornermarkstyle.GetDramaCornerMark(userID, dramasInfoList)
	if err != nil {
		return nil, err
	}
	// 为剧集元素补充 sort 和 module_id 字段
	elementMap := goutil.ToMap(elements, "ElementID").(map[int64]mrecommendedelements.MRecommendedElement)
	dramaElementInfo := make(map[int64]ElementDrama, len(dramasInfoList))
	for _, drama := range dramasInfoList {
		element, exists := elementMap[drama.ID]
		if !exists {
			continue
		}
		elementDramaItem := ElementDrama{
			ID:         drama.ID,
			CoverColor: drama.CoverColor,
			Newest:     drama.Newest,
			PayType:    int(drama.PayType),
			ViewCount:  drama.ViewCount,
			FrontCover: drama.CoverURL,
			Sort:       element.Sort,
			ModuleID:   element.ModuleID,
		}
		if drama.Name != nil {
			elementDramaItem.Name = *drama.Name
		}
		// 小标题优先采用一句话简介，当其为空时返回剧集简介内容
		shortIntroInfo, exists := shortIntroMap[drama.ID]
		if exists {
			elementDramaItem.Abstract = shortIntroInfo.ShortIntro
		}
		if elementDramaItem.Abstract == "" && drama.Abstract != nil {
			elementDramaItem.Abstract = *drama.Abstract
		}
		elementDramaItem.Abstract = goutil.ExtractHTMLPlainText(elementDramaItem.Abstract)
		if drama.Integrity != nil {
			elementDramaItem.Integrity = int(*drama.Integrity)
		}
		if drama.NeedPay != nil {
			elementDramaItem.NeedPay = *drama.NeedPay
		}
		cornerMark, exists := cornerMarkMap[drama.ID]
		if exists {
			elementDramaItem.CornerMark = &cornerMark
		}
		dramaElementInfo[drama.ID] = elementDramaItem
	}
	// 按照模块对推荐剧集分组
	elemsModuleGroup := make(map[int64][]int64, modulesLen)
	for _, element := range elements {
		if len(elemsModuleGroup[element.ModuleID]) < mrecommendedelements.ElementRecommendCount {
			elemsModuleGroup[element.ModuleID] = append(elemsModuleGroup[element.ModuleID], element.ElementID)
		}
	}
	recommendModules := make([]RecommendModuleDrama, 0, modulesLen)
	for _, module := range modules {
		moduleElementIDs := elemsModuleGroup[module.ModuleID]
		if len(moduleElementIDs) == 0 {
			logger.WithField("module_id", module.ModuleID).Error("推荐剧集模块可推荐过审剧集为空，请补充")
			// PASS
			continue
		}
		moduleDramas := make([]ElementDrama, 0, modulesLen)
		for _, elementID := range moduleElementIDs {
			if dramaElementInfo[elementID].ID != 0 {
				// 部分剧集未过审将不会写入 dramaElementInfo[elementID]，需要过滤掉
				moduleDramas = append(moduleDramas, dramaElementInfo[elementID])
			}
		}
		recommendModule := RecommendModuleDrama{
			BlockType: mpersonamoduleelement.BlockTypeCustomModule,
			ModuleID:  module.ModuleID,
			Title:     module.Title,
			Type:      module.Type,
			Style:     module.ElementStyle,
			Elements:  moduleDramas,
		}
		if module.MoreInfo.URL != "" {
			recommendModule.More = &youmightlikemodule.More{
				URL: module.MoreInfo.URL,
			}
		}
		recommendModules = append(recommendModules, recommendModule)
	}
	return recommendModules, nil
}
