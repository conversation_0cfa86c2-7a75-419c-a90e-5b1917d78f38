package personalrecommendblocks

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermarkstyle"
	"github.com/MiaoSiLa/missevan-main/models/mpersonamoduleelement"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/models/youmightlikemodule"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRecommendDramaModules(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	modules, err := RecommendDramaModules(persona.TypeVip, 12)
	require.NoError(err)

	expectModules := []RecommendModuleDrama{
		{
			BlockType: mpersonamoduleelement.BlockTypeCustomModule,
			ModuleID:  1001,
			Title:     "会员剧集模块一",
			Type:      2,
			Style:     1,
			More: &youmightlikemodule.More{
				URL: "https://www.test.com/mdrama/123",
			},
			Elements: []ElementDrama{
				{
					ID:         1,
					Name:       "测试更改修改查看次数（勿删）",
					Abstract:   "简介 1",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    1,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     0,
					ModuleID: 1001,
				},
				{
					ID:         8,
					Name:       "剧集名称（审核通过）",
					Abstract:   "一句话简介",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    2,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     1,
					ModuleID: 1001,
				},
				{
					ID:         4,
					Name:       "测试批量追剧",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    1,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     3,
					ModuleID: 1001,
				},
				{
					ID:         6,
					Name:       "测试批量追剧（用户已经追过的剧）",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    1,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     5,
					ModuleID: 1001,
				},
			},
		},
		{
			BlockType: mpersonamoduleelement.BlockTypeCustomModule,
			ModuleID:  1002,
			Title:     "会员剧集模块二",
			Type:      2,
			Style:     2,
			More: &youmightlikemodule.More{
				URL: "https://www.test.com/mdrama/124",
			},
			Elements: []ElementDrama{
				{
					ID:         12,
					Name:       "剧集名称（审核通过）",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    2,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     2,
					ModuleID: 1002,
				},
				{
					ID:         13,
					Name:       "剧集名称（审核通过）",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    2,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     3,
					ModuleID: 1002,
				},
				{
					ID:         11,
					Name:       "剧集名称（审核通过）",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    2,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     6,
					ModuleID: 1002,
				},
			},
		},
		{
			BlockType: mpersonamoduleelement.BlockTypeCustomModule,
			ModuleID:  1003,
			Title:     "会员剧集模块三",
			Type:      2,
			Style:     3,
			// more 为空不下发
			Elements: []ElementDrama{
				{
					ID:         104,
					Name:       "测试",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    1,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     1,
					ModuleID: 1003,
				},
				{
					ID:         101,
					Name:       "测试",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    1,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     4,
					ModuleID: 1003,
				},
				{
					ID:         102,
					Name:       "测试",
					Abstract:   "",
					CoverColor: 12434877,
					Integrity:  0,
					Newest:     "",
					PayType:    1,
					ViewCount:  4,
					NeedPay:    1,
					FrontCover: "https://static-test.maoercdn.com/dramacoversmini/201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg",
					CornerMark: &dramacornermarkstyle.CornerMark{
						Text:         "付费",
						TextColor:    "#FFFFFF",
						BgStartColor: "#E66465",
						BgEndColor:   "#E66465",
					},
					Sort:     5,
					ModuleID: 1003,
				},
				// 剧集 103 已下架
			},
		},
	}
	assert.Equal(expectModules, modules)
}
