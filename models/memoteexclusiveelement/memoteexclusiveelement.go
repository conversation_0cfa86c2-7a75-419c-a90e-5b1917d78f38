package memoteexclusiveelement

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-main/service"
)

// ElementTypeDrama 剧集专属表情
const ElementTypeDrama = 1

// 专属表情状态
const (
	StatusOffline int = iota - 1 // 专属表情已下线
	StatusLocked                 // 专属表情未解锁
	StatusUnlock                 // 专属表情已解锁
)

// MEmoteExclusiveElement 活动专属表情
type MEmoteExclusiveElement struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	ElementID    int64  `gorm:"column:element_id"`
	ElementType  int    `gorm:"column:element_type"`
	PackageID    int64  `gorm:"column:package_id"`
	StartTime    int64  `gorm:"column:start_time"`
	EndTime      int64  `gorm:"column:end_time"`
	More         string `gorm:"column:more"`

	MoreInfo More `gorm:"-"`
}

// More 其他信息
type More struct {
	Unlock         bool   `json:"unlock"`
	UnlockScore    int64  `json:"unlock_score"`
	Tip            string `json:"tip"`
	ExpireDuration int64  `json:"expire_duration"` // 专属表情解锁后多久下线（从解锁日零点开始计算），为 0 时表示专属表情无下线时间。单位：秒
}

// DB the db instance of MEmoteExclusiveElement model
func (m MEmoteExclusiveElement) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MEmoteExclusiveElement) TableName() string {
	return "m_emote_exclusive_element"
}

// AfterFind is a GORM hook for query
func (m *MEmoteExclusiveElement) AfterFind() error {
	if m.More != "" {
		err := json.Unmarshal([]byte(m.More), &m.MoreInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// GetExclusiveEmotes 获取专属表情配置信息
func GetExclusiveEmotes(elementID int64, elementType int) ([]*MEmoteExclusiveElement, error) {
	var emotes []*MEmoteExclusiveElement
	err := MEmoteExclusiveElement{}.DB().Where("element_id = ? AND element_type = ?", elementID, elementType).Find(&emotes).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return emotes, nil
}
