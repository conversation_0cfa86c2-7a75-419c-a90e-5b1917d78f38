package mreportreason

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MReportReason{}, "id", "create_time", "modified_time", "parent_id", "name", "sort", "scene")
}

func TestListReportReasonsByScene(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取不存在场景下的举报信息
	res, err := ListReportReasonsByScene(8)
	require.NoError(err)
	assert.Len(res, 0)

	// 测试获取场景下的举报信息
	res, err = ListReportReasonsByScene(SceneSound)
	require.NoError(err)
	assert.Len(res, 6)
	assert.EqualValues(2, res[0].ID)
	assert.Equal("违法违规", res[0].Name)
	assert.EqualValues(1, res[1].ID)
	assert.Equal("谣言类不实信息", res[1].Name)
	assert.EqualValues(5, res[2].ID)
	assert.Equal("违法违禁", res[2].Name)
	assert.EqualValues(4, res[3].ID)
	assert.Equal("赌博诈骗", res[3].Name)
	assert.EqualValues(6, res[4].ID)
	assert.Equal("其他", res[4].Name)
	assert.EqualValues(3, res[5].ID)
	assert.Equal("涉政谣言", res[5].Name)
}
