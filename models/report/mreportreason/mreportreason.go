package mreportreason

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-main/service"
)

const tableName = "m_report_reason"

// 举报场景
const (
	// SceneSound 稿件举报
	SceneSound = 1
	// SceneInteraction 互动举报
	SceneInteraction = 2
	// SceneLive 直播举报
	SceneLive = 4
)

// MReportReason model
type MReportReason struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间。单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间。单位：秒
	ParentID     int64  `gorm:"column:parent_id"`     // 上级分类，无上级分类时为 0
	Name         string `gorm:"column:name"`          // 名称
	Sort         int64  `gorm:"column:sort"`          // 举报原因顺序
	Scene        int    `gorm:"column:scene"`         // 举报场景，使用比特位标识场景。第 1 位: 稿件举报；第 2 位: 互动举报；第 3 位: 直播举报
}

// DB the db instance of MReportReason model
func (m MReportReason) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MReportReason) TableName() string {
	return tableName
}

// ListReportReasonsByScene 通过场景查找举报原因列表
func ListReportReasonsByScene(scene int) ([]MReportReason, error) {
	var reason []MReportReason
	err := MReportReason{}.DB().Where("scene & ?", scene).
		Order("parent_id ASC, sort ASC").
		Find(&reason).Error
	if err != nil {
		return nil, err
	}
	return reason, nil
}
