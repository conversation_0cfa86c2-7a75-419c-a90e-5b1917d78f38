//go:build !release
// +build !release

package service

import (
	"path"
	"runtime"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-main/service/bilibili/settle"
)

func initComponents() {
	logger.InitTestLog()
}

// InitTest 初始化单元测试
func InitTest() {
	initComponents()
	params.InitTestParams()

	conf := &Config{
		Redis: serviceredis.Config{
			Addr:     "redis.srv.maoer.co:6379",
			DB:       500,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
		LRURedis: serviceredis.Config{
			Addr:     "redis.srv.maoer.co:6379",
			DB:       700,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
		MRPC: mrpc.TestConfig(),
		PushService: pushservice.Config{
			URL: "http://mpush.srv.maoer.co:8098/",
			Key: "testkey",
		},
		Storage: storage.Config{
			"test": {
				Type:            "oss",
				AccessKeyID:     "LTAIsNW7Hxzgnxu2",
				AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
				Bucket:          "missevan-test",
				Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
				PublicURLs: []entryconfig.SectionPublicURL{
					{
						URL:    "https://static-test.maoercdn.com/",
						Weight: 1,
					},
				},
			},
		},
		Captcha:        captcha.BuildDefaultConfig(),
		Geetest:        geetest.TestConfig(),
		BilibiliSettle: settle.TestConfig(),
	}
	conf.DB = servicedb.Config{
		Host:         "mysql.srv.maoer.co",
		Name:         "app_missevan",
		Port:         3306,
		User:         "root",
		Pass:         "rootmysql",
		MaxIdleConns: servicedb.DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
	}
	conf.MainDB = conf.DB
	conf.DramaDB = conf.DB
	conf.PayDB = conf.DB
	conf.NewADB = conf.DB

	initDB()
	err := initService(conf)
	if err != nil {
		panic(err)
	}
}

func initDB() {
	_, file, _, _ := runtime.Caller(2)
	dbFile := path.Dir(file) + "/test.db" // db 放在每个单元测试的包目录下
	dbMainFile := path.Dir(file) + "/main.db"
	dbDramaFile := path.Dir(file) + "/drama.db"
	dbPayFile := path.Dir(file) + "/pay.db"
	newADBFile := path.Dir(file) + "/new_adb.db"

	_, file, _, _ = runtime.Caller(0)
	queryFile := path.Dir(file) + "/../testdata/test.sql" // query 共用同一个
	queryMainFile := path.Dir(file) + "/../testdata/test_missevan_main.sql"
	queryDramaFile := path.Dir(file) + "/../testdata/test_missevan_radio_drama.sql"
	queryPayFile := path.Dir(file) + "/../testdata/test_missevan_pay.sql"
	queryNewADBFile := path.Dir(file) + "/../testdata/test_adb.sql"

	DB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbFile,
		QueryFile: queryFile,
	}, DB)

	MainDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbMainFile,
		QueryFile: queryMainFile,
	}, MainDB)

	DramaDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbDramaFile,
		QueryFile: queryDramaFile,
	}, DramaDB)
	PayDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbPayFile,
		QueryFile: queryPayFile,
	}, PayDB)

	NewADB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    newADBFile,
		QueryFile: queryNewADBFile,
	}, NewADB)

	servicedb.Driver = servicedb.DriverSqlite
}

func initService(conf *Config) error {
	var err error

	Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return err
	}
	LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		return err
	}

	MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return err
	}
	PushService, err = pushservice.NewPushServiceClient(&conf.PushService)
	if err != nil {
		return err
	}
	SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		return err
	}

	Storage = storage.NewClient(conf.Storage)

	Captcha, err = captcha.NewClient(conf.Captcha)
	if err != nil {
		return err
	}
	Geetest = geetest.NewClient(conf.Geetest)
	BilibiliSettle = settle.NewClient(conf.BilibiliSettle)

	Databus.AppLogPub = databus.TestNew(&databus.Config{Action: "pub"})

	return AfterInit()
}
