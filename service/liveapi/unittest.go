//go:build !release
// +build !release

package liveapi

import (
	"testing"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// SetMockGetLiveExtraBanners sets a mock for the GetLiveExtraBanners function.
func SetMockGetLiveExtraBanners(t *testing.T, mockResp ExtraBannersResponse, mockErr error) {
	t.Cleanup(mrpc.SetMock(URIGetExtraBanners, func(input any) (any, error) {
		return mockResp, mockErr
	}))
}
