package liveapi

import (
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// live URI
const (
	// URIGetExtraBanners 获取直播通栏信息
	URIGetExtraBanners = "live://live/extra-banners"
	// URIGetRecommendedLiveCard 获取首页 Feed 流直播卡信息
	URIGetRecommendedLiveCard = "live://live-service/chatroom/open/home-feed"
	// URIGetRecommendedHomepage 获取 APP 首页"正在直播"模块的内容
	URIGetRecommendedHomepage = "live://live-service/recommended/homepage"
	// URIGetNobleRecommend 获取贵族推荐直播间
	URIGetNobleRecommend = "live://live-service/recommended/noble-recommend"
)

// ExtraBannersParam 获取直播通栏信息请求
type ExtraBannersParam struct {
	UserID    int64   `json:"user_id"`
	Positions []int64 `json:"positions"`
}

// ExtraBannersResponse 获取直播通栏信息响应
type ExtraBannersResponse struct {
	Data map[int64][]ExtraBannerItem `json:"data"`
}

// ExtraBannerItem 直播通栏信息
type ExtraBannerItem struct {
	Pic string `json:"pic"` // 通栏图地址
	URL string `json:"url"` // 直播间地址
}

// Tag 标签信息
type Tag struct {
	Type int    `json:"type"` // 标签类型
	Text string `json:"text"` // 标签文本
}

// LiveCard 直播卡片信息
type LiveCard struct {
	RoomID          int64  `json:"room_id"`          // 直播间 ID
	Name            string `json:"name"`             // 直播间名称
	CreatorID       int64  `json:"creator_id"`       // 主播 ID
	CreatorUsername string `json:"creator_username"` // 主播用户名
	CreatorIconURL  string `json:"creator_iconurl"`  // 主播头像
	CoverURL        string `json:"cover_url"`        // 直播间封面
	CatalogID       int64  `json:"catalog_id"`       // 分区 ID
	CustomTagID     int64  `json:"custom_tag_id"`    // 个性词条 ID
	Announcement    string `json:"announcement"`

	Status struct {
		Open      int `json:"open"`       // 开播状态
		RedPacket int `json:"red_packet"` // 红包状态，0 或不存在表示无待抢红包或者可抢红包
		LuckyBag  int `json:"lucky_bag"`  // 福袋状态，0 或不存在表示直播间没有福袋; 1: 直播间存在进行中的福袋
	} `json:"status"`

	Statistics *struct {
		Score int64 `json:"score"` // 直播间热度
	} `json:"statistics,omitempty"`

	ExtraInfo *struct {
		IconURL string `json:"icon_url"` // 图标 URL
		Title   string `json:"title"`    // 标题
	} `json:"extra_info,omitempty"`

	RecommendTag *Tag `json:"recommend_tag,omitempty"` // 主播关系标签，6: 搜索页用户关系标签
	CatalogTag   *Tag `json:"catalog_tag,omitempty"`   // 二级分区标签，7: 二级分区标签
	CustomTag    *Tag `json:"custom_tag,omitempty"`    // 个性词条，4: 个性词条
}

// RecommendedLiveCardResponse 获取推荐直播卡响应
type RecommendedLiveCardResponse struct {
	Cards []*LiveCard `json:"cards"`
}

// RecommendedHomepageRoom APP 首页推荐直播间信息
type RecommendedHomepageRoom struct {
	Position        int64  `json:"position"`         // 位置
	RoomID          int64  `json:"room_id"`          // 房间号
	Name            string `json:"name"`             // 直播标题
	CoverURL        string `json:"cover_url"`        // 直播封面图
	CreatorID       int64  `json:"creator_id"`       // 主播 ID
	CreatorUsername string `json:"creator_username"` // 主播昵称
	CreatorIconURL  string `json:"creator_iconurl"`  // 主播头像
	CatalogID       int64  `json:"catalog_id"`       // 分区 ID
	CatalogName     string `json:"catalog_name"`     // 分区名称
	CatalogColor    string `json:"catalog_color"`    // 分区颜色
	CustomTag       struct {
		TagID   int64  `json:"tag_id"`   // 个性词条 ID
		TagName string `json:"tag_name"` // 个性词条名称
	} `json:"custom_tag"`
	Status struct {
		Open      int `json:"open"`       // 开播状态
		PK        int `json:"pk"`         // PK 状态，1：直播间在 PK 状态，0 或不存在：直播间不在 PK 状态
		RedPacket int `json:"red_packet"` // 红包状态，0 或不存在表示无待抢红包或者可抢红包
	} `json:"status"`
}

// RecommendedHomepageResponse APP 首页"正在直播"模块的响应结构
type RecommendedHomepageResponse struct {
	Rooms []*RecommendedHomepageRoom `json:"rooms"`
}

// GetLiveExtraBanners 获取直播通栏信息
func GetLiveExtraBanners(c mrpc.UserContext, req ExtraBannersParam) (*ExtraBannersResponse, error) {
	var resp ExtraBannersResponse

	err := service.MRPC.Do(c, URIGetExtraBanners, req, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

// GetRecommendedLiveCard 获取推荐直播卡信息
func GetRecommendedLiveCard(c mrpc.UserContext, req struct {
	UserID  int64   `json:"user_id"`
	RoomIDs []int64 `json:"room_ids"`
}) (*RecommendedLiveCardResponse, error) {
	var resp RecommendedLiveCardResponse

	err := service.MRPC.Do(c, URIGetRecommendedLiveCard, req, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

// GetRecommendedHomepage 获取 APP 首页"正在直播"模块的内容
func GetRecommendedHomepage(c mrpc.UserContext) ([]*RecommendedHomepageRoom, error) {
	var resp RecommendedHomepageResponse

	err := service.MRPC.Do(c, URIGetRecommendedHomepage, struct{}{}, &resp)
	if err != nil {
		return nil, err
	}

	return resp.Rooms, nil
}

// NobleRecommendResponse 贵族推荐直播间响应结构
type NobleRecommendResponse struct {
	Recommend *NobleRecommend `json:"recommend"`
}

// NobleRecommend 贵族推荐直播间信息
type NobleRecommend struct {
	FromUserID int64 `json:"from_user_id"` // 推荐人 ID
	CreatorID  int64 `json:"creator_id"`   // 推荐的主播 ID
	RoomID     int64 `json:"room_id"`      // 被推荐的房间 ID
	Anonymous  int   `json:"anonymous"`    // 匿名推荐
	Status     int   `json:"status"`       // 是否取消 0：未取消 1：取消
	StartTime  int64 `json:"start_time"`   // 推荐开始时间
	EndTime    int64 `json:"end_time"`     // 推荐结束时间
	CreateTime int64 `json:"create_time"`  // 创建时间
	IsHighness bool  `json:"is_highness"`  // 是否为上神推荐
}

// GetNobleRecommend 获取贵族推荐直播间
func GetNobleRecommend(c mrpc.UserContext) (*NobleRecommendResponse, error) {
	var resp NobleRecommendResponse

	err := service.MRPC.Do(c, URIGetNobleRecommend, struct{}{}, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}
