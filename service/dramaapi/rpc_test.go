package dramaapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// TestGetReviewDramas
func TestGetReviewDramas(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URIGetReviewDramas, func(input interface{}) (output interface{}, err error) {
		return &[]GetReviewDramasResp{
			{
				Name:            "囧囧有神",
				DramaID:         34,
				Catalog:         89,
				RateDescription: "基础分成比例为 0.3",
				Mode:            0,
				Rates:           0.3,
			},
			{
				Name:            "纨绔",
				DramaID:         29,
				Catalog:         89,
				RateDescription: "按订单量梯度分成，基础分成比例为 0.65，销量 1000 后的订单分成比例变为 0.7；",
				Mode:            1,
				Rates: []DramaOrderVolumeRates{
					{Threshold: 0, Rate: 0.65},
					{Threshold: 1000, Rate: 0.7},
				},
			},
		}, nil
	})
	defer cancel()

	d, err := GetReviewDramas(mrpc.UserContext{}, 1)
	require.NoError(err)
	assert.Len(d, 2)
}
