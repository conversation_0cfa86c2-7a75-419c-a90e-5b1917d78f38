package dramaapi

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

// drama URI
const (
	// URIGetReviewDramas 提供用户收费剧集信息
	URIGetReviewDramas = "drama://api/get-review-dramas"

	// URIDramaSoundCheck 音频审核后对剧集的操作（给音频审核后台调用）
	URIDramaSoundCheck = "drama://drama/sound-check"
)

// GetReviewDramasResp 提供用户收费剧集信息返回值
/*
  [
    {
      "name": "囧囧有神",
      "drama_id": 34,
      "catalog": 89,
      "rate_description": "基础分成比例为 0.3",
      "mode": 0,
      "rates": 0.3
    },
    {
      "name": "纨绔",
      "drama_id": 29,
      "catalog": 89,
      "rate_description": "按订单量梯度分成，基础分成比例为 0.65，销量 1000 后的订单分成比例变为 0.7；",
      "mode": 1,
      "rates": [
        {"threshold": 0, "rate": 0.65},
        {"threshold": 1000, "rate": 0.7}
      ]
    },
    {
      "name": "魔道",
      "drama_id": 30,
      "catalog": 89,
      "rate_description": "交易时间梯度分成，基础分成比例为 0，在 1598889600 后的订单分成比例变为 0.05；",
      "mode": 2,
      "rates": [
	    {"threshold": 0, "rate": 0},
	    {"threshold": 1598889600, "rate": 0.05}
      ]
    }
  ]
*/
type GetReviewDramasResp struct {
	Name            string `json:"name"`
	DramaID         int64  `json:"drama_id"`
	Catalog         int64  `json:"catalog"`
	RateDescription string `json:"rate_description"`
	// 订单分成模式：0 普通，1 订单量梯度，2 交易时间梯度，3 单独的打赏分成比例
	Mode int64 `json:"mode"`
	// Rates 根据 Mode 的不同，结构不同，含义不同
	Rates interface{} `json:"rates"`
}

// DramaOrderVolumeRates 当 mode = 1 OR mode = 2 时使用，可以看 GetReviewDramasResp 的注释
type DramaOrderVolumeRates struct {
	// 订单阈值 OR 指定时间戳
	Threshold int64
	// 比例
	Rate util.Float2DP
}

// GetReviewDramas 提供用户收费剧集信息
func GetReviewDramas(c mrpc.UserContext, userID int64) ([]*GetReviewDramasResp, error) {
	var resp []*GetReviewDramasResp
	err := service.MRPC.Do(c, URIGetReviewDramas, handler.M{
		"user_id": userID,
		"catalog": 0,
	}, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// OperationPassSound 音频过审后对剧集的操作
const OperationPassSound = 1

// SoundCheckResp 音频过审后对剧集的操作响应
type SoundCheckResp struct {
	Update SoundCheckUpdateInfo `json:"update"`
}

// SoundCheckUpdateInfo update 信息
type SoundCheckUpdateInfo struct {
	IPR    bool `json:"ipr"`
	Newest bool `json:"newest"`
	IsSaw  bool `json:"is_saw"`
}

// SoundCheck 音频过审后对剧集的操作
func SoundCheck(c mrpc.UserContext, soundID int64, checked, operation int) (*SoundCheckResp, error) {
	var resp *SoundCheckResp
	err := service.MRPC.Do(c, URIDramaSoundCheck, map[string]interface{}{
		"sound_id":      soundID,
		"sound_checked": checked,
		"operation":     operation,
	}, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
