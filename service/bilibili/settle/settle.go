package settle

import (
	"net/http"

	"github.com/google/go-querystring/query"
)

// APIPartnerPop 点播-签约信息/获取登录合作方是否签约接口
// 接口文档: https://cloud.bilibili.co/akali/appsManage?appId=archive.copyright.missevan-settle-interface&level=2&itemId=344353&appVersion=undefined#sh/sh001/prod
const APIPartnerPop = "/x/missevan/settle/interface/partner/pop"

// 弹窗显示状态
const (
	StateHide = iota + 1 // 隐藏弹窗
	StatePop             // 显示弹窗
)

// APIPartnerPopParams 接口参数
type APIPartnerPopParams struct {
	MID int64 `url:"mid"`
}

// APIPartnerPopResult 接口返回值
type APIPartnerPopResult struct {
	Mid         int64    `json:"mid"`
	PartnerName string   `json:"partner_name"` // 合作方名称 有待签约的进行返回
	State       int64    `json:"state"`        // 是否显示弹窗 1：不弹出；2：未签约完成 弹出
	URLList     []string `json:"url_list"`     // 未签约状态下 签约地址
}

// PartnerPop partner pop api request
func (c *Client) PartnerPop(params *APIPartnerPopParams) (*APIPartnerPopResult, error) {
	values, err := query.Values(params)
	if err != nil {
		return nil, err
	}
	path := APIPartnerPop + "?" + values.Encode()
	req, err := c.newRequest(http.MethodGet, path, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	var resp APIPartnerPopResult
	err = c.request(req, &resp, false)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}
