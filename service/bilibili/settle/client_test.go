package settle

import (
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestSignParams(t *testing.T) {
	assert := assert.New(t)

	c := NewClient(TestConfig())
	appKey, appSecret := c.SignParams()
	assert.Equal(c.<PERSON>pp<PERSON>ey, appKey)
	assert.Equal(c.AppSecret, appSecret)
}

func TestNewRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	req, err := c.newRequest(http.MethodGet, APIPartnerPop, nil)
	require.NoError(err)
	require.NotNil(req)
	assert.NotEmpty(req.Host)
	assert.Equal(http.MethodGet, req.Method)
	assert.NotEqual("", req.URL.String())
}

func TestRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	req, err := c.newRequest(http.MethodGet, APIPartnerPop, nil)
	require.NoError(err)
	require.NotNil(req)
	_ = c.request(req, nil, false)
	assert.True(strings.Contains(req.URL.String(), "sign="))
	assert.True(strings.Contains(req.URL.String(), "appkey="))
}
