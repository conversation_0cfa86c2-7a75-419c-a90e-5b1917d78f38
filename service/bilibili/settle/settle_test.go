package settle

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(1, StateHide)
	assert.EqualValues(2, StatePop)
}

func TestClient_PartnerPop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testMID := int64(1)
	c := NewClient(TestConfig())
	cancel := c.SetMock(APIPartnerPop, APIPartnerPopResult{
		Mid:         testMID,
		PartnerName: "测试",
		State:       StateHide,
		URLList: []string{
			"https://test.com/x",
		},
	})
	defer cancel()

	params := &APIPartnerPopParams{
		MID: testMID,
	}
	data, err := c.PartnerPop(params)
	require.NoError(err)
	assert.EqualValues(testMID, data.Mid)
	assert.EqualValues("测试", data.PartnerName)
	assert.EqualValues(StateHide, data.State)
	assert.Len(data.URLList, 1)
}
