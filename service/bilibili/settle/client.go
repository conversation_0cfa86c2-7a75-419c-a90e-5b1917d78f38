package settle

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

var (
	appName    = "missevan-main"
	appVersion = "0.0.1-dev"
	// UserAgent for http request
	UserAgent = appName + "/" + appVersion
)

// Config service config
type Config struct {
	URL       string `yaml:"url"`
	AppKey    string `yaml:"app_key"`
	AppSecret string `yaml:"app_secret"`
}

// Client api client
type Client struct {
	Config
	c *http.Client
}

// SignParams sign params
func (c *Client) SignParams() (appKey, appSecret string) {
	return c.AppKey, c.AppSecret
}

// NewClient new Client
func NewClient(conf Config) *Client {
	c := &Client{
		Config: conf,
		c: &http.Client{
			Timeout: 5 * time.Second,
		},
	}
	return c
}

type apiResp struct {
	Code    int64           `json:"code"`
	Data    json.RawMessage `json:"data"`
	Message string          `json:"message"`
}

func (c *Client) newRequest(method, path string, body io.Reader) (*http.Request, error) {
	req, err := http.NewRequest(method, c.URL+path, body)
	if err != nil {
		return nil, err
	}
	req.URL.RawQuery = blademaster.Sign(c, req.URL.Query())
	req.Header.Set("User-Agent", UserAgent)
	return req, nil
}

func (c *Client) request(req *http.Request, response interface{}, raw bool) error {
	logger.Debugf("%s %s", req.Method, req.URL.String())
	ok, err := c.mockDo(req.URL.Path, response)
	if ok {
		return err
	}
	resp, err := c.c.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, body)
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode >= http.StatusBadRequest {
			logger.Warnf("%s %s\nHTTP %s\n%s", req.Method, req.URL.String(), resp.Status, body)
		}
		return &serviceutil.APIError{
			Status:  resp.StatusCode,
			Message: string(body),
		}
	}

	if raw {
		// 返回原始数据
		err = json.Unmarshal(body, response)
		if err != nil {
			return &serviceutil.APIError{
				Status:  http.StatusBadRequest,
				Message: fmt.Sprintf("raw data unmarshal failed: %v", err),
			}
		}
		return nil
	}

	var apiResp apiResp
	err = json.Unmarshal(body, &apiResp)
	if err != nil {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("data unmarshal failed: %v", err),
		}
	}
	if apiResp.Code != 0 {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("(%d) - %s", apiResp.Code, apiResp.Message),
		}
	}
	err = json.Unmarshal(apiResp.Data, response)
	if err != nil {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("(%d) - %v", apiResp.Code, err),
		}
	}
	return nil
}
