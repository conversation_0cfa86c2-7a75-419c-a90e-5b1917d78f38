package settle

import "encoding/json"

// TestConfig test config
func TestConfig() Config {
	return Config{
		URL:       "http://127.0.0.1",
		AppKey:    "testkey",
		AppSecret: "testSecret",
	}
}

var (
	mockedResponse map[string]json.RawMessage
)

func (c *Client) mockDo(uri string, resp interface{}) (bool, error) {
	if mockedResponse == nil {
		return false, nil
	}
	b := mockedResponse[uri]
	if b == nil {
		return false, nil
	}
	return true, json.Unmarshal(mockedResponse[uri], resp)
}

// SetMock set mock
func (c *Client) SetMock(uri string, body interface{}) func() {
	if mockedResponse == nil {
		mockedResponse = make(map[string]json.RawMessage, 2)
	}
	b, err := json.Marshal(body)
	if err != nil {
		panic(err)
	}
	mockedResponse[uri] = b
	return func() {
		delete(mockedResponse, uri)
	}
}
