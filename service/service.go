package service

import (
	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"

	goservice "github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-main/service/bilibili/settle"
)

// DatabusPairs databus 配置
type DatabusPairs struct {
	AppLogPub databus.Config `yaml:"app_log_pub"`
}

// DatabusClient .
type DatabusClient struct {
	AppLogPub *databus.Databus
}

// Config is service config
type Config struct {
	DB      servicedb.Config `yaml:"db"`
	MainDB  servicedb.Config `yaml:"main_db"`
	DramaDB servicedb.Config `yaml:"drama_db"`
	PayDB   servicedb.Config `yaml:"pay_db"`
	NewADB  servicedb.Config `yaml:"new_adb"`

	Redis    serviceredis.Config `yaml:"redis"`
	LRURedis serviceredis.Config `yaml:"lru_redis"`

	MRPC        mrpc.Config        `yaml:"mrpc"`
	PushService pushservice.Config `yaml:"pushservice"`

	Storage storage.Config `yaml:"storage"`

	Captcha *captcha.Config `yaml:"captcha"`
	// TODO: 合并到 captcha 配置中
	Geetest        geetest.Config `yaml:"geetest"`
	BilibiliSettle settle.Config  `yaml:"bilibili_settle"`

	Databus DatabusPairs `yaml:"databus"`
}

// service vars
var (
	DefaultConfig *Config

	DB      *gorm.DB
	MainDB  *gorm.DB
	DramaDB *gorm.DB
	PayDB   *gorm.DB
	NewADB  *gorm.DB

	Redis    *redis.Client
	LRURedis *redis.Client

	MRPC        *mrpc.Client
	PushService *pushservice.Client
	SSO         *sso.Client

	Storage *storage.Client

	Captcha        *captcha.Client
	Geetest        *geetest.Client
	BilibiliSettle *settle.Client

	Databus DatabusClient
)

func init() {
	maxIdleConns := servicedb.DefaultMaxIdleConns()
	DefaultConfig = &Config{
		DB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		MainDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		DramaDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		PayDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		NewADB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		Redis: serviceredis.Config{
			Addr:     "127.0.0.1:6379",
			DB:       0,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
		LRURedis: serviceredis.Config{
			Addr:     "127.0.0.1:6379",
			DB:       0,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
	}
}

// AfterInit 初始化完成后的一些赋值操作
func AfterInit() error {
	goservice.DB = DB
	goservice.PayDB = PayDB
	goservice.Redis = Redis
	goservice.LRURedis = LRURedis
	goservice.Storage = Storage
	goservice.MRPC = MRPC
	goservice.PushService = PushService
	goservice.SSO = SSO
	return nil
}
