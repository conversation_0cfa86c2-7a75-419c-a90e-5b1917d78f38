package keys

import "github.com/MiaoSiLa/missevan-go/service/cache"

// DatabusKeySubscribeDramaDetailLog1 追剧 databus 消息
// params: user_id
var DatabusKeySubscribeDramaDetailLog1 = cache.DatabusFormatter{
	"subscribe_drama_detail_log:%d",
	"subscribe_drama_detail_log:",
}

// DatabusKeyUserFollowLog1 user follow/unfollow databus key
// params: user_id 发起关注的用户 ID
var DatabusKeyUserFollowLog1 = cache.DatabusFormatter{
	"user-follow-log:%d",
	"user-follow-log:",
}

// DatabusKeyRecomendExposureLog1 推荐曝光日志 databus key
// params: user_id 或 ip hash
var DatabusKeyRecomendExposureLog1 = cache.DatabusFormatter{
	"recommend_exposure_log:%s",
	"recommend_exposure_log:",
}
