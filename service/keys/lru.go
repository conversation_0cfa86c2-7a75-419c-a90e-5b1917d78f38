package keys

import (
	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// KeySoundPlayRecommendStrategySounds1 音频播放页的推荐音频 ID（按推荐策略分类），%d 代表当前播放音频 ID
const KeySoundPlayRecommendStrategySounds1 cache.KeyFormat = "sound_play_recommend_strategy_v2:id:%d" // STRING

// KeyAppPersonHomepage1 App 个人主页缓存，%d 代表当前用户 ID
const KeyAppPersonHomepage1 cache.KeyFormat = "person:homepage:user:%d" // STRING

// KeyWebPersonHomepageSubscription1 Web 个人主页追剧信息第一页缓存，%d 代表当前用户 ID
const KeyWebPersonHomepageSubscription1 cache.KeyFormat = "user:%d:subscription" // STRING

// KeyDramaCornerMarkStyle0 剧集角标样式信息
// 举例: {"1": {"text": "已购", "text_color": "#FFFFFF", "bg_start_color": "#E66465", "bg_end_color": "#E66465"}}
const KeyDramaCornerMarkStyle0 cache.KeyFormat = "drama:corner_mark_style" // STRING

// KeyDramaCornerMarkViewRanks1 剧集自然周新增播放量，%s 代表日期（比如 20060102，每周周一日期）
const KeyDramaCornerMarkViewRanks1 cache.KeyFormat = "drama:corner_mark_view_rank:%s" // STRING

// KeyAppDiscoveryRanks0 App 搜索页排行榜（有效时间为 1 分钟）
const KeyAppDiscoveryRanks0 cache.KeyFormat = "app_discovery:ranks" // STRING

// KeyVipPaymentMethod0 vip 支付方式（json 格式）（有效时间为 5 分钟）
const KeyVipPaymentMethod0 cache.KeyFormat = "vip_payment_method" // STRING

// KeyAppHomepageExtraBanner1 首页轮播通栏图数据
// params:
// - platform: 平台（1：安卓，2：iOS）
const KeyAppHomepageExtraBanner1 cache.KeyFormat = "app_homepage:extra_banner_v3:%d" // STRING

// KeyAppHomepageFavors1 首页推荐模块缓存，%d 代表当前用户 ID
const KeyAppHomepageFavors1 cache.KeyFormat = "app_homepage:favors:user:%d" // STRING

// KeyAppHomepageFeedFallbackDramas0 首页 Feed 流兜底剧集 ID 缓存（有效时间为 1 小时）
const KeyAppHomepageFeedFallbackDramas0 cache.KeyFormat = "app_homepage:feed:fallback_dramas" // STRING
