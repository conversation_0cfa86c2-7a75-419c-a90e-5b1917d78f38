package keys

import (
	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// formats - `Key${name}${argc}`
const (
	// KeyDramaRevenueUserAuth1 查看剧集收益后台身份认证
	// params: 用户 ID
	KeyDramaRevenueUserAuth1 cache.KeyFormat = "drama_revenue_auth:user_id:%d" // STRING

	// KeyUserPersonaPoints1 用户画像分数
	// params 为画像 ID，元素为用户 ID，元素分数为画像分
	KeyUserPersonaPoints1 cache.KeyFormat = "user_persona_%d_points" // HASH

	// KeyEquipPersonaPoints1 设备画像分数
	// params 为画像 ID，元素为设备号，元素分数为画像分
	KeyEquipPersonaPoints1 cache.KeyFormat = "equip_persona_%d_points" // HASH

	// LockUserBatchSubscribeDramas1 用户批量追剧锁
	// params: user_id
	LockUserBatchSubscribeDramas1 cache.KeyFormat = "lock:user_batch_subscribe_drama:user_id:%d" // STRING

	// LockUserTsSound3 用户每天投食音频限制（有效时间为 1 个自然日）
	// params: user_id, 格式化后的日期 e.g. 20240510, sound_id
	LockUserTsSound3 cache.KeyFormat = "lock:user_id:%d:ts:%s:sound_id:%d" // STRING

	// LockUserTsDrama3 用户每天投食剧集限制（有效时间为 1 个自然日）
	// params: user_id, 格式化后的日期 e.g. 20240510, drama_id
	LockUserTsDrama3 cache.KeyFormat = "lock:user_id:%d:ts:%s:drama_id:%d" // STRING

	// LockBatchFollowUser1 用户批量关注用户锁
	// params: user_id
	LockBatchFollowUser1 cache.KeyFormat = "lock:batch_follow_user:user_id:%d" // STRING

	// KeyCounterBatchFollowUser2 批量关注用户计数
	// 用于对用户每个自然日关注用户数量进行限制，时长 24 小时方便出错时排查问题
	// params: user_id, date: 格式化后的计数当天日期 e.g. 20060102
	KeyCounterBatchFollowUser2 cache.KeyFormat = "counter:batch_follow_user:user_id:%d:date:%s" // STRING

	// KeyUserTabBarLiveLastRequestTime1 记录用户访问底部导航栏直播 Tab 页的时间，值为时间戳，单位：秒（有效时间为一周）
	// params: user_id
	KeyUserTabBarLiveLastRequestTime1 cache.KeyFormat = "user:%d:tab_bar_live:last_request_time" // STRING

	// KeyHotSearchKeyword0 热门搜索
	// 举例：[{"checked":0,"key":"天官赐福","level":1,"url":"missevan://search?keyword=%E5%A4%A9%E5%AE%98%E8%B5%90%E7%A6%8F"},{"checked":0,"key":"吞海","level":0,"url":"missevan://search?keyword=%E5%90%9E%E6%B5%B7"}]
	KeyHotSearchKeyword0 cache.KeyFormat = "hot_search_key_word" // STRING

	// KeyUserPlayEquip1 账号播放设备缓存集合
	KeyUserPlayEquip1 cache.KeyFormat = "equip_play:user_id:%d" // ZSET

	// LockUserAvatarFrame1 用户佩戴或卸下头像框锁
	// params: user_id
	LockUserAvatarFrame1 cache.KeyFormat = "lock:avatar_frame:user_id:%d" // STRING

	// KeyAppHomeFeedRecommendMock0 首页 Feed 流推荐 Mock 数据
	KeyAppHomeFeedRecommendMock0 cache.KeyFormat = "app:home_feed:recommend_mock" // STRING

	// KeyPersonHomepage1 App 个人主页缓存
	// params: user_id
	KeyPersonHomepage1 cache.KeyFormat = "person:homepage:user:%d" // STRING

	// KeyThirdTaskToken2 每日任务第三方任务 third_task_token 值
	// 有效期 24 小时，* 依次为第三方场景、完成任务当天时间日期，日期格式为：20260102
	KeyThirdTaskToken2 cache.KeyFormat = "third_task_token:scene:%d:date:%s" // Set
)
