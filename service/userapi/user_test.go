package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestGetUsersInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params := []int64{
		2333,
	}
	testResp := GetUsersInfoResp{
		Data: []UserInfo{{
			ID:          2333,
			QQUID:       "123",
			WeiboUID:    "123",
			BilibiliUID: "123",
			WechatUID:   "123",
			Confirm:     67,
			Username:    "InVinCiblezzz",
			Email:       "<EMAIL>",
			CIP:         "127.0.0.1",
			UIP:         "127.0.0.1",
			CTime:       1474172157,
			UTime:       1554098410,
			IconID:      0,
			IconURL:     "http://static.maoercdn.com/avatars/201812/10/91e6b6ca134490973e61fb702.jpg",
			IconColor:   "#B1B1B1m#CECECEm#B1B1B1m#6A6A6Am#B1B1B1",
			TeamID:      0,
			TeamName:    "",
			Subtitle:    "",
			Mobile:      "18613395805",
			Region:      86,
		}},
	}
	cancel := mrpc.SetMock(URIList, func(i interface{}) (interface{}, error) {
		assert.Equal(map[string]interface{}{
			"user_ids": params,
		}, i)
		return testResp, nil
	})
	defer cancel()

	resp, err := GetUsersInfo(mrpc.UserContext{}, params)
	require.NoError(err)
	assert.Equal(testResp.Data, resp)
}
