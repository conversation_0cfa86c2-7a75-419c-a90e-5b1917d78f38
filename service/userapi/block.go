package userapi

import (
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service"
)

// URIBlockStatusList 获取 check_user_ids 与 user_id 之间的拉黑关系
const URIBlockStatusList = "go://user/block-status-list"

// blockStatusListParam 获取 check_user_ids 与 user_id 之间的拉黑关系接口参数
type blockStatusListParam struct {
	UserID       int64   `json:"user_id"`
	CheckUserIDs []int64 `json:"check_user_ids"`
}

// BlockStatusListResponse 获取 check_user_ids 与 user_id 之间的拉黑关系接口返回值
type BlockStatusListResponse struct {
	BlockUserList []int64 `json:"block_user_list"` // check_user_ids 中拉黑 user_id 的用户 IDs
	UserBlockList []int64 `json:"user_block_list"` // check_user_ids 中被 user_id 拉黑的用户 IDs
}

// BlockStatusList 获取 check_user_ids 与 user_id 之间的拉黑关系
func BlockStatusList(c mrpc.UserContext, userID int64, checkUserIDs []int64) (blockUserList, userBlockList []int64, err error) {
	var resp BlockStatusListResponse
	err = service.MRPC.Do(c, URIBlockStatusList, blockStatusListParam{
		UserID:       userID,
		CheckUserIDs: checkUserIDs,
	}, &resp)
	if err != nil {
		return nil, nil, err
	}
	return resp.BlockUserList, resp.UserBlockList, nil
}
