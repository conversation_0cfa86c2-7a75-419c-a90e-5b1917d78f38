package userapi

import (
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service"
)

// sso URI
const (
	// URIList 根据参数获取多个用户记录
	URIList = "sso://sso/list"
)

// GetUsersInfoResp 获取多个用户信息返回
type GetUsersInfoResp struct {
	Data []UserInfo `json:"data"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID          int64  `json:"id"`
	QQUID       string `json:"qquid"`
	WeiboUID    string `json:"weibouid"`
	BilibiliUID string `json:"bilibiliuid"`
	WechatUID   string `json:"wechatuid"`
	Confirm     int    `json:"confirm"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	CIP         string `json:"cip"`
	UIP         string `json:"uip"`
	CTime       int64  `json:"ctime"`
	UTime       int64  `json:"utime"`
	IconID      int    `json:"iconid"`
	IconURL     string `json:"iconurl"`
	IconColor   string `json:"iconcolor"`
	TeamID      int64  `json:"teamid"`
	TeamName    string `json:"teamname"`
	Subtitle    string `json:"subtitle"`
	Mobile      string `json:"mobile"`
	Region      int    `json:"region"`
}

// GetUsersInfo 通过用户 ID 数组获取多个用户信息
func GetUsersInfo(c mrpc.UserContext, userIDs []int64) ([]UserInfo, error) {
	var resp GetUsersInfoResp
	err := service.MRPC.Do(c, URIList, map[string]interface{}{"user_ids": userIDs}, &resp)
	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}
