package userapi

import (
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service"
)

// URIUserLiveRank 获取主播收益榜列表
const URIUserLiveRank = "live://user/live/rank"

// 榜单类型
const (
	RankTypeDay = iota + 1
	RankTypeWeek
	RankTypeMonth
	RankTypeHour
	RankTypeGashaponWeek // 盲盒主播周榜
	_
	RankTypeNova      // 新人榜
	RankTypeLastMonth // 上期月榜
	rankTypeLimit

	RankTypeHourHomepage = 101 // 首页排行榜的直播小时榜
)

type userLiveRankParam struct {
	RankType int   `json:"type"`
	RankNum  int64 `json:"rank_num"`
}

// UserLiveRankResp 主播收益榜列表响应数据
type UserLiveRankResp struct {
	Data []*UserLiveRank `json:"data"`
}

// UserLiveRank 主播收益榜数据
type UserLiveRank struct {
	*Info
	Room *RoomInfo `json:"room"`
}

// Info 榜单收益信息
type Info struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
	Revenue  int64  `json:"revenue"`
	Rank     int64  `json:"rank"`

	RankUp      int64   `json:"rank_up"`                // RankUp = 前一名的收益 - 自己的收益 + 1
	RankChanges **int64 `json:"rank_changes,omitempty"` // 主播排名变化 = 上一场的名次 - 本场名次，null 表示新上榜，除了需要显示排名变化的地方返回该字段，其余地方不返回该字段
	IsNova      bool    `json:"is_nova,omitempty"`      // 是否是新人（只有满足新人榜上榜条件或未创建直播间的情况才有值），仅新人榜使用该值
}

// RoomInfo 房间信息
type RoomInfo struct {
	RoomID          int64             `json:"room_id"`
	CatalogID       int64             `json:"catalog_id"`
	Name            string            `json:"name"`
	Announcement    string            `json:"announcement"`
	CreatorID       int64             `json:"creator_id"`
	CreatorUsername string            `json:"creator_username"`
	Status          *SimpleRoomStatus `json:"status"`
	CoverURL        string            `json:"cover_url"`
}

// SimpleRoomStatus 房间状态简要信息
type SimpleRoomStatus struct {
	Open int `json:"open"` // 开播状态
}

// LiveRank 获取主播收益榜列表
func LiveRank(c mrpc.UserContext, rankType int, rankNum int64) ([]*UserLiveRank, error) {
	var resp UserLiveRankResp
	err := service.MRPC.Do(c, URIUserLiveRank, userLiveRankParam{
		RankType: rankType,
		RankNum:  rankNum,
	}, &resp)
	if err != nil {
		return nil, err
	}

	return resp.Data, nil
}
