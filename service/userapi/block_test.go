package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestUpdateUserPoint(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(1000)
	testCheckUserIDs := []int64{1001, 1002, 1003}

	cancel := mrpc.SetMock(URIBlockStatusList, func(input interface{}) (interface{}, error) {
		return BlockStatusListResponse{
			BlockUserList: []int64{1001, 1002},
			UserBlockList: []int64{1002, 1003},
		}, nil
	})
	defer cancel()

	var c mrpc.UserContext
	blockUserList, userBlockList, err := BlockStatusList(c, testUserID, testCheckUserIDs)
	require.NoError(err)
	assert.Equal([]int64{1001, 1002}, blockUserList)
	assert.Equal([]int64{1002, 1003}, userBlockList)
}
