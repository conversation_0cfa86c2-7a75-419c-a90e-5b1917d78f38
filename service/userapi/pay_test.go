package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestGetPaidDramaIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(1)

	cancel := mrpc.SetMock(URIGetPaidDramas, func(input interface{}) (interface{}, error) {
		return []int64{1, 2}, nil
	})
	defer cancel()

	var c mrpc.UserContext
	paidDramaIDs, err := GetPaidDramaIDs(c, testUserID)
	require.NoError(err)
	assert.Equal([]int64{1, 2}, paidDramaIDs)
}
