package userapi

import (
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service"
)

// URIGetPaidDramas 获取用户已购买的剧集
const URIGetPaidDramas = "app://drama/get-paid-dramas"

// getPaidDramaIDsParam 获取用户已购的剧集 IDs 参数
type getPaidDramaIDsParam struct {
	UserID int64 `json:"user_id"`
}

// GetPaidDramaIDs 获取用户已购的剧集 IDs
func GetPaidDramaIDs(c mrpc.UserContext, userID int64) ([]int64, error) {
	param := &getPaidDramaIDsParam{
		UserID: userID,
	}
	var dramaIDs []int64
	err := service.MRPC.Do(c, URIGetPaidDramas, param, &dramaIDs)
	if err != nil {
		return nil, err
	}
	return dramaIDs, nil
}
