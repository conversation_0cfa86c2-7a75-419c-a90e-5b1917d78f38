package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, RankTypeDay)
	assert.Equal(2, RankTypeWeek)
	assert.Equal(3, RankTypeMonth)
	assert.Equal(4, RankTypeHour)
	assert.Equal(5, RankTypeGashaponWeek)
	assert.Equal(7, RankTypeNova)
	assert.Equal(8, RankTypeLastMonth)
	assert.Equal(9, rankTypeLimit)
	assert.Equal(101, RankTypeHourHomepage)
}

func TestUserLiveRankTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(userLiveRankParam{}, "type", "rank_num")
	kc.Check(UserLiveRankResp{}, "data")
	kc.Check(UserLiveRank{}, "room")
	kc.Check(Info{}, "user_id", "username", "iconurl", "revenue", "rank", "rank_up", "rank_changes", "is_nova")
	kc.Check(RoomInfo{}, "room_id", "catalog_id", "name", "announcement", "creator_id", "creator_username", "status", "cover_url")
	kc.Check(SimpleRoomStatus{}, "open")

	kc.CheckOmitEmpty(Info{}, "rank_changes", "is_nova")
}

func TestLiveRank(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mrpc.SetMock(URIUserLiveRank, func(input interface{}) (interface{}, error) {
		return UserLiveRankResp{
			Data: []*UserLiveRank{
				{
					Info: &Info{
						UserID:   12,
						Username: "测试用户",
						IconURL:  "https://test.com/test.png",
						Revenue:  100,
						Rank:     1,
					},
					Room: &RoomInfo{
						RoomID:          1111,
						CatalogID:       145,
						Name:            "测试直播间 1",
						Announcement:    "欢迎来到我的直播间！",
						CreatorID:       9467681,
						CreatorUsername: "用户 1",
						Status: &SimpleRoomStatus{
							Open: 1,
						},
						CoverURL: "https://test.com/cover.png",
					},
				},
			},
		}, nil
	})
	defer cancel()

	var c mrpc.UserContext
	rankList, err := LiveRank(c, RankTypeHour, 2)
	require.NoError(err)
	require.NotNil(rankList)
	assert.Len(rankList, 1)
}
