package appapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestGetMyFavors(t *testing.T) {
	// 设置模拟响应
	mockResp := GetMyFavorsResponse{
		VideoCard: map[string]VideoCard{
			"1": {
				Type:    1,
				ID:      21,
				Title:   "视频标题",
				Cover:   "http://static-test.maoercdn.com/test/test.jpg",
				SoundID: 2333,
			},
		},
		Modules: []Module{
			{
				ModuleID: 9,
				Title:    "音频模块标题",
				Type:     3,
			},
		},
		Marker: "233:1,234:1",
	}

	// 设置模拟RPC调用
	cancel := mrpc.SetMock(URIYouMightLikeMyFavors+"?is_mrpc=1&persona_id=123&user_id=456", func(input interface{}) (interface{}, error) {
		return mockResp, nil
	})
	defer cancel()

	// 执行测试
	var ctx mrpc.UserContext
	params := GetMyFavorsParam{
		PersonaID: 123,
		IsAll:     0,
		Marker:    "",
		UserID:    456,
	}

	resp, err := GetMyFavors(ctx, params)

	// 验证结果
	require.NoError(t, err)
	assert.NotNil(t, resp)

	// 验证视频大卡信息
	assert.NotEmpty(t, resp.VideoCard)
	videoCard, ok := resp.VideoCard["1"]
	assert.True(t, ok)
	assert.Equal(t, int64(21), videoCard.ID)
	assert.Equal(t, "视频标题", videoCard.Title)

	// 验证模块列表
	assert.NotEmpty(t, resp.Modules)
	assert.Equal(t, int64(9), resp.Modules[0].ModuleID)
	assert.Equal(t, "音频模块标题", resp.Modules[0].Title)

	// 验证标识值
	assert.Equal(t, "233:1,234:1", resp.Marker)
}

func TestGetRecommendPopup(t *testing.T) {
	// 设置模拟响应
	mockResp := RecommendGetPopupResponse{
		PopupURL: "http://static.missevan.com/standalone/app/test/index.html",
		OpenURL:  "missevan://live/463640018?from=ad.0.1.80000_0_0_0&event_id_from=ad.0.1.80000_0_0_0",
	}

	// 设置模拟 RPC 调用
	cancel := mrpc.SetMock(URIRecommendGetPopup, func(input interface{}) (interface{}, error) {
		return mockResp, nil
	})
	defer cancel()

	// 执行测试
	var ctx mrpc.UserContext
	params := RecommendGetPopupParam{
		Channel: "test_channel",
		Buvid:   "test_buvid",
		EquipID: "test_equip_id",
	}

	resp, err := GetRecommendPopup(ctx, params)

	// 验证结果
	require.NoError(t, err)
	assert.NotNil(t, resp)

	// 验证弹窗和跳转链接
	assert.Equal(t, "http://static.missevan.com/standalone/app/test/index.html", resp.PopupURL)
	assert.Equal(t, "missevan://live/463640018?from=ad.0.1.80000_0_0_0&event_id_from=ad.0.1.80000_0_0_0", resp.OpenURL)

	// 测试无弹窗和跳转链接的情况
	mockEmptyResp := RecommendGetPopupResponse{}
	cancel = mrpc.SetMock(URIRecommendGetPopup, func(input interface{}) (interface{}, error) {
		return mockEmptyResp, nil
	})
	defer cancel()

	respEmpty, err := GetRecommendPopup(ctx, params)
	require.NoError(t, err)
	assert.NotNil(t, respEmpty)
	assert.Empty(t, respEmpty.PopupURL)
	assert.Empty(t, respEmpty.OpenURL)
}
