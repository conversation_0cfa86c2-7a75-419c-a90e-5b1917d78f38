package appapi

import (
	"net/url"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/models/drama/dramacornermarkstyle"
	"github.com/MiaoSiLa/missevan-main/service"
)

// URI 常量
const (
	// URIYouMightLikeMyFavors 用户的推荐模块接口
	URIYouMightLikeMyFavors = "app://you-might-like/my-favors"
	// URIRecommendGetPopup 获取推荐弹窗和跳转链接
	URIRecommendGetPopup = "app://recommend/get-popup"
)

// GetMyFavorsIsAll 是否获取所有数据
const (
	GetMyFavorsIsAllFalse int = 0
	GetMyFavorsIsAllTrue  int = 1
)

// GetMyFavorsIsMRPC 是否使用 MRPC 请求
const (
	GetMyFavorsIsMRPCFalse int = 0
	GetMyFavorsIsMRPCTrue  int = 1
)

// GetMyFavorsParam 用户的推荐模块请求参数
type GetMyFavorsParam struct {
	PersonaID int64  `json:"persona_id"` // 画像 ID
	IsAll     int    `json:"is_all"`     // 是否获取所有数据 0：返回所需数据；1：返回所有数据
	IsMRPC    *int   `json:"is_mrpc"`    // 是否使用 MRPC 请求 0：使用 HTTP 请求；1：使用 MRPC 请求
	Marker    string `json:"marker"`     // 随机标识，第一次请求时不传该值
	UserID    int64  `json:"user_id"`    // 用户 ID
	Method    string `json:"_method"`    // 方法名，默认 "GET"，无需设置
}

// VideoResource 视频资源信息
type VideoResource struct {
	Quality   int    `json:"quality"`    // 视频质量，16: 360P; 32: 480P; 64: 720P; 128: 1080P
	Name      string `json:"name"`       // 质量名称
	ShortName string `json:"short_name"` // 质量简称
	URL       string `json:"url"`        // 视频地址
	Size      int64  `json:"size"`       // 视频大小，单位 Bytes
	Width     int    `json:"width"`      // 视频宽度
	Height    int    `json:"height"`     // 视频高度
	Status    int    `json:"status"`     // 视频播放状态
}

// GameCard 游戏卡片信息
type GameCard struct {
	ID                 int64  `json:"id"`                   // 游戏 ID
	Name               string `json:"name"`                 // 游戏名称
	URL                string `json:"url"`                  // 跳转链接
	Icon               string `json:"icon"`                 // 游戏图标
	Title              string `json:"title"`                // 标题
	Subtitle           string `json:"subtitle"`             // 副标题
	Status             int    `json:"status"`               // 状态
	DownloadURL        string `json:"download_url"`         // 下载链接
	PackageName        string `json:"package_name"`         // 包名
	PackageVersionCode int    `json:"package_version_code"` // 包版本号
}

// VideoCard 视频大卡信息
type VideoCard struct {
	Type      int             `json:"type"`      // 视频大卡类型，1: 普通视频大卡；2: 游戏视频大卡
	ID        int64           `json:"id"`        // 视频大卡 ID
	Title     string          `json:"title"`     // 视频标题
	Cover     string          `json:"cover"`     // 封面
	SoundID   int64           `json:"sound_id"`  // 音频 ID
	URL       string          `json:"url"`       // 视频落地页链接
	Priority  int             `json:"priority"`  // 是否优先于音频播放
	Duration  int64           `json:"duration"`  // 视频时长，单位毫秒
	Resources []VideoResource `json:"resources"` // 视频资源列表
	GameCard  *GameCard       `json:"game_card"` // 游戏卡片信息，仅游戏视频大卡返回
}

// RankElement 榜单元素
type RankElement struct {
	// 剧集类型
	ID         int64                            `json:"id,omitempty"`          // 剧集 ID
	Name       string                           `json:"name,omitempty"`        // 剧集名称
	Cover      string                           `json:"cover,omitempty"`       // 剧集封面
	Abstract   string                           `json:"abstract,omitempty"`    // 剧集简介
	CoverColor int64                            `json:"cover_color,omitempty"` // 背景图主颜色
	PayType    int                              `json:"pay_type,omitempty"`    // 付费类型
	ViewCount  int64                            `json:"view_count,omitempty"`  // 播放量
	Integrity  int                              `json:"integrity,omitempty"`   // 完结度
	Newest     string                           `json:"newest,omitempty"`      // 最近更新
	CornerMark *dramacornermarkstyle.CornerMark `json:"corner_mark,omitempty"` // 角标信息

	// 音频类型
	Duration   int64  `json:"duration,omitempty"`    // 音频时长
	Soundstr   string `json:"soundstr,omitempty"`    // 音频标题
	FrontCover string `json:"front_cover,omitempty"` // 音频封面
	UserID     int64  `json:"user_id,omitempty"`     // UP 主用户 ID
	Username   string `json:"username,omitempty"`    // UP 主用户名称
	IconURL    string `json:"iconurl,omitempty"`     // UP 主头像
	Video      bool   `json:"video,omitempty"`       // 音频是否包含对应的视频

	// 直播类型
	Revenue int64     `json:"revenue,omitempty"` // 收益
	RankUp  int64     `json:"rank_up,omitempty"` // 前一名的收益 - 自己的收益 + 1
	Room    *RoomInfo `json:"room,omitempty"`    // 直播间信息
}

// RoomInfo 直播间信息
type RoomInfo struct {
	RoomID          int64             `json:"room_id"`
	CatalogID       int64             `json:"catalog_id"`
	Name            string            `json:"name"`
	Announcement    string            `json:"announcement"`
	CreatorID       int64             `json:"creator_id"`
	CreatorUsername string            `json:"creator_username"`
	Status          *SimpleRoomStatus `json:"status"`
	CoverURL        string            `json:"cover_url"`
}

// SimpleRoomStatus 房间状态简要信息
type SimpleRoomStatus struct {
	Open int `json:"open"` // 开播状态
}

// RankData 排行榜数据
type RankData struct {
	Type        int           `json:"type"`         // 榜单类型
	Name        string        `json:"name"`         // 榜单名称
	Active      int           `json:"active"`       // 默认定位到该榜单
	OpenURL     string        `json:"open_url"`     // 跳转链接
	ElementType int           `json:"element_type"` // 榜单内容类型
	Elements    []RankElement `json:"elements"`     // 榜单数据
}

// More 更多按钮信息
type More struct {
	URL string `json:"url"` // 更多跳转链接
}

// Ranks 排行榜模块数据
type Ranks struct {
	Title string     `json:"title"` // 模块标题
	More  *More      `json:"more"`  // 更多按钮信息
	Data  []RankData `json:"data"`  // 榜单数据
}

// ModuleElement 模块元素
type ModuleElement struct {
	// 通用字段
	ID         int64                            `json:"id"`                    // 元素 ID
	FrontCover string                           `json:"front_cover"`           // 封面
	ViewCount  int64                            `json:"view_count"`            // 播放次数
	Sort       int                              `json:"sort"`                  // 排序
	ModuleID   int64                            `json:"module_id"`             // 模块 ID
	Type       int                              `json:"type"`                  // 类型，1: 音单；2: 剧集；3: 音频
	CornerMark *dramacornermarkstyle.CornerMark `json:"corner_mark,omitempty"` // 角标信息

	// 音单模块字段（type = 1）
	Title      string `json:"title,omitempty"`       // 音单标题
	Intro      string `json:"intro,omitempty"`       // 音单简介
	MusicCount int    `json:"music_count,omitempty"` // 音乐数量

	// 剧集模块字段（type = 2）
	Name       string `json:"name,omitempty"`        // 剧集名称
	Abstract   string `json:"abstract,omitempty"`    // 剧集简介
	CoverColor int64  `json:"cover_color,omitempty"` // 背景图主颜色
	Integrity  int    `json:"integrity,omitempty"`   // 完结度 1：长篇未完结；2：长篇完结；3：全一期；4：微小剧
	Newest     string `json:"newest,omitempty"`      // 最近更新
	PayType    int    `json:"pay_type,omitempty"`    // 付费类型，0：免费；1：单集付费；2：整剧付费
	Outline    any    `json:"outline,omitempty"`     // 剧集设定
	NeedPay    int    `json:"need_pay,omitempty"`    // 付费状态，0：免费；1：付费剧集未付费；2：付费剧集已付费

	// 音频模块字段（type = 3）
	Soundstr     string `json:"soundstr,omitempty"`      // 音频标题
	CommentCount int64  `json:"comment_count,omitempty"` // 弹幕数量
	AllComments  int64  `json:"all_comments,omitempty"`  // 总评论数量
	UserID       int64  `json:"user_id,omitempty"`       // UP 主的用户 ID
	Username     string `json:"username,omitempty"`      // UP 主的用户名
	Video        bool   `json:"video,omitempty"`         // 是否绑定了视频
}

// Module 模块信息
type Module struct {
	ModuleID int64           `json:"module_id"` // 模块 ID
	Title    string          `json:"title"`     // 模块标题
	Type     int             `json:"type"`      // 模块类型，1: 音单；2: 剧集；3: 音频
	Style    int             `json:"style"`     // 排版方式，0：竖版；1：横版；2：排行榜；3：滑动
	More     *More           `json:"more"`      // 更多按钮信息
	Elements []ModuleElement `json:"elements"`  // 模块元素
}

// GetMyFavorsResponse 用户的推荐模块响应数据
type GetMyFavorsResponse struct {
	VideoCard map[string]VideoCard `json:"video_card"` // 推荐模块中的视频大卡信息
	Ranks     *Ranks               `json:"ranks"`      // 排行榜模块数据
	Modules   []Module             `json:"modules"`    // 模块列表
	Marker    string               `json:"marker"`     // 标识值
}

// GetMyFavors 获取用户的推荐模块
func GetMyFavors(c mrpc.UserContext, params GetMyFavorsParam) (*GetMyFavorsResponse, error) {
	paramsWithMethod := params
	paramsWithMethod.Method = "GET"

	uri := URIYouMightLikeMyFavors
	q := url.Values{}
	if params.PersonaID != 0 {
		q.Add("persona_id", strconv.FormatInt(params.PersonaID, 10))
	}
	if params.IsAll != 0 {
		q.Add("is_all", strconv.Itoa(params.IsAll))
	}
	if params.IsMRPC == nil {
		isMRPC := GetMyFavorsIsMRPCTrue
		params.IsMRPC = &isMRPC
	}
	q.Add("is_mrpc", strconv.Itoa(*params.IsMRPC))
	if params.Marker != "" {
		q.Add("marker", params.Marker)
	}
	if params.UserID != 0 {
		q.Add("user_id", strconv.FormatInt(params.UserID, 10))
	}
	if len(q) > 0 {
		uri = uri + "?" + q.Encode()
	}

	var resp GetMyFavorsResponse
	err := service.MRPC.Do(c, uri, paramsWithMethod, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

// RecommendGetPopupParam 获取推荐弹窗和跳转链接请求参数
type RecommendGetPopupParam struct {
	Channel string `json:"channel"`  // 渠道标识
	Buvid   string `json:"buvid"`    // 唯一设备标识
	EquipID string `json:"equip_id"` // 设备 ID
}

// RecommendGetPopupResponse 获取推荐弹窗和跳转链接响应数据
type RecommendGetPopupResponse struct {
	PopupURL string `json:"popup_url,omitempty"` // 推荐弹窗 URL，无推荐弹窗的情况下不含该参数
	OpenURL  string `json:"open_url,omitempty"`  // 跳转地址，无跳转地址时不含该参数
}

// GetRecommendPopup 获取推荐弹窗和跳转链接
func GetRecommendPopup(c mrpc.UserContext, params RecommendGetPopupParam) (*RecommendGetPopupResponse, error) {
	var resp RecommendGetPopupResponse
	err := service.MRPC.Do(c, URIRecommendGetPopup, params, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}
